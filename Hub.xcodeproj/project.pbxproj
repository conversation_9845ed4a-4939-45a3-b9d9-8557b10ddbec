// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 71;
	objects = {

/* Begin PBXBuildFile section */
		2104B67F2935D258004C8133 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 2104B67E2935D258004C8133 /* Alamofire */; };
		2104B69429374EC0004C8133 /* CoreBluetooth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2104B69329374EC0004C8133 /* CoreBluetooth.framework */; };
		212485AC290F5E6D00E83EAC /* SnapKit in Frameworks */ = {isa = PBXBuildFile; productRef = 212485AB290F5E6D00E83EAC /* SnapKit */; };
		212485AF290F5E8900E83EAC /* RxDataSources in Frameworks */ = {isa = PBXBuildFile; productRef = 212485AE290F5E8900E83EAC /* RxDataSources */; };
		212485B2290F5E9F00E83EAC /* RxCocoa in Frameworks */ = {isa = PBXBuildFile; productRef = 212485B1290F5E9F00E83EAC /* RxCocoa */; };
		212485B4290F5E9F00E83EAC /* RxRelay in Frameworks */ = {isa = PBXBuildFile; productRef = 212485B3290F5E9F00E83EAC /* RxRelay */; };
		212485B6290F5E9F00E83EAC /* RxSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 212485B5290F5E9F00E83EAC /* RxSwift */; };
		2127F1F629F8CE28004EE9DB /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = 2127F1F529F8CE28004EE9DB /* Lottie */; };
		2146066E293EE071005EE5BC /* NetworkExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2146066D293EE070005EE5BC /* NetworkExtension.framework */; };
		21DF3AD2295AAF9B00D8B841 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21DF3AD1295AAF9B00D8B841 /* SystemConfiguration.framework */; };
		21E7C64729CAD55E004EEC89 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21E7C64629CAD55E004EEC89 /* WebKit.framework */; };
		21EF9C1129FA809000B09477 /* Gzip in Frameworks */ = {isa = PBXBuildFile; productRef = 21EF9C1029FA809000B09477 /* Gzip */; };
		6A0F2A952D2F70DE0098308F /* LookinServer in Frameworks */ = {isa = PBXBuildFile; productRef = 6A0F2A942D2F70DE0098308F /* LookinServer */; };
		6A2EACE42D5EB9DB00BE4422 /* DGCharts in Frameworks */ = {isa = PBXBuildFile; productRef = 6A2EACE32D5EB9DB00BE4422 /* DGCharts */; };
		6AEA64052D5EEF6F00324E1B /* DGCharts in Frameworks */ = {isa = PBXBuildFile; productRef = 6AEA64042D5EEF6F00324E1B /* DGCharts */; };
		7926F4F22D8D535D00A318D3 /* CombineSchedulers in Frameworks */ = {isa = PBXBuildFile; productRef = 7926F4F12D8D535D00A318D3 /* CombineSchedulers */; };
		7926F4F42D8D538C00A318D3 /* CombineSchedulers in Frameworks */ = {isa = PBXBuildFile; productRef = 7926F4F32D8D538C00A318D3 /* CombineSchedulers */; };
		793230DF2DE01E0900143B56 /* VLCKitSPM in Frameworks */ = {isa = PBXBuildFile; productRef = 793230DE2DE01E0900143B56 /* VLCKitSPM */; };
		793896CA2DE84FDE000E7EA9 /* WoodpeckeriOS in Frameworks */ = {isa = PBXBuildFile; productRef = 793896C92DE84FDE000E7EA9 /* WoodpeckeriOS */; };
		793896CC2DE97190000E7EA9 /* DebugSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 793896CB2DE97190000E7EA9 /* DebugSwift */; };
		794A83342D9252F1007E1078 /* SVProgressHUD in Frameworks */ = {isa = PBXBuildFile; productRef = 794A83332D9252F1007E1078 /* SVProgressHUD */; };
		795E2B232C5A0D700020CAEF /* DebugSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 795E2B222C5A0D700020CAEF /* DebugSwift */; };
		795E2B432C634E3C0020CAEF /* Atlantis in Frameworks */ = {isa = PBXBuildFile; productRef = 795E2B422C634E3C0020CAEF /* Atlantis */; };
		797B5F182D968BAB00F8F1B6 /* Atlantis in Frameworks */ = {isa = PBXBuildFile; productRef = 797B5F172D968BAB00F8F1B6 /* Atlantis */; };
		798720702D1A4CF000CA1867 /* Pretendard in Frameworks */ = {isa = PBXBuildFile; productRef = 7987206F2D1A4CF000CA1867 /* Pretendard */; };
		798720722D1A5B7300CA1867 /* Pretendard in Frameworks */ = {isa = PBXBuildFile; productRef = 798720712D1A5B7300CA1867 /* Pretendard */; };
		79A376322AEF99A500672E98 /* Gzip in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374542AEF99A500672E98 /* Gzip */; };
		79A376332AEF99A500672E98 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374482AEF99A500672E98 /* Alamofire */; };
		79A376352AEF99A500672E98 /* CVCalendar in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374662AEF99A500672E98 /* CVCalendar */; };
		79A376382AEF99A500672E98 /* CoreBluetooth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2104B69329374EC0004C8133 /* CoreBluetooth.framework */; };
		79A376392AEF99A500672E98 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 79A3745B2AEF99A500672E98 /* FirebaseMessaging */; };
		79A3763B2AEF99A500672E98 /* GRDB in Frameworks */ = {isa = PBXBuildFile; productRef = 79A3745E2AEF99A500672E98 /* GRDB */; };
		79A3763C2AEF99A500672E98 /* NetworkExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2146066D293EE070005EE5BC /* NetworkExtension.framework */; };
		79A3763E2AEF99A500672E98 /* SnapKit in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374402AEF99A500672E98 /* SnapKit */; };
		79A376412AEF99A500672E98 /* IQKeyboardManagerSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 79A3745C2AEF99A500672E98 /* IQKeyboardManagerSwift */; };
		79A376422AEF99A500672E98 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21DF3AD1295AAF9B00D8B841 /* SystemConfiguration.framework */; };
		79A376452AEF99A500672E98 /* RxSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374472AEF99A500672E98 /* RxSwift */; };
		79A376472AEF99A500672E98 /* RxRelay in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374462AEF99A500672E98 /* RxRelay */; };
		79A376482AEF99A500672E98 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374522AEF99A500672E98 /* Lottie */; };
		79A3764C2AEF99A500672E98 /* RxDataSources in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374422AEF99A500672E98 /* RxDataSources */; };
		79A3764D2AEF99A500672E98 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = 79A374592AEF99A500672E98 /* FirebaseCrashlytics */; };
		79A376522AEF99A500672E98 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21E7C64629CAD55E004EEC89 /* WebKit.framework */; };
		79C03D5B2D5ED87800813611 /* DGCharts in Frameworks */ = {isa = PBXBuildFile; productRef = 79C03D5A2D5ED87800813611 /* DGCharts */; };
		79C49A072E0BBD43006B680E /* SSAppUpdater in Frameworks */ = {isa = PBXBuildFile; productRef = 79C49A062E0BBD43006B680E /* SSAppUpdater */; };
		79C837FD2E1CFE460056746D /* Toast in Frameworks */ = {isa = PBXBuildFile; productRef = 79C837FC2E1CFE460056746D /* Toast */; };
		79C838002E1E11190056746D /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 79C837FF2E1E11190056746D /* Kingfisher */; };
		79E1FBC42AF33BD500AE6A8B /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = 79E1FBC32AF33BD500AE6A8B /* FirebaseCrashlytics */; };
		79E1FBC62AF33CEF00AE6A8B /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 79E1FBC52AF33CEF00AE6A8B /* FirebaseMessaging */; };
		7C2574B12AF872D40008E183 /* LookinServer in Frameworks */ = {isa = PBXBuildFile; productRef = 7C2574B02AF872D40008E183 /* LookinServer */; };
		7C3B9C822A92EAF20080D857 /* IQKeyboardManagerSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 7C3B9C812A92EAF20080D857 /* IQKeyboardManagerSwift */; };
		7C3D660B2AE145610056CA29 /* CVCalendar in Frameworks */ = {isa = PBXBuildFile; productRef = 7C3D660A2AE145610056CA29 /* CVCalendar */; };
		7C47FDB12B1D7D3900778216 /* FFmpeg in Frameworks */ = {isa = PBXBuildFile; productRef = 7C47FDB02B1D7D3900778216 /* FFmpeg */; };
		7C47FDB32B1D7D3900778216 /* FFmpeg-Kit in Frameworks */ = {isa = PBXBuildFile; productRef = 7C47FDB22B1D7D3900778216 /* FFmpeg-Kit */; };
		7C47FDB42B1D939500778216 /* FFmpeg in Embed Frameworks */ = {isa = PBXBuildFile; productRef = 7C47FDB02B1D7D3900778216 /* FFmpeg */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		7C47FDB62B1D939700778216 /* FFmpeg-Kit in Embed Frameworks */ = {isa = PBXBuildFile; productRef = 7C47FDB22B1D7D3900778216 /* FFmpeg-Kit */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		7C68F49B2C37A07A001877B3 /* RxGesture in Frameworks */ = {isa = PBXBuildFile; productRef = 7C68F49A2C37A07A001877B3 /* RxGesture */; };
		7C68F4C62C3D18F4001877B3 /* RxGesture in Frameworks */ = {isa = PBXBuildFile; productRef = 7C68F4C52C3D18F4001877B3 /* RxGesture */; };
		7C7797582C57715F005C2319 /* Atlantis in Frameworks */ = {isa = PBXBuildFile; productRef = 7C7797572C57715F005C2319 /* Atlantis */; };
		7C8D243E2BBF86C000A4C2A1 /* Starscream in Frameworks */ = {isa = PBXBuildFile; productRef = 7C8D243D2BBF86C000A4C2A1 /* Starscream */; };
		7CA260B72B50E4EB00B82323 /* RxCocoa in Frameworks */ = {isa = PBXBuildFile; productRef = 7CA260B62B50E4EB00B82323 /* RxCocoa */; };
		7CA9AC212BCE6A6C00704A1D /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 7CA9AC202BCE6A6C00704A1D /* ZIPFoundation */; };
		7CA9AC292BCE7A3800704A1D /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 7CA9AC282BCE7A3800704A1D /* ZIPFoundation */; };
		7CBF56E82A93456200DAC3E9 /* GRDB in Frameworks */ = {isa = PBXBuildFile; productRef = 7CBF56E72A93456200DAC3E9 /* GRDB */; };
		7CD8A8582BE085CD00695C01 /* Starscream in Frameworks */ = {isa = PBXBuildFile; productRef = 7CD8A8572BE085CD00695C01 /* Starscream */; };
		7CDC751A2B341ABE0003C065 /* GoogleMaps in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC75192B341ABE0003C065 /* GoogleMaps */; };
		7CDC751C2B341ABE0003C065 /* GoogleMapsBase in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC751B2B341ABE0003C065 /* GoogleMapsBase */; };
		7CDC751E2B341ABE0003C065 /* GoogleMapsCore in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC751D2B341ABE0003C065 /* GoogleMapsCore */; };
		7CDC75222B341AC90003C065 /* GoogleMaps in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC75212B341AC90003C065 /* GoogleMaps */; };
		7CDC75242B341AC90003C065 /* GoogleMapsBase in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC75232B341AC90003C065 /* GoogleMapsBase */; };
		7CDC75262B341AC90003C065 /* GoogleMapsCore in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDC75252B341AC90003C065 /* GoogleMapsCore */; };
		7CDEDB5A2B468AA000C1BB65 /* FFmpeg in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDEDB592B468AA000C1BB65 /* FFmpeg */; };
		7CDEDB5B2B468AA000C1BB65 /* FFmpeg in Embed Frameworks */ = {isa = PBXBuildFile; productRef = 7CDEDB592B468AA000C1BB65 /* FFmpeg */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		7CDEDB5D2B468AA000C1BB65 /* FFmpeg-Kit in Frameworks */ = {isa = PBXBuildFile; productRef = 7CDEDB5C2B468AA000C1BB65 /* FFmpeg-Kit */; };
		7CDEDB5E2B468AA000C1BB65 /* FFmpeg-Kit in Embed Frameworks */ = {isa = PBXBuildFile; productRef = 7CDEDB5C2B468AA000C1BB65 /* FFmpeg-Kit */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		79CD83AE2D3610BE00EF9315 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2124858B290F5DC800E83EAC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 79A3743F2AEF99A500672E98;
			remoteInfo = "Hub-Dev";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7C47FDB52B1D939500778216 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				7C47FDB62B1D939700778216 /* FFmpeg-Kit in Embed Frameworks */,
				7C47FDB42B1D939500778216 /* FFmpeg in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		7C7973F72B4688070052EF92 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				7CDEDB5E2B468AA000C1BB65 /* FFmpeg-Kit in Embed Frameworks */,
				7CDEDB5B2B468AA000C1BB65 /* FFmpeg in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		2104B69329374EC0004C8133 /* CoreBluetooth.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreBluetooth.framework; path = System/Library/Frameworks/CoreBluetooth.framework; sourceTree = SDKROOT; };
		2146066D293EE070005EE5BC /* NetworkExtension.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = NetworkExtension.framework; path = System/Library/Frameworks/NetworkExtension.framework; sourceTree = SDKROOT; };
		21DF3AD1295AAF9B00D8B841 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		21E7C64629CAD55E004EEC89 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		79559B512D8D5DE200510518 /* Hub.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Hub.app; sourceTree = BUILT_PRODUCTS_DIR; };
		79559B522D8D5DE200510518 /* Hub-Dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Hub-Dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		79559B532D8D5DE200510518 /* HubDevTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = HubDevTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7955A7D22D8D5E1C00510518 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				"Resources/Info-dev.plist",
				Resources/Info.plist,
			);
			target = 21248592290F5DC800E83EAC /* Hub */;
		};
		7955A7D32D8D5E1C00510518 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Config/Prod.xcconfig,
				README.md,
				Resources/Image/20250509_124330_INF_FLB.png,
				"Resources/Info-dev.plist",
				Resources/Info.plist,
			);
			target = 79A3743F2AEF99A500672E98 /* Hub-Dev */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7955A4252D8D5E1C00510518 /* Hub */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (7955A7D22D8D5E1C00510518 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, 7955A7D32D8D5E1C00510518 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = Hub; sourceTree = "<group>"; };
		79CD83AB2D3610BD00EF9315 /* HubDevTests */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = HubDevTests; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		21248590290F5DC800E83EAC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				212485B2290F5E9F00E83EAC /* RxCocoa in Frameworks */,
				79E1FBC62AF33CEF00AE6A8B /* FirebaseMessaging in Frameworks */,
				21EF9C1129FA809000B09477 /* Gzip in Frameworks */,
				2104B67F2935D258004C8133 /* Alamofire in Frameworks */,
				7C3D660B2AE145610056CA29 /* CVCalendar in Frameworks */,
				2104B69429374EC0004C8133 /* CoreBluetooth.framework in Frameworks */,
				793230DF2DE01E0900143B56 /* VLCKitSPM in Frameworks */,
				7CBF56E82A93456200DAC3E9 /* GRDB in Frameworks */,
				2146066E293EE071005EE5BC /* NetworkExtension.framework in Frameworks */,
				212485AC290F5E6D00E83EAC /* SnapKit in Frameworks */,
				793896CA2DE84FDE000E7EA9 /* WoodpeckeriOS in Frameworks */,
				7CDC751C2B341ABE0003C065 /* GoogleMapsBase in Frameworks */,
				79C837FD2E1CFE460056746D /* Toast in Frameworks */,
				797B5F182D968BAB00F8F1B6 /* Atlantis in Frameworks */,
				6A2EACE42D5EB9DB00BE4422 /* DGCharts in Frameworks */,
				79E1FBC42AF33BD500AE6A8B /* FirebaseCrashlytics in Frameworks */,
				7C3B9C822A92EAF20080D857 /* IQKeyboardManagerSwift in Frameworks */,
				21DF3AD2295AAF9B00D8B841 /* SystemConfiguration.framework in Frameworks */,
				7926F4F22D8D535D00A318D3 /* CombineSchedulers in Frameworks */,
				212485B6290F5E9F00E83EAC /* RxSwift in Frameworks */,
				212485B4290F5E9F00E83EAC /* RxRelay in Frameworks */,
				79C838002E1E11190056746D /* Kingfisher in Frameworks */,
				2127F1F629F8CE28004EE9DB /* Lottie in Frameworks */,
				798720722D1A5B7300CA1867 /* Pretendard in Frameworks */,
				7CA9AC292BCE7A3800704A1D /* ZIPFoundation in Frameworks */,
				212485AF290F5E8900E83EAC /* RxDataSources in Frameworks */,
				7CDC751A2B341ABE0003C065 /* GoogleMaps in Frameworks */,
				7CDEDB5A2B468AA000C1BB65 /* FFmpeg in Frameworks */,
				79C49A072E0BBD43006B680E /* SSAppUpdater in Frameworks */,
				6A0F2A952D2F70DE0098308F /* LookinServer in Frameworks */,
				7CDC751E2B341ABE0003C065 /* GoogleMapsCore in Frameworks */,
				21E7C64729CAD55E004EEC89 /* WebKit.framework in Frameworks */,
				7C68F4C62C3D18F4001877B3 /* RxGesture in Frameworks */,
				7CDEDB5D2B468AA000C1BB65 /* FFmpeg-Kit in Frameworks */,
				793896CC2DE97190000E7EA9 /* DebugSwift in Frameworks */,
				7CD8A8582BE085CD00695C01 /* Starscream in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79A3762F2AEF99A500672E98 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7C47FDB12B1D7D3900778216 /* FFmpeg in Frameworks */,
				79A376322AEF99A500672E98 /* Gzip in Frameworks */,
				79A376332AEF99A500672E98 /* Alamofire in Frameworks */,
				79A376352AEF99A500672E98 /* CVCalendar in Frameworks */,
				7C8D243E2BBF86C000A4C2A1 /* Starscream in Frameworks */,
				79A376382AEF99A500672E98 /* CoreBluetooth.framework in Frameworks */,
				7CDC75222B341AC90003C065 /* GoogleMaps in Frameworks */,
				798720702D1A4CF000CA1867 /* Pretendard in Frameworks */,
				7C68F49B2C37A07A001877B3 /* RxGesture in Frameworks */,
				79A376392AEF99A500672E98 /* FirebaseMessaging in Frameworks */,
				7C47FDB32B1D7D3900778216 /* FFmpeg-Kit in Frameworks */,
				795E2B232C5A0D700020CAEF /* DebugSwift in Frameworks */,
				795E2B432C634E3C0020CAEF /* Atlantis in Frameworks */,
				79A3763B2AEF99A500672E98 /* GRDB in Frameworks */,
				7C7797582C57715F005C2319 /* Atlantis in Frameworks */,
				79A3763C2AEF99A500672E98 /* NetworkExtension.framework in Frameworks */,
				79A3763E2AEF99A500672E98 /* SnapKit in Frameworks */,
				7926F4F42D8D538C00A318D3 /* CombineSchedulers in Frameworks */,
				79A376412AEF99A500672E98 /* IQKeyboardManagerSwift in Frameworks */,
				7CA9AC212BCE6A6C00704A1D /* ZIPFoundation in Frameworks */,
				7CA260B72B50E4EB00B82323 /* RxCocoa in Frameworks */,
				794A83342D9252F1007E1078 /* SVProgressHUD in Frameworks */,
				7C2574B12AF872D40008E183 /* LookinServer in Frameworks */,
				7CDC75262B341AC90003C065 /* GoogleMapsCore in Frameworks */,
				79A376422AEF99A500672E98 /* SystemConfiguration.framework in Frameworks */,
				6AEA64052D5EEF6F00324E1B /* DGCharts in Frameworks */,
				79A376452AEF99A500672E98 /* RxSwift in Frameworks */,
				79A376472AEF99A500672E98 /* RxRelay in Frameworks */,
				7CDC75242B341AC90003C065 /* GoogleMapsBase in Frameworks */,
				79A376482AEF99A500672E98 /* Lottie in Frameworks */,
				79A3764C2AEF99A500672E98 /* RxDataSources in Frameworks */,
				79A3764D2AEF99A500672E98 /* FirebaseCrashlytics in Frameworks */,
				79C03D5B2D5ED87800813611 /* DGCharts in Frameworks */,
				79A376522AEF99A500672E98 /* WebKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79CD83A72D3610BD00EF9315 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2104B69229374EC0004C8133 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				21E7C64629CAD55E004EEC89 /* WebKit.framework */,
				21DF3AD1295AAF9B00D8B841 /* SystemConfiguration.framework */,
				2146066D293EE070005EE5BC /* NetworkExtension.framework */,
				2104B69329374EC0004C8133 /* CoreBluetooth.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		2124858A290F5DC800E83EAC = {
			isa = PBXGroup;
			children = (
				7955A4252D8D5E1C00510518 /* Hub */,
				79CD83AB2D3610BD00EF9315 /* HubDevTests */,
				2104B69229374EC0004C8133 /* Frameworks */,
				79559B512D8D5DE200510518 /* Hub.app */,
				79559B522D8D5DE200510518 /* Hub-Dev.app */,
				79559B532D8D5DE200510518 /* HubDevTests.xctest */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		21248592290F5DC800E83EAC /* Hub */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 212485A7290F5DC800E83EAC /* Build configuration list for PBXNativeTarget "Hub" */;
			buildPhases = (
				2124858F290F5DC800E83EAC /* Sources */,
				21248590290F5DC800E83EAC /* Frameworks */,
				21248591290F5DC800E83EAC /* Resources */,
				7C7973F72B4688070052EF92 /* Embed Frameworks */,
				7C709B152B469249000FE908 /* Firebase dSYM script */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7955A4252D8D5E1C00510518 /* Hub */,
			);
			name = Hub;
			packageProductDependencies = (
				212485AB290F5E6D00E83EAC /* SnapKit */,
				212485AE290F5E8900E83EAC /* RxDataSources */,
				212485B1290F5E9F00E83EAC /* RxCocoa */,
				212485B3290F5E9F00E83EAC /* RxRelay */,
				212485B5290F5E9F00E83EAC /* RxSwift */,
				2104B67E2935D258004C8133 /* Alamofire */,
				2127F1F529F8CE28004EE9DB /* Lottie */,
				21EF9C1029FA809000B09477 /* Gzip */,
				7C3B9C812A92EAF20080D857 /* IQKeyboardManagerSwift */,
				7CBF56E72A93456200DAC3E9 /* GRDB */,
				7C3D660A2AE145610056CA29 /* CVCalendar */,
				79E1FBC32AF33BD500AE6A8B /* FirebaseCrashlytics */,
				79E1FBC52AF33CEF00AE6A8B /* FirebaseMessaging */,
				7CDC75192B341ABE0003C065 /* GoogleMaps */,
				7CDC751B2B341ABE0003C065 /* GoogleMapsBase */,
				7CDC751D2B341ABE0003C065 /* GoogleMapsCore */,
				7CDEDB592B468AA000C1BB65 /* FFmpeg */,
				7CDEDB5C2B468AA000C1BB65 /* FFmpeg-Kit */,
				7CA9AC282BCE7A3800704A1D /* ZIPFoundation */,
				7CD8A8572BE085CD00695C01 /* Starscream */,
				7C68F4C52C3D18F4001877B3 /* RxGesture */,
				798720712D1A5B7300CA1867 /* Pretendard */,
				6A0F2A942D2F70DE0098308F /* LookinServer */,
				6A2EACE32D5EB9DB00BE4422 /* DGCharts */,
				7926F4F12D8D535D00A318D3 /* CombineSchedulers */,
				797B5F172D968BAB00F8F1B6 /* Atlantis */,
				793230DE2DE01E0900143B56 /* VLCKitSPM */,
				793896C92DE84FDE000E7EA9 /* WoodpeckeriOS */,
				793896CB2DE97190000E7EA9 /* DebugSwift */,
				79C49A062E0BBD43006B680E /* SSAppUpdater */,
				79C837FC2E1CFE460056746D /* Toast */,
				79C837FF2E1E11190056746D /* Kingfisher */,
			);
			productName = Hub;
			productReference = 79559B512D8D5DE200510518 /* Hub.app */;
			productType = "com.apple.product-type.application";
		};
		79A3743F2AEF99A500672E98 /* Hub-Dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 79A3766C2AEF99A500672E98 /* Build configuration list for PBXNativeTarget "Hub-Dev" */;
			buildPhases = (
				79A374682AEF99A500672E98 /* Sources */,
				79A3762F2AEF99A500672E98 /* Frameworks */,
				79A376552AEF99A500672E98 /* Resources */,
				7C47FDB52B1D939500778216 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7955A4252D8D5E1C00510518 /* Hub */,
			);
			name = "Hub-Dev";
			packageProductDependencies = (
				79A374402AEF99A500672E98 /* SnapKit */,
				79A374422AEF99A500672E98 /* RxDataSources */,
				79A374462AEF99A500672E98 /* RxRelay */,
				79A374472AEF99A500672E98 /* RxSwift */,
				79A374482AEF99A500672E98 /* Alamofire */,
				79A374522AEF99A500672E98 /* Lottie */,
				79A374542AEF99A500672E98 /* Gzip */,
				79A374592AEF99A500672E98 /* FirebaseCrashlytics */,
				79A3745B2AEF99A500672E98 /* FirebaseMessaging */,
				79A3745C2AEF99A500672E98 /* IQKeyboardManagerSwift */,
				79A3745E2AEF99A500672E98 /* GRDB */,
				79A374662AEF99A500672E98 /* CVCalendar */,
				7C2574B02AF872D40008E183 /* LookinServer */,
				7C47FDB02B1D7D3900778216 /* FFmpeg */,
				7C47FDB22B1D7D3900778216 /* FFmpeg-Kit */,
				7CDC75212B341AC90003C065 /* GoogleMaps */,
				7CDC75232B341AC90003C065 /* GoogleMapsBase */,
				7CDC75252B341AC90003C065 /* GoogleMapsCore */,
				7CA260B62B50E4EB00B82323 /* RxCocoa */,
				7C8D243D2BBF86C000A4C2A1 /* Starscream */,
				7CA9AC202BCE6A6C00704A1D /* ZIPFoundation */,
				7C68F49A2C37A07A001877B3 /* RxGesture */,
				795E2B222C5A0D700020CAEF /* DebugSwift */,
				795E2B422C634E3C0020CAEF /* Atlantis */,
				7987206F2D1A4CF000CA1867 /* Pretendard */,
				79C03D5A2D5ED87800813611 /* DGCharts */,
				7926F4F32D8D538C00A318D3 /* CombineSchedulers */,
				794A83332D9252F1007E1078 /* SVProgressHUD */,
			);
			productName = Hub;
			productReference = 79559B522D8D5DE200510518 /* Hub-Dev.app */;
			productType = "com.apple.product-type.application";
		};
		79CD83A92D3610BD00EF9315 /* HubDevTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 79CD83B02D3610BE00EF9315 /* Build configuration list for PBXNativeTarget "HubDevTests" */;
			buildPhases = (
				79CD83A62D3610BD00EF9315 /* Sources */,
				79CD83A72D3610BD00EF9315 /* Frameworks */,
				79CD83A82D3610BD00EF9315 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				79CD83AF2D3610BE00EF9315 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				79CD83AB2D3610BD00EF9315 /* HubDevTests */,
			);
			name = HubDevTests;
			packageProductDependencies = (
			);
			productName = HubDevTests;
			productReference = 79559B532D8D5DE200510518 /* HubDevTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2124858B290F5DC800E83EAC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					21248592290F5DC800E83EAC = {
						CreatedOnToolsVersion = 13.4.1;
					};
					79CD83A92D3610BD00EF9315 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 79A3743F2AEF99A500672E98;
					};
				};
			};
			buildConfigurationList = 2124858E290F5DC800E83EAC /* Build configuration list for PBXProject "Hub" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				ko,
				ja,
				he,
				fr,
				es,
			);
			mainGroup = 2124858A290F5DC800E83EAC;
			packageReferences = (
				212485AA290F5E6D00E83EAC /* XCRemoteSwiftPackageReference "SnapKit" */,
				212485AD290F5E8900E83EAC /* XCRemoteSwiftPackageReference "RxDataSources" */,
				212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */,
				2104B67D2935D258004C8133 /* XCRemoteSwiftPackageReference "Alamofire" */,
				217858EB29C19BA80076B75C /* XCRemoteSwiftPackageReference "Charts" */,
				2127F1F429F8CE28004EE9DB /* XCRemoteSwiftPackageReference "lottie-ios" */,
				21EF9C0F29FA809000B09477 /* XCRemoteSwiftPackageReference "GzipSwift" */,
				7C3B9C802A92EAF20080D857 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */,
				7CBF56E62A93456200DAC3E9 /* XCRemoteSwiftPackageReference "GRDB.swift" */,
				7C3D66092AE145610056CA29 /* XCRemoteSwiftPackageReference "CVCalendar" */,
				79E1FBC22AF33BD500AE6A8B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				7C2574AF2AF872560008E183 /* XCRemoteSwiftPackageReference "LookinServer" */,
				7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */,
				7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */,
				7C8D24322BBF7B1D00A4C2A1 /* XCRemoteSwiftPackageReference "Starscream" */,
				7CA9AC1F2BCE6A6C00704A1D /* XCRemoteSwiftPackageReference "ZIPFoundation" */,
				7C68F4992C37A07A001877B3 /* XCRemoteSwiftPackageReference "RxGesture" */,
				793722012C51DBDB00DAAB28 /* XCRemoteSwiftPackageReference "DebugSwift" */,
				795E2B412C634E3C0020CAEF /* XCRemoteSwiftPackageReference "atlantis" */,
				7987206E2D1A4CF000CA1867 /* XCRemoteSwiftPackageReference "swift-pretendard" */,
				7926F4F02D8D535D00A318D3 /* XCRemoteSwiftPackageReference "combine-schedulers" */,
				794A83322D9252F1007E1078 /* XCRemoteSwiftPackageReference "SVProgressHUD" */,
				793230DD2DE01E0900143B56 /* XCRemoteSwiftPackageReference "vlckit-spm" */,
				793896C82DE84FDE000E7EA9 /* XCRemoteSwiftPackageReference "woodpecker-ios" */,
				79C49A052E0BBD43006B680E /* XCRemoteSwiftPackageReference "SSAppUpdater" */,
				79C837FB2E1CFE460056746D /* XCRemoteSwiftPackageReference "toast-swift" */,
				79C837FE2E1E11190056746D /* XCRemoteSwiftPackageReference "Kingfisher" */,
			);
			productRefGroup = 2124858A290F5DC800E83EAC;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				21248592290F5DC800E83EAC /* Hub */,
				79A3743F2AEF99A500672E98 /* Hub-Dev */,
				79CD83A92D3610BD00EF9315 /* HubDevTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		21248591290F5DC800E83EAC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79A376552AEF99A500672E98 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79CD83A82D3610BD00EF9315 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		7C709B152B469249000FE908 /* Firebase dSYM script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}",
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist",
				"$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist",
				"$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)",
			);
			name = "Firebase dSYM script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n\"${BUILD_DIR%/Build/*}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2124858F290F5DC800E83EAC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79A374682AEF99A500672E98 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79CD83A62D3610BD00EF9315 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		79CD83AF2D3610BE00EF9315 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 79A3743F2AEF99A500672E98 /* Hub-Dev */;
			targetProxy = 79CD83AE2D3610BE00EF9315 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		212485A5290F5DC800E83EAC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		212485A6290F5DC800E83EAC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Prod.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 6.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		212485A8290F5DC800E83EAC /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hub/Hub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 94;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Hub/Resources/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "The Hub App is using Bluetooth to find dashcam. Communicate with the dashcam via Bluetooth, get information from the dashcam and connect.";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "";
				INFOPLIST_KEY_NSCameraUsageDescription = "The Hub App uses Camera information to get video information of the dashcam.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "Connect with the Hub device";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "The Hub App uses microphone information to get video information of the dashcam.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app requires access to the photo library";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Allow “Vueroid DASH CAM” to access the gallery to use the function to save downloaded videos to the gallery.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.6;
				PRODUCT_BUNDLE_IDENTIFIER = com.ncand.VueroidHub;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "RELEASE DEBUG USE_UPPERCASE_SSID";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		212485A9290F5DC800E83EAC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Prod.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hub/Hub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 94;
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Hub/Resources/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "The Hub App is using Bluetooth to find dashcam. Communicate with the dashcam via Bluetooth, get information from the dashcam and connect.";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "";
				INFOPLIST_KEY_NSCameraUsageDescription = "The Hub App uses Camera information to get video information of the dashcam.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "Connect with the Hub device";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "The Hub App uses microphone information to get video information of the dashcam.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app requires access to the photo library";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Allow “Vueroid DASH CAM” to access the gallery to use the function to save downloaded videos to the gallery.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.6;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.ncand.VueroidHub;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "RELEASE USE_UPPERCASE_SSID";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		79A3766D2AEF99A500672E98 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-DEV";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hub/Hub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Hub/Resources/Info-dev.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "The Hub App is using Bluetooth to find dashcam. Communicate with the dashcam via Bluetooth, get information from the dashcam and connect.";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "";
				INFOPLIST_KEY_NSCameraUsageDescription = "The Hub App uses Camera information to get video information of the dashcam.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "For Debug";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "The Hub App uses microphone information to get video information of the dashcam.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app requires access to the photo library";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Allow “Vueroid DASH CAM” to access the gallery to use the function to save downloaded videos to the gallery.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 9.9.9;
				PRODUCT_BUNDLE_IDENTIFIER = com.ncand.VueroidHubDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEVELOP DEBUG";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		79A3766E2AEF99A500672E98 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = 7955A4252D8D5E1C00510518 /* Hub */;
			baseConfigurationReferenceRelativePath = Config/Prod.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-DEV";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Hub/Hub.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = "";
				"GCC_PREPROCESSOR_DEFINITIONS[arch=*]" = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Hub/Resources/Info-dev.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "The Hub App is using Bluetooth to find dashcam. Communicate with the dashcam via Bluetooth, get information from the dashcam and connect.";
				INFOPLIST_KEY_NSCalendarsUsageDescription = "";
				INFOPLIST_KEY_NSCameraUsageDescription = "The Hub App uses Camera information to get video information of the dashcam.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "For Debug";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Allow access to your location to connect IoT devices";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "The Hub App uses microphone information to get video information of the dashcam.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app requires access to the photo library";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Allow “Vueroid DASH CAM” to access the gallery to use the function to save downloaded videos to the gallery.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 9.9.9;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.ncand.VueroidHubDev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEVELOP;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		79CD83B12D3610BE00EF9315 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = kr.ncn.HubDevTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Hub-Dev.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Hub-Dev";
			};
			name = Debug;
		};
		79CD83B22D3610BE00EF9315 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = GCHCSAWTF2;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = kr.ncn.HubDevTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Hub-Dev.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Hub-Dev";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2124858E290F5DC800E83EAC /* Build configuration list for PBXProject "Hub" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				212485A5290F5DC800E83EAC /* Debug */,
				212485A6290F5DC800E83EAC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		212485A7290F5DC800E83EAC /* Build configuration list for PBXNativeTarget "Hub" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				212485A8290F5DC800E83EAC /* Debug */,
				212485A9290F5DC800E83EAC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		79A3766C2AEF99A500672E98 /* Build configuration list for PBXNativeTarget "Hub-Dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				79A3766D2AEF99A500672E98 /* Debug */,
				79A3766E2AEF99A500672E98 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		79CD83B02D3610BE00EF9315 /* Build configuration list for PBXNativeTarget "HubDevTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				79CD83B12D3610BE00EF9315 /* Debug */,
				79CD83B22D3610BE00EF9315 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		2104B67D2935D258004C8133 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		212485AA290F5E6D00E83EAC /* XCRemoteSwiftPackageReference "SnapKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SnapKit/SnapKit.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		212485AD290F5E8900E83EAC /* XCRemoteSwiftPackageReference "RxDataSources" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RxSwiftCommunity/RxDataSources.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ReactiveX/RxSwift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.6.0;
			};
		};
		2127F1F429F8CE28004EE9DB /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.0;
			};
		};
		217858EB29C19BA80076B75C /* XCRemoteSwiftPackageReference "Charts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/danielgindi/Charts.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.1.0;
			};
		};
		21EF9C0F29FA809000B09477 /* XCRemoteSwiftPackageReference "GzipSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/1024jp/GzipSwift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		7926F4F02D8D535D00A318D3 /* XCRemoteSwiftPackageReference "combine-schedulers" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/pointfreeco/combine-schedulers";
			requirement = {
				kind = exactVersion;
				version = 1.0.3;
			};
		};
		793230DD2DE01E0900143B56 /* XCRemoteSwiftPackageReference "vlckit-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/tylerjonesio/vlckit-spm";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.6.0;
			};
		};
		793722012C51DBDB00DAAB28 /* XCRemoteSwiftPackageReference "DebugSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/DebugSwift/DebugSwift";
			requirement = {
				kind = exactVersion;
				version = 1.1.0;
			};
		};
		793896C82DE84FDE000E7EA9 /* XCRemoteSwiftPackageReference "woodpecker-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/appwoodpecker/woodpecker-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.4.3;
			};
		};
		794A83322D9252F1007E1078 /* XCRemoteSwiftPackageReference "SVProgressHUD" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SVProgressHUD/SVProgressHUD.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.3.1;
			};
		};
		795E2B412C634E3C0020CAEF /* XCRemoteSwiftPackageReference "atlantis" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ProxymanApp/atlantis";
			requirement = {
				kind = exactVersion;
				version = 1.28.0;
			};
		};
		7987206E2D1A4CF000CA1867 /* XCRemoteSwiftPackageReference "swift-pretendard" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/wonkwh/swift-pretendard";
			requirement = {
				kind = exactVersion;
				version = 0.4.0;
			};
		};
		79A374412AEF99A500672E98 /* XCRemoteSwiftPackageReference "SnapKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SnapKit/SnapKit.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		79A374432AEF99A500672E98 /* XCRemoteSwiftPackageReference "RxDataSources" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RxSwiftCommunity/RxDataSources.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		79A374452AEF99A500672E98 /* XCRemoteSwiftPackageReference "RxSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ReactiveX/RxSwift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.6.0;
			};
		};
		79A374492AEF99A500672E98 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		79A374532AEF99A500672E98 /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.0;
			};
		};
		79A374552AEF99A500672E98 /* XCRemoteSwiftPackageReference "GzipSwift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/1024jp/GzipSwift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
		79A374572AEF99A500672E98 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 9.0.0;
			};
		};
		79A3745D2AEF99A500672E98 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hackiftekhar/IQKeyboardManager.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.0.0;
			};
		};
		79A3745F2AEF99A500672E98 /* XCRemoteSwiftPackageReference "GRDB.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/groue/GRDB.swift.git";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		79A374672AEF99A500672E98 /* XCRemoteSwiftPackageReference "CVCalendar" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/CVCalendar/CVCalendar.git";
			requirement = {
				kind = exactVersion;
				version = 1.7.0;
			};
		};
		79C49A052E0BBD43006B680E /* XCRemoteSwiftPackageReference "SSAppUpdater" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SimformSolutionsPvtLtd/SSAppUpdater";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.7.0;
			};
		};
		79C837FB2E1CFE460056746D /* XCRemoteSwiftPackageReference "toast-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/wonkwh/toast-swift.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
		79C837FE2E1E11190056746D /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.4.0;
			};
		};
		79E1FBC22AF33BD500AE6A8B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 10.17.0;
			};
		};
		7C2574AF2AF872560008E183 /* XCRemoteSwiftPackageReference "LookinServer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/QMUI/LookinServer/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.3;
			};
		};
		7C3B9C802A92EAF20080D857 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hackiftekhar/IQKeyboardManager.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.0.0;
			};
		};
		7C3D66092AE145610056CA29 /* XCRemoteSwiftPackageReference "CVCalendar" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/CVCalendar/CVCalendar.git";
			requirement = {
				kind = exactVersion;
				version = 1.7.0;
			};
		};
		7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/tylerjonesio/ffmpeg-kit-spm";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.1.0;
			};
		};
		7C68F4992C37A07A001877B3 /* XCRemoteSwiftPackageReference "RxGesture" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RxSwiftCommunity/RxGesture";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.4;
			};
		};
		7C7797562C57715F005C2319 /* XCRemoteSwiftPackageReference "atlantis" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ProxymanApp/atlantis";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.24.0;
			};
		};
		7C8D24322BBF7B1D00A4C2A1 /* XCRemoteSwiftPackageReference "Starscream" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/daltoniam/Starscream.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.8;
			};
		};
		7CA9AC1F2BCE6A6C00704A1D /* XCRemoteSwiftPackageReference "ZIPFoundation" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/weichsel/ZIPFoundation";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.9.19;
			};
		};
		7CBF56E62A93456200DAC3E9 /* XCRemoteSwiftPackageReference "GRDB.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/groue/GRDB.swift.git";
			requirement = {
				kind = exactVersion;
				version = 7.5.0;
			};
		};
		7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/googlemaps/ios-maps-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		2104B67E2935D258004C8133 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2104B67D2935D258004C8133 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		212485AB290F5E6D00E83EAC /* SnapKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485AA290F5E6D00E83EAC /* XCRemoteSwiftPackageReference "SnapKit" */;
			productName = SnapKit;
		};
		212485AE290F5E8900E83EAC /* RxDataSources */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485AD290F5E8900E83EAC /* XCRemoteSwiftPackageReference "RxDataSources" */;
			productName = RxDataSources;
		};
		212485B1290F5E9F00E83EAC /* RxCocoa */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxCocoa;
		};
		212485B3290F5E9F00E83EAC /* RxRelay */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxRelay;
		};
		212485B5290F5E9F00E83EAC /* RxSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxSwift;
		};
		2127F1F529F8CE28004EE9DB /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = 2127F1F429F8CE28004EE9DB /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
		21EF9C1029FA809000B09477 /* Gzip */ = {
			isa = XCSwiftPackageProductDependency;
			package = 21EF9C0F29FA809000B09477 /* XCRemoteSwiftPackageReference "GzipSwift" */;
			productName = Gzip;
		};
		6A0F2A942D2F70DE0098308F /* LookinServer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2574AF2AF872560008E183 /* XCRemoteSwiftPackageReference "LookinServer" */;
			productName = LookinServer;
		};
		6A2EACE32D5EB9DB00BE4422 /* DGCharts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 217858EB29C19BA80076B75C /* XCRemoteSwiftPackageReference "Charts" */;
			productName = DGCharts;
		};
		6AEA64042D5EEF6F00324E1B /* DGCharts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 217858EB29C19BA80076B75C /* XCRemoteSwiftPackageReference "Charts" */;
			productName = DGCharts;
		};
		7926F4F12D8D535D00A318D3 /* CombineSchedulers */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7926F4F02D8D535D00A318D3 /* XCRemoteSwiftPackageReference "combine-schedulers" */;
			productName = CombineSchedulers;
		};
		7926F4F32D8D538C00A318D3 /* CombineSchedulers */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7926F4F02D8D535D00A318D3 /* XCRemoteSwiftPackageReference "combine-schedulers" */;
			productName = CombineSchedulers;
		};
		793230DE2DE01E0900143B56 /* VLCKitSPM */ = {
			isa = XCSwiftPackageProductDependency;
			package = 793230DD2DE01E0900143B56 /* XCRemoteSwiftPackageReference "vlckit-spm" */;
			productName = VLCKitSPM;
		};
		793896C92DE84FDE000E7EA9 /* WoodpeckeriOS */ = {
			isa = XCSwiftPackageProductDependency;
			package = 793896C82DE84FDE000E7EA9 /* XCRemoteSwiftPackageReference "woodpecker-ios" */;
			productName = WoodpeckeriOS;
		};
		793896CB2DE97190000E7EA9 /* DebugSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 793722012C51DBDB00DAAB28 /* XCRemoteSwiftPackageReference "DebugSwift" */;
			productName = DebugSwift;
		};
		794A83332D9252F1007E1078 /* SVProgressHUD */ = {
			isa = XCSwiftPackageProductDependency;
			package = 794A83322D9252F1007E1078 /* XCRemoteSwiftPackageReference "SVProgressHUD" */;
			productName = SVProgressHUD;
		};
		795E2B222C5A0D700020CAEF /* DebugSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 793722012C51DBDB00DAAB28 /* XCRemoteSwiftPackageReference "DebugSwift" */;
			productName = DebugSwift;
		};
		795E2B422C634E3C0020CAEF /* Atlantis */ = {
			isa = XCSwiftPackageProductDependency;
			package = 795E2B412C634E3C0020CAEF /* XCRemoteSwiftPackageReference "atlantis" */;
			productName = Atlantis;
		};
		797B5F172D968BAB00F8F1B6 /* Atlantis */ = {
			isa = XCSwiftPackageProductDependency;
			package = 795E2B412C634E3C0020CAEF /* XCRemoteSwiftPackageReference "atlantis" */;
			productName = Atlantis;
		};
		7987206F2D1A4CF000CA1867 /* Pretendard */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7987206E2D1A4CF000CA1867 /* XCRemoteSwiftPackageReference "swift-pretendard" */;
			productName = Pretendard;
		};
		798720712D1A5B7300CA1867 /* Pretendard */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7987206E2D1A4CF000CA1867 /* XCRemoteSwiftPackageReference "swift-pretendard" */;
			productName = Pretendard;
		};
		79A374402AEF99A500672E98 /* SnapKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374412AEF99A500672E98 /* XCRemoteSwiftPackageReference "SnapKit" */;
			productName = SnapKit;
		};
		79A374422AEF99A500672E98 /* RxDataSources */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374432AEF99A500672E98 /* XCRemoteSwiftPackageReference "RxDataSources" */;
			productName = RxDataSources;
		};
		79A374462AEF99A500672E98 /* RxRelay */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374452AEF99A500672E98 /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxRelay;
		};
		79A374472AEF99A500672E98 /* RxSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374452AEF99A500672E98 /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxSwift;
		};
		79A374482AEF99A500672E98 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374492AEF99A500672E98 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		79A374522AEF99A500672E98 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374532AEF99A500672E98 /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};
		79A374542AEF99A500672E98 /* Gzip */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374552AEF99A500672E98 /* XCRemoteSwiftPackageReference "GzipSwift" */;
			productName = Gzip;
		};
		79A374592AEF99A500672E98 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374572AEF99A500672E98 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		79A3745B2AEF99A500672E98 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374572AEF99A500672E98 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		79A3745C2AEF99A500672E98 /* IQKeyboardManagerSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A3745D2AEF99A500672E98 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */;
			productName = IQKeyboardManagerSwift;
		};
		79A3745E2AEF99A500672E98 /* GRDB */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A3745F2AEF99A500672E98 /* XCRemoteSwiftPackageReference "GRDB.swift" */;
			productName = GRDB;
		};
		79A374662AEF99A500672E98 /* CVCalendar */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79A374672AEF99A500672E98 /* XCRemoteSwiftPackageReference "CVCalendar" */;
			productName = CVCalendar;
		};
		79C03D5A2D5ED87800813611 /* DGCharts */ = {
			isa = XCSwiftPackageProductDependency;
			package = 217858EB29C19BA80076B75C /* XCRemoteSwiftPackageReference "Charts" */;
			productName = DGCharts;
		};
		79C49A062E0BBD43006B680E /* SSAppUpdater */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79C49A052E0BBD43006B680E /* XCRemoteSwiftPackageReference "SSAppUpdater" */;
			productName = SSAppUpdater;
		};
		79C837FC2E1CFE460056746D /* Toast */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79C837FB2E1CFE460056746D /* XCRemoteSwiftPackageReference "toast-swift" */;
			productName = Toast;
		};
		79C837FF2E1E11190056746D /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79C837FE2E1E11190056746D /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		79E1FBC32AF33BD500AE6A8B /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79E1FBC22AF33BD500AE6A8B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		79E1FBC52AF33CEF00AE6A8B /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 79E1FBC22AF33BD500AE6A8B /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		7C2574B02AF872D40008E183 /* LookinServer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C2574AF2AF872560008E183 /* XCRemoteSwiftPackageReference "LookinServer" */;
			productName = LookinServer;
		};
		7C3B9C812A92EAF20080D857 /* IQKeyboardManagerSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C3B9C802A92EAF20080D857 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */;
			productName = IQKeyboardManagerSwift;
		};
		7C3D660A2AE145610056CA29 /* CVCalendar */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C3D66092AE145610056CA29 /* XCRemoteSwiftPackageReference "CVCalendar" */;
			productName = CVCalendar;
		};
		7C47FDB02B1D7D3900778216 /* FFmpeg */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */;
			productName = FFmpeg;
		};
		7C47FDB22B1D7D3900778216 /* FFmpeg-Kit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */;
			productName = "FFmpeg-Kit";
		};
		7C68F49A2C37A07A001877B3 /* RxGesture */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C68F4992C37A07A001877B3 /* XCRemoteSwiftPackageReference "RxGesture" */;
			productName = RxGesture;
		};
		7C68F4C52C3D18F4001877B3 /* RxGesture */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C68F4992C37A07A001877B3 /* XCRemoteSwiftPackageReference "RxGesture" */;
			productName = RxGesture;
		};
		7C7797572C57715F005C2319 /* Atlantis */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C7797562C57715F005C2319 /* XCRemoteSwiftPackageReference "atlantis" */;
			productName = Atlantis;
		};
		7C8D243D2BBF86C000A4C2A1 /* Starscream */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C8D24322BBF7B1D00A4C2A1 /* XCRemoteSwiftPackageReference "Starscream" */;
			productName = Starscream;
		};
		7CA260B62B50E4EB00B82323 /* RxCocoa */ = {
			isa = XCSwiftPackageProductDependency;
			package = 212485B0290F5E9F00E83EAC /* XCRemoteSwiftPackageReference "RxSwift" */;
			productName = RxCocoa;
		};
		7CA9AC202BCE6A6C00704A1D /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CA9AC1F2BCE6A6C00704A1D /* XCRemoteSwiftPackageReference "ZIPFoundation" */;
			productName = ZIPFoundation;
		};
		7CA9AC282BCE7A3800704A1D /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CA9AC1F2BCE6A6C00704A1D /* XCRemoteSwiftPackageReference "ZIPFoundation" */;
			productName = ZIPFoundation;
		};
		7CBF56E72A93456200DAC3E9 /* GRDB */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CBF56E62A93456200DAC3E9 /* XCRemoteSwiftPackageReference "GRDB.swift" */;
			productName = GRDB;
		};
		7CD8A8572BE085CD00695C01 /* Starscream */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C8D24322BBF7B1D00A4C2A1 /* XCRemoteSwiftPackageReference "Starscream" */;
			productName = Starscream;
		};
		7CDC75192B341ABE0003C065 /* GoogleMaps */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMaps;
		};
		7CDC751B2B341ABE0003C065 /* GoogleMapsBase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMapsBase;
		};
		7CDC751D2B341ABE0003C065 /* GoogleMapsCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMapsCore;
		};
		7CDC75212B341AC90003C065 /* GoogleMaps */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMaps;
		};
		7CDC75232B341AC90003C065 /* GoogleMapsBase */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMapsBase;
		};
		7CDC75252B341AC90003C065 /* GoogleMapsCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7CDC75182B341AA90003C065 /* XCRemoteSwiftPackageReference "ios-maps-sdk" */;
			productName = GoogleMapsCore;
		};
		7CDEDB592B468AA000C1BB65 /* FFmpeg */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */;
			productName = FFmpeg;
		};
		7CDEDB5C2B468AA000C1BB65 /* FFmpeg-Kit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7C47FDAF2B1D7D3900778216 /* XCRemoteSwiftPackageReference "ffmpeg-kit-spm" */;
			productName = "FFmpeg-Kit";
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2124858B290F5DC800E83EAC /* Project object */;
}
