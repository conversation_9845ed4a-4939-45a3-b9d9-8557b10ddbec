//
//  AppEnvironment.swift
//  Hub
//
//  Created by ncn on 4/2/24.
//
// https://www.pointfree.co/blog/posts/21-how-to-control-the-world

import Foundation

#if QA || DEV
  import LocalConsole
#endif

enum WillAppearWith {
  case toMode
}

enum S1Channel: Int {
  case F = 0b001
  case FR = 0b011
  case FI = 0b101
  case FRI = 0b111

  var displayNames: [String] {
    switch self {
    case .F: return [L.channel_front.localized]
    case .FR: return [L.channel_front.localized, L.channel_rear.localized]
    case .FI: return [L.channel_front.localized, L.channel_interior.localized]
    case .FRI: return [L.channel_front.localized, L.channel_rear.localized, L.channel_interior.localized]
    }
  }
}

enum HotSpotState: String, CustomDebugStringConvertible {
  case connecting
  case connected
  case canceled
  case failed
  case none

  var debugDescription: String {
    self.rawValue
  }
}

struct AppEnvironment {
  let firmWareUrl = UrlList.firmwareUrl()
  var firmWarePrefix: String
  var modelName: String
  var deviceVersion: Version = .init(1, 00)
  var remoteFirmwareVersion: Version = .init(0, 00)
  var notificationHandler: NotificationCenter = NotificationCenter.default
  #if QA || DEV
    var console = LCManager.shared
  #endif
  var useKvsWebRTC = false
  var awsRegion: String {
    AppManager.shared.awsRegion ?? "ap-northeast-2"
  }

  var serialNumber: String {
    AppManager.shared.dashcam?.serial ?? AppManager.shared.dashcam?.udG?.serial ?? ""
  }

  var timeRegion: Int?
  var timezone: TimeZone {
#if HUB
    if let timeRegion = Current.timeRegion {
      let timeZoneStr = _timezone[timeRegion]
      aLogger.info("get timeRegion: \(timeZoneStr)")
      let timeZoneID = hubTimeZoneIdentifiers[timeZoneStr] ?? "Europe/Dublin"
      aLogger.info("timeZoneID: \(timeZoneID)")
      return TimeZone(identifier: timeZoneID) ?? .current
    }
#endif
    // FIXME: Hub 에서는 device에서 timezone에 따라 변경된 값 저장. app에서는 UTC time만 사용
    return TimeZone(secondsFromGMT: 0) ?? .current
  }

  let milliseconds = Int(Date().timeIntervalSince1970 * 1000)

  var locale: Locale {
    let timezone = AppManager.shared.dashcam?.timeZone ?? "+9:00 Seoul, Tokyo"
    let timeZoneID = timeZoneIdentifiers[timezone] ?? "Asia/Seoul"
    let localeID = timeZoneLocaleMap[timeZoneID] ?? "ko_KR"
    return Locale(identifier: localeID)
  }
  
  var lang: LangType {
    // App's language setting
    let code = Locale.current.languageCode?.components(separatedBy: "_").first?.uppercased() ?? "EN"
    var langType: LangType?
    switch code {
    case "EN", "KO", "ES":
      langType = LangType(rawValue: code)
    default:
      langType = LangType.en
    }
    return  langType ?? LangType.en
  }

  var s1Channel: S1Channel {
    if let info = AppManager.shared.deviceInfo,
      let channelBits = info.vchannelbits {
       return channelBits.attachChannel
    }
    
    if let initInfo = AppManager.shared.initInfo,
       let channelBits = initInfo.vchannelbits {
      return channelBits.attachChannel
    }
    
    return .FRI
  }

  public var isConnect: Bool {
    let models = UserDefaults.shared.dashcamCardCellModels
    return models.contains { $0.isConnect }
//    return true
  }

  public var connectedMac: String?
  var appKeepAliveTimerControl: RepeatingTimerControl?

  var bleScanTimeout: TimeInterval = 60
  var wifiConnectRetryCount = 3

  var mode: AppModeType {
    AppManager.shared.mode
  }

  var isSecure: Bool = false
  
  /// Selection Count
  var playbackSelectionLimit = 3
  
  var speedUnit: SpeedType = .kph
  var wifiHistorySpeedUnit: SpeedType = .kph
  var playbackItemSelected = false
  var connectingWifiHotspot: HotSpotState = .none
  var connectedWifiPassWord: String = "238k44bh"
  var connectedWifiSsid: String?
  let requiredMB: Double = 512 // 512MB
  
  let companyLocation = (37.400280, 127.106918)
}

extension AppEnvironment {
  var isUpdateAvailable: Bool {
    return deviceVersion < remoteFirmwareVersion
  }
}

#if DEBUG
  var Current = AppEnvironment(
//    firmWarePrefix: "S1-4K_",
    firmWarePrefix: "Vueroid_",
    modelName: "S1-4K",
    isSecure: false
  )
#else
  var Current = AppEnvironment(
    firmWarePrefix: "Vueroid_",
    modelName: "S1-4K",
    isSecure: true
  )
#endif
