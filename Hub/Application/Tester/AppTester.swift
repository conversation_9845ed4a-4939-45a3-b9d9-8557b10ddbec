//
//  AppTester.swift
//  Hub
//
//  Created by ncn on 5/30/25.
//

import DebugSwift
import Kingfisher

func setCustomAction() {
  DebugSwift.App.shared.customAction = {[
    .init(
      title: "Tester", actions: [
        .init(title: "Foundation") {
          testStringExtension()
        },
        .init(title: "HubFileNameGenerator") {
          testHubFileNameGenerator()
        },
        .init(title: "Clear KF cache") {
          KingfisherManager.shared.cache.clearMemoryCache()
          KingfisherManager.shared.cache.clearDiskCache()
        },
      ]
    )
  ]}
}

// MARK: - Foundation Extension Test
func testStringExtension() {
  let result = rot13and5("jreg7890")
  tLogger.warning("rot13n5: \(result)")
  
  let fileName = "20250605_064442_EVT_F_N.mp4"
  let fileName2 = "001_20250605_064442_EVT_F_N.jpg"
  let result2 = fileName.isBookmarkFileNamePattern()
  let result3 = fileName2.isBookmarkFileNamePattern()
  
  tLogger.warning("isBookmarkFileNamePattern : 20250605_064442_EVT_F_N.mp4: \(result2)")
  tLogger.warning("isBookmarkFileNamePattern: 001_20250605_064442_EVT_F_N.jpg: \(result3)")
}

func testHubFileNameGenerator() {
  #if DEBUG
  HubFileNameGeneratorExamples.runAllExamples()
  #endif
}
