//
//  TestCropController.swift
//  Hub
//
//  Created by ncn on 6/9/25.
//

import UIKit

class TestCropController: UIViewController {
  
  var imageScrollView: ImageScrollView!
  
  override func viewDidLoad() {
    super.viewDidLoad()
    imageScrollView = ImageScrollView()
    imageScrollView.setup()
    imageScrollView.imageScrollViewDelegate = self
    imageScrollView.imageContentMode = .aspectFill
    imageScrollView.initialOffset = .center
    
    if let image = UIImage(named: "20250509_124330_INF_FLB.png") {
      imageScrollView.display(image: image)
    }
    
    view.addSubview(imageScrollView)
    imageScrollView.pinInSuperview()
    view.transform = CGAffineTransform(rotationAngle: .pi / 2)
    imageScrollView.changeOrientationNotification()
  }
}

extension TestCropController: ImageScrollViewDelegate {
    func imageScrollViewDidChangeOrientation(imageScrollView: ImageScrollView) {
      imageScrollView.pinInSuperview()
        print("Did change orientation")
    }
    
    func scrollViewDidEndZooming(_ scrollView: UIScrollView, with view: UIView?, atScale scale: CGFloat) {
        print("scrollViewDidEndZooming at scale \(scale)")
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        print("scrollViewDidScroll at offset \(scrollView.contentOffset)")
    }
}


