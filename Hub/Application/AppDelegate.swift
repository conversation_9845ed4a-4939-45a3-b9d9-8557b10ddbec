//
//  AppDelegate.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import BackgroundTasks
import CoreLocation
import FirebaseCore
import FirebaseMessaging
import IQ<PERSON>eyboardManagerSwift
import OSLog
import UIKit
import Photos
import SSAppUpdater
import Atlantis
#if DEBUG
  import DebugSwift
#endif

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
  var orientationLock = UIInterfaceOrientationMask.portrait

  #if DEBUG
  let debugSwift = DebugSwift()
  #endif
  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {

    // Firebase Setting
    FirebaseApp.configure()
    // message delegate
    Messaging.messaging().delegate = self
    // Push Noti Setting
    registerNotification(application: application)
    // Get Location Auth
    checkLocationAuth()
    // get
    checkPhotoPermission()
    // googleMap
    AppManager.shared.setGoogleMapKey()
    // IQKeyboardManager
    IQKeyboardManager.shared.enable = true
    IQKeyboardManager.shared.enableAutoToolbar = false
    IQKeyboardManager.shared.shouldResignOnTouchOutside = true

    // app update check
    SSAppUpdater.shared.performCheck(isForceUpdate: true) { (versionInfo) in
      if versionInfo.isAppUpdateAvailable {
        aLogger.info("App update available: \(versionInfo.appVersion ?? "1.x.x")")
        aLogger.info("App update note: \(versionInfo.appReleaseNote ?? "1.x.x")")
      }
    }

    aLogger.info("App LanguageType: \(Current.lang.rawValue)")

    setDebugingFeature()
    UserDefaults.standard.register(defaults: [_adas_all_setting_value: true])
    BGTaskScheduler.shared.register(forTaskWithIdentifier: "com.example.pingTask", using: nil) { task in }

    // app 시작시에 UserDefaults.shared.dashcamCardCellModels 의 isConnect를 모두 false로
    var savedModels = UserDefaults.shared.dashcamCardCellModels
    savedModels = savedModels.map { model in
      var updatedModel = model
      updatedModel.isConnect = false
      aLogger.info("### updatedModel: \(updatedModel.ssid)")
      return updatedModel
    }
    UserDefaults.shared.dashcamCardCellModels = savedModels
    Current.connectedMac = nil

    return true
  }

  // MARK: UISceneSession Lifecycle
  func application(
    _ application: UIApplication,
    configurationForConnecting connectingSceneSession: UISceneSession,
    options: UIScene.ConnectionOptions
  ) -> UISceneConfiguration {
    return UISceneConfiguration(
      name: "Default Configuration", sessionRole: connectingSceneSession.role)
  }

  func application(
    _ application: UIApplication,
    supportedInterfaceOrientationsFor window: UIWindow?
  ) -> UIInterfaceOrientationMask {
    return self.orientationLock
  }

  func applicationWillTerminate(_ application: UIApplication) {
    aLogger.log("applicationWillTerminate")
    Composers.socketRepository.disConnect()
    sendWifiOffSetCommand()
  }

  private func sendWifiOffSetCommand() {
    if case .wifi = AppManager.shared.mode {
      let header = HeaderModel(cmd: Command.setcommand)
      let command = SetCommandModel(command: "wifi_disconnect", param: 2)
      let send = SetCommand.Send(header: header, setcommand: command)

      aLogger.info("send: wifi off")
      Composers.settingUseCase.send(to: send) { response, error in
        if let error = error {
          aLogger.error("error: \(error)")
        } else {
          aLogger.info("response: \(response.debugDescription)")
        }
      }
    }
  }

  func applicationDidEnterBackground(_ application: UIApplication) {
    aLogger.log("applicationDidEnterBackground")
  }
}

// MARK: - Debuging Feature
extension AppDelegate {
  func setDebugingFeature(){
    Atlantis.start()

    #if DEBUG
    
    debugSwift.setup(hideFeatures: [
      .interface, .network
    ],disable: [
      .network, .views
    ])

    setCustomAction()
    DebugSwift.App.shared.customControllers = {
      let featureFlag = AppConfigViewController()
      featureFlag.title = "Flag"
      return [featureFlag]
    }
    debugSwift.show()
    #endif
  }
}

extension AppDelegate: UNUserNotificationCenterDelegate {
  // register remote noti
  func registerNotification(application: UIApplication) {
    UNUserNotificationCenter.current().delegate = self
    let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
    UNUserNotificationCenter.current().requestAuthorization(
      options: authOptions,
      completionHandler: { _, _ in }
    )
    application.registerForRemoteNotifications()
  }
  func application(
    _ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
  ) {
    aLogger.info(#function)
    Messaging.messaging().apnsToken = deviceToken
  }

  // 메시지가 떳을때 화면 안에 있다면 작동됨
  func userNotificationCenter(
    _ center: UNUserNotificationCenter, willPresent notification: UNNotification,
    withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
  ) {
    let content = notification.request.content
    let userInfo = content.userInfo
    aLogger.info("userInfo : \(userInfo)")

    // NotificationModel 생성 및 저장
    if let notificationModel = NotificationModel(from: userInfo) {
      saveNotificationToUserDefaults(notificationModel)
      aLogger.info("Notification saved while app is in foreground: \(notificationModel.fileName)")
    }

    completionHandler([.sound, .badge, .banner])
  }

  func userNotificationCenter(
    _ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse
  ) async {
    let userInfo = response.notification.request.content.userInfo
    aLogger.info("userInfo : \(userInfo)")

    if let notificationModel = NotificationModel(from: userInfo) {
      saveNotificationToUserDefaults(notificationModel)
      aLogger.info("Notification saved and user tapped: \(notificationModel.fileName)")

      popupDownload(model: notificationModel)
    }
  }

  // MARK: - Notification Helper Methods

  private func popupDownload(model: NotificationModel) {
    do {
      let savefilename = try HubFileNameGenerator.generatePushResultFileName(baseFileName: model.fileName)
      fLogger.info("##@@@ push downloda input fileName: \(model.fileName) result filename: \(savefilename)")
      let vm = PopupNotificationDownloadViewModel(useCase: Composers.aiUseCase, model: model, saveFileName: savefilename)
      let vc = PopupWifiDownloadViewController(
        coordinator: nil,
        editType: .trim,
        fileName: savefilename,
        totalCount: -1
      )
      vc.wifiViewModel = nil
      vc.notificationDownlaodViewModel = vm
      
      let rootViewController = UIApplication.shared.windows.first?.rootViewController
      rootViewController?.present(vc, animated: true)
    } catch {
      fLogger.error("Error generating AI service file names: \(error)")
    }
    
  }

  private func saveNotificationToUserDefaults(_ notification: NotificationModel) {
    var notifications = UserDefaults.shared.notifications

    // 중복 확인 (같은 fileUrl이 있는지 확인)
    if !notifications.contains(where: { $0.fileUrl == notification.fileUrl }) {
      notifications.insert(notification, at: 0) // 최신 알림을 맨 위에 추가
      UserDefaults.shared.notifications = notifications
      aLogger.info("New notification saved: \(notification.fileName)")
    } else {
      aLogger.info("Duplicate notification ignored: \(notification.fileName)")
    }
  }
}

extension AppDelegate: MessagingDelegate {
  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    aLogger.info("FCM Token: \(fcmToken ?? "")")
    AppManager.shared.fcmToken = fcmToken
  }
}

extension AppDelegate: CLLocationManagerDelegate {
  func checkLocationAuth() {
    let locationManager = CLLocationManager()
    let status = locationManager.authorizationStatus
    if status != .authorizedWhenInUse {
      locationManager.delegate = self
      locationManager.requestWhenInUseAuthorization()
    }
  }

  func checkPhotoPermission() {
    // We still check at app launch, but we'll use the new PermissionManager for subsequent checks
    let photoStatus = PHPhotoLibrary.authorizationStatus()
    if photoStatus == .notDetermined {
      PHPhotoLibrary.requestAuthorization { status in
        Log.message(to: "Requested photo permission: \(status)")
      }
    }
  }
}
