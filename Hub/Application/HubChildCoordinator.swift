//
//  HubChildCoordinator.swift
//  Hub
//
//  Created by ncn on 4/23/25.
//
import Foundation

public enum HubChildCoordinator: Int, CustomDebugStringConvertible {
  case home
  case live
  case playback
  case myLibrary
  case more
  
  static func fromLiveToPlayback(prevTab: Self, nextTab: Self) -> Bool {
    if prevTab == .live && nextTab == .playback {
      aLogger.info("from LiveTab to Playback")
      return true
    }
    return false
  }
  
  static func fromPlaybackToLive(prevTab: Self, nextTab: Self) -> Bool {
    if prevTab == .playback && nextTab == .live {
      aLogger.info("from PlaybackTab to LiveTab")
      return true
    }
    return false
  }

  public var debugDescription: String {
    switch self {
    case .home:
      return "Home"
    case .live:
      return "Live"
    case .playback:
      return "Playback"
    case .myLibrary:
      return "MyLibrary"
    case .more:
      return "More"
    }
  }
}
