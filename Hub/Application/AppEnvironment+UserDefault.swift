//
//  AppEnvironment+UserDefault.swift
//
//
//  Created by ncn on 7/9/24.
//

import Foundation

extension AppEnvironment {
  private var userIdKey: String {
    "userId"
  }

  private var uuidKey: String {
    "appUUID"
  }

  private var connectedDeviceKey: String {
    "connectedDevice"
  }

  private var apiAccessTokenKey: String {
    "apiAccessToken"
  }

  private var apiRefreshTokenKey: String {
    "apiRefreshToken"
  }

  private var accountSpeedUnitKey: String {
    "AccountSpeedUnit"
  }

  private var accountTempUnitKey: String {
    "AccountTempUnit"
  }

  private var onBoardingShowKey: String {
    "isOnboardingShow"
  }

  private var adasAllSettingValueKey: String {
    "AdasAllSettingValue"
  }

  var dashcamCardCellModelsKey: String {
    "DashcamCardCellModels"
  }

  var notificationModelsKey: String {
    "notificationModels"
  }

  var homeShortcutKey: String {
    "HomeShortcut"
  }

  // MARK: - Daily Usage Limit Keys
  var dailyAiPrivacyFeatureUsageCountKey: String {
    "DailyAiPrivacyUsageCount"
  }

  var dailyAiLicenseFeatureUsageCountKey: String {
    "DailyAiLicenseUsageCount"
  }

  var lastAiFeatureUsageDateKey: String {
    "LastAiFeatureUsageDate"
  }
}

extension AppEnvironment {
  var accessToken: String? {
    get {
      guard let token = loadUserDefault(key: apiAccessTokenKey) as? String else { return nil }
      return token
    }

    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: apiAccessTokenKey, value: v)
      }
    }
  }

  var refreshToken: String? {
    get {
      guard let token = loadUserDefault(key: apiRefreshTokenKey) as? String else { return nil }
      return token
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: apiRefreshTokenKey, value: v)
      }
    }
  }

  var uuid: Data? {
    get {
      guard let token = loadUserDefault(key: uuidKey) as? Data else { return nil }
      return token
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: uuidKey, value: v)
      }
    }
  }

  var connectedDevice: Data? {
    get {
      guard let loadedData = loadUserDefault(key: connectedDeviceKey) as? Data else { return Data()
      }
      return loadedData
    }

    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: connectedDeviceKey, value: v)
      }
    }
  }

  var adasAllSettingValue: Bool? {
    get {
      guard let token = loadUserDefault(key: adasAllSettingValueKey) as? Bool else { return nil }
      return token
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: adasAllSettingValueKey, value: v)
      }
    }
  }

  //! TODO: UserDefault(LocalStorage) 모듈화 해서 이쪽 코드, AppManager 코드 정리필요.
  var accountSpeedUnit: String? {  // Y: Kilo / N:Mile
    get {
      return loadUserDefault(key: accountSpeedUnitKey) as? String
    }
    set {
      updateUserDefault(key: accountSpeedUnitKey, value: newValue ?? "")
    }
  }

  var accountTempUnit: String? {  // Y: °C / N: °F
    get {
      return loadUserDefault(key: accountTempUnitKey) as? String
    }
    set {
      updateUserDefault(key: accountTempUnitKey, value: newValue ?? "")
    }
  }

  var isOnBoardingShow: Bool? {  // Y: °C / N: °F
    get {
      return loadUserDefault(key: onBoardingShowKey) as? Bool
    }
    set {
      updateUserDefault(key: onBoardingShowKey, value: newValue ?? "")
    }
  }

  private func loadUserDefault(key: String) -> Any? {
    return UserDefaults.standard.value(forKey: key)
  }

  @discardableResult
  private func updateUserDefault(key: String, value: Any) -> Bool {
    let defaults = UserDefaults.standard
    defaults.setValue(value, forKey: key)
    return defaults.synchronize()
  }

}
