//
//  AppConfigController.swift
//  Hub
//
//  Created by ncn on 5/30/25.
//

import UIKit

/// The Hub App Configuration.
struct HubAppConfig {
  var isNewVideoTrimEnabled: Bool
  var isHardDeleteEnabled: Bool
  var isAtlantisEnabled: Bool
  var isMessageDebuggerEnabled: Bool
  var isLocationAttachmentsEnabled: Bool
  var shouldShowConnectionBanner: Bool
  var isPremiumMemberFeatureEnabled: Bool
  var shouldDeletePollOnMessageDeletion: Bool
}

class AppConfig {
  var hubAppConfig: HubAppConfig
  
  static var shared = AppConfig()
  
  private init() {
    hubAppConfig = HubAppConfig(
      isNewVideoTrimEnabled: false,
      isHardDeleteEnabled: false,
      isAtlantisEnabled: false,
      isMessageDebuggerEnabled: false,
      isLocationAttachmentsEnabled: false,
      shouldShowConnectionBanner: false,
      isPremiumMemberFeatureEnabled: false,
      shouldDeletePollOnMessageDeletion: false
    )
    
    hubAppConfig.isNewVideoTrimEnabled = false
    hubAppConfig.isAtlantisEnabled = true
    hubAppConfig.isMessageDebuggerEnabled = true
    hubAppConfig.isLocationAttachmentsEnabled = true
    hubAppConfig.isLocationAttachmentsEnabled = true
    hubAppConfig.isHardDeleteEnabled = true
    hubAppConfig.shouldShowConnectionBanner = true
    hubAppConfig.isPremiumMemberFeatureEnabled = true
    hubAppConfig.shouldDeletePollOnMessageDeletion = true
  }
}

class UserConfig {
  var isInvisible = false
  var typingIndicatorsEnabled: Bool?
  var readReceiptsEnabled: Bool?
  
  static var shared = UserConfig()
  
  private init() {}
}

class AppConfigViewController: UITableViewController {
  var hubAppConfig: HubAppConfig {
    get { AppConfig.shared.hubAppConfig }
    set {
      AppConfig.shared.hubAppConfig = newValue
      tableView.reloadData()
    }
  }
  
  init() {
    super.init(style: .grouped)
  }
  
  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  enum ConfigOption {
    case info([DemoAppInfoOption])
    case demoApp([DemoAppConfigOption])
    
    var numberOfOptions: Int {
      switch self {
      case let .info(options):
        return options.count
      case let .demoApp(options):
        return options.count
      }
    }
    
    var sectionTitle: String {
      switch self {
      case .demoApp:
        return "Demo App Configuration"
      case .info:
        return "General Info"
      }
    }
  }
  
  enum DemoAppInfoOption: CustomStringConvertible, CaseIterable {
    case environment
    //    case pushConfiguration
    
    var description: String {
      switch self {
      case .environment:
        return "App Key"
        //      case .pushConfiguration:
        //        let configuration = Bundle.pushProviderName ?? "Not set"
        //        return "Push Configuration: \(configuration)"
      }
    }
  }
  
  enum DemoAppConfigOption: String, CaseIterable {
    case newVideoTrimEnabled
    case isHardDeleteEnabled
    case isAtlantisEnabled
    case isMessageDebuggerEnabled
    case isLocationAttachmentsEnabled
    case shouldShowConnectionBanner
    case isPremiumMemberFeatureEnabled
  }

  enum UserConfigOption: String, CaseIterable {
    case isInvisible
    case language
    case typingIndicatorsEnabled
    case readReceiptsEnabled
  }
  
  let options: [ConfigOption] = [
    .info(DemoAppInfoOption.allCases),
    .demoApp(DemoAppConfigOption.allCases),
  ]
  
  override func viewDidLoad() {
    super.viewDidLoad()
    
    title = "Configuration"
  }
  
  // MARK: Table View Data Source
  
  override func numberOfSections(in tableView: UITableView) -> Int {
    options.count
  }
  
  override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    options[section].numberOfOptions
  }
  
  override func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
    options[section].sectionTitle
  }
  
  override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    let cell = UITableViewCell(style: .subtitle, reuseIdentifier: nil)
    
    switch options[indexPath.section] {
    case let .info(options):
      configureDemoAppInfoCell(cell, at: indexPath, options: options)
      
    case let .demoApp(options):
      configureDemoAppOptionsCell(cell, at: indexPath, options: options)
    }
    
    return cell
  }
  
  override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    guard let cell = tableView.cellForRow(at: indexPath) else { return }
    
    switch options[indexPath.section] {
    case let .info(options):
      didSelectInfoOptionsCell(cell, at: indexPath, options: options)
    case let .demoApp(options):
      didSelectDemoAppOptionsCell(cell, at: indexPath, options: options)
    }
  }
  
  // MAKR: - Hub App Info
  
  private func configureDemoAppInfoCell(
    _ cell: UITableViewCell,
    at indexPath: IndexPath,
    options: [DemoAppInfoOption]
  ) {
    let option = options[indexPath.row]
    cell.textLabel?.text = option.description
    switch option {
    case .environment:
      cell.accessoryType = .disclosureIndicator
      cell.detailTextLabel?.text = ""
    }
  }
  
  // MARK: - Demo App Options
  
  private func configureDemoAppOptionsCell(
    _ cell: UITableViewCell,
    at indexPath: IndexPath,
    options: [DemoAppConfigOption]
  ) {
    let option = options[indexPath.row]
    cell.textLabel?.text = option.rawValue
    
    switch option {
    case .newVideoTrimEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isNewVideoTrimEnabled) { [weak self] newValue in
        self?.hubAppConfig.isNewVideoTrimEnabled = newValue
      }

    case .isHardDeleteEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isHardDeleteEnabled) { [weak self] newValue in
        self?.hubAppConfig.isHardDeleteEnabled = newValue
      }
    case .isAtlantisEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isAtlantisEnabled) { [weak self] newValue in
        self?.hubAppConfig.isAtlantisEnabled = newValue
      }
    case .isMessageDebuggerEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isMessageDebuggerEnabled) { [weak self] newValue in
        self?.hubAppConfig.isMessageDebuggerEnabled = newValue
      }
    case .isLocationAttachmentsEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isLocationAttachmentsEnabled) { [weak self] newValue in
        self?.hubAppConfig.isLocationAttachmentsEnabled = newValue
      }
    case .shouldShowConnectionBanner:
      cell.accessoryView = makeSwitchButton(hubAppConfig.shouldShowConnectionBanner) { [weak self] newValue in
        self?.hubAppConfig.shouldShowConnectionBanner = newValue
      }
    case .isPremiumMemberFeatureEnabled:
      cell.accessoryView = makeSwitchButton(hubAppConfig.isPremiumMemberFeatureEnabled) { [weak self] newValue in
        self?.hubAppConfig.isPremiumMemberFeatureEnabled = newValue
      }
    }
  }
  
  private func didSelectInfoOptionsCell(
    _ cell: UITableViewCell,
    at indexPath: IndexPath,
    options: [DemoAppInfoOption]
  ) {
    let option = options[indexPath.row]
    switch option {
    case .environment:
      pushEnvironmentSelectorVC()
    }
  }
  
  
  private func didSelectDemoAppOptionsCell(
    _ cell: UITableViewCell,
    at indexPath: IndexPath,
    options: [DemoAppConfigOption]
  ) {
    let option = options[indexPath.row]
  }
  
  // MARK: - Helpers
  
  private func makeSwitchButton(_ initialValue: Bool, _ didChangeValue: @escaping (Bool) -> Void) -> SwitchButton {
    let switchButton = SwitchButton()
    switchButton.isOn = initialValue
    switchButton.didChangeValue = didChangeValue
    return switchButton
  }
  
  private func pushEnvironmentSelectorVC() { }
  
}


class SwitchButton: UISwitch {
  var didChangeValue: ((Bool) -> Void)?
  
  override init(frame: CGRect) {
    super.init(frame: frame)
    addTarget(self, action: #selector(didChangeValue(sender:)), for: .valueChanged)
  }
  
  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  @objc func didChangeValue(sender: UISwitch) {
    didChangeValue?(sender.isOn)
  }
}
