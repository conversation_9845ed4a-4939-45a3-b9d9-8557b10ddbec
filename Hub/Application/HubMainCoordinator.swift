//
//  HubMainCoordinator.swift
//  Hub
//
//  Created by ncn on 11/26/24.
//

import UIKit
import Photos

public class HubMainCoordinator: NSObject, Coordinator {
  let tabBarController: UITabBarController
  private var childCoordinators = [HubChildCoordinator: Coordinator]()
  var previousSelectedTab: HubChildCoordinator = .home

  public init(tabBarController: UITabBarController) {
    self.tabBarController = tabBarController
    super.init()
    self.setTabBarAppearance()
    self.tabBarController.delegate = self
  }

  func setTabBarAppearance() {
    if #available(iOS 15.0, *) {
      let appearance = UITabBarAppearance()
      appearance.stackedLayoutAppearance.normal.iconColor = .mainBlack
      appearance.stackedLayoutAppearance.selected.iconColor = .vueroidBlue

      appearance.backgroundColor = .white
      appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
        .font: UIFont.pretendard(ofSize: 12)!,
        .foregroundColor: UIColor.mainBlack
      ]

      appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
        .font: UIFont.pretendard(ofSize: 12)!,
        .foregroundColor: UIColor.vueroidBlue
      ]

      tabBarController.tabBar.standardAppearance = appearance
      tabBarController.tabBar.scrollEdgeAppearance = appearance
    }
  }
  public func start() {
    showMain()
  }

//  let myLibNavigation = UINavigationController()

  private func showMain() {
    let homeNavigation = UINavigationController()
    homeNavigation.tabBarItem = .init(title: L.menu_home_title.localized, image: #imageLiteral(resourceName: "home.pdf"), selectedImage: #imageLiteral(resourceName: "home_selected.pdf"))
    buildHubHomeScene(in: homeNavigation)

    setNavigationBarAppearance()

    AppManager.shared.mode = .wifi
    let liveNavigation = UINavigationController()
    liveNavigation.tabBarItem = .init(title: L.menu_live_title.localized, image: #imageLiteral(resourceName: "live.pdf"), selectedImage: #imageLiteral(resourceName: "live_selected.pdf"))

    let vodNavigation = UINavigationController()
    vodNavigation.tabBarItem = .init(title: L.fileviewer_title.localized, image: #imageLiteral(resourceName: "playback.pdf"), selectedImage: #imageLiteral(resourceName: "playback_selected.pdf"))

    let myLibNavigation = UINavigationController()
    myLibNavigation.tabBarItem = .init(title: L.my_library_title.localized, image: #imageLiteral(resourceName: "folder.pdf"), selectedImage: #imageLiteral(resourceName: "folder_selected.pdf"))
    buildMyLibraryScene(in: myLibNavigation)

    let moreNavigation = UINavigationController()
    moreNavigation.tabBarItem = .init(title: L.menu_more_title.localized, image: #imageLiteral(resourceName: "more.pdf"), selectedImage: #imageLiteral(resourceName: "more_selected.pdf"))
    buildMoreScene(in: moreNavigation)

    tabBarController.setViewControllers([
      homeNavigation, liveNavigation, vodNavigation, myLibNavigation, moreNavigation
    ], animated: true)
  }

  private func buildHubHomeScene(in navigation: UINavigationController) {
    let homeCoordinator = DefaultHubHomeCoordinator(navigationController: navigation)
    childCoordinators[.home] = homeCoordinator
    homeCoordinator.start()
  }

  private func buildLiveScene(in navigation: UINavigationController) {
    aLogger.info("buildLiveScene")
    childCoordinators[.live] = nil
    let liveCoordinator = DefaultLiveCoordinator(navigationController: navigation)
    childCoordinators[.live] = liveCoordinator
    liveCoordinator.start()
  }

  private func buildVodScene(in navigation: UINavigationController) {
    AppManager.shared.mode = .wifi
    aLogger.info("buildVodScene")
    LKPopupView.popup.loading()
    let fileListCoordinator = DefaultFileListCoordinator(navigationController: navigation)
    childCoordinators[.playback] = fileListCoordinator
    fileListCoordinator.start()
  }

  private func buildMyLibraryScene(in navigation: UINavigationController) {
    AppManager.shared.mode = .file
    let fileListCoordinator = DefaultFileListCoordinator(navigationController: navigation)
    childCoordinators[.myLibrary] = fileListCoordinator
    fileListCoordinator.start()
  }

  private func buildMoreScene(in navigation: UINavigationController) {
    let moreCoordinator = DefaultMoreCoordinator(navigationController: navigation)
    childCoordinators[.more] = moreCoordinator
    moreCoordinator.start()
  }
}

extension HubMainCoordinator: UITabBarControllerDelegate {

  public func tabBarController(
    _ tabBarController: UITabBarController,
    didSelect viewController: UIViewController
  ) {
    aLogger.info("@@ Tab didSelect isConnect: \(Current.isConnect)")
    LKPopupView.popup.hideLoading()

    var viewControllers = tabBarController.viewControllers
    guard let selectedIndex = viewControllers?.firstIndex(of: viewController) else { return }

    let currentTab = HubChildCoordinator(rawValue: selectedIndex) ?? .home
    if currentTab == previousSelectedTab {
      return
    }

    if currentTab == .live {
      // Double-check photo permission (in case it was just granted)
      let status = PHPhotoLibrary.authorizationStatus()
      if status == .authorized || status == .limited {
        aLogger.info("live tab select")
        let liveNavigation = UINavigationController()
        liveNavigation.tabBarItem = .init(title: L.menu_live_title.localized, image: #imageLiteral(resourceName: "live.pdf"), selectedImage: #imageLiteral(resourceName: "live_selected.pdf"))
        buildLiveScene(in: liveNavigation)
        // 뷰 컨트롤러 배열에서 기존 뷰 컨트롤러 제거 후 새로운 뷰 컨트롤러 추가
        viewControllers?[selectedIndex] = liveNavigation
        tabBarController.setViewControllers(viewControllers, animated: false)
      } else {
        // If permission is still not granted, go back to previous tab
        tabBarController.selectedIndex = previousSelectedTab.rawValue
      }

    } else if selectedIndex == HubChildCoordinator.playback.rawValue {
      // Double-check photo permission (in case it was just granted)
      let status = PHPhotoLibrary.authorizationStatus()
      if status == .authorized || status == .limited {
        aLogger.info("Playback tab select")
        let vodNavigation = UINavigationController()
        vodNavigation.tabBarItem = .init(title: L.fileviewer_title.localized, image: #imageLiteral(resourceName: "playback.pdf"), selectedImage: #imageLiteral(resourceName: "playback_selected.pdf"))
        buildVodScene(in: vodNavigation)
        viewControllers?[selectedIndex] = vodNavigation
        tabBarController.setViewControllers(viewControllers, animated: false)
      } else {
        // If permission is still not granted, go back to previous tab
        tabBarController.selectedIndex = previousSelectedTab.rawValue
      }
    } else if selectedIndex == HubChildCoordinator.myLibrary.rawValue {
      let myLibNavigation = UINavigationController()
      myLibNavigation.tabBarItem = .init(title: L.my_library_title.localized, image: #imageLiteral(resourceName: "folder.pdf"), selectedImage: #imageLiteral(resourceName: "folder_selected.pdf"))
      buildMyLibraryScene(in: myLibNavigation)
      viewControllers?[selectedIndex] = myLibNavigation
      tabBarController.setViewControllers(viewControllers, animated: false)
    }
    
    previousSelectedTab = currentTab
    aLogger.info("Tab didSelect saved prevTab: \(self.previousSelectedTab.debugDescription)")
  }



  public func tabBarController(
    _ tabBarController: UITabBarController,
    shouldSelect viewController: UIViewController
  ) -> Bool {
    aLogger.info("@@ Tab shouldSelect isConnect: \(Current.isConnect)")
    guard let index = tabBarController.viewControllers?.firstIndex(of: viewController) else {
      return false
    }

    let toTab: HubChildCoordinator = HubChildCoordinator(rawValue: index) ?? .home
    
    aLogger.info("\(self.previousSelectedTab.debugDescription) => \(toTab.debugDescription) index: \(index)")
    // Check photo permission for Live and Playback tabs
    if index == HubChildCoordinator.playback.rawValue {
      PermissionManager.checkPhotosPermissionForTab { granted in
        if granted == false {
          return
        }
      }
    }

    guard checkConnectPopup(selectTabIndex: index, tabBarController: tabBarController) else {
      return false
    }

    // set Mode
    if toTab == .playback || toTab == .live {
      AppManager.shared.mode = .wifi
    } else if toTab == .myLibrary {
      removeDownloadedThumbFolder()
      AppManager.shared.mode = .file
    }

    return true
  }

  func pushFile() {
    AppManager.shared.mode = .file
    LKPopupView.popup.hideLoading()

    let myLibNavigation = UINavigationController()
    myLibNavigation.tabBarItem = .init(title: L.my_library_title.localized, image: #imageLiteral(resourceName: "folder.pdf"), selectedImage: #imageLiteral(resourceName: "folder_selected.pdf"))
    buildMyLibraryScene(in: myLibNavigation)
  }
  
  /// delete DOWNLOADEDTHUMB folder
  func removeDownloadedThumbFolder() {
    guard let thumbFolder = UrlList.downloadedThumbPath() else {
      return
    }
    
    if FileManager.default.fileExists(atPath: thumbFolder.path) {
      do {
        try FileManager.default.removeItem(at: thumbFolder)
        hLogger.info("DOWNLOADEDTHUMB 폴더 삭제 성공")
      } catch {
        hLogger.error("DOWNLOADEDTHUMB 폴더 삭제 실패: \(error)")
      }
    }
  }
  
  func checkConnectPopup(
    selectTabIndex: Int,
    tabBarController: UITabBarController
  ) -> Bool {
    if (selectTabIndex == HubChildCoordinator.live.rawValue ||
        selectTabIndex == HubChildCoordinator.playback.rawValue) &&
        Current.isConnect == false {

      hLogger.info("select tab index:\(selectTabIndex): \(Current.isConnect)")
      disconnectPopup()
      return false
    }

    return true
  }

  private func disconnectPopup() {
    LKPopupView.popup.alert {[
      .subTitle(L.blackbox_disconnect.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          self.tabBarController.selectedIndex = HubChildCoordinator.home.rawValue
        })
      ])
    ]}
  }
}

