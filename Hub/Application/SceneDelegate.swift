//
//  SceneDelegate.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import UIKit
import Pretendard

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

  var window: UIWindow?
  var appCoordinator: AppCoordinator?
  var backgroundUpdateTask: UIBackgroundTaskIdentifier = .invalid

  private var keepaliveTask: URLSessionDataTask?
  
  func scene(
    _ scene: UIScene,
    willConnectTo session: UISceneSession,
    options connectionOptions: UIScene.ConnectionOptions
  ) {
    Log.message(to: "")
    //AppUtility.lockOrientation(.portrait)
    guard let windowScene = (scene as? UIWindowScene) else { return }

    if #available(iOS 16.0, *) {
      windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: .portrait))
    } else {
      let value = UIDeviceOrientation.portrait.rawValue
      UIDevice.current.setValue(value, forKey: "orientation")
    }

    registerFont()

    window = UIWindow(frame: UIScreen.main.bounds)
    window?.windowScene = windowScene
    window?.overrideUserInterfaceStyle = .light
    setupLoadingWindow()
//    Task {
//      await checkConnectState()

      appCoordinator = AppCoordinator(window: window!)
      appCoordinator?.start()
//    }
  }

  func registerFont() {
    do {
      try Pretendard.registerFonts()
    } catch  {
      fatalError()
    }
  }

  func setupLoadingWindow() {
    _ = LoadingWindow.shared
  }

  func sceneDidDisconnect(_ scene: UIScene) {
    aLogger.info("sceneDidDisconnect")
    Current.appKeepAliveTimerControl?.suspend()
    sendKeepAliveRequest(timeout: 1)
  }

  func sceneDidBecomeActive(_ scene: UIScene) {
    hLogger.notice("sceneDidBecomeActive connectingWifiHotspot: \(Current.connectingWifiHotspot.rawValue)")
    self.endBackgroundUpdateTask()
  }

  func sceneWillResignActive(_ scene: UIScene) {
    aLogger.info("sceneWillResignActive")
//    self.backgroundUpdateTask = UIApplication.shared.beginBackgroundTask(expirationHandler: {
//      // 백그라운드 작업 만료 시에도 마지막 keepAlive 요청
//      self.endBackgroundUpdateTask()
//    })
  }

  // TODO(HUB): HttpKeepAliveController로 대체
  private func makeKeepAliveTask(_ url: URL, timeout: TimeInterval) {
    let session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/x-www-form-urlencoded; charset=utf-8", forHTTPHeaderField: "Content-Type")
    let postData = "timeout=\(timeout)".data(using: .utf8)
    request.httpBody = postData
    
    keepaliveTask = session.dataTask(with: request) { data, response, error in
//      hLogger.debug("sendKeepAliveRequest")
      if let error = error {
        hLogger.error("keepalive error : \(error.localizedDescription)")
        hLogger.error("isConnect: \(Current.isConnect), connectedMac: \(Current.connectedMac ?? "")")
        Composers.socketRepository.disConnect()
        NotificationCenter.default.post(name: .disconnectedDevice, object: nil)
        Current.appKeepAliveTimerControl?.suspend()
        Current.connectingWifiHotspot = .failed
      } else {
//        hLogger.log("keepalive success: \(String(describing: response))")
        Current.connectingWifiHotspot = .connected
      }
      if timeout <= 1 {
//        hLogger.log("makeKeepAliveTask suspend: timeout: \(timeout)")
        Current.appKeepAliveTimerControl?.suspend()
      }
    }
  }
  
  func scheduleKeepAliveTimerIfNeeded() {
    guard Current.appKeepAliveTimerControl == nil else { return }
    hLogger.notice("scheduleKeepAliveTimerIfNeeded")
    Current.appKeepAliveTimerControl = DefaultTimer.scheduleRepeating(
        timeInterval: 3,
        queue: .main
    ) { [weak self] in
      DispatchQueue.global().async {
        self?.sendKeepAliveRequest()
      }
    }
    Current.appKeepAliveTimerControl?.resume()
  }

  func sendKeepAliveRequest(timeout: TimeInterval = 6) {
    let keepAliveUrl = UrlList.blackBoxUrl(path: "keepalive", port: "8010")
    guard let url = URL(string: keepAliveUrl) else {
      nLogger.error("Invalid URL")
      return
    }
    makeKeepAliveTask(url, timeout: timeout)
    keepaliveTask?.resume()
  }

  func sceneWillEnterForeground(_ scene: UIScene) {
    hLogger.notice("sceneWillEnterForeground")
    if Current.isConnect {
      Current.appKeepAliveTimerControl?.resume()
      scheduleKeepAliveTimerIfNeeded()
      NotificationCenter.default.post(name: .hubWillEnterForeground, object: nil)
    }
  }

  func sceneDidEnterBackground(_ scene: UIScene) {
    aLogger.info("sceneDidEnterBackground")
    if Current.connectingWifiHotspot == .connected {
      Current.appKeepAliveTimerControl?.suspend()
      self.sendKeepAliveRequest(timeout: 10)
    }
  }

  func scenewillResignActive(_ scene: UIScene) {
    Current.appKeepAliveTimerControl?.suspend()
    sendKeepAliveRequest(timeout: 10)
  }
  
    

  func endBackgroundUpdateTask() {
    UIApplication.shared.endBackgroundTask(self.backgroundUpdateTask)
    self.backgroundUpdateTask = UIBackgroundTaskIdentifier.invalid
  }
  
  
}

  
extension SceneDelegate {
  enum AnimatePath {
    case leftToRight
    case rightToLeft
  }

  func setRootViewController(
    viewController: UIViewController, animated: Bool = true, path: AnimatePath = .rightToLeft
  ) {
    guard let window = self.window else { return }
    if animated {
      window.rootViewController = viewController
      UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve) {

      }
    } else {
      window.rootViewController = viewController
    }
  }
}

extension SceneDelegate: URLSessionDelegate {
  // URLSessionDelegate: 인증서 검증
  func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
    
    if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
      if let serverTrust = challenge.protectionSpace.serverTrust {
        // 서버 인증서 체인 가져오기
        if let serverCerts = SecTrustCopyCertificateChain(serverTrust) as? [SecCertificate],
           let serverCert = serverCerts.first {
//           SecCertificateCopyData(serverCert) == SecCertificateCopyData(localCert) {
          #if false
          if let dic = SecTrustCopyResult(serverTrust) {
            hLogger.info("certificate info:  \(dic)")
          }
          let secKey = SecTrustCopyKey(serverTrust)
          hLogger.info("public key:  \(secKey.debugDescription)")
          #endif
          
//          hLogger.debug("https certificate")
          completionHandler(.useCredential, URLCredential(trust: serverTrust))
          return
        }
      }
    }
    
    LKPopupView.popup.toast(hit: "https 인증서 없음")
    // 인증서 없을 시 연결 취소
    hLogger.critical("https cancelAuthenticationChallenge")
    completionHandler(.cancelAuthenticationChallenge, nil)
  }
}
