//
//  AppUtility.swift
//  Hub
//
//  Created by ncn on 5/30/25.
//

import UIKit

struct AppUtility {
  static func lockOrientation(_ orientation: UIInterfaceOrientationMask) {
    if let delegate = UIApplication.shared.delegate as? AppDelegate {
      delegate.orientationLock = orientation
    }
  }
  
  static func getOrientation() -> UIInterfaceOrientationMask {
    if let delegate = UIApplication.shared.delegate as? AppDelegate {
      return delegate.orientationLock 
    }
    
    return .portrait
  }

  /// OPTIONAL Added method to adjust lock and rotate to the desired orientation
  static func lockOrientation(
    _ orientation: UIInterfaceOrientationMask,
    andRotateTo rotateOrientation: UIInterfaceOrientation
  ) {
    self.lockOrientation(orientation)
    UIDevice.current.setValue(rotateOrientation.rawValue, forKey: "orientation")
    UINavigationController.attemptRotationToDeviceOrientation()
  }
}
