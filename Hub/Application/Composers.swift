//
//  Composers.swift
//  Hub
//
//  Created by ncn on 7/26/24.
//

import UIKit
import CombineSchedulers

public var defaultScheduler: AnySchedulerOf<DispatchQueue> = .main

public enum Composers {

  // repository
  static let socketRepository = DefaultCommandRepository(websocket: AppManager.shared.commandSocket)
//  static let socketRepository = HubCommandRepository(webSocket: AppManager.shared.hubSocket)
  static let wifiRepository = DefaultWifiRepository(manager: HotspotManager())
//  static let popupDownloadRepository = DefaultPopupDownloadRepository()
  static let popupDownloadRepository = HubPopupDownloadRepository()
  static let dbRepository = DefaultDataBaseRepository()

  // usecase
  static let commandUseCase = DefaultCommandUseCase(repository: socketRepository)
  static let settingUseCase = DefaultSettingCommandUseCase(socketRepository: socketRepository)
  static let homeUseCase = DefaultHomeUseCase(socketRepository: socketRepository)
  static let wifiUseCase = DefaultWifiUseCase(repository: wifiRepository)
  
  static let fileCommandUseCase = DefaultFileCommandUseCase(socketRepository: socketRepository)
  static let streamUseCase = DefaultStreamUseCase(socketRepository: socketRepository)

  static let popupDownloadUseCase = DefaultPopupDownloadUseCase(repository: popupDownloadRepository)
  static let liveUseCase = DefaultLiveUseCase(socketRepository: socketRepository)
  static let sideMenuUseCase = DefaultSideMenuUseCase(repo: Composers.socketRepository)

  static let historyUseCase = DefaultHistoryCommandUseCase(socketRepository: socketRepository)
  static let dbUsecase = DefaultDataBaseUseCase(repository: dbRepository)
  
  static let aiUseCase = DefaultAIUseCase()
}

// ViewModel
extension Composers {
  static func buildHubHomeConnectSectionViewModel() -> HubHomeConnectSectionViewModelProtocol {
    HubHomeConnectSectionViewModel(useCase: homeUseCase)
  }
  
  static func buildBleListViewModel(coordinator: BleListCoordinator) -> BleListViewModel {
    BleListViewModel(useCase: wifiUseCase, coordinator: coordinator)
  }
  
  static func buildHotSpotViewModel(coordinator: BleListCoordinator) -> HotspotViewModel {
    HotspotViewModel(useCase: wifiUseCase, coordinator: coordinator)
  }
  
  static func buildWifiConnectViewModel(
    coordinator: WifiConnectCoordinator,
    bleItem: BleItemModel?
  ) -> WifiConnectViewModel {
    WifiConnectViewModel(
      useCase: wifiUseCase,
      coordinator: coordinator,
      selectedItem: bleItem
    )
  }
  
  static func buildFavoriteSectionViewModel() -> FavoriteSectionViewModelProtocol {
    FavoriteSectionViewModel()
  }
}

