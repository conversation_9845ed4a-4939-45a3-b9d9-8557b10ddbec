//
//  NotificationName+.swift
//  Hub
//
//  Created by ncn on 1/22/25.
//

import Foundation

extension Notification.Name {
  static let hubWillEnterForeground = Notification.Name("com.vueroidHub.scene_will_enter_foreground")
  static let updateDeviceList = Notification.Name("com.vueroidHub.update_device_list")
  static let disconnectedDevice = Notification.Name("com.vueroidHub.disconnected_device")
  static let reloadLocalFileList = Notification.Name("com.vueroidHub.reload_local_file_list")
}
