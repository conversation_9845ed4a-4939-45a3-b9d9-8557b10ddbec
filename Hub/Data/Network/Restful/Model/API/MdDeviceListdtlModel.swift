//
//  MdDeviceListdtlModel.swift
//  Hub
//
//  Created by ncn on 2023/06/07.
//

import UIKit

enum MdDeviceListdtl {
  struct Request: Codable {
    let dvcId: String
    let dvc_als: String
  }

  struct Response: Codable {
    let resultMessage: String
    let resultCode: Int
    let data: [mdDeviceModel]?
  }
}

extension MdDeviceListdtl.Response {
  func toDmain() -> HomeDashcamModel {
    guard let obj = data?.first,
      let udg = obj.udDeviceLast
    else {
      Log.message(to: "Invalid property.")
      return .init()
    }
    // MARK: 영어 처리
    var net = "OFFLINE"
    //        var drive = "전원꺼짐"
    var drive = "Power off"

    switch udg.status {
    case 1:
      net = "ONLINE"
      //            drive = "주행중"
      drive = "Driving"
      break
    case 2:
      net = "ONLINE"
      //            drive = "주차중"
      drive = "Parking"
      break
    case 3:
      net = "OFFLINE"
      break
    default:
      break
    }

    var timezone = ""
    if obj.dvcGmt > 0,
      _timezone.count > obj.dvcGmt
    {
      timezone = _timezone[obj.dvcGmt]
    }
    return .init(
      voltage: udg.voltage, drivemode: drive, netStatus: net, timeZone: timezone, udG: udg)
  }
}
