//
//  GetVodModel.swift
//  Hub
//
//  Created by ncn on 2023/05/25.
//

import Foundation

struct MetaDateModel: Codable {
  let g_y: [Double]
  let g_x: [Double]
  let g_z: [Double]
  let lng: [Double]
  let lat: [Double]
  let adasAlertFV: [Int]
  let adasAlertPC: [Int]
  let adasAlertRC: [Int]
  let adasAlertLD: [Int]
  let adasAlertFC: [Int]
  let fastStart: [Int]
  let fastStop: [Int]
  let fastBreak: [Int]
  let fastAccel: [Int]
  let speed: [Double]
  let time: [String]
}

struct GetVodModel: Codable {
  let fileList: [String]
  let metaDate: MetaDateModel
}

enum GetVod {
  struct Request: Codable {
    let fileNm: String
    let serial: String
  }

  struct Response: Codable {
    let error: String?
    let authCode: Int?
    var data: GetVodModel?
  }
}

enum VideoRemove {
  struct Request: Codable {
    let serialList: [String]
  }

  struct Response: Codable {
    let error: String?
    let authCode: Int?
    var data: String?
  }

}
