//
//  DvcListModel.swift
//  Hub
//
//  Created by ncn on 2023/04/05.
//

import Foundation

struct MdModel: Codable {
  let mdId: Int?
  let mdName: String
  let useYn: String
  let inMng: Int
  let mspec1: String
  let mspec2: String
  let mspec3: String
  let mspec4: String
  let mspec5: String
  let metc: String
  let modelFile: ModelFileModel?

  func toDomain() -> String {
    return mdName
  }
}

//Delete unused files
struct ModelFileModel: Codable {
  let id: Int
  let mdlID: Int
  let fileNm: String
  let fileDesc: String
  let fileType: String
  let fileSnm: String
  let filePath: String
  let fileUrl: String
  let fileTopYn: String
  let imageFileUrl: String?
}

struct UdGModel: Codable {
  let serial: String
  let time: String
  let latitude: Double?
  let longitude: Double?
  let direction: String
  let status: Int
  let speed: String
  let voltage: String?

  init(
    serial: String, time: String, latitude: Double?, longitude: Double?, direction: String,
    status: Int, speed: String, voltage: String?
  ) {
    self.serial = serial
    self.time = time
    self.latitude = latitude
    self.longitude = longitude
    self.direction = direction
    self.status = status
    self.speed = speed
    self.voltage = voltage
  }

  init(latitude: Double, longitude: Double) {
    self.serial = ""
    self.time = ""
    self.latitude = latitude
    self.longitude = longitude
    self.direction = ""
    self.status = 0
    self.speed = ""
    self.voltage = ""
  }

  init(status: Int) {
    self.serial = ""
    self.time = ""
    self.latitude = 0.0
    self.longitude = 0.0
    self.direction = ""
    self.status = status
    self.speed = ""
    self.voltage = ""
  }
}

struct udDeviceHistoryModel: Codable {
  let id: Int
  let type: String
  let start: Int
  let startTm: [Int]
  let end: Int
  let endTm: [Int]
  let distance: Int
  let speedmax: Int
  let speedavg: Int
  let evtnum: Int
  let motnum: Int
  let offtype: Int
  let reserved: Int
  let serial: String
}

struct mdDeviceModel: Codable {
  let serial: String
  let imei: String
  let status: String
  let uaId: Int?
  let ucId: String?
  let dvc_als: String?
  let dvc_status: String?
  let ucG06: String?
  let md: MdModel
  let modelFile: ModelFileModel?
  let udDeviceHistory: udDeviceHistoryModel?
  let udDeviceLast: UdGModel?
  //let deviceUcG07Match: [Int]
  let now_status: String
  let imgFileUrl: String
  let privateCd: String
  let outDt: String
  let email: String
  let command: String?
  let useStart: String?
  let useEnd: String?
  let firmware: String?
  let uploadType: String?
  let address: String?
  let dvc_order: Int
  let dvcGmt: Int
  let safetyDbVersion: String?

  func toDmain() -> DashcamCellModel {
    return .init(
      mdName: md.mdName,
      serial: serial,
      status: status,
      nowStatus: now_status,
      dvcStatus: dvc_status,
      imageUrl: md.modelFile?.fileUrl,
      dvcAls: dvc_als,
      uaId: uaId,
      firmware: firmware,
      safetyDbVersion: safetyDbVersion,
      privateCd: privateCd,
      useStart: useStart,
      useEnd: useEnd,
      udG: udDeviceLast)
  }
}

/*
 * /VC_MNG/web/mdDevice/list"
 */
enum DvcList {
  struct Request: Codable {

  }

  struct Response: Codable {
    let resultMessage: String
    let resultCode: Int
    let data: [mdDeviceModel]
  }
}
