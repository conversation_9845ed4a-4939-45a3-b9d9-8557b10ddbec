//
//  CloudUsageModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 7/29/24.
//

import Foundation

enum CloudUsageModel {
  struct Request: Codable {
  }

  struct Response: Codable {
    let data: [Usage]?
    let resultCode: Int?
    let resultMessage: String?
    let total, totalPage: Int?

    struct Usage: Codable {
      let ratio, totalAmount, usedAmount: Double?
    }
  }
}
