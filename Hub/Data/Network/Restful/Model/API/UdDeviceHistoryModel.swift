//
//  UdDeviceHistoryModel.swift
//  Hub
//
//  Created by ncn on 2023/07/03.
//

import UIKit

struct UdDeviceHistoryModel: Codable {
  let dvcId: String
  let time: Int
  let latitude: Int
  let longitude: Int
}

enum UdDeviceHistory {
  struct Request: Codable {
    let id: Int
  }

  struct Response: Codable {
    let resultMessage: String
    let resultCode: Int
    let total: Int
    let totalPage: Int
    let data: [UdDeviceHistoryModel]?
  }
}
