//
//  DvfListModel.swift
//  Hub
//
//  Created by ncn on 2023/05/24.
//

import UIKit

struct DvModel: Codable {
  let id: Int
  let name: String
  let fileType: String
  let path: String
  let inDtm: String
  let serial: String
  let bucket: String
  let region: String
  let size: Double
  let useyn: String
  let thumbnailUrl: String
  let type: String
  let uploadType: String
  let cloudInDtm: String?
  let videoTime: String?
}

enum DvList {
  struct Request: Codable {
    let dvcId: String
    var pageNo: Int = 0
    var pageSize: Int = 50
    var type: String = "Auto"
  }

  struct Response: Codable {
    let resultMessage: String
    let resultCode: Int
    let total: Int?
    let totalPage: Int?
    var data: [DvModel]
  }
}

extension DvModel {
  func toDomain() -> FileListCellModel {
    return .init(
      type: .cloud,
      id: id,
      serial: serial,
      fileName: name,
      fileSize: size,
      imageUrl: thumbnailUrl,
      inDtm: inDtm,
      videoTime: videoTime)
  }
}

enum DvfStream {
  struct Request: Codable {
    let fileName: String
    let serial: String
  }

  struct Response: Codable {
    let timestamp: Int
    let resultMessage: String
    let status: Int
    let path: String
  }
}
