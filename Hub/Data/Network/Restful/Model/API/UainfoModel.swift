//
//  UainfoModel.swift
//  Hub
//
//  Created by ncn on 2023/04/07.
//

import Foundation

struct uaSnsModel: Codable {
  let id: Int
  let uaId: Int
  let cd: String
  let indtm: [Int]?
  //let outdtm:
  let email: String
}

struct UainfoModel: Codable {
  let codeKey: String?
  let token: String?
  let uaId: Int
  let status: String
  var email: String?
  let name: String
  let password: String?
  let authDtoSet: String?
  let authorities: String?
  let newpassword: String?
  let ua_telno: String?
  let ua_q_num: Int?
  let ua_q_anw: String?
  let country: String
  let language: String?
  let birthday: String
  let uaSns: [uaSnsModel]?
  let resetyn: String?
}

enum Uainfo {
  struct Request: Codable {

  }

  struct Response: Codable {
    let resultMessage: String
    let resultCode: Int
    let data: [UainfoModel]
  }

}
