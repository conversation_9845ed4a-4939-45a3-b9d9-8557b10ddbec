//
//  CalendarModel.swift
//  Hub
//
//  Created by ncn on 2023/06/23.
//

import UIKit

struct WebCalendarModel: Codable {
  let date: String
}

enum WebCalendar {
  struct Request: Codable {
    let schDate: String
    let serial: String
  }

  struct Response: Codable {
    let resultCode: Int
    let resultMessage: String
    var data: [WebCalendarModel]?
  }
}

extension WebCalendar.Response {
  func toDomain() -> [Date] {
    if let array = data {
      return array.map { $0.date.toDate(format: "yyyy/MM/dd")! }
    } else {
      return []
    }
  }
}
