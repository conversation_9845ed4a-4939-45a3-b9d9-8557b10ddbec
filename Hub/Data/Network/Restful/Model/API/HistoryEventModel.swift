//
//  EventDayModel.swift
//  Hub
//
//  Created by ncn on 2023/06/28.
//

import UIKit

struct HistoryEventModel: Codable {
  let serial: String
  let date: String
  let cnt1: Int
  let cnt2: Int
  let cnt3: Int
  let sumtime1: String
  let sumtime2: String
  let maxspeed: Int
  let avgspeed: Int
  let distance: Int
}

extension HistoryEventModel {

  func toDomain(type: PeriodType) -> EventChartModel {
    var aDate: Date?
    var string = ""
    switch type {
    case .day:
      aDate = date.toDate(format: "yyyy/MM/dd")
      string = aDate?.toString(format: "dd/MM") ?? date
    case .week:
      aDate = date.toDate(format: "yyyy-ww")
      string = aDate?.toString(format: "W/MM") ?? date
    case .month:
      aDate = date.toDate(format: "yyyy-MM")
      string = aDate?.toString(format: "MM") ?? date
    case .year:
      string = date
    }

    return .init(
      date: string,
      cnt1: cnt1,
      cnt2: cnt2,
      cnt3: cnt3,
      sumtime1: sumtime1,
      sumtime2: sumtime2,
      maxspeed: maxspeed,
      avgspeed: avgspeed,
      distance: distance,
      periodType: type,
      originDate: date
    )
  }
}
