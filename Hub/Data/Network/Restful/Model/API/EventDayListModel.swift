//
//  EventDayListModel.swift
//  Hub
//
//  Created by ncn on 2023/06/26.
//

import UIKit

struct EventDayListModel: Codable {
  let ud_d_idx: Int
  let serial: String
  let date: String
  let type: Int
  let startTm: String
  let endTm: String
  let distance: Int
  let avgspeed: Int
  let maxspeed: Int
  let sumtime1: String
  let sumtime2: String
  let cnt1: Int
  let cnt2: Int
  let cnt3: Int
  var speedType: SpeedType?
}

extension EventDayListModel {
  func toLogDomain() -> TravelLogCellModel {
    return .init(
      ud_d_idx: ud_d_idx,
      date: date,
      startTm: startTm,
      endTm: endTm,
      type: type,
      distance: distance,
      avgSpeed: avgspeed,
      maxSpeed: maxspeed,
      evtNum: cnt1,
      motNum: cnt2)

  }

  func toDrivingDomain() -> EventChartModel {
    return .init(
      date: date,
      cnt1: cnt1,
      cnt2: cnt2,
      cnt3: cnt3,
      sumtime1: sumtime1,
      sumtime2: sumtime2,
      maxspeed: maxspeed,
      avgspeed: avgspeed,
      distance: distance,
      originDate: date
    )
  }
}
