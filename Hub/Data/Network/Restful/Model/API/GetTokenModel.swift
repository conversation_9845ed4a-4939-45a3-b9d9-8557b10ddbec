//
//  GetTokenModel.swift
//  Hub
//
//  Created by ncn on 2023/04/04.
//

import Foundation

enum GetToken {
  struct Request: TokenModel {
    //        var token: String? = "{}"
    var refreshToken: String
    //        var resetyn: String? = "{}"
    //        var name: String? = "{}"
    //        var email: String? = "{}"
  }

  struct Response: TokenModel {
    var token: String?
    var refreshToken: String
    var resetyn: String?
    var name: String?
    var email: String?
  }
}
