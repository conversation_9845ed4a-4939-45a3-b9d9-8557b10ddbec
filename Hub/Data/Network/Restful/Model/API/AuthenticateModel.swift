//
//  AuthenticateModel.swift
//  Hub
//
//  Created by ncn on 2023/04/04.
//

import Foundation

protocol TokenModel: Codable {
  //    var token: String? { get set }
  var refreshToken: String { get set }
  //    var resetyn: String? { get set }
  //    var name: String? { get set }
  //    var email: String? { get set }
}

enum Authenticate {
  struct Request: Codable {
    var deviceOs: String = "1"
    let email: String
    let password: String
    var pushToken: String = ""
  }

  struct Response: TokenModel {
    var token: String?
    var resetyn: String?
    var name: String?
    var email: String?
    var refreshToken: String
    var region: String
  }
}
