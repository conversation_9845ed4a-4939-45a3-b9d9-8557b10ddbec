//
//  GetSerialCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/14/23.
//

import Foundation

enum GetSerialCloud {
  struct Send: Codable {
    let header: HeaderCloud
    let data: SendData

    struct SendData: Codable {
      let serial: String
    }
  }

  struct Response: Codable {
    let data: ResponseData

    struct ResponseData: Codable {
      let serial: String
    }
  }
}
