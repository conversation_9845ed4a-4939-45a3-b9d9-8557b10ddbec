//
//  SseHeaderCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 4/5/24.
//

import Foundation

struct SseHeaderCloud: Codable {
  let from: String /* app, sever */
  let type: String
  let res: Int
  let rmsg: String
  let cmd: String
  let id: String  // email

  init(
    from: String = "client", type: String, res: Int = 200, rmsg: String = "OK",
    cmd: String = "data", id: String
  ) {
    self.from = from
    self.type = type
    self.res = res
    self.rmsg = rmsg
    self.cmd = cmd
    self.id = id
  }
}
