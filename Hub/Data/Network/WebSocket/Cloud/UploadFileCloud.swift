//
//  UploadFileCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/20/23.
//

import Foundation

enum UploadFileCloud {
  struct Send: Codable {
    let header: HeaderCloud
    let data: SendData

    struct SendData: Codable {
      let filename: String
      let type: Int
      let videotime: Int
      let to: String
    }
  }

  struct Response: Codable {
    let filename: String
    let to: String
  }
}
