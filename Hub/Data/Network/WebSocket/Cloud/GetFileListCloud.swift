//
//  GetFileListCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/14/23.
//

import Foundation

enum GetFileListCloud {
  struct Send: Codable {
    let header: HeaderCloud
    let data: SendData

    struct SendData: Codable {
      let type: Int
      let thumb: Int
    }
  }

  struct Response: Codable {
    let type: Int
    let path: String
    let subpath: String
    var filelist: [File]

    struct File: Codable {
      let name: String
      let subname: String
      let length: Int
      let thumbnail: String?
    }

  }
}
