//
//  HeaderCloud.swift
//  Hub
//
//  Created by <PERSON>ejung<PERSON><PERSON> on 11/14/23.
//

import Foundation

struct HeaderCloud: Codable {
  let from: String /* app, sever */
  let type: String
  let res: Int
  let rmsg: String
  let time: String
  let cmd: String
  let serial: String

  init(
    from: String = "server", type: String, res: Int = 200, rmsg: String = "OK",
    cmd: String = "data", serial: String
  ) {
    self.from = from
    self.type = type
    self.res = res
    self.rmsg = rmsg

    let currentMilliseconds = Int(Date().timeIntervalSince1970 * 1000)
    self.time = "\(currentMilliseconds)"
    self.cmd = cmd
    self.serial = serial
  }
}
