//
//  GetThumbnailCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/14/23.
//

import Foundation

enum GetThumbnailCloud {
  struct Send: Codable {
    let header: HeaderCloud
    let data: SendData

    struct SendData: Codable {
      let thumbnailcnt: Int
      let thumbnaildata: [ThumbanilData]

      struct ThumbanilData: Codable {
        let name: String
        let channel: Int
      }
    }
  }

  struct Response: Codable {
    //    let data: ResponseData
    //
    //    struct ResponseData: Codable {
    let cnt: Int
    let data: [ThumbanilData]

    struct ThumbanilData: Codable {
      let name: String
      let thumbnail: String
    }
    //    }
  }
}
