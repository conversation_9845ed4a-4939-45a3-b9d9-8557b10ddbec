//
//  SetCommandCloud.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 11/14/23.
//

import Foundation

enum SetCommandCloud {
  struct Send: Codable {
    let header: HeaderCloud
    let data: SendData

    struct SendData: Codable {
      let command: String
    }
  }

  struct Response: Codable {
    let header: HeaderCloud
    let data: ResponseData

    struct ResponseData: Codable {
      let command: String
    }
  }
}
