//
//  NewFirmwareModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/16/24.
//

import Foundation

enum NewFirmwareModel {
  struct Send: SocketMessage {
    var header: HeaderModel
    var newfirmware: Newfirmware

    struct Newfirmware: Codable {
      var file: String
    }
  }
}

enum NewBinaryModel {
  struct Send: SocketMessage {
    var header: HeaderModel
    var newbinaryfile: NewBinary

    struct NewBinary: Codable {
      var file: String
    }
  }
}
