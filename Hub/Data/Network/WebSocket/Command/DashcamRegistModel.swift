//
//  DashcamRegistModel.swift
//  Hub
//
//  Created by leej<PERSON><PERSON><PERSON> on 2023/09/04.
//

import Foundation

enum DashcamRegist {
  struct Send: Codable {
    let header: Header
    let data: Data

    struct Header: Codable {
      let from: String
      let res: Int?
      let type: String
      let rmsg: String?
      let cmd: String
    }

    struct Data: Codable {
      let serial: String
      let imei: String
      let privateCode: String
      let email: String
    }
  }

  struct Response: Codable {
    let header: Header
    let data: Data

    struct Header: Codable {
      let from: String
      let res: Int?
      let type: String
      let rmsg: String?
      let cmd: String
    }

    struct Data: Codable {
      let serial: String?
      let imei: String?
      let privateCode: String?
      let email: String?
    }
  }
}
