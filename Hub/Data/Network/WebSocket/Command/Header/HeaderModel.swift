//
//  HeaderModel.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import Foundation

var _mark = 1001

protocol SocketMessage: Codable {
  var header: HeaderModel { get set }
}

struct HeaderModel: Codable {
  let from: String /* app, sever */
  let version: Int /* 프로토콜 버전 */
  var seq: Int /* sequence number */
  let res: Int /* 결과 코드 200, 400, 500 */
  let rmsg: String /* 결과 메시지 */
  let cmd: String /* command */

  init(
    from: String = "app", version: Int = 1, seq: Int = 0, res: Int = 200, rmsg: String = "ok",
    cmd: String
  ) {
    self.from = from
    self.version = version
    self.seq = seq
    self.res = res
    self.rmsg = rmsg
    self.cmd = cmd
    self.seq = self.getMark()
  }

  private func getMark() -> Int {
    defer { _mark += 1 }
    return _mark
  }
}
