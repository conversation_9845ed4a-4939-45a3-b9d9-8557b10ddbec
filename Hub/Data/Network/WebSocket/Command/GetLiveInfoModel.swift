//
//  GetLiveInfoModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/26/24.
//

import Foundation

enum GetLiveInfoModel {
  struct Send: SocketMessage {
    var header: HeaderModel
    var getliveinfo = "{}"
  }

  struct Response: Codable {
    var header: HeaderModel
    let getliveinfo: GetLiveInfoHistory?
  }

  struct GetLiveInfoHistory: Codable {
    let history: GetInitInfoHistoryModel?
  }
}
