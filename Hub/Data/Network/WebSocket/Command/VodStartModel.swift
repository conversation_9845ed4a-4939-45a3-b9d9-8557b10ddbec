//
//  VODStartModel.swift
//  Hub
//
//  Created by ncn on 2023/03/09.
//

import UIKit

struct StartRecordVodModel: Codable {
  let path: String
  let name: String
  var mount = "vod"
  let port: Int
  let ipaddress: String
  var video: Int
  let audio: Int
}

enum VodStart {
  struct Send: SocketMessage {
    var header: HeaderModel
    let startrecordvod: StartRecordVodModel
  }

  struct Response: SocketMessage {
    var header: HeaderModel
    let startrecordvod: GSensorWifiVodModel?
  }

}

struct GSensorWifiVodModel: Codable {
  let interval: Int
  let gSensorData: [GSensor]

  enum CodingKeys: String, CodingKey {
    case interval
    case gSensorData = "udata"
  }

  struct GSensor: Codable {
    let lat: Double
    let lon: Double
    let x: Double
    let y: Double
    let z: Double

    func toUdgModel() -> UdGModel {
      return UdGModel(latitude: lat, longitude: lon)
    }
  }
}
