//
//  SetinfomationModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/29/24.
//

import Foundation

enum SetInformationModel {
  struct Send: SocketMessage {
    var header: HeaderModel
    var setinformation: SetInformation
  }

  struct Response: SocketMessage {
    var header: HeaderModel
  }

  struct SetInformation: Codable {
    let name: String
    let company: String  // 서버 모델을 송유하고 있는 회사 명
    let `protocol`: String  // 스트리밍에 사용 가능한 프로토콜 'RTP/RTSP, 'HLS''
    var userlanguage: Int?
    var countrycode: Int?
    var gmt: Int?
  }
}
