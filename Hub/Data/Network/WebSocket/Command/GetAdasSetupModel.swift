//
//  GetAdasSetupModel.swift
//  Hub
//
//  Created by ncn on 2023/05/10.
//

import UIKit

struct AdasSetupModel: Codable {
  let version: Int
  let channel: Int
  let width: Int
  let height: Int
  let ldwsSenseBoth: Int?
  let collisionSense: Int?
  var bonnetY: Int
  var centerX: Int
  var trunkY: Int
  var rearCenterX: Int
  let cartype: Int?

  enum CodingKeys: String, CodingKey {
    case version
    case channel
    case width
    case height
    case ldwsSenseBoth = "ldws-sense-both"
    case collisionSense = "collision-sense"
    case bonnetY = "bonnet-y"
    case centerX = "center-x"
    case trunkY = "trunk-y"
    case rearCenterX = "rear-center-x"
    case cartype
  }
}

enum GetAdasSetup {
  struct Request: Codable {
    let header: HeaderModel
    var adasget = "{}"
  }

  struct Response: Codable {
    let header: HeaderModel
    let adasget: AdasSetupModel
  }
}

/*
 ▿ 0 : 2 elements
   let height
   - value : 1080
 ▿ 1 : 2 elements
   let channel
   - value : 1
 ▿ 2 : 2 elements
   let trunk-y
   - value : 864
 ▿ 3 : 2 elements
   let center-x
   - value : 960
 ▿ 4 : 2 elements
   let width
   - value : 1920
 ▿ 5 : 2 elements
   let rear-center-x
   - value : 960
 ▿ 6 : 2 elements
   let cartype
   - value : 0
 ▿ 7 : 2 elements
   let version
   - value : 3
 ▿ 8 : 2 elements
   let ldws-sense-both
   - value : 2
 ▿ 9 : 2 elements
   let collision-sense
   - value : 0
 ▿ 10 : 2 elements
   let bonnet-y
   - value : 864

 {
   "header": {
     "from": "device",
     "version": 1,
     "seq": 1007,
     "res": 200,
     "rmsg": "OK",
     "cmd": "adasget"
   },
   "adasget": {
     "version": 3,
     "channel": 1,
     "width": 1920,
     "height": 1080,
     "bonnet-y": 864,
     "center-x": 960,
     "trunk-y": 864,
     "rear-center-x": 960
   }
 }
 */
