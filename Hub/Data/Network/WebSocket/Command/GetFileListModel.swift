//
//  GetFileListModel.swift
//  Hub
//
//  Created by ncn on 2023/05/19.
//

import UIKit

struct ListModel: Codable {
  let filename: String
  let url: String?
  let thumbsize: Int?
  let thumbdata: String?
  let channelbits: Int?
}

struct GetFileListModel: Codable {
  let type: Int  // 0 : 상시, 1: 이벤트, 2: 주차, 3: 메뉴얼, 4: 마이폴더(옵션)
  let thumb: Int
  let url: Int? // Hub 에서 추가 file url return
}

struct ResponseFileListModel: Codable {
  let path: String
  let list: [ListModel]
}

enum GetFileList {
  struct Send: SocketMessage {
    var header: HeaderModel
    let getfilelist: GetFileListModel
  }

  struct Response: Codable {
    let header: HeaderModel
    let getfilelist: ResponseFileListModel?
  }
}

extension ListModel {
  func toDomain(path: String?, completion: @escaping (FileListCellModel) -> Void) {
    var image: UIImage? = nil
    if let string = thumbdata,
       let urlString = url,
       let mp4Url = URL(string: urlString),
       let thumbSize = thumbsize {
      if string == "NUL", thumbSize == 0, urlString.lowercased().hasSuffix(".jpg") {
        Composers.popupDownloadRepository.request(url: urlString) { progress in } completion: { result in
          switch result {
          case .success(let data):
            image = UIImage(data: data)
            fLogger.info("jpg 원격 이미지 다운로드 성공")
          case .failure(let error):
            fLogger.error("jpg 원격 이미지 다운로드 실패: \(error)")
          }
          let model = FileListCellModel(type: .wifi, image: image, fileName: self.filename, path: path, channelbits: self.channelbits)
          completion(model)
        }
        return
      } else if thumbSize > 0 && string.count > 4, let data = Data(base64Encoded: string) {
        image = UIImage(data: data)
      } else {
        Task {
          do {
            if let thumbnail = try await generateThumbnailFromRemoteVideo(url: mp4Url) {
              fLogger.info("썸네일 생성 성공")
              image = thumbnail
            } else {
              fLogger.error("fail generateThumbnailFromRemoteVideo")
            }
            let model = FileListCellModel(type: .wifi, image: image, fileName: self.filename, path: path, channelbits: self.channelbits)
            completion(model)
          } catch {
            fLogger.error("썸네일 생성 중 에러 발생: \(error)")
            let model = FileListCellModel(type: .wifi, image: nil, fileName: self.filename, path: path, channelbits: self.channelbits)
            completion(model)
          }
        }
        return
      }
    }
    let model = FileListCellModel(type: .wifi, image: image, fileName: filename, path: path, channelbits: channelbits)
    completion(model)
  }
}

extension ResponseFileListModel {
  func toDomain(completion: @escaping (VodThumbnailModel) -> Void) {
    let group = DispatchGroup()
    var items: [FileListCellModel?] = Array(repeating: nil, count: list.count)
    for (index, model) in list.enumerated() {
      group.enter()
      model.toDomain(path: path) { cellModel in
        items[index] = cellModel
        group.leave()
      }
    }
    group.notify(queue: .main) {
      let validItems = items.compactMap { $0 }
      completion(VodThumbnailModel(path: self.path, items: validItems))
    }
  }
}
