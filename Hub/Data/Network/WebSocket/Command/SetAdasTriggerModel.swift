//
//  SetAdasTriggerModel.swift
//  Hub
//
//  Created by ncn on 2023/05/08.
//

import UIKit

enum SetAdasTrigger {
  struct Send: Codable {
    let header: HeaderModel
    let adastriggerset: SetAdasTriggerModel
  }

  struct Response: Codable {
    let header: HeaderModel
  }

  struct SetAdasTriggerModel: Codable {
    var version: Int = 3  // version
    var cartype: Int  // 0: sedan, 1: SUV, 3: Truck

    var ldwsOn: Int  // 0: Off, 1: On (차선이탈 경고)
    var ldwsSpeed: Int  // 0: 0Km/h, 1: 50Km/h, 2: 60Km/h, 3: 80Km/h, 4: 100Km/h,
    var ldwsSenseBoth: Int  // 차선 민감도 (0: 둔감 ~ 2: 민감)

    var fvsaOn: Int  // 0: Off, 1: On (전방차량 출발 안내)

    var fcwsOn: Int  // 0: Off, 1: On (전방추돌 경고)
    var fcwsSense: Int  // 전방추돌 민감도 (0: 둔감 ~ 2: 민감)

    var rcwsOn: Int  // 0: Off, 1: On (후방추돌 경고)
    var rcwsSense: Int  // 후방추돌 민감도 (0: 둔감 ~ 2: 민감)

    var pcwsOn: Int  // 0: Off, 1: On (보행자추돌 경고)
    var rpwsOn: Int  // 0: Off, 1: On (후방보행자추돌 경고)

    var ongpsOn: Int  // 0: Off, 1: On (안전도우미 경고)
    var ongpsMobileOn: Int  // 0: Off, 1: On (안전도우미 mobile 사용 여부)

    var speedtype: Int  // 0: Km/h, 1: MPH (안전도우미 mobile 사용 여부)

    enum CodingKeys: String, CodingKey {
      case version, cartype, speedtype
      case ldwsOn = "ldws-on"
      case ldwsSpeed = "ldws-speed"
      case ldwsSenseBoth = "ldws-sense-both"
      case fvsaOn = "fvsa-on"
      case fcwsOn = "fcws-on"
      case fcwsSense = "fcws-sense"
      case rcwsOn = "rcws-on"
      case rcwsSense = "rcws-sense"
      case pcwsOn = "pcws-on"
      case rpwsOn = "rpws-on"
      case ongpsOn = "ongps-on"
      case ongpsMobileOn = "ongps-mobile-on"
    }
    init(model: GetAdasTriggerModel, isAllOn: Bool) {
      if isAllOn {
        self.cartype = model.cartype
        self.ldwsOn = model.ldwsOn
        self.ldwsSpeed = model.ldwsSpeed
        self.ldwsSenseBoth = model.ldwsSenseBoth
        self.fvsaOn = model.fvsaOn
        self.fcwsOn = model.fcwsOn
        self.fcwsSense = model.fcwsSense
        self.rcwsOn = model.rcwsOn
        self.rcwsSense = model.rcwsSense
        self.pcwsOn = model.pcwsOn
        self.rpwsOn = model.rpwsOn
        self.ongpsOn = model.ongpsOn
        self.ongpsMobileOn = model.ongpsMobileOn
        self.speedtype = 0
      } else {
        self.cartype = model.defaultCartype
        self.ldwsOn = model.defaultLdwsOn
        self.ldwsSpeed = model.defaultLdwsSpeed
        self.ldwsSenseBoth = model.defaultLdwsSenseBoth
        self.fvsaOn = model.defaultFvsaOn
        self.fcwsOn = model.defaultFcwsOn
        self.fcwsSense = model.defaultFcwsSense
        self.rcwsOn = model.defaultRcwsOn
        self.rcwsSense = model.defaultRcwsSense
        self.pcwsOn = model.defaultPcwsOn
        self.rpwsOn = model.defaultRpwsOn
        self.ongpsOn = model.defaultOngpsOn
        self.ongpsMobileOn = model.defaultOngpsMobileOn
        self.speedtype = 0
      }
    }
  }

}
