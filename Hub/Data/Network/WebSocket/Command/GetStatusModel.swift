//
//  GetStatusModel.swift
//  Hub
//
//  Created by ncn on 2023/04/18.
//

import UIKit

/*
 "getstatus": {
   "recmode": 1,
   "gpsstatus": 2,
   "gpslevel": 0,
   "voltage": 1430,
   "temperature": 76,
   "g_sensor_x": -53,
   "g_sensor_y": 89,
   "g_sensor_z": 8,
   "rearcam": 2,
   "ssid": "\"VUEROID_S1-4K\"",
   "drivemode": 0
 }
 */
struct GetStatusModel: Codable {
  let recmode: Int
  let gpsstatus: Int
  let gpslevel: Int
  let ongpsver: String?
  let voltage: Int
  let temperature: Int
  let g_sensor_x: Int
  let g_sensor_y: Int
  let g_sensor_z: Int
  let rearcam: Int
  let ssid: String
  let drivemode: Int
}

enum GetStatus {
  struct Send: SocketMessage {
    var header: HeaderModel
    var getstatus = "{}"
  }

  struct Response: Codable {
    let getstatus: GetStatusModel
  }
}

extension GetStatus.Response {
  func toDomain() -> HomeDashcamModel {
    let mode = getstatus.drivemode == 0 ? "Driving" : "Parking"
    var v: Float = 0.0
    if getstatus.voltage > 0 {
      v = Float(getstatus.voltage) / 100
    }
    let string = String(format: "%.1f", v)
    cLogger.info("voltage: \(getstatus.voltage), string: \(string)")
    let udg = UdGModel(status: getstatus.drivemode + 1)
    //        return .init(voltage: "전압 \(string)V", drivemode: mode)
    return .init(voltage: "\(string)V", drivemode: mode, udG: udg)
  }
}
