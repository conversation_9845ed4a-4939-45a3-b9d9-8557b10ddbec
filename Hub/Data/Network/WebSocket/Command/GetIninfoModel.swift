//
//  GetIninfoModel.swift
//  Hub
//
//  Created by leejung<PERSON><PERSON> on 2023/10/18.
//

import Foundation

/*
 "getinitinfo": {
   "version": "0.66",
   "vchannels": "3",
   "vchannelbits": 7,
   "ssid": "\"VUEROID_S1-4K\"",
   "serial": "",
   "modelname": "S1-4K",
   "drivemode": 0,
   "signal": -1,
   "voltage": "14.6V",
   "timeregion": 14,
   "language": 0,
   "speedunit": 1,
   "latitude": "37.400280",
   "longitude": "127.106918",
   "history": {
     "speedunit": 1,
     "duration": 251,
     "distance": 1,
     "speedavg": 1,
     "speedmax": 3,
     "eventnum": 0
   }
 */
struct GetInitInfoModel: Codable {
  #if LTE
  let imei: String?
  let iccid: String?
  let telecom: String?
  let apn: String?
  let usimInserted: Int?
  let usimRegistered: Int?
  let usimPin: String
  let userName: String?
  let password: String?
  let onGpsVer: String?
  #endif
  let version: String?
  let vchannels: String?
  let vchannelbits: Int?
  let ssid: String?
  let serial: String?
  let modelName: String?
  let driveMode: Int?
  let signal: Int? // Hub 추가
  let voltage: String?
  let timeRegion: Int?
  let language: Int?
  let speedUnit: Int?  // 0: kmh, 1:mph
  let latitude: String?
  let longitude: String?
  let history: GetInitInfoHistoryModel?


  enum CodingKeys: String, CodingKey {
#if LTE
    case imei
    case iccid
    case telecom
    case apn = "APN"
    case userName = "username"
    case password
    case usimInserted = "usim-inserted"
    case usimRegistered = "usim-registered"
    case usimPin = "usim-pin"
    case onGpsVer = "ongpsver"
#endif
    case version
    case vchannels
    case vchannelbits
    case ssid
    case serial
    case modelName = "modelname"
    case driveMode = "drivemode"
    case voltage
    case signal
    case timeRegion = "timeregion"
    case language
    case speedUnit = "speedunit"
    case latitude
    case longitude
    case history
  }
}

public struct GetInitInfoHistoryModel: Codable {
  let speedUnit: Int?  // 0: kmh, 1:mph
  let duration: Int?  // 초
  let distance: Int?  // 속도단위
  let speedAvg: Int?  // 속도단위
  let speedMax: Int?  // 속도단위
  let eventNum: Int?  // 횟수
  enum CodingKeys: String, CodingKey {
    case speedUnit = "speedunit"
    case duration
    case distance
    case speedAvg = "speedavg"
    case speedMax = "speedmax"
    case eventNum = "eventnum"
  }

  public init(speedUnit: Int?, duration: Int?, distance: Int?, speedAvg: Int?, speedMax: Int?, eventNum: Int?) {
    self.speedUnit = speedUnit
    self.duration = duration
    self.distance = distance
    self.speedAvg = speedAvg
    self.speedMax = speedMax
    self.eventNum = eventNum
  }

  public static func fake() -> GetInitInfoHistoryModel {
    return .init(
      speedUnit: 99,
      duration: 999,
      distance: 9999,
      speedAvg: 99999,
      speedMax: 999999,
      eventNum: 9999999
    )
  }
}

enum GetInitInfo {
  struct Send: SocketMessage {
    var header: HeaderModel
    var getinitinfo = "{}"
  }

  struct Response: Codable {
    var header: HeaderModel
    let getinitinfo: GetInitInfoModel?
  }
}

//extension GetStatus.Response {
//    func toDomain() -> HomeDashcamModel {
//        let mode = getstatus.drivemode == 0 ? "Driving" : "Parking"
//        var v: Float = 0.0
//        if getstatus.voltage > 0 {
//             v = Float(getstatus.voltage) / 100
//        }
//        let string = String(format: "%.2f", v)
//
////        return .init(voltage: "전압 \(string)V", drivemode: mode)
//        return .init(voltage: "\(string)V", drivemode: mode)
//    }
//}
