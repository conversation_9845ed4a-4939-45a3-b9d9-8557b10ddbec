//
//  GetConfiguration.swift
//  Hub
//
//  Created by ncn on 2023/04/18.
//

import UIKit

struct ConfigurationModel: Codable {
  let videobitrate: Int
  let voltagelimit: Int
  let parkrectime: Int
  let parkmode: Int
  let timelapse: Int
  let sleepmode: Int
  let drivetimelapse: Int

  let driveispmode: Int
  let parkispmode: Int

  let infsensorsen: Int
  let parksensorsen: Int
  let frontmotionsensoren: Int
  let rearmotionsensoren: Int

  let audiorec: Int
  let speakervolume: Int
  let alertevent: Int
  let timeregion: Int
  let summertime: Int
  let time: String

  let memoryassign: Int
  let secureled: Int
  let statusled: Int
  let parkled: Int
  let speedunit: Int
  let speedmode: Int
  var wifionauto: Int
  let wifibandwidth: Int

  let ongpson: Int
  let gmtIndexTime: Int
  
  // 새로 추가된 속성
  let lcdOntime: Int
  let parkRearCam3chOnOff: Int //실내 카메라
  let frontResolution: Int
  let frequency: Int
  let parkDelay: Int
  let wintermode: Int
  let audioguide: Int
  let frontRotate: Int
  let frontMirror: Int
  let rearRotate: Int
  let interiorRotate: Int
  let rearMirror: Int
  let interiorMirror: Int
  let eventProtection: Int
  let rearCamera: Int
  let interiorCamera: Int
  let datetype: Int
  let hdrAlwaysTime: Int
  let hdrSunriseTime: Int
  let hdrSunSetTime: Int
  let watermark: Int
  let secretmode: Int //비밀번호 잠금
  let secretpass: Int //비밀번호 변경
  let language: Int
  let resetPopup: Int? //초기화 팝업
  
  enum CodingKeys: String, CodingKey {
    case videobitrate
    case voltagelimit
    case parkrectime
    case parkmode
    case timelapse
    case sleepmode
    case drivetimelapse
    case driveispmode = "drive-isp-mode"
    case parkispmode = "park-isp-mode"
    case infsensorsen
    case parksensorsen
    case frontmotionsensoren
    case rearmotionsensoren
    case audiorec
    case speakervolume
    case alertevent
    case timeregion
    case summertime
    case time
    case memoryassign
    case secureled
    case statusled
    case parkled
    case parkDelay = "park_delay"
    case speedunit
    case speedmode
    case wifionauto
    case wifibandwidth
    case ongpson = "ongps-on"
    case gmtIndexTime = "gmt-index-time"
    
    // 새로 추가된 속성 CodingKeys
    case lcdOntime = "lcd-ontime"
    case parkRearCam3chOnOff = "ParkRearCam3chOnOff"
    case frontResolution
    case frequency
    case wintermode
    case audioguide
    case frontRotate
    case frontMirror
    case rearRotate = "RearRotate"
    case interiorRotate = "InteriorRotate"
    case rearMirror = "RearMirror"
    case interiorMirror = "InteriorMirror"
    case eventProtection
    case rearCamera
    case interiorCamera
    case datetype
    case hdrAlwaysTime
    case hdrSunriseTime
    case hdrSunSetTime
    case watermark
    case secretmode
    case secretpass
    case language
    case resetPopup
  }
}

enum GetConfiguration {
  struct Send: SocketMessage {
    var header: HeaderModel
    var getconfiguration = "{}"
  }

  struct Response: Codable {
    var getconfiguration: ConfigurationModel
  }
}

extension GetConfiguration.Response {
  func toDmoain() -> HomeDashcamModel {
    let tz =
      _timezone.count > getconfiguration.timeregion
      ? _timezone[getconfiguration.timeregion] : "Unknown"
    let time = getconfiguration.time
    return .init(timeZone: tz, time: time)
  }
}
