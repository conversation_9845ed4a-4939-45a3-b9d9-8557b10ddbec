
//
//  VerifyPasswordModel.swift
//  Hub
//
//  Created by ncn on 2/6/25.
//

import Foundation

/*
 {"header":{"cmd":"verifypassword","from":"app","res":200,"rmsg":"OK","seq":1,"version":1},
 "verifypassword":{"connect":"Fconnect"}}
 */

enum VerifyPasswordModel {
  struct Send: SocketMessage {
    var header: HeaderModel
    var verifypassword: VerifyPassword
  }

  struct Response: SocketMessage {
    var header: HeaderModel
    var verifypassword: VerifyPassword
  }

  enum ConnectValue: String {
    case firstConnect = "Fconnect"
    case misMatch = "MISMATCH"
    case userCancel = "USERCANCEL"
    case success = "SUCCESS"

    func isNotValid(result: String) -> Bool {
      [.misMatch, .userCancel].contains(self)
    }
  }

  struct VerifyPassword: Codable {
    let connect: String
  }
}
