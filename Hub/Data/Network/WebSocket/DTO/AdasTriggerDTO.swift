//
//  AdasTriggerDTO.swift
//  Hub
//
//  Created by ncn on 2023/05/08.
//

import UIKit

extension GetAdasTriggerModel {
  static let onOff = [L.setting_detail_off_value.localized, L.setting_detail_on_value.localized]
  static let cartypes = ["Sedan", "SUV", "Truck"]
  static let ldwsSpeed = [
    L.setting_detail_off_value.localized, "50km/h", "60km/h", "80km/h", "100km/h",
  ]
  static let ldwsSensitivities = [
    L.setting_detail_low_value.localized, "Middle", L.setting_detail_high_value.localized,
  ]
  static let fcwsSensitivities = [
    L.setting_detail_low_value.localized, "Middle", L.setting_detail_high_value.localized,
  ]
  static let rcwsSensitivities = [
    L.setting_detail_low_value.localized, "Middle", L.setting_detail_high_value.localized,
  ]
}

extension GetAdasTrigger.Response {
  private func getVehcileType() -> String {
    if adastriggerget.cartype > GetAdasTriggerModel.cartypes.count - 1 {
      return "Unkonw"
    }
    return GetAdasTriggerModel.cartypes[adastriggerget.cartype]
  }

  private func getLDWSSpeed() -> String {
    if adastriggerget.ldwsSpeed > GetAdasTriggerModel.ldwsSpeed.count - 1 {
      return "Unkonw"
    }
    return GetAdasTriggerModel.ldwsSpeed[adastriggerget.ldwsSpeed]
  }

  private func getLDWSSensitivity() -> String {
    if adastriggerget.ldwsSenseBoth > GetAdasTriggerModel.ldwsSensitivities.count - 1 {
      return "Unkonw"
    }
    return GetAdasTriggerModel.ldwsSensitivities[adastriggerget.ldwsSenseBoth]
  }

  private func getFCWSSensitivity() -> String {
    if adastriggerget.fcwsSense > GetAdasTriggerModel.fcwsSensitivities.count - 1 {
      return "Unkonw"
    }
    return GetAdasTriggerModel.fcwsSensitivities[adastriggerget.fcwsSense]
  }

  private func getRCWSSensitivity() -> String {
    if adastriggerget.rcwsSense > GetAdasTriggerModel.rcwsSensitivities.count - 1 {
      return "Unkonw"
    }
    return GetAdasTriggerModel.rcwsSensitivities[adastriggerget.rcwsSense]
  }
}
