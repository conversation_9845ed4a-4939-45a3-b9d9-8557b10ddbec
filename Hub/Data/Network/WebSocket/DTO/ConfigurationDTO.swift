//
//  ConfigurationDTO.swift
//  Hub
//
//  Created by ncn on 2023/04/25.
//

import Foundation

extension ConfigurationModel {
  static let s1Resolution1ch = [
    "4K@60p(F)", "4K@30p(F)", "QHD@60p(F)", "QHD@30p(F)",
  ]

  static let s1Resolution2chFR = [
    "4K@60p(F) + QHD(R)", "4K@30p(F) + QHD(R)",
    "QHD@60p(F) + QHD(R)", "QHD@30p(F) + QHD(R)",
  ]

  static let s1Resolution2chFI = [
    "4K@60p(F) + FHD(I)", "4K@30p(F) + FHD(I)",
    "QHD@60p(F) + FHD(I)", "QHD@30p(f) + FHD(I)",
  ]

  static let s1Resolution3ch = [
    "4K 60p(F)+QHD(R)+FHD(I)",
    "4K 30p(F)+QHD(R)+FHD(I)",
    "QHD 60p(F)+QHD(R)+FHD(I)",
    "QHD 30p(F)+QHD(R)+FHD(I)",
  ]

  static let videobitrates = [
    L.setting_detail_low_value.localized, L.setting_detail_mid_value.localized,
    L.setting_detail_high_value.localized,
  ]  // 녹화화질 reboot (확인)
  static let frequency = ["50hz (PAL)", "60hz (NTSC/US)"]

  static let audiorec = [L.s1_setting_off.localized, "on", "Event only"]

  static let drive_sip_mode = ["50hz (PAL)", "60hz (NTSC/US)"]
  static let frontRotate = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let frontMirror = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let rearRotate = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let rearMirror = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let interiorRotate = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let interiorMIrror = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let eventProtection = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  
  // rearCamera, interiorCamera
  // "초기값은 2 (UI는 OFF로표시) 사용자가 변경안하는 경우 2값 보내줘야함"
  static let rearCameraUsage = [
    L.setting_detail_off_value.localized,
    L.setting_detail_on_value.localized,
    L.setting_detail_off_value.localized
  ]
  
  static let interiorCameraUsage = [
    L.setting_detail_off_value.localized,
    L.setting_detail_on_value.localized,
    L.setting_detail_off_value.localized
  ]
  //section 2
  static let audioGuide = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let alertEvent = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let secureled = ["Security LED off", "Off in Parking Mode", "Full On"]
  static let recType = [
    L.rectype_low_powermode.localized,
    L.rectype_impact_motion.localized,
    L.rectype_timeplase.localized,
  ]
  static let sleepMode = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  //  static let parkDelay = ["Off", "90초", "3분",]

  //section 3
  static let lcdOntimes = [
    L.lcdoff_alwayson.localized, L.lcdoff_halfmin.localized, L.lcdoff_onemin.localized,
    L.lcdoff_clock.localized,
  ]
  static let watermark = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let passwordLock = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let passwordChange = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]
  static let languageSheet = ["English", "Spanish"]

  enum lcdOntime: Int, CaseIterable {
    case alwaysOn = 0
    case sec_30 = 1
    case min_1 = 2
    case clockOnLcd = 3

    var viewIndex: Int {
      switch self {
      case .alwaysOn: 0
      case .sec_30: 1
      case .min_1: 2
      case .clockOnLcd: 3
      }
    }
    static func fromViewIndex(_ index: Int) -> lcdOntime {
      switch index {
      case 0: return .alwaysOn
      case 1: return .sec_30
      case 2: return .min_1
      case 3: return .clockOnLcd
      default: return .alwaysOn
      }
    }
    var name: String {
      switch self {
      case .alwaysOn: L.lcdoff_alwayson.localized
      case .sec_30: L.lcdoff_halfmin.localized
      case .min_1: L.lcdoff_onemin.localized
      case .clockOnLcd: L.lcdoff_clock.localized
      }
    }

    // enum name to array
    static var all: [String] {
      return ParkRECTimeType.allCases.map { $0.name }
    }
  }

  static let winterMode = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]

  static let innerCamera = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]

  static let voltagelimits = [
    L.setting_detail_voltage_value01.localized,
    L.setting_detail_voltage_value02.localized,
    L.setting_detail_voltage_value03.localized,
    L.setting_detail_voltage_value04.localized,
    L.setting_detail_voltage_value05.localized,
    L.setting_detail_voltage_value06.localized,
    L.setting_detail_voltage_value07.localized,
    L.setting_detail_voltage_value08.localized,
    L.setting_detail_voltage_value09.localized,
    L.setting_detail_voltage_value10.localized,
    L.setting_detail_voltage_value11.localized,
    L.setting_detail_voltage_value12.localized,
    L.setting_detail_voltage_value13.localized,
    L.setting_detail_voltage_value14.localized,
  ]  // 방전차단전압 (확인)

  enum ParkRECTimeType: Int, CaseIterable {
    case off = 0
    case hour_1 = 1
    case hour_2 = 2
    case hour_3 = 3
    case hour_6 = 4
    case hour_12 = 5
    case hour_24 = 6
    case hour_48 = 7

    var viewIndex: Int {
      switch self {
      case .off: 0
      case .hour_1: 1
      case .hour_2: 2
      case .hour_3: 3
      case .hour_6: 4
      case .hour_12: 5
      case .hour_24: 6
      case .hour_48: 7
      }
    }

    static func fromViewIndex(_ index: Int) -> ParkRECTimeType {
      switch index {
      case 0: return .off
      case 1: return .hour_1
      case 2: return .hour_2
      case 3: return .hour_3
      case 4: return .hour_6
      case 5: return .hour_12
      case 6: return .hour_24
      case 7: return .hour_48
      default: return .off
      }
    }

    var name: String {
      switch self {
      case .off: L.setting_detail_off_value.localized
      case .hour_1: L.setting_park_rec_time_value06.localized
      case .hour_2: L.setting_park_rec_time_value07.localized
      case .hour_3: L.setting_park_rec_time_value05.localized
      case .hour_6: L.setting_park_rec_time_value01.localized
      case .hour_12: L.setting_park_rec_time_value02.localized
      case .hour_24: L.setting_park_rec_time_value03.localized
      case .hour_48: L.setting_park_rec_time_value04.localized
      }
    }

    // enum name to array
    static var all: [String] {
      return ParkRECTimeType.allCases.map { $0.name }
    }
  }

  static let parkmodes = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 자동주차 (확인)
  //  static let driveTimelapses = [
  //    "Time-lapse Off","Time-lapse On",
  //  ]  // 주차녹화방식 (확인)
  static let parkfpses = [
    L.setting_parkfps_value01.localized, L.setting_parkfps_value02.localized,
    L.setting_parkfps_value03.localized,
  ]  //주차녹화프레임 format (확인)
  static let drivetimelapses = [
    L.setting_drive_time_lapse_value01.localized, L.setting_drive_time_lapse_value02.localized,
  ]  // 상시녹화방식 reboot (확인)

  static let parkeventnoti = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 주차녹화 이벤트 알림 (확인)

  static let sleepmodes = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 저전력모드 (on시 방전차단시간-off / 주차녹화방식-Motion)
  static let driveispmodes = [
    L.setting_drive_isp_mode_value01.localized,
    L.setting_drive_isp_mode_value02.localized,
    L.HDR_infinite_plate_capture.localized,
  ]  //상시녹화화질 설정  reboot (확인)

  static let hdrAlwaysTimes = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]

  static let hdrSunriseTimes = [
    "0H", "1H", "2H", "3H", "4H", "5H", "6H", "7H", "8H", "9H", "11H", "12H",
    "13H", "14H", "15H", "16H", "17H", "18H", "19H", "20H", "21H", "22H", "23H", "24H",
  ]

  static let hdrSunsetTimes = [
    "0H", "1H", "2H", "3H", "4H", "5H", "6H", "7H", "8H", "9H", "11H", "12H",
    "13H", "14H", "15H", "16H", "17H", "18H", "19H", "20H", "21H", "22H", "23H", "24H",
  ]

  static let parkispmodes = [
    L.setting_park_isp_mode_value01.localized, L.setting_park_isp_mode_value02.localized,
    /*"Preminum Night Vision",*/L.setting_park_isp_mode_value03.localized,
  ]  //주차 영상 화질 모드 (확인)

  // 주차모드 지연
  static let parkdelaymodes = [
    L.s1_setting_parkdelay_no_delay.localized,
    L.s1_setting_parkdelay_ninety_s.localized,
    L.s1_setting_parkdelay_three_m.localized,
  ]

  static let rearuses = [L.setting_detail_off_value.localized, L.setting_detail_on_value.localized]  //후방 주차 녹화
  //Event Sensitivity
  //  static let infsensorsenses = [
  //    L.driving_impact_sensitivity.localized, L.parking_impact_sensitivity.localized,
  //    L.motion_impact_sensitivity.localized,
  //  ]  // 상시충격감도 (확인)
  static let infsensorsenses = [
    L.setting_detail_off_value.localized, L.setting_detail_low_value.localized,
    L.setting_detail_mid_value.localized, L.setting_detail_high_value.localized,
  ]
  static let parksensorsenses = [
    L.setting_detail_low_value.localized,
    L.setting_detail_mid_value.localized, L.setting_detail_high_value.localized,
  ]  // 주차충격감도 (확인)
  static let frontmotionsensorens = [
    L.setting_detail_off_value.localized, L.setting_detail_low_value.localized,
    L.setting_detail_mid_value.localized, L.setting_detail_high_value.localized,
  ]  // 전방모션감도 (확인)
  static let rearmotionsensorens = [
    L.setting_detail_off_value.localized, L.setting_detail_low_value.localized,
    L.setting_detail_mid_value.localized, L.setting_detail_high_value.localized,
  ]  // 후방모션감도 (확인)

  static let audiorecs = [
    L.setting_detail_off_value.localized, L.setting_audiorec_value00.localized,
    L.setting_audiorec_value01.localized,
  ]  // 음성녹음 (확인)
  static let speakervolumes = [L.setting_detail_off_value.localized, "1", "2", "3"]  // 1: low, 5: high 스피커볼륨 (확인)
  static let alertevents = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 이벤트 알림 (확인)

  static let timeregions = _timezone  // 표준시간대 (확인)
  static let summertimes = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 썸머타임 (확인)
  static let timeDateType = ["YYYY/MM/DD", "MM/DD/YYYY", "DD/MM/YYYY"]

  static let memoryassigns = [
    L.setting_memory_assign_value01.localized, L.setting_memory_assign_value02.localized,
    L.setting_memory_assign_value03.localized, L.setting_memory_assign_value04.localized,
  ]  // 메모리할당

  static let secureleds = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 전방 시큐리티 LED
  static let statusleds = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 상태 시큐리티 LED
  static let parkleds = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 주차 시큐리티 LED
  static let speedunits = [L.unit_kmh.localized, L.unit_mph.localized]  // 속도단위 (확인)
  static let speedmodes = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // 속도표시 (확인)
  static let wifionautos = [
    L.setting_detail_off_value.localized, L.setting_detail_on_value.localized,
  ]  // wifi 자동 켜짐 (확인)
  static let wifibandwidths = ["2.4GHz", "5GHz"]
}

extension GetConfiguration.Response {
  //해상도& 프레임레이트
  private func getFrontResolution() -> String {
    if getconfiguration.frontResolution > ConfigurationModel.s1Resolution3ch.count - 1 {
      return "Unknown"
    }
    
    let  s1ResolutionValues = getS1ResolutionValues()
    return s1ResolutionValues[getconfiguration.frontResolution]
  }
  
  private func getS1ResolutionValues() -> [String] {
    var s1ResolutionValues: [String] = []
    if getconfiguration.rearCamera == 1 && getconfiguration.interiorCamera == 1 {
      s1ResolutionValues = ConfigurationModel.s1Resolution3ch
    } else if getconfiguration.rearCamera == 1 &&
                (getconfiguration.interiorCamera == 0 || getconfiguration.interiorCamera == 2) {
      s1ResolutionValues = ConfigurationModel.s1Resolution2chFR
    } else if (getconfiguration.rearCamera == 0 || getconfiguration.rearCamera == 2) &&
                getconfiguration.interiorCamera == 1 {
      s1ResolutionValues = ConfigurationModel.s1Resolution2chFI
    } else if (getconfiguration.rearCamera == 0 || getconfiguration.rearCamera == 2) &&
                (getconfiguration.interiorCamera == 0 || getconfiguration.interiorCamera == 2) {
      s1ResolutionValues = ConfigurationModel.s1Resolution1ch
    }
    
    return s1ResolutionValues
  }
  
  // 녹화 화질
  private func getVideobitrate() -> String {
    if getconfiguration.videobitrate > ConfigurationModel.videobitrates.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.videobitrates[getconfiguration.videobitrate]
  }
  //주파수
  private func getfrequency() -> String {
    if getconfiguration.frequency > ConfigurationModel.frequency.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.frequency[getconfiguration.frequency]
  }

  private func getFrontRotate() -> String {
    if getconfiguration.frontRotate > ConfigurationModel.frontRotate.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.frontRotate[getconfiguration.frontRotate]
  }

  private func getFrontMirror() -> String {
    if getconfiguration.frontMirror > ConfigurationModel.frontMirror.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.frontMirror[getconfiguration.frontMirror]
  }

  private func getRearRotate() -> String {
    if getconfiguration.rearRotate > ConfigurationModel.rearRotate.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.rearRotate[getconfiguration.rearRotate]
  }

  private func getRearMirror() -> String {
    if getconfiguration.rearMirror > ConfigurationModel.rearMirror.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.rearMirror[getconfiguration.rearMirror]
  }

  private func getInteriorRotate() -> String {
    if getconfiguration.interiorRotate > ConfigurationModel.interiorRotate.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.interiorRotate[getconfiguration.interiorMirror]
  }

  private func getInteriorMirror() -> String {
    if getconfiguration.interiorMirror > ConfigurationModel.interiorMIrror.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.interiorMIrror[getconfiguration.interiorMirror]
  }

  private func getEventProtection() -> String {
    if getconfiguration.eventProtection > ConfigurationModel.eventProtection.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.eventProtection[getconfiguration.eventProtection]
  }

  private func getRearCameraUsage() -> String {
    if getconfiguration.rearCamera > ConfigurationModel.rearCameraUsage.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.rearCameraUsage[getconfiguration.rearCamera]
  }

  private func getInteriorCameraUsage() -> String {
    if getconfiguration.interiorCamera > ConfigurationModel.interiorCameraUsage.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.interiorCameraUsage[getconfiguration.interiorCamera]
  }

  //자동주차
  private func getAudiogGuide() -> String {
    if getconfiguration.audioguide > ConfigurationModel.audioGuide.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.audioGuide[getconfiguration.audioguide]
  }

  //방전차단전압
  private func getVoltagelimit() -> String {
    if getconfiguration.voltagelimit > ConfigurationModel.voltagelimits.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.voltagelimits[getconfiguration.voltagelimit]
  }

  //  private func getParkrectime() -> String {
  //    if getconfiguration.parkrectime > ConfigurationModel.parkrectimes.count - 1 {
  //      return "Unknown"
  //    }
  //    return ConfigurationModel.parkrectimes[getconfiguration.parkrectime]
  //  }

  //방전차단 시간
  private func getParkRecTime() -> String {
    Log.debug(
      category: .Setting, to: "getconfiguration.parkrectime: \(getconfiguration.parkrectime)")
    return ConfigurationModel.ParkRECTimeType(rawValue: getconfiguration.parkrectime)?.name
      ?? "Unknown"
  }

  //겨울철 모드
  private func getWintermode() -> String {
    if getconfiguration.wintermode > ConfigurationModel.winterMode.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.winterMode[getconfiguration.wintermode]
  }

  private func getParkmode() -> String {
    if getconfiguration.parkmode > ConfigurationModel.parkmodes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.parkmodes[getconfiguration.parkmode]
  }

  private func getRecTimelapse() -> String {
    if getconfiguration.timelapse > ConfigurationModel.recType.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.recType[getconfiguration.timelapse]
  }

  //Get 상시 타임 랩스
  private func getTimelapse() -> String {
    if getconfiguration.drivetimelapse > ConfigurationModel.drivetimelapses.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.drivetimelapses[getconfiguration.drivetimelapse]
  }

  //  private func getDrivetimelapse() -> String {
  //    if getconfiguration.drivetimelapse > ConfigurationModel.drivetimelapses.count - 1 {
  //      return "Unknown"
  //    }
  //    return ConfigurationModel.drivetimelapses[getconfiguration.drivetimelapse]
  //  }

  private func getDriveIspMode() -> String {
    if getconfiguration.driveispmode > ConfigurationModel.driveispmodes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.driveispmodes[getconfiguration.driveispmode]
  }

  private func getHdrAlwaysTime() -> String {
    if getconfiguration.hdrAlwaysTime > ConfigurationModel.hdrAlwaysTimes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.hdrAlwaysTimes[getconfiguration.hdrAlwaysTime]
  }

  private func getHdrSunriseTime() -> String {
    return ConfigurationModel.hdrSunriseTimes[getconfiguration.hdrSunriseTime]
  }

  private func getHdrSunsetTime() -> String {
    return ConfigurationModel.hdrSunriseTimes[getconfiguration.hdrSunSetTime]
  }

  //주차 모드 지연get
  private func getParkDelay() -> String {
    if getconfiguration.parkDelay > ConfigurationModel.parkdelaymodes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.parkdelaymodes[getconfiguration.parkDelay]
  }
  //주차 영상 화질 모드
  private func getParkIspMode() -> String {
    if getconfiguration.parkispmode > ConfigurationModel.parkispmodes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.parkispmodes[getconfiguration.parkispmode]
  }
  
  //녹화 방식
  private func getRecSleepMode() -> String {
    if getconfiguration.sleepmode > ConfigurationModel.sleepMode.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.sleepMode[getconfiguration.sleepmode]
  }

  private func getRecTimelapseMode() -> String {
    if getconfiguration.timelapse > ConfigurationModel.recType.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.recType[getconfiguration.timelapse]
  }

  // Event
  private func getInfsensorsen() -> String {
    if getconfiguration.infsensorsen > ConfigurationModel.infsensorsenses.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.infsensorsenses[getconfiguration.infsensorsen]
  }

  private func getParksensorsen() -> String {
    if getconfiguration.parksensorsen > ConfigurationModel.parksensorsenses.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.parksensorsenses[getconfiguration.parksensorsen]
  }

  private func getFrontmotionsensoren() -> String {
    if getconfiguration.frontmotionsensoren > ConfigurationModel.frontmotionsensorens.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.frontmotionsensorens[getconfiguration.frontmotionsensoren]
  }

  private func getRearmotionsensoren() -> String {
    if getconfiguration.rearmotionsensoren > ConfigurationModel.rearmotionsensorens.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.rearmotionsensorens[getconfiguration.rearmotionsensoren]
  }

  private func getinnerCam() -> String {
    if getconfiguration.parkRearCam3chOnOff > ConfigurationModel.innerCamera.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.innerCamera[getconfiguration.parkRearCam3chOnOff]
  }

  //MARK: - section 3 system
  private func getSpeakervolume() -> String {
    if getconfiguration.speakervolume > ConfigurationModel.speakervolumes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.speakervolumes[getconfiguration.speakervolume]
  }

  // Sound (음성녹음)
  private func getAudiorec() -> String {
    if getconfiguration.audiorec > ConfigurationModel.audiorecs.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.audiorecs[getconfiguration.audiorec]
  }
  //음성안내
  private func getAlertevent() -> String {
    if getconfiguration.alertevent > ConfigurationModel.alertevents.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.alertevents[getconfiguration.alertevent]
  }

  //시큐리티 LED
  private func getSecureled() -> String {
    //    if getconfiguration.secureled > ConfigurationModel.secureleds.count - 1 {
    if getconfiguration.secureled > ConfigurationModel.secureleds.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.secureleds[getconfiguration.secureled]
  }

  //상태 LED
  private func getStatusled() -> String {
    //    if getconfiguration.secureled > ConfigurationModel.secureleds.count - 1 {
    if getconfiguration.statusled > ConfigurationModel.statusleds.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.statusleds[getconfiguration.statusled]
  }

  //주차모드 LED
  private func getParkled() -> String {
    //    if getconfiguration.secureled > ConfigurationModel.secureleds.count - 1 {
    if getconfiguration.parkled > ConfigurationModel.parkleds.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.parkleds[getconfiguration.parkled]
  }

  // 시간
  private func getTimeregion() -> String {
    if getconfiguration.timeregion > ConfigurationModel.timeregions.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.timeregions[getconfiguration.timeregion]
  }

  // 날짜 설정 (New 25.01.15)
  private func getDateType() -> String {
    if getconfiguration.datetype > ConfigurationModel.timeDateType.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.timeDateType[getconfiguration.datetype]
  }

  private func getSummertime() -> String {
    if getconfiguration.summertime > ConfigurationModel.summertimes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.summertimes[getconfiguration.summertime]
  }

  // 메모리
  private func getMemoryassign() -> String {
    if getconfiguration.memoryassign > ConfigurationModel.memoryassigns.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.memoryassigns[getconfiguration.memoryassign]
  }

  // 시스템

  private func getSpeedunit() -> String {
    if getconfiguration.speedunit > ConfigurationModel.speedunits.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.speedunits[getconfiguration.speedunit]
  }

  private func getSpeedmode() -> String {
    if getconfiguration.speedmode > ConfigurationModel.speedmodes.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.speedmodes[getconfiguration.speedmode]
  }

  private func getWifionauto() -> String {
    if getconfiguration.wifionauto > ConfigurationModel.wifionautos.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.wifionautos[getconfiguration.wifionauto]
  }

  private func getWifibandwidth() -> String {
    if getconfiguration.wifibandwidth > ConfigurationModel.wifibandwidths.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.wifibandwidths[getconfiguration.wifibandwidth]
  }
  //LCD off
  private func getLcdOntime() -> String {
    return ConfigurationModel.lcdOntime(rawValue: getconfiguration.lcdOntime)?.name
      ?? "Unknown"
  }

  private func getWatermark() -> String {
    if getconfiguration.watermark > ConfigurationModel.watermark.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.watermark[getconfiguration.watermark]
  }

  private func getpasswordMode() -> String {
    sLogger.debug(":: Test  secretmode - \(getconfiguration.secretmode)")
    sLogger.debug(":: Test  passwordLock - \(ConfigurationModel.passwordLock.count)")
    if getconfiguration.secretmode > ConfigurationModel.passwordLock.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.passwordLock[getconfiguration.secretmode]
  }

  private func getpasswordChange() -> String {
    if getconfiguration.secretpass > ConfigurationModel.passwordChange.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.passwordChange[getconfiguration.secretpass]
  }

  private func getLanguage() -> String {
    if getconfiguration.language > ConfigurationModel.languageSheet.count - 1 {
      return "Unknown"
    }
    return ConfigurationModel.languageSheet[getconfiguration.language]
  }
}

extension GetConfiguration.Response {

  func toDashcamDomain() -> [SettingCellSectionModel] {

    //MARK: - Section1 카메라 설정 UI data
    //해상도 & 프레임레이트
    let s1ResolutionValues = getS1ResolutionValues()
    let frontResolution = SettingCellModel(
      title: L.s1_setting_resolution_framerate.localized,
      subTitle: L.msg_setting_alert2.localized,
      key: "frontResolution",
      rowIntValue: getconfiguration.frontResolution,
      value: getFrontResolution(),
      selectionValues: s1ResolutionValues,
      viewType: .sheetSelect,
      resetType: .Format
    )

    // 화질
    let bitrate = SettingCellModel(
      title: L.s1_setting_bitrate.localized,
      subTitle: L.msg_setting_alert2.localized,
      key: "videobitrate",
      rowIntValue: getconfiguration.videobitrate,
      value: getVideobitrate(),
      selectionValues: ConfigurationModel.videobitrates,
      viewType: .sheetSelect,
      resetType: .Format
    )

    //주파수
    let frequency = SettingCellModel(
      title: L.s1_setting_frequency.localized,
      key: "frequency",
      rowIntValue: getconfiguration.frequency,
      value: getfrequency(),
      selectionValues: ConfigurationModel.frequency,
      viewType: .sheetSelect
    )

    // 음성녹음
    let audiorec = SettingCellModel(
      title: L.sound_audio_rec.localized,
      key: "audiorec",
      rowIntValue: getconfiguration.audiorec,
      value: getAudiorec(),
      selectionValues: ConfigurationModel.audiorecs,
      viewType: .sheetSelect
    )

    // 상시 영상 화질 모드
    let driveispmode = SettingHdrCellModel(
      title: L.s1_setting_drive_isp.localized,
      key: "drive-isp-mode",
      subKeys: ["hdrAlwaysTime", "hdrSunriseTime", "hdrSunsetTime"],
      rowIntValue: getconfiguration.driveispmode,
      subRowIntValues: [
        getconfiguration.hdrAlwaysTime, getconfiguration.hdrSunriseTime,
        getconfiguration.hdrSunSetTime,
      ],
      value: getDriveIspMode(),
      selectionValues: ConfigurationModel.driveispmodes,
      hdrAlwaysOnSelectionValues: ConfigurationModel.hdrAlwaysTimes,
      hdrSunriseSelectionValues: ConfigurationModel.hdrSunriseTimes,
      hdrSunsetSelectionValues: ConfigurationModel.hdrSunsetTimes,
      viewType: .sheetSelect
    )

    // 상시 타임 랩스
    let timelapse = SettingCellModel(
      title: L.s1_setting_timelapse.localized,
      key: "drivetimelapse",
      rowIntValue: getconfiguration.drivetimelapse,
      value: getTimelapse(),
      selectionValues: ConfigurationModel.drivetimelapses,
      viewType: .sheetSelect
    )

    //상시 충격 감도
    let driveImpactSensitivity = SettingCellModel(
      title: L.driving_impact_sensitivity.localized,
      key: "infsensorsen",
      rowIntValue: getconfiguration.infsensorsen,
      value: getInfsensorsen(),
      selectionValues: ConfigurationModel.infsensorsenses,
      viewType: .sheetSelect
    )

    //주차 충격 감도
    let parkImpactSensitivity = SettingCellModel(
      title: L.parking_impact_sensitivity.localized,
      key: "parksensorsen",
      rowIntValue: getconfiguration.parksensorsen,
      value: getParksensorsen(),
      selectionValues: ConfigurationModel.parksensorsenses,
      viewType: .sheetSelect
    )

    //주차 모션 감도
    let parkMotionSensitivity = SettingCellModel(
      title: L.motion_impact_sensitivity.localized,
      key: "frontmotionsensoren",
      //      key: ["frontmotionsensoren","rearmotionsensoren"],
      rowIntValue: getconfiguration.frontmotionsensoren,
      value: getFrontmotionsensoren(),
      selectionValues: ConfigurationModel.frontmotionsensorens,
      viewType: .sheetSelect
    )

    //이미지 회전 및 미러
    let rotateMirror = SettingRotateCellModel(
      title: L.s1_setting_rotate_mirror.localized,
      hasSubKeys: true,
      key: "frontRotate",
      subKeys: ["frontMirror", "RearRotate", "RearMirror", "InteriorRotate", "InteriorMirror"],
      rowIntValue: getconfiguration.frontRotate,
      subRowIntValue: [
        getconfiguration.frontMirror,
        getconfiguration.rearRotate,
        getconfiguration.rearMirror,
        getconfiguration.interiorRotate,
        getconfiguration.interiorMirror,
      ],
      value: getInfsensorsen(),
      selectionValues: ConfigurationModel.frontRotate,
      viewType: .sheetSelectNoValue,
      enable: false
    )

    // 이벤트 영상 보호
    let eventProtection = SettingCellModel(
      title: L.s1_setting_event_protection.localized,
      key: "eventProtection",
      rowIntValue: getconfiguration.eventProtection,
      value: getEventProtection(),
      selectionValues: ConfigurationModel.eventProtection,
      viewType: .onOffSwitch
    )

    // 후방카메라
    let rearCameraUsage = SettingCellModel(
      title: L.s1_setting_rear_camera_usage.localized,
      key: "rearCamera",
      rowIntValue: getconfiguration.rearCamera,
      value: getRearCameraUsage(),
      selectionValues: ConfigurationModel.rearCameraUsage,
      viewType: .onOffSwitch,
      resetType: .Format
    )

    let interiorCameraUsage = SettingCellModel(
      title: L.s1_setting_interior_camera_usage.localized,
      key: "interiorCamera",
      rowIntValue: getconfiguration.interiorCamera,
      value: getInteriorCameraUsage(),
      selectionValues: ConfigurationModel.interiorCameraUsage,
      viewType: .onOffSwitch,
      resetType: .Format
    )

    let items01: [SettingCellPresentable] = [
      frontResolution, bitrate, frequency, audiorec, driveispmode, timelapse,
      driveImpactSensitivity, parkImpactSensitivity, parkMotionSensitivity,
      rotateMirror, eventProtection, rearCameraUsage, interiorCameraUsage,
    ]

    let camera = SettingCellSectionModel(sectionType: .camera, items: items01)

    //MARK: - section 2  주차 녹화 UI Data
    //자동주차
    let parkmode = SettingCellModel(
      title: L.record_park_mode.localized,
      key: "parkmode",
      rowIntValue: getconfiguration.parkmode,
      value: getParkmode(),
      selectionValues: ConfigurationModel.parkmodes,
      viewType: .onOffSwitch
    )

    //방전차단전압
    let voltage = SettingCellModel(
      title: L.record_voltage_limit.localized,
      key: "voltagelimit",
      rowIntValue: getconfiguration.voltagelimit,
      value: getVoltagelimit(),
      selectionValues: ConfigurationModel.voltagelimits,
      viewType: .sheetSelect
    )

    //방전차단시간
    let parkrectime = SettingCellModel(
      title: L.record_park_rec_time.localized,
      key: "parkrectime",
      rowIntValue: ConfigurationModel.ParkRECTimeType(rawValue: getconfiguration.parkrectime)?
        .viewIndex ?? 0,
      value: getParkRecTime(),
      selectionValues: ConfigurationModel.ParkRECTimeType.all,
      viewType: .sheetSelect
    )

    //겨울철 모드
    let winterMode = SettingCellModel(
      title: L.s1_setting_wintermode.localized,
      key: "wintermode",
      rowIntValue: getconfiguration.wintermode,
      value: getWintermode(),
      selectionValues: ConfigurationModel.winterMode,
      viewType: .onOffSwitch
    )

    //주차 모드 지연
    let park_delay = SettingCellModel(
      title: L.s1_setting_parking_mode_delay.localized,
      key: "park_delay",
      rowIntValue: getconfiguration.parkDelay,
      value: getParkDelay(),
      selectionValues: ConfigurationModel.parkdelaymodes,
      viewType: .sheetSelect
    )

    //주차 영상 화질 모드
    let parkispmode = SettingCellModel(
      title: L.s1_setting_park_isp_mode.localized,
      key: "park-isp-mode",
      rowIntValue: getconfiguration.parkispmode,
      value: getParkIspMode(),
      selectionValues: ConfigurationModel.parkispmodes,
      viewType: .sheetSelect
    )

    //녹화 방식
    let rectimelapse = SettingRectypeCellModel(
      title: L.s1_setting_rec_type.localized,
      hasSubKeys: true,
      key: "sleepmode",
      subKey: "timelapse",
      rowIntValue: getconfiguration.sleepmode,
      subRowIntValue: getconfiguration.timelapse,
      value: getRecTimelapseMode(),
      selectionValues: ConfigurationModel.sleepmodes,
      timelapseSelectionValues: ConfigurationModel.recType,
      viewType: .sheetSelectNoValue
    )

    // 실내카메라
    let innerCamera = SettingCellModel(
      title: L.s1_setting_inner_camera.localized,
      subTitle: L.msg_setting_alert2.localized,
      key: "ParkRearCam3chOnOff",
      rowIntValue: getconfiguration.parkRearCam3chOnOff,
      value: getinnerCam(),
      selectionValues: ConfigurationModel.innerCamera,
      viewType: .onOffSwitch,
      resetType: .Format
    )

    let items02: [SettingCellPresentable] = [
      parkmode, voltage, parkrectime, winterMode, park_delay, parkispmode, rectimelapse,
      innerCamera,
    ]
    let parkingRecording = SettingCellSectionModel(sectionType: .parkingRecording, items: items02)

    //MARK: - section3

    //스피커 볼륨
    let speakervolume = SettingCellModel(
      title: L.sound_speaker_volume.localized,
      key: "speakervolume",
      rowIntValue: getconfiguration.speakervolume,
      value: getSpeakervolume(),
      selectionValues: ConfigurationModel.speakervolumes,
      viewType: .sheetSelect

    )

    //음성안내
    let audioguide = SettingCellModel(
      title: L.voice_alert.localized,
      key: "audioguide",
      rowIntValue: getconfiguration.audioguide,
      value: getAudiogGuide(),
      selectionValues: ConfigurationModel.audioGuide,
      viewType: .onOffSwitch
    )
    sLogger.info(":: selectionValues ::: \(audioguide.selectionValues)")

    //이벤트 녹화 Beep
    let alertevent = SettingCellModel(
      title: L.sound_event_alert.localized,
      key: "alertevent",
      rowIntValue: getconfiguration.alertevent,
      value: getAlertevent(),
      selectionValues: ConfigurationModel.alertevents,
      viewType: .onOffSwitch
    )

    //시큐리티 LED
    let secureled = SettingCellModel(
      title: L.system_secure_led.localized,
      key: "secureled",
      rowIntValue: getconfiguration.secureled,
      value: getSecureled(),
      selectionValues: ConfigurationModel.secureleds,
      viewType: .onOffSwitch
    )

    //상태LED
    let statusled = SettingCellModel(
      title: L.setting_status_led.localized,
      key: "statusled",
      rowIntValue: getconfiguration.statusled,
      value: getStatusled(),
      selectionValues: ConfigurationModel.statusleds,
      viewType: .onOffSwitch
    )

    //주차모드 LED
    let pakrmodeled = SettingCellModel(
      title: L.setting_parking_led.localized,
      key: "parkled",
      rowIntValue: getconfiguration.parkled,
      value: getParkled(),
      selectionValues: ConfigurationModel.parkleds,
      viewType: .onOffSwitch
    )

    // 날짜 설정
    let timeset = SettingCellModel(
      title: L.time_set_time.localized,
      key: "datetype",
      rowIntValue: getconfiguration.datetype,
      value: getDateType(),
      selectionValues: ConfigurationModel.timeDateType,
      //      type: .date,
      viewType: .sheetSelect
    )

    // 시간 설정
    let timeregion = SettingCellModel(
      title: L.time_region.localized,
      key: "timeregion",
      rowIntValue: getconfiguration.timeregion,
      value: getTimeregion(),
      selectionValues: ConfigurationModel.timeregions,
      viewType: .sheetSelect
    )
    // 시간/서머타임
    let summertime = SettingCellModel(
      title: L.daylight_saving_time.localized,
      key: "summertime",
      rowIntValue: getconfiguration.summertime,
      value: getSummertime(),
      selectionValues: ConfigurationModel.summertimes,
      viewType: .onOffSwitch
    )

    // 메모리
    let memoryassign = SettingCellModel(
      title: L.memory_assign.localized,
      subTitle: L.msg_setting_alert2.localized,
      key: "memoryassign",
      rowIntValue: getconfiguration.memoryassign,
      value: getMemoryassign(),
      selectionValues: ConfigurationModel.memoryassigns,
      viewType: .sheetSelect,
      resetType: .Format
    )

    //속도 단위
    let speedunit = SettingCellModel(
      title: L.system_speed_unit.localized,
      key: "speedunit",
      rowIntValue: getconfiguration.speedunit,
      value: getSpeedunit(),
      selectionValues: ConfigurationModel.speedunits,
      viewType: .sheetSelect
    )
    //속도 표시
    let speedmode = SettingCellModel(
      title: L.system_speed_mode.localized,
      key: "speedmode",
      rowIntValue: getconfiguration.speedmode,
      value: getSpeedmode(),
      selectionValues: ConfigurationModel.speedmodes,
      viewType: .onOffSwitch
    )
    //WIFI
    let wifiNetwork = SettingCellModel(
      title: L.system_wifi_on_auto.localized,
      key: "wifibandwidth",
      rowIntValue: getconfiguration.wifibandwidth,
      value: getWifibandwidth(),
      selectionValues: ConfigurationModel.wifibandwidths,
      //      type: .wifi,
      viewType: .sheetSelect
    )

    //LCD Off
    let lcdOntime = SettingCellModel(
      title: L.lcd_alwaysOn.localized,
      key: "lcd-ontime",
      rowIntValue: getconfiguration.lcdOntime,
      value: getLcdOntime(),
      selectionValues: ConfigurationModel.lcdOntimes,
      viewType: .sheetSelect
    )

    //시스템 정보
    let info = SettingCellModel(
      title: L.system_info.localized,
      key: "systemInfo",
      value: "",
      selectionValues: [],
      type: .noti,
      viewType: .sheetInfo
    )

    //SD카드 포맷
    let format = SettingCellModel(
      title: L.memory_sdcard_format.localized,
      subTitle: L.msg_setting_alert2.localized,
      key: "sdcard",
      value: "",
      selectionValues: [],
      type: .sdcardformat,
      viewType: .sheetSelect
    )

    //초기화
    let initialize = SettingCellModel(
      title: L.system_reset.localized,
      key: "init",
      value: "",
      selectionValues: [],
      type: .initialize,
      viewType: .sheetSelect
    )

    //공장초기화
    let factoryreset = SettingCellModel(
      title: L.s1_setting_factoryreset.localized,
      key: "factoryreset",
      value: "",
      selectionValues: [],
      type: .factoryreset,
      viewType: .sheetSelect
    )

    //영상 로고
    let watermark = SettingCellModel(
      title: L.setting_watermark.localized,
      key: "watermark",
      rowIntValue: getconfiguration.watermark,
      value: getWatermark(),
      selectionValues: ConfigurationModel.watermark,
      viewType: .onOffSwitch
    )

    //비밀번호 잠금
    let setPassword = SettingCellModel(
      title: L.setting_setpassword.localized,
      key: "secretmode",
      rowIntValue: getconfiguration.secretmode,
      value: getpasswordMode(),
      selectionValues: ConfigurationModel.passwordLock,
      viewType: .onOffSwitch
    )

    //비밀번호 변경
    let changePassword = SettingCellModel(
      title: L.setting_changepassword.localized,
      key: "secretpass",
      rowIntValue: getconfiguration.secretpass,
      value: String(format: "%04d", getconfiguration.secretpass),       // "0000" 형태로 
      selectionValues: ConfigurationModel.passwordChange,
      viewType: .textField
    )

    //언어 설정
    let changeLanguage = SettingCellModel(
      title: L.setting_language.localized,
      key: "language",
      rowIntValue: getconfiguration.language,
      value: getLanguage(),
      selectionValues: ConfigurationModel.languageSheet,
      viewType: .sheetSelect
    )

    let fw = SettingCellModel(
      title: L.system_upgrade.localized,
      key: "",
      value: "",
      selectionValues: [],
      type: .fwUpdate,
      viewType: .sheetSelect
    )

    let items03_1 = [speakervolume, audioguide, alertevent, secureled, statusled, pakrmodeled]
    let items03_2 = [timeset, timeregion, summertime]
    let items03 = [
      memoryassign, lcdOntime, speedunit, speedmode,
      wifiNetwork, info, format, initialize, factoryreset, watermark, setPassword, changePassword,
      changeLanguage, fw,
    ]

    let alarmSection = SettingCellSectionModel(sectionType: .system, items: items03_1)
    let timeSection = SettingCellSectionModel(sectionType: .system, items: items03_2)
    let system = SettingCellSectionModel(sectionType: .system, items: items03)
    return [camera, parkingRecording, alarmSection, timeSection, system]
  }

  /// 국가 설정 Japan이면 getconfiguration.secureled 값이 -1
  /// 전,후방 security LED 항목 보이지 않아야 한다.
  func addSecurityLed(systemItems: inout [SettingCellModel]) {
    if getconfiguration.secureled != -1 {
      let secureled = SettingCellModel(
        title: L.system_front_secure_led.localized,
        key: "secureled",
        rowIntValue: getconfiguration.secureled,
        value: getSecureled(),
        selectionValues: ConfigurationModel.secureleds,
        viewType: .onOffSwitch
      )

      let securityLedsItems = [secureled]
      systemItems.insert(contentsOf: securityLedsItems, at: 0)
    }
  }

  /// 국가 설정 Japan이면 getconfiguration.wifibandwidth 값이 -1
  func addWifiBandwidth(systemItems: inout [SettingCellModel]) {
    if getconfiguration.wifibandwidth != -1 {
      let wifibandwidth = SettingCellModel(
        title: L.wifi_bandwidth.localized,
        key: "wifibandwidth",
        rowIntValue: getconfiguration.wifibandwidth,
        value: getWifibandwidth(),
        selectionValues: ConfigurationModel.wifibandwidths,
        viewType: .sheetSelect,
        resetType: .ResetNetwork
      )
      systemItems.insert(wifibandwidth, at: 3)
    }
  }
}

extension GetConfiguration.Response {
  func toDomain() -> [SettingCellSectionModel] {
    let dashcam = SettingCellSectionModel(sectionType: .dashcam, items: [])

    let cloud = SettingCellSectionModel(sectionType: .cloud, items: [])
    return [dashcam, cloud]
  }
}
