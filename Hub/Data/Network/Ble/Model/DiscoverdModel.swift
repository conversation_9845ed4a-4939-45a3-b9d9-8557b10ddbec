//
//  DiscoverdModel.swift
//  Hub
//
//  Created by ncn on 2022/11/30.
//

import CoreBluetooth
import Foundation

class DiscoverdModel: Hashable {
  let uuid: String
  let peripheral: CBPeripheral
  let advertisementData: [String: Any]
  let rssi: NSNumber /* Received Signal Strength Indicator */
  let locaName: String
  let name: String
  let mac: String
  let bandWidth: String
  let password: String

  static func == (lhs: DiscoverdModel, rhs: DiscoverdModel) -> Bool {
    return lhs.uuid == rhs.uuid
  }

  func hash(into hasher: inout Hasher) {
    hasher.combine(uuid)
    hasher.combine(peripheral)
    hasher.combine(rssi)
  }

  init(
    uuid: String, peripheral: CBPeripheral, advertisementData: [String: Any],
    rssi: NSNumber, localName: String
  ) {
    self.uuid = uuid
    self.peripheral = peripheral
    self.advertisementData = advertisementData
    self.rssi = rssi
    self.locaName = localName
    // S1-4K & 5G & 3BF834 & jreg7890
    hLogger.info("@@ localName-> \(localName)")
    let array = localName.components(separatedBy: "&")
    let name = array.first ?? "S1-4K"
    let macSuffix = array[2]

    self.name = name
    self.mac = macSuffix
    self.bandWidth = array[1]
    hLogger.info("@@ name: \(self.name), bandWidth-> \(self.bandWidth)")
    let before = array.last ?? "jreg7890"
    self.password = rot13and5(before)
  }

  func toDomain() -> BleItemModel {
    // mac: @@ ble mac-> jreg7890
    hLogger.info("@@ ble mac-> \(self.mac)")
    let macParts = mac.split(separator: ":")
#if USE_UPPERCASE_SSID
    let macSuffix = macParts.suffix(3).joined().uppercased()
#else
    let macSuffix = macParts.suffix(3).joined().lowercased()
#endif
    hLogger.log("@@ bandWidth-> \(self.bandWidth)")
    Current.modelName = name
    let bandWidth = bandWidth.contains("2G") ? "2.4G" : bandWidth
    let ssid = "VUEROID_\(Current.modelName)_\(macSuffix)_\(bandWidth)"
    
    
    hLogger.log("@@ ssid-> \(ssid)")
    hLogger.log("@@ mac-> \(self.formatMacSuffix(macSuffix: macSuffix))")
    hLogger.log("@@ password-> \(self.password)")
    
    return .init(
      uuid: uuid,
      mac: formatMacSuffix(macSuffix: mac),
      name: name,
      discoverServices: nil,
      date: nil,
      isDiscovered: true,
      wifiSSID: ssid,
      wifiPW: password
    )
  }
  
  func formatMacSuffix(macSuffix: String) -> String {
    var formattedSuffix = ""
    var count = 0
    for char in macSuffix {
      formattedSuffix.append(char)
      count += 1
      if count % 2 == 0 && count < macSuffix.count {
        formattedSuffix.append(":")
      }
    }
    return "CC:64:1A:\(formattedSuffix)"
  }
}
