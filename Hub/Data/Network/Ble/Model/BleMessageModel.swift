//
//  BleModel.swift
//  Hub
//
//  Created by ncn on 2022/12/01.
//

import Foundation

enum BLE {
  enum Command {
    case uuid /* uuid send */
    case check /* uuid check */
    case button /* button click */
    case ap /* ap mode ssid, pw */
    case mode /* ap, sta mode */
    case sta /* sta mode ssid, pw */
    case unkonwn
  }

  struct Send {
    let command: Command
    let value: String?
  }

  struct Response {
    let command: Command
    let value: Data?
    let error: Error?

    func toCheckDamain() -> BleCheckModel {
      var isVaule = false
      var message = ""
      if let d = value {
        var ret: UInt8 = 0
        let intValue: UInt8 = d.int8()
        ret = intValue.bigEndian
        isVaule = (ret == 1)
        message =
          isVaule
          ? ""
          //                : "블랙박스를 인식 할 수 있도록 \n 블랙박스의 Set Button을 눌러주세요. \n 최초 등록 시 15초 이상 소요될 수 있습니다."
          : "To recognize the black box, press \n Set Button on the black box. \n Initial registration may take more than 15 seconds."
      } else {
        message = error?.localizedDescription ?? "수신된 데이터가 없습니다."
      }
      return .init(command: command, message: message, isRegist: isVaule)
    }

    func toButtonDamain() -> BleCheckModel {
      var isVaule = false
      var message = ""
      if let d = value {
        var ret: UInt8 = 0
        let intValue: UInt8 = d.int8()
        ret = intValue.bigEndian
        isVaule = (ret == 1)
        message =
          isVaule
          ? "등록 되었습니다."
          //                : "블랙박스를 인식 할 수 있도록 \n 블랙박스의 Set Button을 눌러주세요. \n 최초 등록 시 15초 이상 소요될 수 있습니다."
          : "To recognize the black box, press \n Set Button on the black box. \n Initial registration may take more than 15 seconds."
      } else {
        message = error?.localizedDescription ?? "수신된 데이터가 없습니다."
      }
      return .init(command: command, message: message, isRegist: isVaule)
    }

    func toApDomain() -> BleApModel {
      var message = ""
      var ssid: String?
      var pw: String?
      if let d = value {
        if let aString = String(data: d, encoding: .utf8) {
          let array = aString.components(separatedBy: ",")
          if let s = array.first, let p = array.last {
            ssid = s
            pw = p
          } else {
            message = "유효하지 않는 데이터가 수신되었습니다. - \(aString)"
          }
        } else {
          message = "유효하지 않는 데이터가 수신되었습니다."
        }
      } else {
        message = error?.localizedDescription ?? "수신된 데이터가 없습니다."
      }
      return .init(command: command, message: message, ssid: ssid, passowrd: pw)
    }

    func toModeDomain() -> BleModeModel {
      var message = ""
      var code: Int = -1
      if let d = value {
        var ret: UInt8 = 0
        let intValue: UInt8 = d.int8()
        ret = intValue.bigEndian
        code = Int(ret)
        switch ret {
        case 0:
          message = "Wi-Fi off"
          break
        case 1:
          message = "Ap Mode"
          break
        case 2:
          message = "STA Mode"
          break
        default:
          message = "Unkonw mode type value(\(ret))"
          break
        }
      } else {
        message = error?.localizedDescription ?? "수신된 데이터가 없습니다."
      }
      return .init(command: command, message: message, value: code)
    }
  }
}
