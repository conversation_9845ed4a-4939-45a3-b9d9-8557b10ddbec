//
//  HISTORY.swift
//  momento
//
//  Created by cheche on 2018. 9. 9..
//  Copyright © 2018년 momento. All rights reserved.
//

import Foundation
import GRDB

class RESULT: Record {
  var T0TIME: Double
  var T1TIME: Double
  var SUMDISTANCE: Int
  var T0EVENT: Int
  var T1EVENT: Int
  var T1MOTNUM: Int
  var MAXSPEED: Int
  var AVGSPEED: Int
  var STARTMIN: Double

  init(
    t0time: Double,
    t1time: Double,
    sumdistance: Int,
    t0event: Int,
    t1event: Int, t1motnum: Int,
    maxspeed: Int, avgspeed: Int,
    startmin: Double
  ) {
    self.T0TIME = t0time
    self.T1TIME = t1time
    self.SUMDISTANCE = sumdistance
    self.T0EVENT = t0event
    self.T1EVENT = t1event
    self.T1MOTNUM = t1motnum
    self.MAXSPEED = maxspeed
    self.AVGSPEED = avgspeed
    self.STARTMIN = startmin
    super.init()
  }

  /// The table name
  override class var databaseTableName: String {
    return "DRIVE"
  }

  /// The table columns
  enum Columns: String, ColumnExpression {
    case t0time, t1time, sumdistance, t0event, t1event, t1motnum, maxspeed, avgspeed, startmin
  }

  /// Creates a record from a database row
  required init(row: Row) throws {

    T0TIME = row[Columns.t0time]
    T1TIME = row[Columns.t1time]
    SUMDISTANCE = row[Columns.sumdistance]
    T0EVENT = row[Columns.t0event]
    T1EVENT = row[Columns.t1event]
    T1MOTNUM = row[Columns.t1motnum]
    MAXSPEED = row[Columns.maxspeed]
    AVGSPEED = row[Columns.avgspeed]
    STARTMIN = row[Columns.startmin]

    try super.init(row: row)
  }

  /// The values persisted in the database
  override func encode(to container: inout PersistenceContainer) {
    container[Columns.t0time] = T0TIME
    container[Columns.t1time] = T1TIME
    container[Columns.sumdistance] = SUMDISTANCE
    container[Columns.t0event] = T0EVENT
    container[Columns.t1event] = T1EVENT
    container[Columns.t1motnum] = T1MOTNUM
    container[Columns.maxspeed] = MAXSPEED
    container[Columns.avgspeed] = AVGSPEED
    container[Columns.startmin] = STARTMIN
  }
}

extension RESULT {
  // day
  func toDomainKilometerForDay() -> DriveModel {
    return .init(
      t0Time: T0TIME / 60.0,
      t1Time: T1TIME / 60.0,
      sumDistance: Int(Double(SUMDISTANCE) / 0.62137),
      t0Event: T0EVENT,
      t1Event: T1EVENT,
      t1motNum: T1MOTNUM,
      maxSpeed: Int(Double(MAXSPEED) / 0.62137),
      avgSpeed: Int(Double(AVGSPEED) / 0.62137),
      startMin: STARTMIN)
  }

  // weak, month, year
  func toDomainKilometerForOthers() -> DriveModel {
    return .init(
      t0Time: Double(round(T0TIME / 3600)),
      t1Time: Double(round(T1TIME / 3600)),
      sumDistance: Int(Double(SUMDISTANCE) / 0.62137),
      t0Event: T0EVENT,
      t1Event: T1EVENT,
      t1motNum: T1MOTNUM,
      maxSpeed: Int(Double(MAXSPEED) / 0.62137),
      avgSpeed: Int(Double(AVGSPEED) / 0.62137),
      startMin: STARTMIN)
  }

  // day
  func toDomainMileForDay() -> DriveModel {
    return .init(
      t0Time: Double(T0TIME / 60),  // 주행정보
      t1Time: Double(T1TIME / 60),
      sumDistance: SUMDISTANCE,
      t0Event: T0EVENT,
      t1Event: T1EVENT,
      t1motNum: T1MOTNUM,
      maxSpeed: MAXSPEED,
      avgSpeed: AVGSPEED,
      startMin: STARTMIN)
  }

  // weak, month, year
  func toDomainMileForOthers() -> DriveModel {
    return .init(
      t0Time: Double(T0TIME / 3600),  // 주행정보
      t1Time: Double(T1TIME / 3600),
      sumDistance: SUMDISTANCE,
      t0Event: T0EVENT,
      t1Event: T1EVENT,
      t1motNum: T1MOTNUM,
      maxSpeed: MAXSPEED,
      avgSpeed: AVGSPEED,
      startMin: STARTMIN)
  }

}

/*
 #if false

 private static final String CP_DRIVERECORD = "CREATE TABLE IF NOT EXISTS " + DRIVERECORD_TABLE + "(" +
             "id INTEGER PRIMARY KEY AUTOINCREMENT, " + // index
             "AP_NAME VARCHAR, " + // wifi name
             "COMPARE_DATE DATE, " + // date
             "COMPARE_START_TIME TIME, " + // time
             "COMPARE_END_TIME TIME, " + // time
             "DRIVE_DB_END INTEGER, " + // 마지막 END 시간
             "TYPE INTEGER, " + // 주행타입 => 1:상시 / 2:주차
             "START INTEGER, " + // 시작시간
             "END INTEGER, " + // 종료시간
             "DISTANCE INTEGER, " + // 이동거리
             "SPEEDMAX INTEGER, " + // 최고속도
             "SPEEDAVG INTEGER, " + //평균속도
             "EVTNUM INTEGER, " + // 이벤트 횟수 => 상시, 주차 모두 가능
             "MOTNUM INTEGER, " + // 모션 횟수 => 주차만 가능
             "OFFTYPE INTEGER, " + // 종료원인 => 0:전원OFF / 1:시동OFF(상시운행 종료) / 2:시동ON(주차모드 종료) / 3:방전차단 / 4:고온차단 / 5:시스템 리셋 / 6:기타
             "RESERVED INTEGER, " + // 미정
             "REG_TIME INTEGER);"; // 마지막 업데이트 시간

     private static final String CP_GPSRECORD = "CREATE TABLE IF NOT EXISTS " + GPS_TABLE + "(" +
             "id INTEGER PRIMARY KEY AUTOINCREMENT, " + // index
             "AP_NAME VARCHAR, " + // wifi name
             "COMPARE_DATE DATE, " + // date
             "COMPARE_TIME TIME, " + // time
             "GPS_DB_TIME INTEGER, " + // 마지막 TIME 시간
             "TIME INTEGER, " + // 시간
             "LATITUDE INTEGER, " + // 위도
             "LONGITUDE INTEGER);"; // 경고

     private static final String CP_BOOKMARKRECORD = "CREATE TABLE " + BOOKMARK_TABLE + "(" +
             "NAME text, " + // name
             "PATH text, " + // PATH
             "STATUS text);"; // BOOKMARK 상태

 #endif
 */
