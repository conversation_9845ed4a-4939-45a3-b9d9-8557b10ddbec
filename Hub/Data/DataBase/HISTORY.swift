//
//  HISTORY.swift
//  momento
//
//  Created by cheche on 2018. 9. 9..
//  Copyright © 2018년 momento. All rights reserved.
//

import Foundation
import GRDB

class HISTORY: Record {

  var TYPE: Int
  var START: Int
  var END: Int
  var DISTANCE: Int
  var SPEEDMAX: Int
  var SPEEDAVG: Int
  var EVTNUM: Int
  var MOTNUM: Int
  var OFFTYPE: Int
  var RESERVED: Int

  init(
    type: Int, start: Int, end: Int, distance: Int, speedmax: Int, speedavg: Int, evtnum: Int,
    motnum: Int, offtype: Int, reserved: Int
  ) {
    self.TYPE = type
    self.START = start
    self.END = end
    self.DISTANCE = distance
    self.SPEEDMAX = speedmax
    self.SPEEDAVG = speedavg
    self.EVTNUM = evtnum
    self.MOTNUM = motnum
    self.OFFTYPE = offtype
    self.RESERVED = reserved
    super.init()
  }

  /// The table name
  override class var databaseTableName: String {
    return "DRIVE"
  }

  /// The table columns
  enum Columns: String, ColumnExpression {
    case type, start, end, distance, speedmax, speedavg, evtnum, motnum, offtype, reserved
  }

  /// Creates a record from a database row
  required init(row: Row) throws {

    TYPE = row[Columns.type]
    START = row[Columns.start]
    END = row[Columns.end]
    DISTANCE = row[Columns.distance]
    SPEEDMAX = row[Columns.speedmax]
    SPEEDAVG = row[Columns.speedavg]
    EVTNUM = row[Columns.evtnum]
    MOTNUM = row[Columns.motnum]
    OFFTYPE = row[Columns.offtype]
    RESERVED = row[Columns.reserved]

    try super.init(row: row)
  }

  /// The values persisted in the database
  override func encode(to container: inout PersistenceContainer) {
    container[Columns.type] = TYPE
    container[Columns.start] = START
    container[Columns.end] = END
    container[Columns.distance] = DISTANCE
    container[Columns.speedmax] = SPEEDMAX
    container[Columns.speedavg] = SPEEDAVG
    container[Columns.evtnum] = EVTNUM
    container[Columns.motnum] = MOTNUM
    container[Columns.offtype] = OFFTYPE
    container[Columns.reserved] = RESERVED
    //        container[Columns.longitude] = coordinate.longitude
  }
}

extension HISTORY {
  func toDomain(day: Date, type: PeriodType, speedType: SpeedType, previousRecord: HISTORY? = nil) -> EventChartModel {
    let string: String
    switch type {
    case .day:
      string = day.toString(format: "yyyy/MM/dd")
    case .week:
      string = day.toString(format: "E")
    case .month:
      string = day.toString(format: "dd")
    case .year:
      string = day.toString(format: "MMM")
    }

    let distance: Int
    let maxspeed: Int
    let avgspeed: Int
    switch speedType {
    case .mph, .kph:
      distance = self.DISTANCE
      maxspeed = self.SPEEDMAX
      avgspeed = self.SPEEDAVG
    case .off:
      distance = 0
      maxspeed = 0
      avgspeed = 0
    }

    // 이전 레코드가 TYPE=2, OFFTYPE=7이고 현재 레코드가 TYPE=2, OFFTYPE=2일 경우 START를 00:00:00으로 조정
    let adjustedStart: Int
    if let prev = previousRecord,
       prev.TYPE == 2 && prev.OFFTYPE == 7 &&
       self.TYPE == 2 && self.OFFTYPE == 2 {
      // 현재 날짜의 00:00:00으로 START 조정
      adjustedStart = self.START.toStartOfSameDay()
      iLogger.info("🔗 연속 주차 세션 감지: START를 00:00:00으로 조정")
      iLogger.info("📅 이전 레코드: TYPE=\(prev.TYPE), OFFTYPE=\(prev.OFFTYPE)")
      iLogger.info("📅 현재 레코드: TYPE=\(self.TYPE), OFFTYPE=\(self.OFFTYPE)")
      iLogger.info("📅 원본 START: \(self.START.toDateString())")
      iLogger.info("🌅 조정된 START (00:00:00): \(adjustedStart.toDateString())")
    } else {
      adjustedStart = self.START
    }

    // TYPE이 2이고 OFFTYPE이 7일 경우 END를 START와 같은 날짜의 24:00:00으로 조정
    let adjustedEnd: Int
    if TYPE == 2 && OFFTYPE == 7 {
      // START와 같은 날짜의 24:00:00 (자정)으로 END 조정
      adjustedEnd = adjustedStart.toEndOfSameDay()
      iLogger.info("🕛 OFFTYPE 7 감지: END를 24:00:00으로 조정")
      iLogger.info("📅 조정된 START: \(adjustedStart.toDateString())")
      iLogger.info("📅 원본 END: \(self.END.toDateString())")
      iLogger.info("🌙 조정된 END (24:00:00): \(adjustedEnd.toDateString())")
    } else {
      adjustedEnd = self.END
    }

    let sumTime1 = TYPE == 1 ? (adjustedEnd - adjustedStart) : 0
    let sumTime2 = TYPE == 2 ? (adjustedEnd - adjustedStart) : 0

    iLogger.info("Day: \(day.toString(format: "yyyy/MM/dd"))")
    iLogger.info("sumTime1: \(sumTime1.secondToTime(seperate: ":")), sumTime2: \(sumTime2.secondToTime(seperate: ":"))")


    let cnt1 = TYPE == 1 ? EVTNUM : 0
    let cnt2 = TYPE == 2 ? MOTNUM : 0
    let cnt3 = TYPE == 2 ? EVTNUM : 0

    return .init(
      date: string,
      cnt1: cnt1,
      cnt2: cnt2,
      cnt3: cnt3,
      sumtime1: sumTime1.secondToTime(seperate: ":"),
      sumtime2: sumTime2.secondToTime(seperate: ":"),
      maxspeed: maxspeed,
      avgspeed: avgspeed,
      distance: distance,
      periodType: type,
      originDate: day.toString(format: "yyyy/MM/dd")
    )
  }
}
/*
 1746085544 - 1746048940
 */
