{"openapi": "3.0.1", "info": {"title": "API Documentation", "description": "NCN AI API", "version": "1.0.0"}, "servers": [{"url": "https://ai.vueroid-cloud.com/api/"}], "security": [{"Authorization": []}], "tags": [{"name": "접근 제어용 액세스 키 API", "description": "액세스 키 발급, 검증, 무효화 API"}, {"name": "AI Inference API", "description": "AI 활용 이미지/비디오 처리 API"}, {"name": "Push API", "description": "App Push API"}], "paths": {"/v2/recvResult": {"put": {"tags": ["AI Inference API"], "summary": "테스크 결과 수신방법 설정", "description": "type(WAIT/PUSH/EMAIL/CANCEL", "operationId": "recvResult", "parameters": [{"name": "recvResultRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/RecvResultRequest"}}], "responses": {"200": {"description": "OK"}}}}, "/v3/restore": {"post": {"tags": ["AI Inference API"], "summary": "번호판 이미지 복원", "description": "복원할 번호판 이미지와, 액세스 키를 업로드하고 처리된 이미지를 다운로드", "operationId": "restoreImageSave", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["accessKey", "image"], "type": "object", "properties": {"image": {"type": "string", "format": "binary"}, "accessKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/v2/statusVideo": {"post": {"tags": ["AI Inference API"], "summary": "비디오 처리 단계 확인", "description": "작업 ID를 업로드하고 처리 단계를 확인 (REGISTERED / IN_PROGRESS / COMPLETED / FAILED)", "operationId": "statusVideoAndDownUrl", "parameters": [{"name": "taskId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StatusLink"}}}}}}}, "/v2/restore": {"post": {"tags": ["AI Inference API"], "summary": "번호판 이미지 복원", "description": "복원할 번호판 이미지와, 액세스 키를 업로드하고 처리된 이미지를 다운로드", "operationId": "restoreImageV2", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["accessKey", "image"], "type": "object", "properties": {"image": {"type": "string", "format": "binary"}, "accessKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/v2/deIdentifyVideo": {"post": {"tags": ["AI Inference API"], "summary": "비디오 비식별화 요청, ATOM 검증헤더 포함", "description": "비식별화할 비디오와, 액세스 키를 업로드하고 작업 ID를 반환, NCN 형식에 맞는 영상파일만 작업 가능", "operationId": "deIdentifyVideo", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["accessKey", "video"], "type": "object", "properties": {"video": {"type": "string", "format": "binary"}, "accessKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/v2/access-key/new": {"post": {"tags": ["접근 제어용 액세스 키 API"], "summary": "새 액세스 키 생성", "description": "새로운 액세스 키를 생성합니다.", "operationId": "createNewAccessKeyV2", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/v1/statusVideo": {"post": {"tags": ["AI Inference API"], "summary": "비디오 처리 단계 확인", "description": "작업 ID를 업로드하고 처리 단계를 확인 (REGISTERED / IN_PROGRESS / COMPLETED / FAILED)", "operationId": "statusVideo", "parameters": [{"name": "taskId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "enum": ["REGISTERED", "IN_PROGRESS", "COMPLETED", "FAILED"]}}}}}}}, "/v1/restore": {"post": {"tags": ["AI Inference API"], "summary": "번호판 이미지 복원", "description": "복원할 번호판 이미지와, 액세스 키를 업로드하고 처리된 이미지를 다운로드", "operationId": "restoreImage", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["accessKey", "image"], "type": "object", "properties": {"image": {"type": "string", "format": "binary"}, "accessKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/v1/push": {"post": {"tags": ["Push API"], "summary": "AppPush를 발송한다.", "description": "OS, pushToken 필요", "operationId": "condition", "parameters": [{"name": "pushRequest", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/PushRequest"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/v1/downloadVideo": {"post": {"tags": ["AI Inference API"], "summary": "비디오 다운로드", "description": "작업 ID와, 액세스 키를 업로드하고 처리가 다 되었다면(COMPLETED) 다운로드", "operationId": "downloadVideo", "parameters": [{"name": "taskId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "accessKey", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/v1/deIdentifyVideo": {"post": {"tags": ["AI Inference API"], "summary": "비디오 비식별화 요청", "description": "비식별화할 비디오와, 액세스 키를 업로드하고 작업 ID를 반환", "operationId": "deIdentifyVideo_1", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["accessKey", "video"], "type": "object", "properties": {"video": {"type": "string", "format": "binary"}, "accessKey": {"type": "string"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}}}}, "/v1/access-key/verify": {"post": {"tags": ["접근 제어용 액세스 키 API"], "summary": "액세스 키 검증", "description": "액세스 키의 유효성을 검증합니다. true / false", "operationId": "verifyAccessKey", "parameters": [{"name": "accessKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/v1/access-key/invalidate": {"post": {"tags": ["접근 제어용 액세스 키 API"], "summary": "액세스 키 무효화", "description": "액세스 키를 무효화합니다.", "operationId": "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "accessKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/test/validate/video": {"post": {"tags": ["test-controller"], "operationId": "validateVideo", "requestBody": {"content": {"application/json": {"schema": {"required": ["video"], "type": "object", "properties": {"video": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/test/upload/video": {"post": {"tags": ["test-controller"], "operationId": "uploadVideo", "requestBody": {"content": {"application/json": {"schema": {"required": ["video"], "type": "object", "properties": {"video": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object"}}}}}}}, "/v2/status": {"get": {"tags": ["AI Inference API"], "summary": "요청건 진행사항 확인", "description": "작업 ID를 업로드하고 처리 단계를 확인 (REGISTERED / IN_PROGRESS / COMPLETED / FAILED)", "operationId": "statusDownUrl", "parameters": [{"name": "taskId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StatusLink"}}}}}}}, "/v2/condition": {"get": {"tags": ["AI Inference API"], "summary": "비식별화 서버 상태 체크", "description": "비식별화 서비스 실행상태 체크", "operationId": "condition_1", "parameters": [{"name": "task", "in": "query", "required": true, "schema": {"title": "테스크 유형 : DE_IDENTIFIED 비식별화 / RESTORED 번호판복원 ", "type": "string", "example": "DE_IDENTIFIED / RESTORED"}, "example": "DE_IDENTIFIED / RESTORED"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Condition"}}}}}}}, "/v1/condition": {"get": {"tags": ["AI Inference API"], "summary": "비식별화 서버 상태 체크", "description": "비식별화 서비스 실행상태 체크", "operationId": "condition_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Condition"}}}}}}}, "/v1/access-key/new": {"get": {"tags": ["접근 제어용 액세스 키 API"], "summary": "새 액세스 키 생성", "description": "새로운 액세스 키를 생성합니다.", "operationId": "createNewAccessKey", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/test/hello": {"get": {"tags": ["test-controller"], "operationId": "hello", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/": {"get": {"tags": ["health-check-controller"], "summary": "헬스헬스", "description": "헬스헬스헬스", "operationId": "healthCheck", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}}, "components": {"schemas": {"RecvResultRequest": {"type": "object", "properties": {"taskId": {"title": "테스크 ID", "type": "integer", "format": "int64", "example": 1}, "resultRecv": {"title": "처리방법 (WAIT/PUSH/EMAIL/CANCEL) ", "type": "string", "example": "WAIT", "enum": ["WAIT", "PUSH", "EMAIL", "CANCEL"]}, "email": {"title": "이메일 주소(EMAIL로 했을때)", "type": "string", "example": "<EMAIL>"}, "os": {"title": "OS (AOS/IOS)", "type": "string", "example": "AOS"}, "pushToken": {"title": "App Push토큰", "type": "string", "example": "abcd-123njkfdsa-fdnjhakfsd"}}}, "StatusLink": {"type": "object", "properties": {"status": {"title": "상태값 (REGISTERED, IN_PROGRESS, COMPLETED, FAILED)", "type": "string", "example": "IN_PROGRESS", "enum": ["REGISTERED", "IN_PROGRESS", "COMPLETED", "FAILED"]}, "url": {"title": "상태값이 COMPLETED 일경우 다운로드 받을수 있는 AWS 링크 (10분간 유효)", "type": "string", "example": "https://ncn-super-resolution-file.s3.ap-northeast-2.amazonaws.com/file/77546393-a5dd-40e0-b766-f2608fca59d0/out_20250324_170352_USR_3.mp4?...."}}}, "PushRequest": {"type": "object", "properties": {"os": {"title": "OS (AOS/IOS)", "type": "string", "example": "AOS"}, "pushToken": {"title": "App Push토큰", "type": "string", "example": "abcd-123njkfdsa-fdnjhakfsd"}}}, "Condition": {"type": "object", "properties": {"size": {"title": "대기열(작업포함)", "type": "integer", "format": "int32", "example": 10}, "waitSec": {"title": "대기열 * 40초", "type": "integer", "format": "int32", "example": 400}, "state": {"title": "상태값", "type": "string", "example": "Server Busy"}}}}, "securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}