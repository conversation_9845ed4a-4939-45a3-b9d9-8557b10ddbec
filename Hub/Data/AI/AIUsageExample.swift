//
//  AIUsageExample.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import Foundation

/**
 * NCN AI API 사용 예제
 */

class AIUsageExample {
  private let aiUseCase: AIUseCase = DefaultAIUseCase()
  
  // MARK: - Access Key 관리 예제
  
  /// 새로운 액세스 키 생성 (v1)
  func createAccessKeyExample() {
    aiUseCase.createAccessKey(version: .v1) { result in
      switch result {
      case .success(let accessKey):
        print("✅ 액세스 키 생성 성공: \(accessKey)")
      case .failure(let error):
        print("❌ 액세스 키 생성 실패: \(error.localizedDescription)")
      }
    }
  }
    
  /// 액세스 키 검증
  func verifyAccessKeyExample(accessKey: String) {
    aiUseCase.verifyAccessKey(accessKey: accessKey) { result in
      switch result {
      case .success(let isValid):
        print("✅ 액세스 키 검증 결과: \(isValid ? "유효" : "무효")")
      case .failure(let error):
        print("❌ 액세스 키 검증 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// 액세스 키 무효화
  func invalidateAccessKeyExample(accessKey: String) {
    aiUseCase.invalidateAccessKey(accessKey: accessKey) { result in
      switch result {
      case .success:
        print("✅ 액세스 키 무효화 성공")
      case .failure(let error):
        print("❌ 액세스 키 무효화 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - 이미지 복원 예제
  
  /// 번호판 이미지 복원 (즉시 반환)
  func restoreImageExample(imageData: Data, accessKey: String) {
    aiUseCase.restoreImage(version: .v2, imageData: imageData, accessKey: accessKey) { result in
      switch result {
      case .success(let restoredImageData):
        print("✅ 이미지 복원 성공: \(restoredImageData.count) bytes")
        // 복원된 이미지 데이터를 파일로 저장하거나 UI에 표시
      case .failure(let error):
        print("❌ 이미지 복원 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// 번호판 이미지 복원 (작업 ID 반환)
//  func restoreImageSaveExample(imageData: Data, accessKey: String) {
//    aiUseCase.restoreImageSave(imageData: imageData, accessKey: accessKey) { result in
//      switch result {
//      case .success(let taskId):
//        print("✅ 이미지 복원 작업 시작: Task ID \(taskId)")
//        // 작업 상태를 주기적으로 확인
//        self.checkTaskStatus(taskId: taskId)
//      case .failure(let error):
//        print("❌ 이미지 복원 작업 시작 실패: \(error.localizedDescription)")
//      }
//    }
//  }
  
  // MARK: - 비디오 비식별화 예제
  
  /// 비디오 비식별화 요청
//  func deIdentifyVideoExample(videoData: Data, accessKey: String) {
//    aiUseCase.deIdentifyVideo(version: .v2, videoData: videoData, accessKey: accessKey) { result in
//      switch result {
//      case .success(let taskId):
//        print("✅ 비디오 비식별화 작업 시작: Task ID \(taskId)")
//        // 작업 상태를 주기적으로 확인
//        self.checkTaskStatus(taskId: taskId)
//      case .failure(let error):
//        print("❌ 비디오 비식별화 작업 시작 실패: \(error.localizedDescription)")
//      }
//    }
//  }
  
  // MARK: - 작업 상태 확인 예제
  
  /// 작업 상태 확인 (상태만 반환)
  func checkTaskStatus(taskId: Int64) {
    aiUseCase.statusVideo(version: .v1, taskId: taskId) { result in
      switch result {
      case .success(let status):
        print("📊 작업 상태: \(status.rawValue)")
        
        switch status {
        case .registered:
          print("⏳ 작업이 등록되었습니다.")
        case .inProgress:
          print("🔄 작업이 진행 중입니다.")
        case .completed:
          print("✅ 작업이 완료되었습니다.")
        case .failed:
          print("❌ 작업이 실패했습니다.")
        case .download, .upload:
          print("download and upload")
        }
        
      case .failure(let error):
        print("❌ 작업 상태 확인 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// 작업 상태 확인 (다운로드 URL 포함)
  func checkTaskStatusWithDownloadUrl(taskId: Int64) {
    aiUseCase.statusVideoWithDownloadUrl(taskId: taskId) { result in
      switch result {
      case .success(let statusLink):
        print("📊 작업 상태: \(statusLink.status)")
        
        if let downloadUrl = statusLink.url, statusLink.taskStatus == .completed {
          print("📥 다운로드 URL: \(downloadUrl)")
          // URL을 사용하여 파일 다운로드
        }
        
      case .failure(let error):
        print("❌ 작업 상태 확인 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - 파일 다운로드 예제
  
  /// 처리된 비디오 다운로드
  func downloadVideoExample(taskId: Int64, accessKey: String) {
    aiUseCase.downloadVideo(taskId: taskId, accessKey: accessKey) { result in
      switch result {
      case .success(let videoData):
        print("✅ 비디오 다운로드 성공: \(videoData.count) bytes")
        // 비디오 데이터를 파일로 저장
      case .failure(let error):
        print("❌ 비디오 다운로드 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - 결과 수신 방법 설정 예제
  
  /// Push 알림으로 결과 수신 설정
  func setResultReceiveMethodExample(taskId: Int64, pushToken: String) {
    let request = RecvResultRequest(
      taskId: taskId,
      resultRecv: .push,
      email: nil,
      os: .ios,
      pushToken: pushToken
    )
    
    aiUseCase.setResultReceiveMethod(request: request) { result in
      switch result {
      case .success:
        print("✅ 결과 수신 방법 설정 성공 (Push)")
      case .failure(let error):
        print("❌ 결과 수신 방법 설정 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// 이메일로 결과 수신 설정
  func setEmailResultReceiveMethodExample(taskId: Int64, email: String) {
    let request = RecvResultRequest(
      taskId: taskId,
      resultRecv: .email,
      email: email,
      os: .ios,
      pushToken: ""
    )
    
    aiUseCase.setResultReceiveMethod(request: request) { result in
      switch result {
      case .success:
        print("✅ 결과 수신 방법 설정 성공 (Email)")
      case .failure(let error):
        print("❌ 결과 수신 방법 설정 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - 서버 상태 확인 예제
  
  /// 서버 상태 및 대기열 확인
  func checkServerConditionExample() {
    aiUseCase.checkCondition(version: .v2, taskType: .deIdentified) { result in
      switch result {
      case .success(let condition):
        print("📊 서버 상태: \(condition.state)")
        print("⏳ 대기열 크기: \(condition.size)")
        print("⏱️ 예상 대기 시간: \(condition.waitSec)초")
      case .failure(let error):
        print("❌ 서버 상태 확인 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - Push 알림 예제
  
  /// Push 알림 전송
  func sendPushExample(pushToken: String) {
    let request = PushRequest(os: .ios, pushToken: pushToken)
    
    aiUseCase.sendPush(request: request) { result in
      switch result {
      case .success(let response):
        print("✅ Push 알림 전송 성공: \(response)")
      case .failure(let error):
        print("❌ Push 알림 전송 실패: \(error.localizedDescription)")
      }
    }
  }
  
  // MARK: - 헬스 체크 예제

  /// API 서버 헬스 체크
  func healthCheckExample() {
    aiUseCase.healthCheck { result in
      switch result {
      case .success(let response):
        print("✅ 서버 상태 정상: \(response)")
      case .failure(let error):
        print("❌ 서버 상태 확인 실패: \(error.localizedDescription)")
      }
    }
  }

  // MARK: - V3 API Examples

  /// V3 API를 사용한 비디오 비식별화 예제
  func deIdentifyVideoV3Example() {
    // V3 API 활성화
    AIServiceManager.shared.useV3API = true

    // 예제 비디오 데이터 (실제로는 파일에서 로드)
    guard let videoData = "mock video data".data(using: .utf8) else {
      print("❌ 비디오 데이터 생성 실패")
      return
    }

    let fileName = "test_video.mp4"

    AIServiceManager.shared.deIdentifyVideoV3(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { progress in
        print("📤 업로드 진행률: \(Int(progress * 100))%")
      },
      progressCallback: { status in
        print("📊 작업 상태: \(status.rawValue)")
      },
      completion: { result in
        switch result {
        case .success(let downloadUrl):
          print("✅ V3 비디오 비식별화 완료")
          print("📥 다운로드 URL: \(downloadUrl)")
        case .failure(let error):
          print("❌ V3 비디오 비식별화 실패: \(error.localizedDescription)")
        }
      }
    )
  }

  /// 자동 API 버전 선택 예제
  func deIdentifyVideoAutoExample() {
    // 설정에 따라 자동으로 v2 또는 v3 선택
    guard let videoData = "mock video data".data(using: .utf8) else {
      print("❌ 비디오 데이터 생성 실패")
      return
    }

    let fileName = "test_video.mp4"

    AIServiceManager.shared.deIdentifyVideoAuto(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { progress in
        print("📤 업로드 진행률: \(Int(progress * 100))%")
      },
      progressCallback: { status in
        print("📊 작업 상태: \(status.rawValue)")
      },
      completion: { result in
        switch result {
        case .success(let downloadUrl):
          print("✅ 자동 비디오 비식별화 완료")
          print("📥 다운로드 URL: \(downloadUrl)")
        case .failure(let error):
          print("❌ 자동 비디오 비식별화 실패: \(error.localizedDescription)")
        }
      }
    )
  }

  /// V3 API 설정 예제
  func configureV3APIExample() {
    // V3 API 활성화
    AIServiceManager.shared.useV3API = true
    print("✅ V3 API 활성화됨")

    // V2 API로 되돌리기
    AIServiceManager.shared.useV3API = false
    print("✅ V2 API로 되돌림")
  }
}
