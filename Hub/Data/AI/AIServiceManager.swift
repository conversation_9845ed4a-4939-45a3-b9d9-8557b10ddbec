//
//  AIServiceManager.swift
//  Hub
//
//  NCN AI 서비스 매니저
//  Created by ncn on 5/26/25.
//

import Foundation

public class AIServiceManager {
  public static let shared = AIServiceManager()

  private let aiUseCase: AIUseCase = DefaultAIUseCase()
  private var currentAccessKey: String?
  private var activeTasks: [Int64: TaskInfo] = [:]
  var currentFileName: String?
  var isPushResult: Bool = false

  /// v3 API 사용 여부
  public var useV3API: Bool = true

  private init() {}
  
  // MARK: - Task Info
  private struct TaskInfo {
    let taskId: Int64
    let type: TaskType
    let startTime: Date
    var lastStatusCheck: Date?
    var status: TaskStatus?
  }
  
  // MARK: - Public Methods
  
  /// 액세스 키 초기화
  public func initializeAccessKey(completion: @escaping (Bool) -> Void) {
    validateCurrentAccessKey { isValid in
      if isValid {
        mLogger.info("✅ 기존 액세스 키가 유효합니다.")
        completion(true)
      } else {
        self.aiUseCase.createAccessKey(version: .v1) { [weak self] result in
          switch result {
          case .success(let accessKey):
            self?.currentAccessKey = accessKey
            LKPopupView.popup.debugToast(hit:"✅ AI 서비스 초기화 완료")
            completion(true)
          case .failure(let error):
            LKPopupView.popup.debugToast(hit:"❌ AI 서비스 초기화 실패: \(error.localizedDescription)")
            completion(false)
          }
        }
      }
    }
  }
  
  /// 액세스 키 무효화
  func invalidateAccessKey() {
    guard let accessKey = currentAccessKey else {
      mLogger.error("❌ 액세스 키 ")
      return
    }
    
    aiUseCase.verifyAccessKey(accessKey: accessKey) { result in
      switch result {
      case .success(let isValid):
        mLogger.info("✅ 액세스 키 검증 결과: \(isValid ? "유효" : "무효")")
        self.aiUseCase.invalidateAccessKey(accessKey: accessKey) { result in
          switch result {
          case .success:
            mLogger.info("✅ 액세스 키 무효화 성공")
          case .failure(let error):
            mLogger.error("❌ 액세스 키 무효화 실패: \(error.localizedDescription)")
          }
        }
        
      case .failure(let error):
        mLogger.error("❌ 액세스 키 검증 실패: \(error.localizedDescription)")
      }
    }
  }

  /// 이미지 복원
  public func restoreImage(
    imageData: Data,
    uploadProgress: @escaping (Double) -> Void,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    guard let accessKey = currentAccessKey else {
      completion(.failure(AIError.invalidAccessKey))
      return
    }
    
    aiUseCase.restoreImageSave(
      imageData: imageData,
      accessKey: accessKey,
      uploadProgress: { upload in
        mLogger.info("이미지 업로드 진행: \(upload)%")
        uploadProgress(upload)
      }
    ) { [weak self] result in
      switch result {
      case .success(let taskId):
        mLogger.info("✅ 이미지 복원 작업 시작: \(taskId)")
        
        // 2. 작업 정보 저장
        let taskInfo = TaskInfo(
          taskId: taskId,
          type: .deIdentified,
          startTime: Date()
        )
        self?.activeTasks[taskId] = taskInfo
        
        // 3. 상태 추적 시작
        self?.trackTaskProgress(
          taskId: taskId,
          accessKey: accessKey,
          progressCallback: progressCallback,
          completion: completion
        )
        
      case .failure(let error):
        completion(.failure(error))
      }
    }
  }
  
  /// 비디오 비식별화 (작업 ID 반환 후 상태 추적) - v2 방식
  public func deIdentifyVideo(
    videoData: Data,
    fileName: String,
    uploadProgress: @escaping (Double) -> Void,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    guard let accessKey = currentAccessKey else {
      completion(.failure(AIError.invalidAccessKey))
      return
    }

    // 1. 비식별화 작업 시작
    aiUseCase.deIdentifyVideo(
      version: .v1,
      videoData: videoData,
      fileName: fileName,
      accessKey: accessKey,
      uploadProgress: { upload in
        mLogger.info("비디오 업로드 진행: \(upload)%")
        uploadProgress(upload)
      }
    ) { [weak self] result in
      switch result {
      case .success(let taskId):
        mLogger.info("✅ 비디오 비식별화 작업 시작: \(taskId)")

        // 2. 작업 정보 저장
        let taskInfo = TaskInfo(
          taskId: taskId,
          type: .deIdentified,
          startTime: Date()
        )
        self?.activeTasks[taskId] = taskInfo

        // 3. 상태 추적 시작
        self?.trackTaskProgress(
          taskId: taskId,
          accessKey: accessKey,
          progressCallback: progressCallback,
          completion: completion
        )

      case .failure(let error):
        completion(.failure(error))
      }
    }
  }

  /// 비디오 비식별화 v3 방식 (새로운 3단계 워크플로우)
  public func deIdentifyVideoV3(
    videoData: Data,
    fileName: String,
    uploadProgress: @escaping (Double) -> Void,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    guard currentAccessKey != nil else {
      completion(.failure(AIError.invalidAccessKey))
      return
    }

    // 1단계: 업로드 URL 및 UUID 획득
    aiUseCase.getUploadUrl(fileName: fileName) { [weak self] result in
      switch result {
      case .success(let uploadResponse):
        mLogger.info("✅ 업로드 URL 획득 성공: \(uploadResponse.uuid)")

        // 2단계: 비디오 파일 업로드
        self?.aiUseCase.uploadVideoToUrl(
          url: uploadResponse.url,
          videoData: videoData,
          uploadProgress: { upload in
            mLogger.info("비디오 업로드 진행: \(upload)%")
            uploadProgress(upload)
          }
        ) { uploadResult in
          switch uploadResult {
          case .success:
            mLogger.info("✅ 비디오 업로드 완료")

            // UUID를 taskId로 변환하여 기존 추적 시스템 사용
            // v3에서는 UUID를 Int64로 변환하여 기존 시스템과 호환
            let taskId = Int64(abs(uploadResponse.uuid.hashValue))

            // 작업 정보 저장
            let taskInfo = TaskInfo(
              taskId: taskId,
              type: .deIdentified,
              startTime: Date()
            )
            self?.activeTasks[taskId] = taskInfo

            // 상태 추적 시작 (기존 메서드 재사용)
            self?.trackV3TaskProgress(
              uuid: uploadResponse.uuid,
              taskId: taskId,
              progressCallback: progressCallback,
              completion: completion
            )

          case .failure(let error):
            mLogger.error("❌ 비디오 업로드 실패: \(error.localizedDescription)")
            completion(.failure(error))
          }
        }

      case .failure(let error):
        mLogger.error("❌ 업로드 URL 획득 실패: \(error.localizedDescription)")
        completion(.failure(error))
      }
    }
  }

  /// 비디오 비식별화 (자동으로 v2/v3 선택)
  public func deIdentifyVideoAuto(
    videoData: Data,
    fileName: String,
    uploadProgress: @escaping (Double) -> Void,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    if useV3API {
      deIdentifyVideoV3(
        videoData: videoData,
        fileName: fileName,
        uploadProgress: uploadProgress,
        progressCallback: progressCallback,
        completion: completion
      )
    } else {
      deIdentifyVideo(
        videoData: videoData,
        fileName: fileName,
        uploadProgress: uploadProgress,
        progressCallback: progressCallback,
        completion: completion
      )
    }
  }

  /// v3 업로드 완료 처리
  private func completeV3Upload(
    uuid: String,
    completion: @escaping (Bool) -> Void
  ) {
    guard let fcmToken = AppManager.shared.fcmToken else {
      LKPopupView.popup.debugToast(hit:"❌ FCM 토큰이 없습니다.")
      return
    }

    let request = RecvResultRequestV3(
      uuid: uuid,
      resultRecv: isPushResult ? .push : .wait,
      email: nil,
      os: .ios,
      pushToken: fcmToken
    )

    aiUseCase.completeUpload(request: request) { [weak self] result in
      switch result {
      case .success:
        mLogger.info("✅ v3 업로드 완료 처리 성공")
        completion(true)

      case .failure(let error):
        self?.isPushResult = false
        mLogger.error("❌ v3 업로드 완료 처리 실패: \(error.localizedDescription)")
        completion(false)
      }
    }
  }

  /// v3 작업 진행 상황 추적 (UUID 기반)
  private func trackV3TaskProgress(
    uuid: String,
    taskId: Int64,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    // v3에서는 UUID를 사용하지만, 기존 상태 확인 API는 taskId를 사용
    // 실제 구현에서는 v3 상태 확인 API가 필요할 수 있음
    // 현재는 기존 trackTaskProgress 메서드를 재사용
    guard let accessKey = currentAccessKey else {
      completion(.failure(AIError.invalidAccessKey))
      return
    }

    trackTaskProgress(
      uuid: uuid,
      taskId: taskId,
      accessKey: accessKey,
      progressCallback: progressCallback,
      completion: completion
    )
  }

  /// 서버 상태 확인
  public func checkServerStatus(
    type: TaskType,
    completion: @escaping (Result< ConditionModel, Error >) -> Void
  ) {
    aiUseCase.checkCondition(version: .v2, taskType: type, completion: completion)
  }
  
  /// 활성 작업 목록 조회
  public func getActiveTasks() -> [Int64] {
    return Array(activeTasks.keys)
  }
  
  /// 특정 작업 상태 조회
  public func getTaskStatus(taskId: Int64) -> TaskStatus? {
    return activeTasks[taskId]?.status
  }
  
  // MARK: - Private Methods
  
  private func status(
    uuid: String? = nil,
    taskId: Int64,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  ) {
    if let uuid = uuid {
      aiUseCase.statusWithDownloadUrlV3(uuid: uuid, completion: completion)
    } else {
      aiUseCase.statusWithDownloadUrl(taskId: taskId, completion: completion)
    }
  }

  /// 작업 진행 상황 추적
  private func trackTaskProgress(
    uuid: String? = nil,
    taskId: Int64,
    accessKey: String,
    progressCallback: @escaping (TaskStatus) -> Void,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    
    func checkStatus() {
      status(uuid: uuid, taskId: taskId) { [weak self] result in
        switch result {
        case .success(let statusLink):
          guard let status = statusLink.taskStatus else {
            completion(.failure(AIError.parsingError))
            return
          }
          
          // 작업 상태 업데이트
          self?.activeTasks[taskId]?.status = status
          self?.activeTasks[taskId]?.lastStatusCheck = Date()
          
          progressCallback(status)
          
          switch status {
          case .completed:
            mLogger.info("작업 완료")
            guard let url = statusLink.url else {
              completion(.failure(AIError.parsingError))
              return
            }

            self?.activeTasks.removeValue(forKey: taskId)
            completion(.success(url))
            
          case .failed:
            completion(.failure(AIError.taskFailed))
            self?.activeTasks.removeValue(forKey: taskId)
            
          case .registered, .inProgress, .upload, .download:
            mLogger.info("작업 진행 중:")
            // 3초 후 다시 확인
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
              checkStatus()
            }
          }
          
        case .failure(let error):
          completion(.failure(error))
          self?.activeTasks.removeValue(forKey: taskId)
        }
      }
    }

    aiUseCase.verifyAccessKey(accessKey: accessKey) { result in
      switch result {
      case .success(let isValid):
        mLogger.log("✅ 액세스 키 검증 결과: \(isValid ? "유효" : "무효")")
        mLogger.info("taskId: \(taskId), uuid: \(uuid ?? "nil"), isPushResult: \(self.isPushResult)")
        
        if let uuid = uuid {
          self.completeV3Upload(uuid: uuid) { isSuccess in
            if isSuccess {
              checkStatus()
            }
          }
        } else {
          self.receiveResult(taskId: taskId) { isSuccess in
            if isSuccess {
              checkStatus()
            }
          }
        }
        
      case .failure(let error):
        mLogger.error("❌ 액세스 키 검증 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// URL에서 파일 다운로드
  func downloadFromUrl(url: String, completion: @escaping (Result<Data, Error>) -> Void) {
    guard let downloadUrl = URL(string: url) else {
      completion(.failure(AIError.invalidURL))
      return
    }
    
    URLSession.shared.dataTask(with: downloadUrl) { data, response, error in
      if let error = error {
        completion(.failure(error))
        return
      }
      
      guard let data = data else {
        completion(.failure(AIError.downloadFailed))
        return
      }
      
      completion(.success(data))
    }.resume()
  }
  
  /// Push 알림 설정
  public func receiveResult(taskId: Int64, completion: @escaping (Bool) -> Void) {
    guard let fcmToken = AppManager.shared.fcmToken else {
      LKPopupView.popup.debugToast(hit:"❌ FCM 토큰이 없습니다.")
      return
    }
    
    let receiveType: RecvType = self.isPushResult ? .push : .wait
    let request = RecvResultRequest(
      taskId: taskId,
      resultRecv: receiveType,
      email: nil,
      os: .ios,
      pushToken: fcmToken
    )
    
    aiUseCase.setResultReceiveMethod(request: request) { [weak self] result in
      guard let self else { return }
      switch result {
      case .success:
        let msg = self.isPushResult ? "✅  Push Notification Registered" : "✅ Wait 설정 완료"
        LKPopupView.popup.debugToast(hit:"\(msg)")
        completion(true)
      case .failure(let error):
        self.isPushResult = false
        LKPopupView.popup.debugToast(hit:"❌ Push 알림 설정 실패: \(error.localizedDescription)")
        
      }
    }
  }
  
//  public func setupWaitResult(taskId: Int64, completion: @escaping (Bool) -> Void) {
//    guard let fcmToken = AppManager.shared.fcmToken else {
//      LKPopupView.popup.debugToast(hit:"❌ FCM 토큰이 없습니다.")
//      return
//    }
//    
//    let request = RecvResultRequest(
//      taskId: taskId,
//      resultRecv: .wait,
//      email: nil,
//      os: .ios,
//      pushToken: fcmToken
//    )
//    
//    aiUseCase.setResultReceiveMethod(request: request) { result in
//      switch result {
//      case .success:
//        LKPopupView.popup.debugToast(hit:"✅ Wait 설정 완료")
//        completion(true)
//      case .failure(let error):
//        LKPopupView.popup.debugToast(hit:"❌ Wait 알림 설정 실패: \(error.localizedDescription)")
//        completion(false)
//      }
//    }
//  }
  
  /// 이메일 알림 설정
  public func setupEmailNotification(taskId: Int64, email: String) {
    let request = RecvResultRequest(
      taskId: taskId,
      resultRecv: .email,
      email: email,
      os: .ios,
      pushToken: ""
    )
    
    aiUseCase.setResultReceiveMethod(request: request) { result in
      switch result {
      case .success:
        LKPopupView.popup.debugToast(hit:"✅ 이메일 알림 설정 완료")
      case .failure(let error):
        LKPopupView.popup.debugToast(hit:"❌ 이메일 알림 설정 실패: \(error.localizedDescription)")
      }
    }
  }
  
  /// 액세스 키 갱신
  public func refreshAccessKey(completion: @escaping (Bool) -> Void) {
    aiUseCase.createAccessKey(version: .v2) { [weak self] result in
      switch result {
      case .success(let accessKey):
        self?.currentAccessKey = accessKey
        completion(true)
      case .failure:
        completion(false)
      }
    }
  }
  
  /// 현재 액세스 키 조회
  public func getCurrentAccessKey() -> String? {
    return currentAccessKey
  }
  
  /// 액세스 키 검증
  public func validateCurrentAccessKey(completion: @escaping (Bool) -> Void) {
    guard let accessKey = currentAccessKey else {
      completion(false)
      return
    }
    
    aiUseCase.verifyAccessKey(accessKey: accessKey) { result in
      switch result {
      case .success(let isValid):
        completion(isValid)
      case .failure:
        completion(false)
      }
    }
  }
}
