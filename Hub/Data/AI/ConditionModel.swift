//
//  ConditionModel.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import Foundation

// MARK: - Condition Model
public struct ConditionModel: Codable, Equatable {
  let size: Int
  let waitSec: Int
  let state: String
}

// MARK: - Enums
public enum RecvType: String, CaseIterable {
  case wait = "WAIT"
  case push = "PUSH"
  case email = "EMAIL"
  case cancel = "CANCEL"
}

public enum OSType: String, CaseIterable {
  case aos = "AOS"
  case ios = "IOS"
}

public enum LangType: String, CaseIterable {
  case en = "EN"
  case ko = "KO"
  case es = "ES"
}

// MARK: - Legacy Type Aliases
public typealias recvType = RecvType
public typealias osType = OSType
public typealias langType = LangType

public enum TaskStatus: String, CaseIterable {
  case registered = "REGISTERED"
  case inProgress = "IN_PROGRESS"
  case completed = "COMPLETED"
  case failed = "FAILED"
  case download = "DOWNLOAD"
  case upload  = "UPLOAD"
}

public enum TaskType: String, CaseIterable {
  case deIdentified = "DE_IDENTIFIED"
  case restored = "RESTORED"
}

// MARK: - Request Models
public struct RecvResultRequest: Codable, Equatable {
  let taskId: Int64
  let resultRecv: String
  let email: String?
  let os: String
  let pushToken: String
  let lang: String
  public init(taskId: Int64, resultRecv: RecvType, email: String? = nil, os: OSType, pushToken: String) {
    self.taskId = taskId
    self.resultRecv = resultRecv.rawValue
    self.email = email
    self.os = os.rawValue
    self.pushToken = pushToken
    self.lang = Current.lang.rawValue
  }
}

public struct PushRequest: Codable, Equatable {
  let os: String
  let pushToken: String

  public init(os: OSType, pushToken: String) {
    self.os = os.rawValue
    self.pushToken = pushToken
  }
}

// MARK: - Response Models
public struct StatusLink: Codable, Equatable {
  let status: String
  let url: String?

  public var taskStatus: TaskStatus? {
    return TaskStatus(rawValue: status)
  }
}

// MARK: - V3 API Models
public struct UploadUrlResponse: Codable, Equatable {
  let url: String
  let uuid: String
}


public struct RecvResultRequestV3: Codable, Equatable {
  let uuid: String
  let resultRecv: String
  let email: String?
  let os: String
  let pushToken: String
  let lang: String
  public init(uuid: String, resultRecv: RecvType, email: String? = nil, os: OSType, pushToken: String) {
    self.uuid = uuid
    self.resultRecv = resultRecv.rawValue
    self.email = email
    self.os = os.rawValue
    self.pushToken = pushToken
    self.lang = Current.lang.rawValue
  }
}
