//
//  AIError.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import Foundation

public enum AIError: Error, LocalizedError {
  case invalidURL
  case invalidAccessKey
  case invalidTaskId
  case invalidImageData
  case invalidVideoData
  case networkError(Error)
  case serverError(String)
  case parsingError
  case fileNotFound
  case uploadFailed
  case downloadFailed
  case taskNotCompleted
  case taskFailed
  case unknownError
  
  public var errorDescription: String? {
    switch self {
    case .invalidURL:
      return "Invalid URL provided"
    case .invalidAccessKey:
      return "Invalid or missing access key"
    case .invalidTaskId:
      return "Invalid task ID"
    case .invalidImageData:
      return "Invalid image data"
    case .invalidVideoData:
      return "Invalid video data"
    case .networkError(let error):
      return "Network error: \(error.localizedDescription)"
    case .serverError(let message):
      return "Server error: \(message)"
    case .parsingError:
      return "Failed to parse response"
    case .fileNotFound:
      return "File not found"
    case .uploadFailed:
      return "File upload failed"
    case .downloadFailed:
      return "File download failed"
    case .taskNotCompleted:
      return "Task is not completed yet"
    case .taskFailed:
      return "Task failed to process"
    case .unknownError:
      return "Unknown error occurred"
    }
  }
}
