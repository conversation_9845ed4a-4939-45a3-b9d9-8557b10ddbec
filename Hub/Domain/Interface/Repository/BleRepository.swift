//
//  BleRepository.swift
//  Hub
//
//  Created by ncn on 2023/02/08.
//

import CoreBluetooth
import UIKit

protocol BleRepository: AnyObject {
  func startScane(services: [CBUUID]?, completion: @escaping ([BleItemModel]?, Error?) -> Void)
  func stopScane()
  func restartBle()
  func close()
  func connect(
    model: DiscoverdModel,
    completion: @escaping (Bool, Bool, Bool, Error?) -> Void,
    readHandler: @escaping (BLE.Response) -> Void)
  func connect(
    uuid: String,
    completion: @escaping (Bool, Bool, Bool, Error?) -> Void,
    readHandler: @escaping (BLE.Response) -> Void)
  func send(
    command: BLE.Send,
    completion: @escaping (BLE.Response) -> Void)
}
