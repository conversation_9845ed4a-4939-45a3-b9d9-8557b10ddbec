//
//  DefaultCommandRepository.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import Foundation

final class DefaultCommandRepository: CommandRepository {

  private let webSocket: WebSocketClient

  init(websocket: WebSocketClient) {
    hLogger.info("websocket open: \(websocket.isOpen)")
    self.webSocket = websocket
  }

  deinit {
    //webSocket.disconnect()
  }

  func connect(completion: @escaping WebSocketCallback) {
    webSocket.connect { response, error in
      hLogger.debug("websocket connect: \(response?.debugDescription ?? "") error: \(error?.localizedDescription ?? "")")
      completion(response, error)
    }
  }
  
  func disConnect() {
    hLogger.info("websocket disConnect")
    webSocket.disconnect()
  }
  
  func startKeepAlive() {
    webSocket.startPingPong()
  }
  
  func stopKeepAlive() {
    webSocket.pingpongWorkItem?.cancel()
  }
}

// MARK: Command
extension DefaultCommandRepository {
  func send(
    to command: SetCommand.Send,
    completion: @escaping (SetCommand.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: SetCommand.Response.self) {
          let statusCode = ret.obj?.header.res ?? -1
          if !(200..<300).contains(statusCode) {
            completion(
              nil,
              NCError(
                title: ret.obj?.header.cmd,
                description: ret.obj?.header.rmsg ?? "",
                code: statusCode
              )
            )
            return
          }
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }
}

// MARK: Live
extension DefaultCommandRepository {
  func send(
    to command: StartLiveView.Send,
    completion: @escaping (StartLiveView.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        guard let response = response else { return }
        if response.contains(key: "gsensor") {
          let ret = response.dictionaryToObject(objectType: GSensorWifiLiveModel.self)
          let dummyHeader = HeaderModel(cmd: "gsensor")
          let gsensorResponse = StartLiveView.Response(header: dummyHeader, gsensorData: ret.obj)
          completion(gsensorResponse, error)
        } else {
          let ret = response.dictionaryToObject(objectType: StartLiveView.Response.self)
          if !(200..<300).contains(ret.obj?.header.res ?? 400) {
            completion(
              nil,
              NCError(
                title: ret.obj?.header.cmd,
                description: ret.obj?.header.rmsg ?? "",
                code: ret.obj?.header.res ?? -1
              )
            )
            return
          }
          Log.message(to: "LiveStart.Response: \(String(describing: ret))")
          completion(ret.obj, error)
        }
      }
    }
  }

  func send(
    to command: StopLiveView.Send,
    completion: @escaping (StopLiveView.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: StopLiveView.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: ChangeLiveView.Send,
    completion: @escaping (Result<ChangeLiveView.Response, Error>) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let error = error {
          completion(.failure(error))
          return
        }
        if let response = response {
          let dictToObject = response.dictionaryToObject(objectType: ChangeLiveView.Response.self)
          if let error = dictToObject.error {
            completion(.failure(error))
          } else {
            guard let obj = dictToObject.obj else {
              let customError = NCError(title: "Custom Error", description: "obj is nil", code: 999)
              completion(.failure(customError))
              return
            }
            completion(.success(obj))
            return
          }
        }

        let customError = NCError(
          title: "Custom Error", description: "response is nil &&  error is nil", code: 999)
        completion(.failure(customError))
      }
    }
  }
  
  func send(
    to command: GetLiveInfoModel.Send,
    completion: @escaping (Result<GetLiveInfoModel.Response, Error>) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let error = error {
          completion(.failure(error))
          return
        }
        if let response = response {
          let dictToObject = response.dictionaryToObject(objectType: GetLiveInfoModel.Response.self)
          if let error = dictToObject.error {
            completion(.failure(error))
          } else {
            guard let obj = dictToObject.obj else {
              let customError = NCError(title: "Custom Error", description: "obj is nil", code: 999)
              completion(.failure(customError))
              return
            }
            completion(.success(obj))
            return
          }
        } else {
          let customError = NCError(
            title: "Custom Error", description: "response is nil", code: 999)
          completion(.failure(customError))
        }
      }
    }
  }
}

// MARK: - Vod
extension DefaultCommandRepository {
  func send(
    to command: VodStart.Send,
    completion: @escaping (VodStart.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      // gsensor id : 10006
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        guard let response = response else { return }
        let ret = response.dictionaryToObject(objectType: VodStart.Response.self)
        completion(ret.obj, ret.error)
      }
    } else {
      let ncError = NCError(
        title: "Json To String Fail", description: "Json To String Fail", code: 99999)
      completion(nil, ncError)
    }
  }

  func send(
    to command: VodStop.Send,
    completion: @escaping (VodStop.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: VodStop.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: VodChange.Send,
    completion: @escaping (VodChange.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: VodChange.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }
}

// MARK: - Configuration
extension DefaultCommandRepository {
  func send(
    to command: GetConfiguration.Send,
    completion: @escaping (GetConfiguration.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetConfiguration.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          AppManager.shared.dashcamWifiConfig = ret.obj?.getconfiguration
          Current.timeRegion = ret.obj?.getconfiguration.timeregion ?? 14 // Default to London time
          if let speedUnit = AppManager.shared.dashcamWifiConfig?.speedunit {
            Current.speedUnit = SpeedType(rawValue: speedUnit) ?? .kph
          }
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: SetConfiguration.Send,
    completion: @escaping (SetConfiguration.Response?, Error?) -> Void
  ) {
    AppManager.shared.dashcamWifiConfig = command.setconfiguration
    Current.timeRegion = command.setconfiguration.timeregion
    Current.speedUnit = SpeedType(rawValue: command.setconfiguration.speedunit) ?? .kph
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: SetConfiguration.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }
}

// MARK: - FastConfiguration
extension DefaultCommandRepository {
  func send(
    to command: GetFastConfiguration.Send,
    completion: @escaping (GetFastConfiguration.Response?, Error?) -> Void
  ) {    
    let headerString = command.header.toJsonString() ?? ""
    let jsonString = """
      {
        "header" : \(headerString),
        "getfastconfiguration": {
          "\(command.getfastconfiguration.type.rawValue)" : \(command.getfastconfiguration.value)
        }
      }
      """
    wsLogger.trace("🩺 \(jsonString) ")
    webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: jsonString) {
      response, error in
      if let ret = response?.dictionaryToObject(objectType: GetFastConfiguration.Response.self) {
        completion(ret.obj, error)
      } else {
        wsLogger.error("🚩 error: \(error?.localizedDescription ?? "")")
        completion(nil, error)
      }
    }
  }

  func send(
    to command: SetFastConfiguration.Send,
    completion: @escaping (SetFastConfiguration.Response?, Error?) -> Void
  ) {
    let headerString = command.header.toJsonString() ?? ""
    let jsonString = """
      {
        "header": \(headerString),
        "setfastconfiguration": {
          "\(command.setfastconfiguration.type.rawValue)" : \(command.setfastconfiguration.value )
        }
      }
      """
    wsLogger.trace("🩺 \(jsonString) ")
    webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: jsonString) {
      response, error in
      if let ret = response?.dictionaryToObject(objectType: SetFastConfiguration.Response.self) {
        completion(ret.obj, error)
      } else {
        wsLogger.error("🚩 error: \(error?.localizedDescription ?? "")")
        completion(nil, error)
      }
    }
  }
}

// MARK: - Get
extension DefaultCommandRepository {
  func send(
    to command: GetStatus.Send,
    completion: @escaping (GetStatus.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        Log.message(to: "error: \(String(describing: response))")
        if let ret = response?.dictionaryToObject(objectType: GetStatus.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetInformation.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          AppManager.shared.deviceInfo = ret.obj?.getinformation
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: SetInformationModel.Send,
    completion: @escaping (Result<SetInformationModel.Response, Error>) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: SetInformationModel.Response.self),
          let obj = ret.obj
        {
          completion(.success(obj))
        } else if let error = error {
          completion(.failure(error))
        } else {
          let unKnownError = NCError(title: "unKnown", description: "unKnown", code: 0)
          completion(.failure(unKnownError))
        }
      }
    }
  }

  func send(
    to command: GetInitInfo.Send,
    completion: @escaping (GetInitInfo.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetInitInfo.Response.self) {
          Log.message(
            to: "ret: \(String(describing: ret.obj)) error: \(String(describing: ret.error))")
          AppManager.shared.initInfo = ret.obj?.getinitinfo
          Current.timeRegion = ret.obj?.getinitinfo?.timeRegion ?? 14 // Default to London time
          if let speedUnit = AppManager.shared.initInfo?.speedUnit {
            Current.speedUnit = SpeedType(rawValue: speedUnit) ?? .kph
          }

          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: GetDriveRecord.Send,
    completion: @escaping (GetDriveRecord.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetDriveRecord.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: GetGpsRecord.Send,
    completion: @escaping (GetGpsRecord.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetGpsRecord.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: GetFileList.Send,
    completion: @escaping (GetFileList.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: GetFileList.Response.self) {
          if !(200..<300).contains(ret.obj?.header.res ?? 400) {
//            completion(
//              nil,
//              NCError(
//                title: ret.obj?.header.cmd,
//                description: ret.obj?.header.rmsg ?? "",
//                code: ret.obj?.header.res ?? -1
//              )
//            )
//            return
          }
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: DeleteFileModel.Send,
    completion: @escaping (DeleteFileModel.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: DeleteFileModel.Response.self) {
          Log.warning(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }
}
// MARK: - Etc
extension DefaultCommandRepository {
  func send(
    to command: TestVolume.Send,
    completion: @escaping (TestVolume.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: TestVolume.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: RestartNetwork.Send,
    completion: @escaping (RestartNetwork.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: RestartNetwork.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: SetNetwork.Send,
    completion: @escaping (SetNetwork.Response?, Error?) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let ret = response?.dictionaryToObject(objectType: SetNetwork.Response.self) {
          Log.message(to: "error: \(String(describing: ret.error))")
          completion(ret.obj, error)
        } else {
          completion(nil, error)
        }
      }
    }
  }

  func send(
    to command: NewFirmwareModel.Send,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let error = error {
          Log.error(to: "error: \(String(describing: error))")
          completion(.failure(error))
        } else {
          completion(.success(()))
        }
      }
    }
  }
  
  func send(
    to command: NewBinaryModel.Send,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    if let string = command.toJsonString() {
      webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: string) {
        response, error in
        if let error = error {
          Log.error(to: "error: \(String(describing: error))")
          completion(.failure(error))
        } else {
          completion(.success(()))
        }
      }
    }
  }

  func send(
    to firmwareFile: Data,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    webSocket.send(type: "firmwareUpload", id: 99999, data: firmwareFile) { response, error in
      if let error = error {
        completion(.failure(error))
      } else {
        completion(.success(()))
      }
    }
  }

  func send(
    to command: VerifyPasswordModel.Send,
    completion: @escaping (VerifyPasswordModel.Response?, Error?) -> Void
  ) {
    guard let commandJson = command.toJsonString() else {
      return
    }

    webSocket.send(type: command.header.cmd, id: command.header.seq, jsonString: commandJson) {
      response, error in
      if let ret = response?.dictionaryToObject(objectType: VerifyPasswordModel.Response.self) {
        Log.message(to: "error: \(String(describing: ret.error))")
        completion(ret.obj, error)
      } else {
        completion(nil, error)
      }
    }
  }
}
