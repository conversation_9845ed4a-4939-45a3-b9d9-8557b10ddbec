//
//  DefaultWifiRepository.swift
//  Hub
//
//  Created by ncn on 2022/12/09.
//

import Foundation

final class DefaultWifiRepository: WifiRepository {
  private let manager: HotspotManager

  init(manager: HotspotManager) {
    self.manager = manager
  }

  deinit {
  }
}

extension DefaultWifiRepository {
  func connect(
    toWifi param: Wifi.ConnectModel, reconnectCount: Int,
    completion: @escaping (Bool, Error?) -> Void
  ) {
    manager.connectHandler = { isConnect, error in
      completion(isConnect, error)
    }
    manager.connect(ssid: param.ssid, reconnectCount: reconnectCount, password: param.password)
  }
}
