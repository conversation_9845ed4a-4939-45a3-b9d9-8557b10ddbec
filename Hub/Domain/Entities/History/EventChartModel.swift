//
//  EventChartModel.swift
//  Hub
//
//  Created by ncn on 2023/06/29.
//

import UIKit

public struct EventChartModel {
  let date: String
  let cnt1: Int  // 상시충격
  let cnt2: Int  // 주차모션
  let cnt3: Int  // 주차충격
  let sumtime1: String  //HH:mm:ss
  let sumtime2: String  //HH:mm:ss
  let maxspeed: Int
  var avgspeed: Int
  let distance: Int
  var periodType: PeriodType = .day
  var originDate: String

  init(day: Date, type: PeriodType) {
    let string: String
    switch type {
    case .day:
      string = day.toString(format: "yyyy/MM/dd")
    case .week:
      string = day.toString(format: "E")
    case .month:
      string = day.toString(format: "dd")
    case .year:
      string = day.toString(format: "MMM")
    }
    self.date = string
    self.cnt1 = 0
    self.cnt2 = 0
    self.cnt3 = 0
    self.sumtime1 = "00:00:00"
    self.sumtime2 = "00:00:00"
    self.maxspeed = 0
    self.avgspeed = 0
    self.distance = 0
    self.periodType = type
    self.originDate = ""
  }

  init(
    date: String, cnt1: Int, cnt2: Int, cnt3: Int, sumtime1: String, sumtime2: String,
    maxspeed: Int, avgspeed: Int, distance: Int, periodType: PeriodType = .day, originDate: String
  ) {
    self.date = date
    self.cnt1 = cnt1
    self.cnt2 = cnt2
    self.cnt3 = cnt3
    self.sumtime1 = sumtime1 == "" ? "00:00:00" : sumtime1
    self.sumtime2 = sumtime2 == "" ? "00:00:00" : sumtime2
    self.maxspeed = maxspeed
    self.avgspeed = avgspeed
    self.distance = distance
    self.periodType = periodType
    self.originDate = originDate
  }

  public static func + (lhs: EventChartModel, rhs: EventChartModel) -> EventChartModel {

    let sumtime1Int = Int(lhs.sumtime1.timeToSecond() + rhs.sumtime1.timeToSecond())
    let sumtime1String = sumtime1Int.secondToTime(seperate: ":")

    let sumtime2Int = Int(lhs.sumtime2.timeToSecond() + rhs.sumtime2.timeToSecond())
    let sumtime2String = sumtime2Int.secondToTime(seperate: ":")
    return EventChartModel(
      date: rhs.date,
      cnt1: lhs.cnt1 + rhs.cnt1,
      cnt2: lhs.cnt2 + rhs.cnt2,
      cnt3: lhs.cnt3 + rhs.cnt3,
      sumtime1: sumtime1String,
      sumtime2: sumtime2String,
      maxspeed: max(lhs.maxspeed, rhs.maxspeed),
      avgspeed: lhs.avgspeed + rhs.avgspeed,
      distance: lhs.distance + rhs.distance,
      periodType: rhs.periodType,
      originDate: rhs.originDate
    )
  }
}
