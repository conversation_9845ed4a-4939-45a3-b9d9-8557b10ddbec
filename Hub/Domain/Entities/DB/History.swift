//
//  History.swift
//  Hub
//
//  Created by ncn on 2023/03/20.
//

import UIKit

enum SpeedType: Int, Codable {
  case kph = 0
  case mph = 1
  case off = 2
  
  var title: String {
    switch self {
    case .mph:
      return L.unit_mph.localized
    case .kph:
      return L.unit_kmh.localized
    case .off:
      return L.s1_setting_off.localized
    }
  }
}

enum DateType: Int {
  case day = 0
  case week = 1
  case month = 2
  case year = 3
}

enum History {
  struct ChartXAxisModel {
    let value: String
    let title: String
  }

  struct ChartYAxisModel {
    let v01: Int
    let v02: Int
    let v03: Int
  }
}

struct ChartDataModel {
  let v01: Int
  let xAxisValue: String
  let xAxisTitle: String
}

enum Drive {
  struct Request {
    let queries: [String]
    let dateType: DateType
    let speedType: SpeedType
  }

  struct Resoponse {
    let value: DriveModel
  }
}

struct DriveModel {
  let t0Time: Double  // 상시녹화 시간
  let t1Time: Double
  let sumDistance: Int
  let t0Event: Int
  let t1Event: Int
  let t1motNum: Int
  let maxSpeed: Int
  let avgSpeed: Int
  let startMin: Double
}

struct TravelLogModel {
  let type: Int

  let start: Int  // 상시녹화 시간
  let end: Int

  let distance: Int
  let maxSpeed: Int
  let avgSpeed: Int

  let evtNum: Int
  let motNum: Int

  let offType: Int
  let reserved: Int

  init(
    type: Int, start: Int, end: Int, distance: Int, maxSpeed: Int, avgSpeed: Int, evtNum: Int,
    motNum: Int, offType: Int, reserved: Int
  ) {
    self.type = type
    self.start = start
    self.end = end
    self.distance = distance
    self.maxSpeed = maxSpeed
    self.avgSpeed = avgSpeed
    self.evtNum = evtNum
    self.motNum = motNum
    self.offType = offType
    self.reserved = reserved
  }

  init(toModel: HISTORY) {
    self.type = toModel.TYPE
    self.offType = toModel.OFFTYPE

    
    let adjustedStart: Int
    if self.type == 2 && self.offType == 2 {
      // 현재 날짜의 00:00:00으로 START 조정
      adjustedStart = toModel.START.toStartOfSameDay()
      iLogger.info("🔗 연속 주차 세션 감지: START를 00:00:00으로 조정")
      iLogger.info("📅 원본 START: \(toModel.START.toDateString())")
      iLogger.info("🌅 조정된 START \(adjustedStart): \(adjustedStart.toDateString())")
    } else {
      adjustedStart = toModel.START
    }
    self.start = adjustedStart
    
    let adjustedEnd: Int
    if type == 2 && offType == 7 {
      // START와 같은 날짜의 24:00:00 (자정)으로 END 조정
      adjustedEnd = start.toEndOfSameDay()
      iLogger.info("🕛 OFFTYPE 7 감지: END를 24:00:00으로 조정")
      iLogger.info("📅 원본 END: \(toModel.END.toDateString())")
      iLogger.info("🌙 조정된 END: \(adjustedEnd), \(adjustedEnd.toDateString())")
    } else {
      adjustedEnd = toModel.END
    }

    self.end = adjustedEnd
    self.distance = toModel.DISTANCE
    self.maxSpeed = toModel.SPEEDMAX
    self.avgSpeed = toModel.SPEEDAVG
    self.evtNum = toModel.EVTNUM
    self.motNum = toModel.MOTNUM

    self.reserved = toModel.RESERVED
  }

}
