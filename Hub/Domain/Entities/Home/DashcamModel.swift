//
//  DashcamModel.swift
//  Hub
//
//  Created by ncn on 2023/04/06.
//

import Foundation

// restful
class DashcamCellModel {
  var isFold: Bool = true

  let mdName: String
  let serial: String
  let status: String
  let nowStatus: String
  let dvcStatus: String?
  let imageUrl: String?
  let dvcAls: String?
  let uaId: Int?
  let firmware: String?
  let safetyDbVersion: String?
  let privateCd: String?
  let useStart: String?
  let useEnd: String?
  let udG: UdGModel?

  init(
    isFold: Bool = true,
    mdName: String,
    serial: String,
    status: String,
    nowStatus: String,
    dvcStatus: String?,
    imageUrl: String?,
    dvcAls: String?,
    uaId: Int?,
    firmware: String?,
    safetyDbVersion: String?,
    privateCd: String?,
    useStart: String?,
    useEnd: String?,
    udG: UdGModel?
  ) {
    self.isFold = isFold
    self.mdName = mdName
    self.serial = serial
    self.status = status
    self.nowStatus = nowStatus
    self.dvcStatus = dvcStatus
    self.imageUrl = imageUrl
    self.dvcAls = dvcAls
    self.uaId = uaId
    self.firmware = (firmware == "") ? "-" : firmware
    self.safetyDbVersion = (safetyDbVersion == "") ? "-" : safetyDbVersion
    self.privateCd = privateCd
    self.useStart = useStart
    self.useEnd = useEnd
    self.udG = udG
  }
}
