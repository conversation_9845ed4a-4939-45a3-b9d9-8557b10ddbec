//
//  DashcamConfiguration.swift
//  Hub
//
//  Created by ncn on 2023/04/18.
//

import Foundation

// command
struct HomeDashcamModel {
  var model: String?
  var serial: String?
  var mdName: String?
  var macaddress: String?
  var voltage: String?
  var drivemode: String?
  var status: Int?
  var netStatus: String?
  var ssid: String?
  var netpasswd: String?
  var imageUrl: String?
  var dvcAls: String?
  var timeZone: String?
  var time: String?

  var udG: UdGModel?
}

extension HomeDashcamModel {
  func merge(with: HomeDashcamModel) -> HomeDashcamModel {

    var new = HomeDashcamModel()
    new.model = with.model ?? model
    new.mdName = with.mdName ?? mdName
    new.serial = with.serial ?? serial
    new.macaddress = with.macaddress ?? macaddress
    new.voltage = with.voltage ?? voltage
    new.netStatus = with.netStatus ?? netStatus
    new.drivemode = with.drivemode ?? drivemode
    new.ssid = with.ssid ?? ssid
    new.netpasswd = with.netpasswd ?? netpasswd
    new.dvcAls = with.dvcAls ?? dvcAls
    new.udG = with.udG ?? udG
    new.timeZone = with.timeZone ?? timeZone
    new.time = with.time ?? time

    return new
  }
}
