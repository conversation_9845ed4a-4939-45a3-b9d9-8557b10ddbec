//
//  BleItemModel.swift
//  Hub
//
//  Created by ncn on 2023/02/06.
//

import Foundation

public struct BleItemModel: Codable {
  var uuid: String
  var mac: String? = nil
  var name: String? = nil
  var discoverServices: [String]? = nil
  var date: Date? = nil
  var isDiscovered: Bool
  var isEmpty: Bool = false

  var wifiSSID: String?
  var wifiPW: String?
}

protocol BleReadModel {
  var command: BLE.Command { get set }
  var message: String { get set }
}

struct BleErrorModel: BleReadModel {
  var command: BLE.Command
  var message: String
}

struct BleCheckModel: BleReadModel {
  var command: BLE.Command
  var message: String
  var isRegist: Bool
}

struct BleModeModel: BleReadModel {
  var command: BLE.Command
  var message: String
  var value: Int
}

struct BleApModel: BleReadModel {
  var command: BLE.Command
  var message: String
  let ssid: String?
  let passowrd: String?
}
