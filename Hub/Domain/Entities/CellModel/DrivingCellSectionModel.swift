//
//  DrivingCellSectionModel.swift
//  Hub
//
//  Created by ncn on 2023/02/14.
//

import RxDataSources
import UIKit

enum HistoryCellType {
  case Distance
  case DrivingEvent
  case ParkingMotion
  case ParkingEvent
  case DrivingTime
  case ParkingTime
  case AvgSpeed
  case MaxSpeed
  case nothing
}

struct DriveingCellModel {
  var type: HistoryCellType = .nothing
  let image: String
  let item: String
  let value: String
}

struct HistoryCellModel {
  var type: HistoryCellType = .nothing
  let item: String
  let value: String
}

struct HistoryCellSectionModel {
  public enum CellSectionType: String {
    case history
  }

  var sectionType: CellSectionType
  var items: [HistoryCellModel]
}

extension HistoryCellSectionModel: SectionModelType {
  typealias Item = HistoryCellModel

  init(original: HistoryCellSectionModel, items: [HistoryCellModel]) {
    self = original
    self.items = items
  }
}

struct DrivingCellSectionModel {
  public enum CellSectionType: String {
    case driving
  }

  var sectionType: CellSectionType
  var items: [DriveingCellModel]
}

extension DrivingCellSectionModel: SectionModelType {
  typealias Item = DriveingCellModel

  init(original: DrivingCellSectionModel, items: [DriveingCellModel]) {
    self = original
    self.items = items
  }
}
