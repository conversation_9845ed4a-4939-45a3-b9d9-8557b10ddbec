//
//  BleSectionModel.swift
//  Hub
//
//  Created by ncn on 2023/01/27.
//

import RxDataSources
import UIKit

struct BleCellSectionModel {
  public enum CellSectionType: String {
    case connected = "connected"
    case scanned = "scaned"
    case `default` = "default"
  }

  var sectionType: CellSectionType
  var items: [BleItemModel]
}

extension BleCellSectionModel: SectionModelType {
  typealias Item = BleItemModel

  init(original: BleCellSectionModel, items: [BleItemModel]) {
    self = original
    self.items = items
  }
}
