//
//  SettingCellSectionModel.swift
//  Hub
//
//  Created by ncn on 2023/04/20.
//

import RxDataSources
import UIKit

public enum SettingType {
  case select
  case format
  case rename
  case wifi
  case fwUpdate
  case scUpdate
  case noti
  case initialize
  case factoryreset
  case sdcardformat // S1 이후 다른 format 메뉴는 back키 시 적용하고 sdcardformat은 바로 포맷 적용
  case date
  case category
  case web
  case safetyDB
}

public enum SettingViewType: String {
  case sheetSelect
  case sheetSelectNoValue
  case sheetInfo
  case onOffSwitch
  case textField
}

public enum FileSettingType: Int {
  case None = 0
  case Reboot = 1
  case Format = 2
  case ResetNetwork = 3
}

public class SettingCellSectionModel {
  public enum SettingCellSectionType: String {
    case camera
    case parkingRecording
    case system
    
    //뭐에쓰는건지 확인 필요
    case dashcam
    case cloud
    
    public var name: String {
      switch self {
      case .camera: L.camera_system.localized
      case .parkingRecording: L.parking_recording_system.localized
      case .system: L.setting_system.localized
      case .dashcam: L.setting_menu_wifi_title.localized
      case .cloud: L.setting_menu_cloud_title.localized
      }
    }
  }
  
  public var sectionType: SettingCellSectionType = .camera
  public var isFold: Bool
  public var items: [SettingCellPresentable] = []
  //  public var image: String?
  public var desc: String?
  
  public init(
    sectionType: SettingCellSectionType,
    isFold: Bool = false,
    items: [SettingCellPresentable],
    desc: String? = nil
  ) {
    self.sectionType = sectionType
    self.isFold = isFold
    self.items = items
    self.desc = desc
  }
}
//  public enum SettingCellSectionType: String {
//    case record
//    case event
//    case sound
//    case time
//    case memory
//    case system
//
//    case account
//    case serviceAlert
//    //case eventAlert = "충돌 감지 이벤트 알림 설정"
//    case recordClould
//    case version
//
//    case daschcam
//    case cloud
//
//    public var name: String {
//      switch self {
//      case .record: L.setting_record.localized
//      case .event: L.setting_event.localized
//      case .sound: L.setting_sound.localized
//      case .time: L.setting_time.localized
//      case .memory: L.setting_memory.localized
//      case .system: L.setting_system.localized
//
//      case .account: L.setting_cloud_item_account.localized
//      case .serviceAlert: L.setting_cloud_item_service.localized
//      case .recordClould: L.setting_cloud_item_Manual_recording_cloud_storage.localized
//      case .version: L.setting_cloud_item_app_info.localized
//
//      case .daschcam: L.setting_menu_wifi_title.localized
//      case .cloud: L.setting_menu_cloud_title.localized
//      }
//    }
//  }

//  public var sectionType: SettingCellSectionType = .record
//  public var isFold: Bool
//  public var items: [SettingCellModel] = []
////  public var image: String?
//  public var desc: String?
//
//  public init(
//    sectionType: SettingCellSectionType,
//    isFold: Bool = false,
//    items: [SettingCellModel],
//    desc: String? = nil
//  ) {
//    self.sectionType = sectionType
//    self.isFold = isFold
//    self.items = items
//    self.image = getImage(type: sectionType)
//    self.desc = desc
//  }

//  public func getImage(type: SettingCellSectionType) -> String {
//    var name: String
//    switch type {
//    case .record:
//      name = "icon_setting01"
//      break
//    case .event:
//      name = "icon_setting02"
//      break
//    case .sound:
//      name = "icon_setting03"
//      break
//    case .time:
//      name = "icon_setting04"
//      break
//    case .memory:
//      name = "icon_setting05"
//      break
//    case .system:
//      name = "icon_setting06"
//      break
//    case .account:
//      name = "icon_setting_id"
//      break
//    case .serviceAlert:
//      name = "icon_setting_alart"
//      break
//    case .recordClould:
//      name = "icon_setting_cloud"
//      break
//    case .version:
//      name = "icon_setting_app"
//      break
//    default:
//      name = ""
//    }
//
//    return name
//  }
//}
