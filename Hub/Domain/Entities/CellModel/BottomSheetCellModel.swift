//
//  BottomSheetCellModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 5/21/24.
//

struct BottomSheetModel {
  let title: String
  var subTitle: String? = nil
  let cellModel: [SheetHistoryCellModel]
}
enum HistoryCellState {
  case Normal
  case Delete
  //  case Select
  //  case DeSelect
}
struct SheetHistoryCellModel: Equatable {
  let ssid: String
  let macAddress: String?
}
