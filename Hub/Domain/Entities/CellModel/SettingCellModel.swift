//
//  SettingCellModel.swift
//  Hub
//
//  Created by ncn on 11/28/24.
//
import Foundation
// generate protocol

public protocol SettingCellPresentable  {
  var title: String { get }
  var subTitle: String? { get }
  var hasSubKeys: Bool { get }
  var key: String { get }
  var rowIntValue: Int? { get set }
  var rowStringValue: String? { get set }
  var value: String { get set }
  var selectionValues: [String] { get }
  var type: SettingType { get }
  var viewType: SettingViewType { get }
  var enable: Bool { get set }
  var resetType: FileSettingType { get }
}

public struct SettingCellModel: SettingCellPresentable {
  public let title: String
  public var subTitle: String? = nil
  public var hasSubKeys: Bool = false
  public let key: String
  public var rowIntValue: Int? = nil
  public var rowStringValue: String? = nil
  public var value: String
  public let selectionValues: [String]
  public var type: SettingType = .select
  public var viewType: SettingViewType = .sheetSelect
  public var enable: Bool = true
  public var resetType: FileSettingType = .None

  public init(
    title: String,
    subTitle: String? = nil,
    key: String,
    rowIntValue: Int? = nil,
    rowStringValue: String? = nil,
    value: String,
    selectionValues: [String],
    type: SettingType = .select,
    viewType: SettingViewType,
    enable: Bool = true,
    resetType: FileSettingType = .None
  ) {
    self.title = title
    self.subTitle = subTitle
    self.key = key
    self.rowIntValue = rowIntValue
    self.rowStringValue = rowStringValue
    self.value = value
    self.selectionValues = selectionValues
    self.type = type
    self.viewType = viewType
    self.enable = enable
    self.resetType = resetType
  }
}

public struct SettingRectypeCellModel: SettingCellPresentable {
  public let title: String
  public var subTitle: String? = nil
  public var hasSubKeys: Bool
  public let key: String
  public var subKey: String
  public var rowIntValue: Int? = nil
  public var subRowIntValue: Int
  public var rowStringValue: String? = nil
  public var value: String
  public let selectionValues: [String]
  public var type: SettingType = .select
  public var viewType: SettingViewType = .sheetSelect
  public var enable: Bool = true
  public var resetType: FileSettingType = .None

  public init(
    title: String,
    subTitle: String? = nil,
    hasSubKeys: Bool,
    key: String,
    subKey: String,
    rowIntValue: Int? = nil,
    subRowIntValue: Int,
    rowStringValue: String? = nil,
    value: String,
    selectionValues: [String],
    timelapseSelectionValues: [String],
    type: SettingType = .select,
    viewType: SettingViewType,
    enable: Bool = true,
    resetType: FileSettingType = .None
  ) {
    self.title = title
    self.subTitle = subTitle
    self.hasSubKeys = hasSubKeys
    self.key = key
    self.subKey = subKey
    self.rowIntValue = rowIntValue
    self.subRowIntValue = subRowIntValue
    self.rowStringValue = rowStringValue
    self.value = value
    self.selectionValues = selectionValues
    self.type = type
    self.viewType = viewType
    self.enable = enable
    self.resetType = resetType
  }
}

public struct SettingHdrCellModel: SettingCellPresentable {
  public let title: String
  public var subTitle: String? = nil
  public let key: String
  public var hasSubKeys: Bool = true
  public var subKeys: [String]? = nil
  public var rowIntValue: Int? = nil
  public var subRowIntValues: [Int]
  public var rowStringValue: String? = nil
  public var value: String
  public let selectionValues: [String]
  public var hdrAlwaysOnSelectionValues: [String]
  public var hdrSunriseSelectionValues: [String]
  public var hdrSunsetSelectionValues: [String]
  public var type: SettingType = .select
  public var viewType: SettingViewType = .sheetSelect
  public var enable: Bool = true
  public var resetType: FileSettingType = .None

  public init(
    title: String,
    subTitle: String? = nil,
    key: String,
    subKeys: [String]? = nil,
    rowIntValue: Int? = nil,
    subRowIntValues: [Int],
    rowStringValue: String? = nil,
    value: String,
    selectionValues: [String],
    hdrAlwaysOnSelectionValues: [String],
    hdrSunriseSelectionValues: [String],
    hdrSunsetSelectionValues: [String],
    type: SettingType = .select,
    viewType: SettingViewType,
    enable: Bool = true,
    resetType: FileSettingType = .None
  ) {
    self.title = title
    self.subTitle = subTitle
    self.key = key
    self.subKeys = subKeys
    self.rowIntValue = rowIntValue
    self.subRowIntValues = subRowIntValues
    self.rowStringValue = rowStringValue
    self.value = value
    self.selectionValues = selectionValues
    self.hdrAlwaysOnSelectionValues = hdrAlwaysOnSelectionValues
    self.hdrSunriseSelectionValues = hdrSunriseSelectionValues
    self.hdrSunsetSelectionValues = hdrSunsetSelectionValues
    self.type = type
    self.viewType = viewType
    self.enable = enable
    self.resetType = resetType
  }
}

public struct SettingRotateCellModel: SettingCellPresentable {
  public let title: String
  public var subTitle: String? = nil
  public var hasSubKeys: Bool
  public let key: String
  public var subKeys: [String]
  public var rowIntValue: Int? = nil
  public var subRowIntValue: [Int]
  public var rowStringValue: String? = nil
  public var value: String
  public let selectionValues: [String]
  public var type: SettingType = .select
  public var viewType: SettingViewType = .sheetSelect
  public var enable: Bool = true
  public var resetType: FileSettingType = .None

  public init(
    title: String,
    subTitle: String? = nil,
    hasSubKeys: Bool,
    key: String,
    subKeys: [String],
    rowIntValue: Int? = nil,
    subRowIntValue: [Int],
    rowStringValue: String? = nil,
    value: String,
    selectionValues: [String],
    type: SettingType = .select,
    viewType: SettingViewType,
    enable: Bool = true,
    resetType: FileSettingType = .None
  ) {
    self.title = title
    self.subTitle = subTitle
    self.hasSubKeys = hasSubKeys
    self.key = key
    self.subKeys = subKeys
    self.rowIntValue = rowIntValue
    self.subRowIntValue = subRowIntValue
    self.rowStringValue = rowStringValue
    self.value = value
    self.selectionValues = selectionValues
    self.type = type
    self.viewType = viewType
    self.enable = enable
    self.resetType = resetType
  }
}
