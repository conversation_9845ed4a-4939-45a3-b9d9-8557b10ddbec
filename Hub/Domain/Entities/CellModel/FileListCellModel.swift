//
//  FileListCellModel.swift
//  Hub
//
//  Created by ncn on 2023/05/22.
//

import UIKit

public enum FileListCellType: Int {
  case file = 0
  case screenshot
  case cloud
  case wifi
  case dummy
  case description
}

public class FileListCellModel: Equatable {
  public static func == (lhs: FileListCellModel, rhs: FileListCellModel) -> Bool {
    return (lhs.fileName == rhs.fileName) && (lhs.filePath == rhs.filePath)
  }

  var type: FileListCellType
  var id: Int?
  var serial: String?
  var thumbimage: UIImage?
  var fileName: String
  let videoUrl: String?
  let fileSize: Double
  var path: String?  // Stream Path
  var filePath: URL?  // Local Path
  var imageUrl: String?
  var inDtm: String?
  var date = Date()
  var videoTime: String?
  var indexPath: IndexPath?
  var channelbits: Int
  var vodType: Int

  init(
    type: FileListCellType,
    id: Int? = nil,
    serial: String? = nil,
    image: UIImage? = nil,
    fileName: String,
    videoUrl: String? = nil,
    fileSize: Double = 0.0,
    path: String? = nil,
    filePath: URL? = nil,
    imageUrl: String? = nil,
    inDtm: String? = nil,
    date: Date = Date(),
    videoTime: String? = nil,
    indexPath: IndexPath? = nil,
    channelbits: Int? = 1
  ) {
    self.type = type
    self.id = id
    self.serial = serial
    
    if image != nil {
      self.thumbimage = image
    }
    
    self.fileName = fileName
    self.videoUrl = videoUrl
    self.fileSize = fileSize
    self.path = path
    self.filePath = filePath
    self.imageUrl = imageUrl
    self.inDtm = inDtm
    self.date = date
    self.videoTime = videoTime
    self.indexPath = indexPath
    self.channelbits = channelbits ?? 7

    let channelCountString = fileName.components(separatedBy: "_")[safe: 3]?.components(
      separatedBy: "."
    ).first

    let frontRearString = fileName.components(separatedBy: "_").last?.components(
      separatedBy: "."
    ).first ?? ""

#if HUB
    var channelCount = 1
    if frontRearString.contains("F") || frontRearString.contains("R") {
      channelCount = FFMpegUtils.getVideoChannelCount(path: filePath)
    } else {
      channelCount = Int(channelCountString ?? "1") ?? 1  // 파일이름 뒤의 숫자가 채널갯수
    }
    self.channel = channelCount
#endif

//    if self.videoTime == nil || self.videoTime == "0" {
////      FFMpegUtils.clearDurationCache()
//      fLogger.info("fileName: \(fileName), path: \(path ?? "")")
//      let pathUrl = URL(fileURLWithPath: "\(path ?? "")/\(fileName)")
//    }


    // FIXME:  bookmark file 은 "001_20250702_134921_INF_F_N.jpg" 이런 형식
    // TODO: `HubNameGenerator` 에 통합
    let array = fileName.components(separatedBy: "_")
    let dateString: String
    if array.count > 5 && array[0].count == 3 {
      dateString = "\(array[safe: 1] ?? "")\(array[safe: 2] ?? "")".trim().replacingOccurrences(of: ".", with: "")
    } else {
      dateString = array.prefix(2).joined().replacingOccurrences(of: ".", with: "")
    }

    if let d = dateString.toDate(format: "yyyyMMddHHmmss") {
      self.date = d
    }

    let vodTypeString = fileName.components(separatedBy: "_")[safe: 2]
    if vodTypeString == "INF" {
      self.vodType = 0
    } else if vodTypeString == "EVT" {
      self.vodType = 1
    } else if vodTypeString == "PRK" {
      self.vodType = 2
    } else if vodTypeString == "USR" {
      self.vodType = 3
    } else {
      self.vodType = -1
    }

  }
}

struct VodThumbnailModel {
  let path: String
  let items: [FileListCellModel]
}
