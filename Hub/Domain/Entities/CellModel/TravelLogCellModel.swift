//
//  TravelLogCellModel.swift
//  Hub
//
//  Created by ncn on 2023/06/27.
//

import UIKit

public struct TravelLogCellModel {
  let ud_d_idx: Int
  let date: String
  let startTm: String
  let endTm: String
  let type: Int
  let distance: Int
  let avgSpeed: Int
  let maxSpeed: Int
  let evtNum: Int
  let motNum: Int

  init(
    ud_d_idx: Int, date: String, startTm: String, endTm: String, type: Int, distance: Int,
    avgSpeed: Int, maxSpeed: Int, evtNum: Int, motNum: Int
  ) {
    self.ud_d_idx = ud_d_idx
    self.date = date
    self.startTm = startTm
    self.endTm = endTm
    self.type = type
    self.distance = distance
    self.avgSpeed = avgSpeed
    self.maxSpeed = maxSpeed
    self.evtNum = evtNum
    self.motNum = motNum
  }
  init(model: TravelLogModel, startDate: Date) {
    self.ud_d_idx = -1
    self.date = startDate.toString(format: "yyyy/MM/dd")
    self.startTm = model.start.toTimeString(
      format: "HH:mm:ss",
      timezone: Current.timezone
    )
    self.endTm = model.end.toTimeString(
      format: "HH:mm:ss",
      timezone: Current.timezone
    )
    self.type = model.type
    self.distance = model.distance
    self.avgSpeed = model.avgSpeed
    self.maxSpeed = model.maxSpeed
    self.evtNum = model.evtNum
    self.motNum = model.motNum
  }
}
