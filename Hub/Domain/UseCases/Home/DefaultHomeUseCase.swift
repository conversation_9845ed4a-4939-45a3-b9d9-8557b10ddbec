//
//  DefaultModeUseCase.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import UIKit

final class DefaultHomeUseCase: HomeUseCase {
  private let socketRepository: CommandRepository

  init(socketRepository: CommandRepository) {
    self.socketRepository = socketRepository
  }
}

extension DefaultHomeUseCase {
  func connect(completion: @escaping WebSocketCallback) {
    socketRepository.connect(completion: completion)
  }

  func send(
    to command: GetInformation.Send,
    completion: @escaping (HomeDashcamModel?, Error?) -> Void
  ) {
    socketRepository.send(to: command) { response, error in
      if let ret = response {
        let obj = ret.toDomain()
        completion(obj, nil)
      } else {
        completion(nil, error)
      }
    }
  }

  public func sendGetInfo(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command) { response, error in
      if let ret = response {
        completion(ret, nil)
      } else {
        completion(nil, error)
      }
    }
  }
  
  func sendGetInitInfo(
    to command: GetInitInfo.Send,
    completion: @escaping (GetInitInfo.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command) { response, error in
      if let ret = response {
        completion(ret, nil)
      } else {
        completion(nil, error)
      }
    }
  }


  func send(
    to command: GetConfiguration.Send,
    completion: @escaping (GetConfiguration.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: GetStatus.Send,
    completion: @escaping (HomeDashcamModel?, Error?) -> Void
  ) {
    socketRepository.send(to: command) { response, error in
      if let ret = response {
        AppManager.shared.statusInfo = ret.getstatus
        let obj = ret.toDomain()
        completion(obj, nil)
      } else {
        completion(nil, error)
      }
    }
  }

  func send(
    to command: SetFastConfiguration.Send,
    completion: @escaping (SetFastConfiguration.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: GetFastConfiguration.Send,
    completion: @escaping (GetFastConfiguration.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: GetInitInfo.Send,
    completion: @escaping (GetInitInfo.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: GetLiveInfoModel.Send,
    completion: @escaping (Result<GetLiveInfoModel.Response, Error>) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: VerifyPasswordModel.Send,
    completion: @escaping (VerifyPasswordModel.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
}
