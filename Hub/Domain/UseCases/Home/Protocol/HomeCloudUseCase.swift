//
//  HomeCloudUseCase.swift
//  Hub
//
//  Created by ncn on 2023/05/30.
//

import UIKit

protocol HomeCloudUseCase: AnyObject {
  func request(
    picList request: PicList.Request,
    completion: @escaping (Bool, PicList.Response?, Error?) -> Void)
  func request(
    mdDeviceDtl request: MdDeviceListdtl.Request,
    completion: @escaping (Bool, MdDeviceListdtl.Response?, Error?) -> Void)
  func request(
    uainfo request: Uainfo.Request,
    completion: @escaping (Bool, Uainfo.Response?, Error?) -> Void)

  // power Off
  func request(
    dvcCommand request: MdDeviceDvcCommand.Request,
    completion: @escaping (Result<MdDeviceDvcCommand.Response, Error>) -> Void)

  // ws connect
  func request(
    wsconnectOne request: [String: String]?,
    completion: @escaping (Bool, Data?, Error?) -> Void)
  func request(
    wsconnectZero request: [String: String]?,
    completion: @escaping (Bool, Data?, Error?) -> Void)

  // history
  func request(
    lastDrive request: LastDrive.Request,
    completion: @escaping (Bool, LastDrive.Response?, Error?) -> Void)
  func request(
    eventDayList request: EventList.Request,
    completion: @escaping (Bool, EventList.Response?, Error?) -> Void)
  func request(
    updateDB request: UpdateDBModel.Request,
    completion: @escaping (Result<UpdateDBModel.Response?, Error>) -> Void)

  // notice
  func request(
    unread request: NoticeUnreadCountModel.Request,
    completion: @escaping (Result<NoticeUnreadCountModel.Response, Error>) -> Void
  )
}
