//
//  ModeUseCase.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import Foundation

protocol HomeUseCase: AnyObject {
  func connect(completion: @escaping WebSocketCallback)

  func send(
    to command: GetInformation.Send,
    completion: @escaping (HomeDashcamModel?, Error?) -> Void
  )

  func sendGetInfo(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  )

  func sendGetInitInfo(
    to command: GetInitInfo.Send,
    completion: @escaping (GetInitInfo.Response?, Error?) -> Void
  )

  func send(
    to command: GetConfiguration.Send,
    completion: @escaping (GetConfiguration.Response?, Error?) -> Void
  )

  func send(
    to command: SetFastConfiguration.Send,
    completion: @escaping (SetFastConfiguration.Response?, Error?) -> Void
  )

  func send(
    to command: GetFastConfiguration.Send,
    completion: @escaping (GetFastConfiguration.Response?, Error?) -> Void
  )

  func send(
    to command: GetStatus.Send,
    completion: @escaping (HomeDashcamModel?, Error?) -> Void
  )

//  func send(
//    to command: GetInitInfo.Send,
//    completion: @escaping (GetInitInfo.Response?, Error?) -> Void
//  )
//
  func send(
    to command: GetLiveInfoModel.Send,
    completion: @escaping (Result<GetLiveInfoModel.Response, Error>) -> Void
  )

  func send(
    to command: VerifyPasswordModel.Send,
    completion: @escaping (VerifyPasswordModel.Response?, Error?) -> Void
  )
}
