//
//  DefaultStreamUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/24.
//

import UIKit

final class DefaultStreamUseCase: StreamUseCase {
  private let socketRepository: CommandRepository

  init(socketRepository: CommandRepository) {
    self.socketRepository = socketRepository
  }
}

extension DefaultStreamUseCase {
  func connect(completion: @escaping WebSocketCallback) {
    socketRepository.connect(completion: completion)
  }

  func send(
    to command: StartLiveView.Send,
    completion: @escaping (StartLiveView.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: StopLiveView.Send,
    completion: @escaping (StopLiveView.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
  
  
  func send(
    to command: ChangeLiveView.Send,
    completion: @escaping (Result<ChangeLiveView.Response, Error>) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
}

// MARK: - Vod
extension DefaultStreamUseCase {
  func send(
    to command: VodStart.Send,
    completion: @escaping (VodStart.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }

  func send(
    to command: VodStop.Send,
    completion: @escaping (VodStop.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
  func send(
    to command: VodChange.Send,
    completion: @escaping (VodChange.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
}
