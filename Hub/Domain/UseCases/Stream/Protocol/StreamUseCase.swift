//
//  StreamUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/24.
//

import UIKit

protocol StreamUseCase: AnyObject {
  func connect(completion: @escaping WebSocketCallback)

  // MARK: - Live
  func send(
    to command: StartLiveView.Send,
    completion: @escaping (StartLiveView.Response?, Error?) -> Void
  )
  
  func send(
    to command: StopLiveView.Send,
    completion: @escaping (StopLiveView.Response?, Error?) -> Void
  )
  
  func send(
    to command: ChangeLiveView.Send,
    completion: @escaping (Result<ChangeLiveView.Response, Error>) -> Void
  )

  // MARK: - Vod
  func send(
    to command: VodStart.Send,
    completion: @escaping (VodStart.Response?, Error?) -> Void)
  func send(
    to command: VodStop.Send,
    completion: @escaping (VodStop.Response?, Error?) -> Void)
  func send(
    to command: VodChange.Send,
    completion: @escaping (VodChange.Response?, Error?) -> Void)
}
