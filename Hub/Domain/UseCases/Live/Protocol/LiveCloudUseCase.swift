//
//  LiveCloudUseCase.swift
//  Hub
//
//  Created by ncn on 2023/07/14.
//

import Foundation

protocol LiveCloudUseCase: AnyObject {
  func request(
    lastDrive request: LastDrive.Request,
    completion: @escaping (Bool, LastDrive.Response?, Error?) -> Void)
  func request(
    eventDayList request: EventList.Request,
    completion: @escaping (Bool, EventList.Response?, Error?) -> Void)
  func request(
    completion: @escaping (Result<LiveCapacity, Error>) -> Void)
}
