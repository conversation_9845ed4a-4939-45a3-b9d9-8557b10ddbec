//
//  DefaultLiveUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/23.
//

import Foundation

final class DefaultLiveUseCase: LiveUseCase {
  private let socketRepository: CommandRepository

  init(socketRepository: CommandRepository) {
    self.socketRepository = socketRepository
  }
}

extension DefaultLiveUseCase {
  func connect(completion: @escaping WebSocketCallback) {
    socketRepository.connect(completion: completion)
  }

  func send(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  ) {
    socketRepository.send(to: command, completion: completion)
  }
}
