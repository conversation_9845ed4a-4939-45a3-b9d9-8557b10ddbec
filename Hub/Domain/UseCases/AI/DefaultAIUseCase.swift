//
//  DefaultAIUseCase.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//
import Foundation
import Alamofire

final class DefaultAIUseCase: AIUseCase {
  private var currentAccessKey: String?
  private var currentTaskId: Int64?

  // MARK: - Access Key Management
  func createAccessKey(
    version: AiVersion = .v1,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "access-key/new", version: version)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    AF.request(url, method: .get)
      .validate()
      .responseString { response in
        switch response.result {
        case .success(let accessKey):
          self.currentAccessKey = accessKey
          completion(.success(accessKey))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func verifyAccessKey(
    accessKey: String,
    completion: @escaping (Result<Bool, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "access-key/verify", version: .v1)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["accessKey": accessKey]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let isValid = try JSONDecoder().decode(Bool.self, from: data)
            completion(.success(isValid))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func invalidateAccessKey(
    accessKey: String,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "access-key/invalidate", version: .v1)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["accessKey": accessKey]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .response { response in
        switch response.result {
        case .success:
          completion(.success(()))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  // MARK: - AI Inference
  func restoreImage(
    version: AiVersion = .v1,
    imageData: Data,
    accessKey: String,
    completion: @escaping (Result<Data, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "restore", version: version)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }
    let fileName = AIServiceManager.shared.currentFileName ?? "image.jpg"
    AF.upload(multipartFormData: { multipartFormData in
      multipartFormData.append(imageData, withName: "image", fileName: fileName, mimeType: "image/jpeg")
      multipartFormData.append(accessKey.data(using: .utf8)!, withName: "accessKey")
    }, to: url, method: .post)
    .validate()
    .responseData { response in
      switch response.result {
      case .success(let data):
        completion(.success(data))
      case .failure(let error):
        completion(.failure(error))
      }
    }
  }

  func restoreImageSave(
    imageData: Data,
    accessKey: String,
    uploadProgress: ((Double) -> Void)? = nil,
    completion: @escaping (Result<Int64, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "restore", version: .v3)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }
    let fileName = AIServiceManager.shared.currentFileName ?? "image.jpg"
    AF.upload(multipartFormData: { multipartFormData in
      multipartFormData.append(imageData, withName: "image", fileName: fileName, mimeType: "image/jpeg")
      multipartFormData.append(accessKey.data(using: .utf8)!, withName: "accessKey", mimeType: "text/plain")
    }, to: url, method: .post)
    .validate()
    .uploadProgress { progress in
      uploadProgress?(progress.fractionCompleted)
      mLogger.debug("restoreImageSave Upload Progress: \(progress.fractionCompleted * 100)%")
    }
    .responseData { response in
      switch response.result {
      case .success(let data):
        do {
          let taskId = try JSONDecoder().decode(Int64.self, from: data)
          self.currentTaskId = taskId
          completion(.success(taskId))
        } catch {
          completion(.failure(AIError.parsingError))
        }
      case .failure(let error):
        completion(.failure(error))
      }
    }
  }

  func deIdentifyVideo(
    version: AiVersion = .v1,
    videoData: Data,
    fileName: String,
    accessKey: String,
    uploadProgress: ((Double) -> Void)? = nil,
    completion: @escaping (Result<Int64, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "deIdentifyVideo", version: version)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    AF.upload(multipartFormData: { multipartFormData in
      multipartFormData.append(videoData, withName: "video", fileName: fileName, mimeType: "video/mp4")
      multipartFormData.append(accessKey.data(using: .utf8)!, withName: "accessKey", mimeType: "text/plain")
    }, to: url, method: .post)
    .validate()
    .uploadProgress { progress in
      uploadProgress?(progress.fractionCompleted)
//      mLogger.debug("deIdentifyVideo Upload Progress: \(progress.fractionCompleted * 100)%")
    }
    .responseData { response in
      switch response.result {
      case .success(let data):
        do {
          let taskId = try JSONDecoder().decode(Int64.self, from: data)
          self.currentTaskId = taskId
          completion(.success(taskId))
        } catch {
          completion(.failure(AIError.parsingError))
        }
      case .failure(let error):
        completion(.failure(error))
      }
    }
  }

  func statusVideo(
    version: AiVersion = .v1,
    taskId: Int64,
    completion: @escaping (Result<TaskStatus, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "statusVideo", version: version)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["taskId": taskId]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .responseString { response in
        switch response.result {
        case .success(let statusString):
          if let status = TaskStatus(rawValue: statusString) {
            completion(.success(status))
          } else {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func statusVideoWithDownloadUrl(
    taskId: Int64,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "statusVideo", version: .v2)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["taskId": taskId]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let statusLink = try JSONDecoder().decode(StatusLink.self, from: data)
            completion(.success(statusLink))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func statusWithDownloadUrl(
    taskId: Int64,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "status", version: .v2)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["taskId": taskId]

    AF.request(url, method: .get, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let statusLink = try JSONDecoder().decode(StatusLink.self, from: data)
            completion(.success(statusLink))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func statusWithDownloadUrlV3(
    uuid: String,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "status", version: .v3)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["uuid": uuid]

    AF.request(url, method: .get, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let statusLink = try JSONDecoder().decode(StatusLink.self, from: data)
            completion(.success(statusLink))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }
  func downloadVideo(
    taskId: Int64,
    accessKey: String,
    completion: @escaping (Result<Data, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "downloadVideo", version: .v1)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = [
      "taskId": taskId,
      "accessKey": accessKey
    ]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          completion(.success(data))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func download(
    url: String,
    download: @escaping (Double) -> Void,
    completion: @escaping (Result<Data, Error>) -> Void
  ) {
    AF.request(url, method: .get, interceptor: NCRequestInterceptor())
      .downloadProgress { progress in
        Log.message(to: progress.fractionCompleted)
        download(progress.fractionCompleted)
      }
      .responseData(completionHandler: { response in
        switch response.result {
        case .success(let data):
          completion(.success(data))
        case .failure(let error):
          completion(.failure(error))
        }
      })
  }
  
  func setResultReceiveMethod(
    request: RecvResultRequest,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "recvResult", version: .v2)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = [
      "taskId": request.taskId,
      "resultRecv": request.resultRecv,
      "os": request.os,
      "pushToken": request.pushToken,
      "lang": request.lang
    ]

    AF.request(url, method: .put, parameters: parameters)
      .validate()
      .response { response in
        switch response.result {
        case .success:
          completion(.success(()))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  // MARK: - V3 Video De-identification
  func getUploadUrl(
    fileName: String,
    completion: @escaping (Result<UploadUrlResponse, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "upload/url", version: .v3)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = ["fileName": fileName]

    AF.request(url, method: .get, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let uploadResponse = try JSONDecoder().decode(UploadUrlResponse.self, from: data)
            completion(.success(uploadResponse))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func uploadVideoToUrl(
    url: String,
    videoData: Data,
    uploadProgress: ((Double) -> Void)? = nil,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    guard let uploadUrl = URL(string: url) else {
      return completion(.failure(AIError.invalidURL))
    }

    var request = URLRequest(url: uploadUrl)
    request.httpMethod = "PUT"
    request.setValue("application/octet-stream", forHTTPHeaderField: "Content-Type")
    request.httpBody = videoData

    AF.request(request)
      .validate()
      .uploadProgress { progress in
        uploadProgress?(progress.fractionCompleted)
      }
      .response { response in
        switch response.result {
        case .success:
          completion(.success(()))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  func completeUpload(
    request: RecvResultRequestV3,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "upload/complete", version: .v3)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = [
      "uuid": request.uuid,
      "resultRecv": request.resultRecv,
      "os": request.os,
      "pushToken": request.pushToken,
      "lang": request.lang
    ]

    AF.request(url, method: .put, parameters: parameters)
      .validate()
      .response { response in
        switch response.result {
        case .success:
          completion(.success(()))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  // MARK: - Server Status
  func checkCondition(
    version: AiVersion = .v1,
    taskType: TaskType? = nil,
    completion: @escaping (Result<ConditionModel, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "condition", version: version)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    var parameters: Parameters = [:]
    if let taskType = taskType {
      parameters["task"] = taskType.rawValue
    }

    AF.request(url, method: .get, parameters: parameters)
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let condition = try JSONDecoder().decode(ConditionModel.self, from: data)
            completion(.success(condition))
          } catch {
            completion(.failure(AIError.parsingError))
          }
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  // MARK: - Push Notification
  func sendPush(
    request: PushRequest,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "push", version: .v1)
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    let parameters: Parameters = [
      "os": request.os,
      "pushToken": request.pushToken
    ]

    AF.request(url, method: .post, parameters: parameters)
      .validate()
      .responseString { response in
        switch response.result {
        case .success(let result):
          completion(.success(result))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }

  // MARK: - Health Check
  func healthCheck(
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    let urlString = UrlList.AiUrl(path: "", version: .v1).replacingOccurrences(of: "/v1/", with: "/")
    guard let url = URL(string: urlString) else {
      return completion(.failure(AIError.invalidURL))
    }

    AF.request(url, method: .get)
      .validate()
      .responseString { response in
        switch response.result {
        case .success(let result):
          completion(.success(result))
        case .failure(let error):
          completion(.failure(error))
        }
      }
  }
}
