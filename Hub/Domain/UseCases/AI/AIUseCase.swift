//
//  AIUseCase.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//
import Foundation

protocol AIUseCase: AnyObject {
  // MARK: - Access Key Management
  func createAccessKey(
    version: AiVersion,
    completion: @escaping (Result<String, Error>) -> Void
  )

  func verifyAccessKey(
    accessKey: String,
    completion: @escaping (Result<Bool, Error>) -> Void
  )

  func invalidateAccessKey(
    accessKey: String,
    completion: @escaping (Result<Void, Error>) -> Void
  )

  // MARK: - AI Inference
  func restoreImage(
    version: AiVersion,
    imageData: Data,
    accessKey: String,
    completion: @escaping (Result<Data, Error>) -> Void
  )

  func restoreImageSave(
    imageData: Data,
    accessKey: String,
    uploadProgress: ((Double) -> Void)?,
    completion: @escaping (Result<Int64, Error>) -> Void
  )

  func deIdentifyVideo(
    version: AiVersion,
    videoData: Data,
    fileName: String,
    accessKey: String,
    uploadProgress: ((Double) -> Void)?,
    completion: @escaping (Result<Int64, Error>) -> Void
  )

  func statusVideo(
    version: AiVersion,
    taskId: Int64,
    completion: @escaping (Result<TaskStatus, Error>) -> Void
  )

  func statusVideoWithDownloadUrl(
    taskId: Int64,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  )

  func statusWithDownloadUrl(
    taskId: Int64,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  )

  func statusWithDownloadUrlV3(
    uuid: String,
    completion: @escaping (Result<StatusLink, Error>) -> Void
  )

  func downloadVideo(
    taskId: Int64,
    accessKey: String,
    completion: @escaping (Result<Data, Error>) -> Void
  )

  func download(
    url: String,
    download: @escaping (Double) -> Void,
    completion: @escaping (Result<Data, Error>) -> Void
  )

  func setResultReceiveMethod(
    request: RecvResultRequest,
    completion: @escaping (Result<Void, Error>) -> Void
  )

  // MARK: - V3 Video De-identification
  func getUploadUrl(
    fileName: String,
    completion: @escaping (Result<UploadUrlResponse, Error>) -> Void
  )

  func uploadVideoToUrl(
    url: String,
    videoData: Data,
    uploadProgress: ((Double) -> Void)?,
    completion: @escaping (Result<Void, Error>) -> Void
  )

  func completeUpload(
    request: RecvResultRequestV3,
    completion: @escaping (Result<Void, Error>) -> Void
  )

  // MARK: - Server Status
  func checkCondition(
    version: AiVersion,
    taskType: TaskType?,
    completion: @escaping (Result<ConditionModel, Error>) -> Void
  )

  // MARK: - Push Notification
  func sendPush(
    request: PushRequest,
    completion: @escaping (Result<String, Error>) -> Void
  )

  // MARK: - Health Check
  func healthCheck(
    completion: @escaping (Result<String, Error>) -> Void
  )
}

