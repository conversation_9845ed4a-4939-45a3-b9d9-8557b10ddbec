//
//  BleUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/08.
//

import CoreBluetooth
import RxSwift
import UIKit

protocol BleUseCase: AnyObject {
  var rxReadValue: PublishSubject<BLE.Response> { get set }
  func startScane(services: [CBUUID]?, completion: @escaping ([BleItemModel]?, Error?) -> Void)
  func stopScane()
  func restartBle()
  func close()
  func connect(model: DiscoverdModel, completion: @escaping (Bool, Bool, Bool, Error?) -> Void)
  func connect(uuid: String, completion: @escaping (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rror?) -> Void)
  func send(
    command: BLE.Send,
    completion: @escaping (BLE.Response) -> Void)
}
