//
//  DefaultBleUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/08.
//

import CoreBluetooth
import RxSwift
import UIKit

final class DefaultBleUseCase: BleUseCase {

  private let repository: BleRepository
  var rxReadValue = PublishSubject<BLE.Response>()

  init(repository: BleRepository) {
    self.repository = repository
  }

  func startScane(services: [CBUUID]?, completion: @escaping ([BleItemModel]?, Error?) -> Void) {
    repository.startScane(services: services, completion: completion)
  }

  func stopScane() {
    repository.stopScane()
  }

  func restartBle() {
    repository.restartBle()
  }

  func close() {
    repository.close()
  }

  func connect(model: DiscoverdModel, completion: @escaping (Bool, Bool, Bool, Error?) -> Void) {
    repository.connect(model: model, completion: completion) { [weak self] response in
      guard self != nil else { return }
      //self.rxReadValue.onNext(response)
    }
  }

  func connect(uuid: String, completion: @escaping (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>?) -> Void) {
    repository.connect(uuid: uuid, completion: completion) { [weak self] response in
      guard self != nil else { return }
      //self.rxReadValue.onNext(response)
    }
  }

  func send(
    command: BLE.Send,
    completion: @escaping (BLE.Response) -> Void
  ) {
    repository.send(command: command, completion: completion)
  }
}
