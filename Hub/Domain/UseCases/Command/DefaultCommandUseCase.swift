//
//  DefaultWebSocketUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import UIKit

final class DefaultCommandUseCase: CommandUseCase {
  private let repository: CommandRepository

  init(repository: CommandRepository) {
    self.repository = repository
  }
}

// MARK: - Connect
extension DefaultCommandUseCase {
  func connect(completion: @escaping WebSocketCallback) {
    repository.connect { reponse, error in
      Log.info(to: "\(String(describing: reponse)) error: \(String(describing: error))")
    }
  }
}

// MARK: - Info
extension DefaultCommandUseCase {
  func send(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  ) {
    repository.send(to: command, completion: completion)
  }

  func send(
    to command: SetInformationModel.Send,
    completion: @escaping (Result<SetInformationModel.Response, Error>) -> Void
  ) {
    repository.send(to: command, completion: completion)
  }

  func send(
    to firmwareFile: Data,
    completion: @escaping (Result<Void, Error>) -> Void
  ) {
    repository.send(to: firmwareFile, completion: completion)
  }
}

// MARK: - Vod
extension DefaultCommandUseCase {
  func send(
    to command: VodStart.Send,
    completion: @escaping (VodStart.Response?, Error?) -> Void
  ) {
    repository.send(to: command, completion: completion)
  }

  func send(
    to command: VodStop.Send,
    completion: @escaping (VodStop.Response?, Error?) -> Void
  ) {
    repository.send(to: command, completion: completion)
  }
}
