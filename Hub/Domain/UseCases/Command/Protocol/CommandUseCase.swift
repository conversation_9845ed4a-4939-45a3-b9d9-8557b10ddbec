//
//  WebSocketUseCase.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import UIKit

protocol CommandUseCase: AnyObject {
  func connect(completion: @escaping WebSocketCallback)

  func send(
    to command: GetInformation.Send,
    completion: @escaping (GetInformation.Response?, Error?) -> Void
  )
  
  func send(
    to command: SetInformationModel.Send,
    completion: @escaping (Result<SetInformationModel.Response, Error>) -> Void
  )

  func send(
    to firmwareFile: Data,
    completion: @escaping (Result<Void, Error>) -> Void
  )
}
