//
//  UdDeviceHistoryUseCase.swift
//  Hub
//
//  Created by ncn on 2023/06/23.
//

import UIKit

protocol UdDeviceHistoryUseCase: AnyObject {
  func request(
    calendar request: WebCalendar.Request,
    completion: @escaping (Bool, WebCalendar.Response?, Error?) -> Void)

  // MARK: - History chart
  func request(
    eventDay request: EventModel.Request,
    completion: @escaping (Bool, EventModel.Response?, Error?) -> Void)
  func request(
    eventWeek request: EventModel.Request,
    completion: @escaping (Bool, EventModel.Response?, Error?) -> Void)
  func request(
    eventMonth request: EventModel.Request,
    completion: @escaping (Bool, EventModel.Response?, Error?) -> Void)
  func request(
    eventYear request: EventModel.Request,
    completion: @escaping (Bool, EventModel.Response?, Error?) -> Void)

  // MARK: - History event list
  func request(
    eventDayList request: EventList.Request,
    completion: @escaping (Bo<PERSON>, EventList.Response?, Error?) -> Void)
  func request(
    eventWeekList request: EventList.Request,
    completion: @escaping (Bool, EventList.Response?, Error?) -> Void)
  func request(
    eventMonthList request: EventList.Request,
    completion: @escaping (Bool, EventList.Response?, Error?) -> Void)
  func request(
    eventYearList request: EventList.Request,
    completion: @escaping (Bool, EventList.Response?, Error?) -> Void)

  func request(
    lastDrive request: LastDrive.Request,
    completion: @escaping (Bool, LastDrive.Response?, Error?) -> Void)
}
