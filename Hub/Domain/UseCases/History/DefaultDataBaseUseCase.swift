//
//  DefaultHistoryUseCase.swift
//  Hub
//
//  Created by ncn on 2023/03/20.
//

import UIKit


final class DefaultDataBaseUseCase: DataBaseUseCase {
  private let repository: DataBaseRepository

  init(repository: DataBaseRepository) {
    self.repository = repository
  }

  func migration() -> Result<Bool, Error> {
    return repository.migration()
  }

  func deleteTravelLog(
    items: [TravelLogCellModel], completion: @escaping (Result<Bool, Error>) -> Void
  ) {
    repository.deleteTravelLog(items: items, completion: completion)
  }
}
