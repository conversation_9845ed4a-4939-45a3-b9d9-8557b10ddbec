//
//  DefaultWifiUseCase.swift
//  Hub
//
//  Created by ncn on 2022/12/09.
//

import Foundation

/*
  restful api ssid, pw
 */
final class DefaultWifiUseCase: WifiUseCase {
  private let repository: WifiRepository

  init(repository: WifiRepository) {
    self.repository = repository
  }

  func connect(
    toWifi param: Wifi.ConnectModel, reconnectCount: Int,
    completion: @escaping (Bool, Error?) -> Void
  ) {
    repository.connect(toWifi: param, reconnectCount: reconnectCount, completion: completion)
  }
}
