//
//  DefaultVodUseCase.swift
//  Hub
//
//  Created by ncn on 2023/05/24.
//

import UIKit

final class DefaultVodUseCase: VodUseCase {

  private let repository: VodRepository

  init(repository: VodRepository) {
    self.repository = repository
  }
}

extension DefaultVodUseCase {
  func disconnect() {
    repository.disconnect()
  }
  func connect(completion: @escaping (Result<[String: Any], Error>) -> Void) {
    repository.connect(completion: completion)
  }

  func send(getSerial: GetSerialCloud.Send, completion: @escaping (Result<String, Error>) -> Void) {
    repository.send(getSerial: getSerial, completion: completion)
  }
  func send(setCommand: SetCommandCloud.Send, completion: @escaping (Result<String, Error>) -> Void)
  {
    repository.send(setCommand: setCommand, completion: completion)
  }
  func send(
    getFileList: GetFileListCloud.Send,
    completion: @escaping (Result<GetFileListCloud.Response, Error>) -> Void
  ) {
    repository.send(getFileList: getFileList, completion: completion)
  }
  func send(
    getThumbnail: GetThumbnailCloud.Send,
    completion: @escaping (Result<GetThumbnailCloud.Response, Error>) -> Void
  ) {
    repository.send(getThumbnail: getThumbnail, completion: completion)
  }

  func send(
    uploadFile: UploadFileCloud.Send,
    completion: @escaping (Result<UploadFileCloud.Response, Error>) -> Void
  ) {
    repository.send(uploadFile: uploadFile, completion: completion)
  }
  func send(
    deleteFile: DeleteFileCloud.Send,
    completion: @escaping (Result<DeleteFileCloud.Response, Error>) -> Void
  ) {
    repository.send(deleteFile: deleteFile, completion: completion)
  }
}

extension DefaultVodUseCase {

  func request(
    deleteFile: CloudFileRemoveModel.Request,
    completion: @escaping (Result<String, Error>) -> Void
  ) {

    repository.request(deleteFile: deleteFile, completion: completion)
  }
  func request(
    toCloudFile: ToCloudFileModel.Request,
    completion: @escaping (Result<String, Error>) -> Void
  ) {
    repository.request(toCloudFile: toCloudFile, completion: completion)
  }

  func request(
    dvfList request: DvList.Request,
    completion: @escaping (Bool, DvList.Response?, Error?) -> Void
  ) {
    repository.request(dvList: request, completion: completion)
  }

  func request(
    cloudUsage request: CloudUsageModel.Request,
    completion: @escaping (Result<CloudUsageModel.Response?, Error>) -> Void
  ) {
    repository.request(cloudUsage: request, completion: completion)
  }
  
  func request(
    searchFile request: CloudSearchFileModel.Request,
    completion: @escaping (Result<CloudSearchFileModel.Response?, Error>) -> Void
  ){
    repository.request(searchFile: request, completion: completion)
  }
}

// MARK: Stream
extension DefaultVodUseCase {
  func request(
    getVod request: GetVod.Request,
    completion: @escaping (Bool, GetVod.Response?, Error?) -> Void
  ) {
    repository.request(getVod: request, completion: completion)
  }

  func request(
    videoRemove request: VideoRemove.Request,
    completion: @escaping (Bool, VideoRemove.Response?, Error?) -> Void
  ) {
    repository.request(videoRemove: request, completion: completion)
  }
}
