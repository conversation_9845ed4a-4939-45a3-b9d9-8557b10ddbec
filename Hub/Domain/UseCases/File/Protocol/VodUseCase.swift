//
//  VodUseCase.swift
//  Hub
//
//  Created by ncn on 2023/05/24.
//

import UIKit

protocol VodUseCase: AnyObject {
  func disconnect()
  func connect(completion: @escaping (Result<[String: Any], Error>) -> Void)
  func send(getSerial: GetSerialCloud.Send, completion: @escaping (Result<String, Error>) -> Void)
  func send(setCommand: SetCommandCloud.Send, completion: @escaping (Result<String, Error>) -> Void)
  func send(
    getFileList: GetFileListCloud.Send,
    completion: @escaping (Result<GetFileListCloud.Response, Error>) -> Void)
  func send(
    getThumbnail: GetThumbnailCloud.Send,
    completion: @escaping (Result<GetThumbnailCloud.Response, Error>) -> Void)
  func send(
    uploadFile: UploadFileCloud.Send,
    completion: @escaping (Result<UploadFileCloud.Response, Error>) -> Void)
  func send(
    deleteFile: DeleteFileCloud.Send,
    completion: @escaping (Result<DeleteFileCloud.Response, Error>) -> Void)

  func request(
    deleteFile: CloudFileRemoveModel.Request,
    completion: @escaping (Result<String, Error>) -> Void)
  func request(
    toCloudFile: ToCloudFileModel.Request,
    completion: @escaping (Result<String, Error>) -> Void)
  func request(
    dvfList request: DvList.Request,
    completion: @escaping (Bool, DvList.Response?, Error?) -> Void)
  func request(
    cloudUsage request: CloudUsageModel.Request,
    completion: @escaping (Result<CloudUsageModel.Response?, Error>) -> Void)
  func request(
    searchFile request: CloudSearchFileModel.Request,
    completion: @escaping (Result<CloudSearchFileModel.Response?, Error>) -> Void)

  /* Stream */
  func request(
    getVod request: GetVod.Request,
    completion: @escaping (Bool, GetVod.Response?, Error?) -> Void)
  func request(
    videoRemove request: VideoRemove.Request,
    completion: @escaping (Bool, VideoRemove.Response?, Error?) -> Void)
}
