<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.214610897257-g52sfkl4ro7icuhvi2bspknfve0vcupn</string>
			</array>
		</dict>
	</array>
	<key>GIDClientID</key>
	<string>214610897257-g52sfkl4ro7icuhvi2bspknfve0vcupn.apps.googleusercontent.com</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>*************</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>NSLocalNetworkUsageDescription</key>
	<string>This app needs access to local network to connect to your device</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_http._tcp</string>
		<string>_https._tcp</string>
		<string>_ws._tcp</string>
		<string>_wss._tcp</string>
		<string>_Proxyman._tcp</string>
		<string>_adhp._tcp._tcp</string>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-SemiBold.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>fetch</string>
		<string>nearby-interaction</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
</dict>
</plist>
