<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>GIDClientID</key>
	<string>214610897257-g52sfkl4ro7icuhvi2bspknfve0vcupn.apps.googleusercontent.com</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.214610897257-g52sfkl4ro7icuhvi2bspknfve0vcupn</string>
			</array>
		</dict>
	</array>
	<key>NSBonjourServices</key>
	<array>
		<string>_http._tcp</string>
		<string>_https._tcp</string>
		<string>_ws._tcp</string>
		<string>_wss._tcp</string>
		<string>_Proxyman._tcp</string>
		<string>_adhp._tcp</string>
	</array>
    <key>NSLocalNetworkUsageDescription</key>
    <string>This app needs access to local network to connect to your device</string>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
			<key>NSExceptionDomains</key>
			<dict>
					<key>*************</key>
					<dict>
							<key>NSIncludesSubdomains</key>
							<true/>
							<key>NSExceptionAllowsInsecureHTTPLoads</key>
							<true/>
							<key>NSExceptionRequiresForwardSecrecy</key>
							<false/>
					</dict>
			</dict>
	</dict>
	<key>UIAppFonts</key>
	<array>
		<string>Montserrat-Bold.ttf</string>
		<string>Montserrat-Medium.ttf</string>
		<string>Montserrat-Regular.ttf</string>
		<string>Montserrat-SemiBold.ttf</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>fetch</string>
		<string>nearby-interaction</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow “S1 DASH CAM” to access the gallery to use the function to save downloaded videos to the gallery.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app requires access to the photo library</string>
</dict>
</plist>
