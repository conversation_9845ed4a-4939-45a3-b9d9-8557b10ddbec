<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Xkr-z7-EAb">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Where Brilliance Meets Basics" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IyN-VY-td0">
                                <rect key="frame" x="99.333333333333329" y="714" width="194.33333333333337" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="launcher" translatesAutoresizingMaskIntoConstraints="NO" id="ABw-Hj-CPX">
                                <rect key="frame" x="72" y="421.66666666666669" width="249" height="34"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hBs-w4-pWD"/>
                        <color key="backgroundColor" red="0.0" green="0.72549019609999998" blue="0.94901960780000005" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="tintColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="ABw-Hj-CPX" firstAttribute="centerX" secondItem="hBs-w4-pWD" secondAttribute="centerX" id="8no-6e-LCG"/>
                            <constraint firstItem="IyN-VY-td0" firstAttribute="centerX" secondItem="ABw-Hj-CPX" secondAttribute="centerX" id="VKK-0h-SVu"/>
                            <constraint firstItem="ABw-Hj-CPX" firstAttribute="centerY" secondItem="hBs-w4-pWD" secondAttribute="centerY" id="oLT-je-5rY"/>
                            <constraint firstItem="hBs-w4-pWD" firstAttribute="bottom" secondItem="IyN-VY-td0" secondAttribute="bottom" constant="87" id="xi1-if-Nfi"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52.671755725190835" y="374.64788732394368"/>
        </scene>
    </scenes>
    <resources>
        <image name="launcher" width="249" height="34"/>
    </resources>
</document>
