//
//  SettingWifiViewController.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 12/13/23.
//

import Foundation
import RxCocoa
import RxSwift
import SwiftUI
import UIKit

protocol FirmWareDownloadable {
  func checkFirmware()
  func compareFileVersion()
  func download()
}

protocol FirmWareUpdater {
  func updateFile()
}

// MARK: - Wifi Mode
extension SettingViewController {
  func bindViewModel(to wifiViewModel: SettingWifiViewModel?) {
    guard let model = wifiViewModel else { return }

    let selectEvent = tableView.rx.itemSelected
    let sheetSelectEvent = sheetMenuViewController.tableView.rx.itemSelected
    let selectedDateEvent = sheetMenuViewController.rxSelectedDate.asObserver()
    let closeSheetEvent = sheetMenuViewController.rxCloseSheetView.asObserver()
    let switchValueChanged = PublishSubject<(IndexPath, Bool)>()

    // TableView cell onOff switch binding
    tableView.rx.willDisplayCell
      .subscribe(onNext: { [weak self] cell, indexPath in
        guard let self = self,
              let settingCell = cell as? SettingCell else { return }
        settingCell.onSwitchValueChanged = { isOn in
          if let currentValue = self.listItem.value[safe: indexPath.section]?.items[safe: indexPath.row]?.rowIntValue {
            self.originalValue = currentValue
            self.originalIndexPath = indexPath
          }
          switchValueChanged.onNext((indexPath, isOn))
        }
      })
      .disposed(by: disposedBag)

    let input = SettingWifiViewModel.Input(
      willAppearEvent: willAppearSubject.asObservable(),  // self.rx.viewWillAppear 대신 직접 생성한 Subject 사용
      itemSelectedEvent: selectEvent,
      switchValueChanged: switchValueChanged,
      sheetSelectedEvent: sheetSelectEvent,
      sheetSelectValueChanged: sheetSelectValueChanged,
      selectedDateEvent: selectedDateEvent,
      closeSheetViewEvent: closeSheetEvent
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxReloadSection
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        self.tableView.reloadSections([indexPath.section], with: .none)
      })
      .disposed(by: self.disposedBag)

    output.rxSelectedIndexPath
      .subscribe(onNext: { [weak self] model in
        guard let self else { return }
        sLogger.info("rxSelectedIndexPath: \(model.title), key: \(model.key) hasSubKeys: \(model.hasSubKeys), enable: \(model.enable)")
        if model.enable && model.hasSubKeys == false {
          guard let cellModel = model as? SettingCellModel else { return }
          if model.key.contains("memoryassign") {
            showMemoryAssignSheetMenu(model: cellModel)
          } else {
            showSheetMenu(model: cellModel)
          }
        }

        if model.hasSubKeys {
          if model.key.contains("sleepmode") {
            if let recCellModel = model as? SettingRectypeCellModel{
              showRecSheetMenu(model: recCellModel)
            }
          } else if model.key.contains("frontRotate") {
            if let rotateCellModel = model as? SettingRotateCellModel{
              showRotateMirrorSheetMenu(model: rotateCellModel)
            }
          } else {
            if let hdrCellModel = model as? SettingHdrCellModel{
              showHdrSheetMenu(model: hdrCellModel)
            }
          }
        }
      })
      .disposed(by: self.disposedBag)

    output.rxSectionItemsShowMessage
      .asObservable()
      .subscribe(onNext: { [weak self] fileSettingType, selectedRow in
        guard let self = self else { return }
        var message = ""
        sLogger.info("reset type: \(fileSettingType.rawValue)")
        switch fileSettingType {
        case .Reboot:
          message = L.msg_setting_alert1.localized
        case .Format:
          message = L.msg_setting_alert2.localized
        case .ResetNetwork:
          message = L.msg_setting_alert4.localized
        default:
          message = ""
        }

        LKPopupView.popup.alert {[
          .title(L.warning.localized),
          .subTitle("\(message)"),
          .subTitleColor(.subText),
          .showCancel(true),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              model.rxSelectedAlert.onNext((true, selectedRow))
            })
          ]),
          .cancelAction([
            .text(L.cancel.localized),
            .tapActionCallback({
              if let originalValue = self.originalValue,
                 let originalIndexPath = self.originalIndexPath {
                let updatedList = self.listItem.value
                updatedList[originalIndexPath.section].items[originalIndexPath.row].rowIntValue = originalValue
                updatedList[originalIndexPath.section].items[originalIndexPath.row].value =
                  updatedList[originalIndexPath.section].items[originalIndexPath.row].selectionValues[originalValue]
                self.listItem.accept(updatedList)
                self.tableView.reloadRows(at: [originalIndexPath], with: .none)
              }
            })
          ])
        ]}
      })
      .disposed(by: self.disposedBag)

    output.rxAlertMessage
      .subscribe(onNext: { [weak self] value in
        guard let self = self else { return }
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
          //          self.showMessageAlert(message: value, title: L.confirm.localized)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsConnect
      .subscribe(onNext: { [weak self] isConnect in
        guard let self else { return }
        if isConnect == false {
          LKPopupView.popup.toast(hit: L.wifi_disconnected.localized, position: .top)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsShowFirmware
      .subscribe(onNext: { [weak self] in
        guard let self = self else { return }

        if Current.isUpdateAvailable && isFirmwareExist {
          LKPopupView.popup.alert {[
            .title(L.system_upgrade.localized),
            .subTitle(L.notification_update_contents.localized),
            .showCancel(true),
            .confirmAction([
              .text(L.ok.localized),
              .bgColor(.vueroidBlue),
              .tapActionCallback({
                model.updateFirmware(output: output)
              })
            ]),
            .textAlignment(.left)
          ]}
        } else {
          LKPopupView.popup.alert {[
            .title(L.system_upgrade.localized),
            .subTitle("\(L.dialog_firmware_error_msg02.localized)"),
            .showCancel(false),
            .confirmAction([
              .text(L.ok.localized),
              LKAlertActionOption
              .bgColor(.vueroidBlue),
              .textColor(.white),
              .tapActionCallback({
            })]),
            .textAlignment(.left)
          ]}
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsShowSafetyCam
      .subscribe(onNext: { [weak self] in
        guard let self = self else { return }
        if isSafetyDBExist {
          self.popup.showPopup(
            title: L.safety_camera_upgrade_txt.localized,
            desc: L.notification_update_contents.localized, isCancel: true,
            confirmAction: {
              model.updateSafetyCam(output: output)
            })
        } else {
          self.popup.showPopup(
            title: L.safety_camera_upgrade_txt.localized,
            desc: L.dialog_firmware_error_msg02.localized, isCancel: false, confirmAction: nil)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxShowInfo
      .subscribe(onNext: { [weak self] deviceInfo in
        guard let self = self else { return }
        self.showInformation(deviceInfo: deviceInfo)
      })
      .disposed(by: self.disposedBag)

    output.rxIsInitialized
      .subscribe(onNext: { _ in
        var initMsg = L.msg_setting_alert3.localized
        let dashcamConfig = AppManager.shared.dashcamWifiConfig
        if dashcamConfig?.resetPopup == 1 {
          initMsg = L.msg_setting_alert5.localized
        }
        LKPopupView.popup.alert {[
          .title(L.system_reset.localized),
          .subTitle(initMsg),
          .subTitleColor(.subText),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              model.requestInitial(output: output)
            })
          ])
        ]}
      })
      .disposed(by: self.disposedBag)

    output.rxIsFactoryReset
      .subscribe(onNext: { _ in
        LKPopupView.popup.alert {[
          .title(L.s1_setting_factoryreset.localized),
          .subTitle(L.s1_setting_factoryreset_msg.localized),
          .subTitleColor(.subText),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              model.requestFactoryReset(output: output)
            })
          ])
        ]}
      })
      .disposed(by: self.disposedBag)

    output.rxIsFormat
      .bind(onNext: {
        LKPopupView.popup.alert {[
          .title(L.setting_formatting_txt.localized),
          .subTitle(L.msg_setting_alert2.localized),
          .showCancel(true),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              //FIXME: S1 이후부터 바로 적용안함
              // model.requestFormat(output: output)
            })
          ])
        ]}
      })
      .disposed(by: disposedBag)

    output.rxIsSdCardFormat
      .bind(onNext: {
        LKPopupView.popup.alert {[
          .title(L.setting_formatting_txt.localized),
          .subTitle(L.msg_setting_alert2.localized),
          .showCancel(true),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              //FIXME: sdcard format 메뉴는 바로 적용
               model.requestFormat(output: output)
            })
          ])
        ]}
      })
      .disposed(by: disposedBag)

    output.rxCloseSheet
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        self.sheetMenuViewController.hide()
      })
      .disposed(by: self.disposedBag)

    output.rxIsShowLoading
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { isShow in
        cLogger.warning("loading isShow: \(isShow)")
        isShow ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxDisConnect
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] in
        sLogger.info("Disconnected from device")
        self?.navigationController?.popToRootViewController(animated: true)
//        NotificationCenter.default.post(name: .disconnectedDevice, object: nil)
      })
      .disposed(by: self.disposedBag)

    output.rxLcdAlwaysOnWarning
      .subscribe(onNext: { [weak self] (indexPath, sectionItems) in
        guard let self = self else { return }
        self.showLcdAlwaysOnWarningPopup {
          model.updateValue(listItem: sectionItems, indexPath: indexPath, output: output)
          output.rxCloseSheet.onNext(())
        } cancelAction: {
          output.rxCloseSheet.onNext(())
        }
      })
      .disposed(by: self.disposedBag)

    model.listItem
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] list in
        guard let self = self else { return }
        self.listItem.accept(list)
        DispatchQueue.main.async {
          self.tableView.reloadData()
        }
      })
      .disposed(by: self.disposedBag)
  }
}

// MARK: - UINavigationControllerDelegate

extension SettingViewController: UINavigationControllerDelegate {
  public func navigationController(
    _ navigationController: UINavigationController,
    willShow viewController: UIViewController,
    animated: Bool
  ) {
    guard let fromVC = navigationController.transitionCoordinator?.viewController(forKey: .from) else {
      return
    }

    // Push
    if navigationController.viewControllers.contains(fromVC) {
      return
    }

    // Pop
    if fromVC is SettingViewController {
      if let (hasChanges, key, _) = wifiViewModel?.isUpdateSettingConfig(),
          hasChanges == true {
        // 변경사항이 있으면 팝업 표시
        LKPopupView.popup.alert {[
          .title(""),
          .subTitle(L.setting_update.localized),
          .showCancel(true),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              self.wifiViewModel?.rxDidPopNavigation.onNext(true)
            })
          ]),
          .cancelAction([
            .text(L.cancel.localized),
            .tapActionCallback({
              navigationController.popViewController(animated: true)
            })
          ])
        ]}

        // wifiBandwidth 가 변경되면 UserDefault에서 삭제
        if key == "wifibandwidth" {
          // UserDefaults.shared.dashcamCardCellModels 에서 isConnect가 true인 항목 삭제
          aLogger.info("Removing dashcamCardCellModels with isConnect true")
          self.sceneDelegate?.sendKeepAliveRequest(timeout: 1)
          UserDefaults.shared.deleteConnectedDevice(macAddress: nil)
//          NotificationCenter.default.post(name: .updateDeviceList, object: nil)
        }
      } else {
        self.wifiViewModel?.rxDidPopNavigation.onNext(true)
      }
    }
  }
}
