//
//  SettingViewController.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import RxCocoa
import RxDataSources
import RxRelay
import RxSwift
import SnapKit
import Swift<PERSON>
import UIKit

public class SettingViewController: UIViewController {
  let tableView = UITableView(frame: .zero, style: .insetGrouped)
  let signOutPopupViewController = SignOutPopupViewController()
  lazy var sheetMenuViewController: SheetViewController = {
    let viewController = SheetViewController()
    let viewModel = SheetMenuViewModel()
    viewController.viewModel = viewModel
    return viewController
  }()


  public var viewModel: SettingViewModel?
  public var wifiViewModel: SettingWifiViewModel?

  var listItem: BehaviorRelay<[SettingCellSectionModel]> = BehaviorRelay(value: [])

  let sheetSwitchValueChanged = PublishSubject<(IndexPath, Bool)>()
  let sheetSelectValueChanged = PublishSubject<(IndexPath, Int?, String?)>()
  let willAppearSubject = PublishSubject<Bool>()

  var isFirmwareExist = false
  var isSafetyDBExist = false

  // 원래 값을 저장할 변수들
  var originalValue: Int?
  var originalIndexPath: IndexPath?

  public var disposedBag = DisposeBag()

  var sceneDelegate: SceneDelegate? = {
    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
       let delegate = scene.delegate as? SceneDelegate {
      return delegate
    }
    return nil
  }()

  public override func viewDidLoad() {
    super.viewDidLoad()
    self.title = "Settings"
    navigationItem.backButtonTitle = ""
    navigationController?.delegate = self

    setComponent()
    setAutoLayout()
    bindViewModel(to: viewModel)
    bindViewModel(to: wifiViewModel)
  }

  public override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    isFirmwareExist = fileCheck(path: UrlList.tempPath())

    tabBarController?.toggleTabbar(isShow: false)
    sLogger.log("SettingViewController viewWillAppear")
    willAppearSubject.onNext(animated)
  }

  private func fileCheck(path: URL?) -> Bool {
    let folderFileList = FileManager.loadFileAtFolder(path: path)
    return folderFileList.contains(where: {
      $0.lastPathComponent.contains(".dat") || $0.lastPathComponent.contains(".zip")
    })
  }

  public func setComponent() {
    self.view.backgroundColor = .background
    setTableView()
    view.addBody(tableView)

    self.signOutPopupViewController.modalTransitionStyle = .crossDissolve
    self.signOutPopupViewController.modalPresentationStyle = .overCurrentContext
  }

  private func setTableView() {
    tableView.separatorStyle = .singleLine
    tableView.backgroundColor = .clear
    tableView.allowsSelection = true
    tableView.estimatedRowHeight = 60
    tableView.showsVerticalScrollIndicator = false
    tableView.rowHeight = UITableView.automaticDimension
    tableView.register(cellType: SettingCell.self)
//    tableView.register(cellType: SettingSectionDividerCell.self)
    tableView.delegate = self
    tableView.dataSource = self
  }

  public func setAutoLayout() { }

  public func bindViewModel(to viewModel: BaseViewModel?) {
    guard viewModel is SettingViewModel else { return }
  }
}

extension SettingViewController {
  func showSheetMenu(model: SettingCellModel) {
    sheetMenuViewController.show(isDate: model.type == .date, dateString: model.value)
    sheetMenuViewController.setItems(isDate: model.type == .date, model: model)

    if let idx = model.rowIntValue {
      DispatchQueue.main.async {
        let path = IndexPath(row: idx, section: 0)
        self.sheetMenuViewController.tableView.selectRow(
          at: path, animated: false, scrollPosition: .none
        )
      }
    }

    if let key = model.rowStringValue,
       let idx = model.selectionValues.firstIndex(of: key)
    {
      let path = IndexPath(row: idx, section: 0)
      self.sheetMenuViewController.tableView.selectRow(
        at: path, animated: false, scrollPosition: .none
      )
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
      if model.title == L.record_voltage_limit.localized {
        self.showVoltageLimitWarningPopup()
      }
    }
  }

  func showRecSheetMenu(model: SettingRectypeCellModel) {
    guard let sleepModeValue = model.rowIntValue else { return }
    let timeLapseValue = model.subRowIntValue
    sLogger.info("sleepMode: \(sleepModeValue), timeLapseValue: \(model.subRowIntValue)")
    let titleView = LKPopupTitleActionView(title: L.s1_setting_rec_type.localized)

    let lowPowerMode = LKPopupSwitchActionView(title: L.rectype_low_powermode.localized)
    lowPowerMode.switchValue = sleepModeValue == 1 ? true : false
    let impactMotion = LKPopupSwitchActionView(title: L.rectype_impact_motion.localized)
    let timelapse = LKPopupSwitchActionView(title: L.rectype_timeplase.localized)

    if timeLapseValue == 1 {
      timelapse.switchValue = true
      impactMotion.switchValue = false
    } else {
      timelapse.switchValue = false
      impactMotion.switchValue = true
    }

    let emptyView = LKPopupEmptyActionView()

    LKPopupView.popup.actionSheet {[
      LKPopupAction(with: titleView, autoDismiss: true, clickActionCallBack: {
        sLogger.log("titleView")
      }),
      LKPopupAction(withSwitchView: lowPowerMode, autoDismiss: false, clickActionCallBack: { isOn in
        sLogger.log("low power mode isOn: \(isOn)")
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 0, section: 0), value, nil) )
      }),

      LKPopupAction(withSwitchView: impactMotion, autoDismiss: false, clickActionCallBack: { isOn in
        sLogger.log("impactMotion isOn: \(isOn)")
        timelapse.switchValue = !isOn
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 1, section: 0), value, nil) )
      }),
      LKPopupAction(withSwitchView: timelapse, autoDismiss: false, clickActionCallBack: { isOn in
        sLogger.log("timelapse isOn: \(isOn)")
        impactMotion.switchValue = !isOn
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 2, section: 0), value, nil) )
      }),
      LKPopupAction(with: emptyView, autoDismiss: false, clickActionCallBack: { }),
    ]}
  }

  func showMemoryAssignSheetMenu(model: SettingCellModel) {
    guard let configValue = model.rowIntValue else { return }

    let titleView = LKPopupTitleActionView(
      title: L.memory_assign.localized,
      subTitle:  L.msg_setting_alert2.localized,
      showCloseButton: false
    )
    let value1 = LKPopupDefaultActionView(
      title: L.setting_memory_assign_value01.localized,
      subTitle: L.memory_assign_prioty_driving.localized
    )
    let value2 = LKPopupDefaultActionView(
      title: L.setting_memory_assign_value02.localized,
      subTitle: L.memory_assign_prioty_parking.localized
    )
    let value3 = LKPopupDefaultActionView(
      title: L.setting_memory_assign_value03.localized,
      subTitle: L.memory_assign_prioty_event.localized
    )
    let value4 = LKPopupDefaultActionView(
      title: L.setting_memory_assign_value04.localized,
      subTitle: L.memory_assign_only_driving.localized
    )

    let emptyView = LKPopupEmptyActionView()
    value1.isSelected = configValue == 0
    value2.isSelected = configValue == 1
    value3.isSelected = configValue == 2
    value4.isSelected = configValue == 3

    LKPopupView.popup.actionSheet {[
      LKPopupAction(with: titleView, autoDismiss: true, clickActionCallBack: { }),

      LKPopupAction(with: value1, autoDismiss: true) {
        self.showMemoryAssignWarningPopup {
          let value = value1.isSelected ? 1 : 0
          self.sheetSelectValueChanged.onNext( (.init(row: 0, section: 0), value, nil) )
        }
      },
      LKPopupAction(with: value2, autoDismiss: true) {
        self.showMemoryAssignWarningPopup {
          let value = value2.isSelected ? 1 : 0
          self.sheetSelectValueChanged.onNext( (.init(row: 1, section: 0), value, nil) )
        }
      },
      LKPopupAction(with: value3, autoDismiss: true) {
        self.showMemoryAssignWarningPopup {
          let value = value3.isSelected ? 1 : 0
          self.sheetSelectValueChanged.onNext( (.init(row: 2, section: 0), value, nil) )
        }
      },
      LKPopupAction(with: value4, autoDismiss: true) {
        self.showMemoryAssignWarningPopup {
          let value = value4.isSelected ? 1 : 0
          self.sheetSelectValueChanged.onNext( (.init(row: 3, section: 0), value, nil) )
        }
      },
      LKPopupAction(with: emptyView, autoDismiss: false, clickActionCallBack: { }),
    ]}
  }

  func showMemoryAssignWarningPopup(completion: @escaping () -> Void) {
    LKPopupView.popup.alert {[
      .subTitle(L.setting_memory_warning_popup.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          completion()
        })
      ])
    ]}
  }

  func showRotateMirrorSheetMenu(model: SettingRotateCellModel) {
    let frontRotateKey = model.key
    let frontMirrorKey = model.subKeys[0]
    let rearRotateKey = model.subKeys[1]
    let rearMirrorKey = model.subKeys[2]
    let interiorRotateKey = model.subKeys[3]
    let interiorMirrorKey = model.subKeys[4]

    guard let dictForSetting = wifiViewModel?.dictionaryForSetting else{ return }
    let frontRotateValue = dictForSetting[frontRotateKey] as? Int ?? 0
    let frontMirrorValue = dictForSetting[frontMirrorKey] as? Int ?? 0
    let rearRotateValue = dictForSetting[rearRotateKey] as? Int ?? 0
    let rearMirrorValue = dictForSetting[rearMirrorKey] as? Int ?? 0
    let interiorRotateValue = dictForSetting[interiorRotateKey] as? Int ?? 0
    let interiorMirrorValue = dictForSetting[interiorMirrorKey] as? Int ?? 0

    let titleView = LKPopupTitleActionView(title: L.s1_setting_rotate_mirror.localized)
    let frontCamera = LKPopupBadgeSelectActionView(title: L.rotate_mirror_front_camera_text.localized.localized)

    if frontMirrorValue == 1 && frontRotateValue == 1 {
      frontCamera.switchValue = .all
    } else if frontMirrorValue == 1 && frontRotateValue == 0 {
      frontCamera.switchValue = .mirror
    } else if frontMirrorValue == 0 && frontRotateValue == 1 {
      frontCamera.switchValue = .rotate
    } else {
      frontCamera.switchValue = .none
    }
    sLogger.log("frontCamera switch: \(frontCamera.switchValue.rawValue): rotate: \(frontRotateValue), mirror: \(frontMirrorValue)")

    let rearCamera = LKPopupBadgeSelectActionView(title: L.rotate_mirror_rear_camera_text_menu.localized)
    if rearRotateValue == 1 && rearMirrorValue == 1 {
      rearCamera.switchValue = .all
    } else if rearMirrorValue == 1 && rearRotateValue == 0 {
      rearCamera.switchValue = .mirror
    } else if rearMirrorValue == 0 && rearRotateValue == 1 {
      rearCamera.switchValue = .rotate
    } else {
      rearCamera.switchValue = .none
    }
    sLogger.log("rearCamera switch: \(rearCamera.switchValue.rawValue): rotate: \(rearRotateValue), mirror: \(rearMirrorValue)")

    let interiorCamera = LKPopupBadgeSelectActionView(title: L.rotate_mirror_interior_camera_text_menu.localized)

    if interiorRotateValue == 1 && interiorMirrorValue == 1 {
      interiorCamera.switchValue = .all
    } else if interiorMirrorValue == 1 && interiorRotateValue == 0 {
      interiorCamera.switchValue = .mirror
    } else if interiorMirrorValue == 0 && interiorRotateValue == 1 {
      interiorCamera.switchValue = .rotate
    } else {
      interiorCamera.switchValue = .none
    }
    sLogger.log("interiorCamera switch: \(interiorCamera.switchValue.rawValue): rotate: \(interiorRotateValue), mirror: \(interiorMirrorValue)")
    let initialize = LKPopupDefaultActionView(title: L.rotate_mirror_initialization_text.localized)
    let emptyView = LKPopupEmptyActionView()

    switch Current.s1Channel {
    case .F:
      frontCamera.isEnable = true
      rearCamera.isEnable = false
      interiorCamera.isEnable = false
    case .FI:
      frontCamera.isEnable = true
      rearCamera.isEnable = false
      interiorCamera.isEnable = true
    case .FR:
      frontCamera.isEnable = true
      rearCamera.isEnable = true
      interiorCamera.isEnable = false
    case .FRI:
      frontCamera.isEnable = true
      rearCamera.isEnable = true
      interiorCamera.isEnable = true
    }

    LKPopupView.popup.actionSheet {[
      LKPopupAction(with: titleView, autoDismiss: true, clickActionCallBack: {}),
      LKPopupAction(withBadgeView: frontCamera, autoDismiss: false, clickActionCallBack: { type, isOn in
        let key = type == .rotate ? frontRotateKey : frontMirrorKey
        sLogger.log("frontCamera \(type.title): key: \(key), \(isOn)")
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 0, section: 0), value, key) )
      }),
      LKPopupAction(withBadgeView: rearCamera, autoDismiss: false, clickActionCallBack: { type, isOn in
        let key = type == .rotate ? rearRotateKey : rearMirrorKey
        sLogger.log("rearCamera \(type.title): key: \(key), \(isOn)")
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 1, section: 0), value, key) )
      }),
      LKPopupAction(withBadgeView: interiorCamera, autoDismiss: false, clickActionCallBack: { type, isOn in
        let key = type == .rotate ? interiorRotateKey : interiorMirrorKey
        sLogger.log("interiorCamera \(type.title): key: \(key), \(isOn)")
        let value = isOn ? 1 : 0
        self.sheetSelectValueChanged.onNext( (.init(row: 2, section: 0), value, key) )
      }),

      LKPopupAction(with: initialize, clickActionCallBack: {
        frontCamera.switchValue = .none
        rearCamera.switchValue = .none
        interiorCamera.switchValue = .none
        self.sheetSelectValueChanged.onNext( (.init(row: 2, section: 0), 0, "Initialization") )
      }),

      LKPopupAction(with: emptyView, autoDismiss: false, clickActionCallBack: { }),
    ]}
  }

  func showHdrSheetMenu(model: SettingHdrCellModel) {
    guard let driveIspValue = model.rowIntValue else { return }
    let hdrSunriseTime = self.wifiViewModel?.dictionaryForSetting?["hdrSunriseTime"] as? Int ?? 12
    let hdrSunSetTime = self.wifiViewModel?.dictionaryForSetting?["hdrSunSetTime"] as? Int ?? 12
    let hdrAlwaysTime = self.wifiViewModel?.dictionaryForSetting?["hdrAlwaysTime"] as? Int ?? 0

    let titleView = LKPopupTitleActionView(title: L.s1_setting_drive_isp.localized, showCloseButton: false)
    let normal = LKPopupDefaultActionView(title: L.setting_drive_isp_mode_value01.localized)
    let hdrInfinite = LKPopupDefaultActionView(title: L.HDR_infinite_plate_capture.localized)
    let hdr = LKPopupDefaultActionView(title: L.setting_drive_isp_mode_value02.localized)
    let hdrsubtitle = LKPopupTitleActionView(title: "",subTitle: L.hdr_subtitle.localized, showCloseButton: false)
    let hdrTimer = LKPopupSectionTitleActionView(title: L.hdr_timer.localized)
    let alwaysOn = LKPopupSwitchActionView(title: L.alwaysOn.localized)
    alwaysOn.switchValue = hdrAlwaysTime == 1 ? true : false
    let nightTime = LKPopupDurationActionView(title: L.night_time.localized)

    // Set initial values for the time views
    sLogger.info( "Setting initial values for time views, sunset: \(hdrSunSetTime), sunrise: \(hdrSunriseTime)")
    nightTime.setTimes(sunset: hdrSunSetTime, sunrise: hdrSunriseTime)
    
    if driveIspValue == 1 {
      hdrTimer.isEnabled = true
      alwaysOn.isEnabled = true
      nightTime.isEnabled = hdrAlwaysTime == 0
    } else {
      hdrTimer.isEnabled = false
      alwaysOn.isEnabled = false
      nightTime.isEnabled = false
    }

    normal.isSelected = driveIspValue == 0
    hdrInfinite.isSelected = driveIspValue == 2
    hdr.isSelected = driveIspValue == 1

    // Handle time changes
    nightTime.onTimeChanged = { [weak self] sunset, sunrise in
      guard let self = self else { return }
      sLogger.log("Time changed - sunset: \(sunset), sunrise: \(sunrise)")
      // Update the model with new times
      self.sheetSelectValueChanged.onNext((.init(row: 1, section: 0), sunset, "hdrSunSetTime"))
      self.sheetSelectValueChanged.onNext((.init(row: 2, section: 0), sunrise, "hdrSunriseTime"))
    }

    var sheetItems: [LKPopupAction] = []
    let driveIspSection = [
      LKPopupAction(with: titleView) {},
      LKPopupAction(with: normal) {
        normal.isSelected = true
        hdrInfinite.isSelected = false
        hdr.isSelected = false
        sLogger.log("Normal")
        hdrTimer.isEnabled = false
        alwaysOn.isEnabled = false
        nightTime.isEnabled = false

        self.sheetSelectValueChanged.onNext( (.init(row: 0, section: 0), 0, nil))
      },
      LKPopupAction(with: hdrInfinite) {
        hdrInfinite.isSelected = true
        normal.isSelected = false
        hdr.isSelected = false

        hdrTimer.isEnabled = false
        alwaysOn.isEnabled = false
        nightTime.isEnabled = false

        sLogger.log("hdrInfinite")
        self.sheetSelectValueChanged.onNext( (.init(row: 1, section: 0), 2, nil))
      },
      LKPopupAction(with: hdr) {
        normal.isSelected = false
        hdrInfinite.isSelected = false
        hdr.isSelected = true

        hdrTimer.isEnabled = true
        alwaysOn.isEnabled = true
        nightTime.isEnabled = alwaysOn.switchValue == false
        sLogger.log("hdr")
        self.sheetSelectValueChanged.onNext( (.init(row: 2, section: 0), 1, nil))
      },
      LKPopupAction(with: hdrsubtitle) {},
    ]

    let hdrSection = getHdrSection(hdrTimer: hdrTimer, alwaysOn: alwaysOn, nightTime: nightTime, driveIspValue: driveIspValue)
    sheetItems = driveIspSection + hdrSection
    LKPopupView.popup.actionSheet { sheetItems }
  }

  func closeSheetMenu() {
    sheetMenuViewController.hide()
  }

  private func getHdrSection(
    hdrTimer: LKPopupActionViewProtocol,
    alwaysOn: LKPopupSwitchActionView,
    nightTime: LKPopupDurationActionView,
    driveIspValue: Int
  ) -> [LKPopupAction] {
    let sectionDividerView = LKPopupDividerActionView()
    let emptyView = LKPopupEmptyActionView()
    let test = LKPopupTitleActionView(
      title: "",
      subTitle:  L.hdr_timer_description.localized,
      showCloseButton: false
    )
    let hdrSection = [
        LKPopupAction(with: sectionDividerView) {},
        LKPopupAction(with: hdrTimer) {},
        LKPopupAction(withSwitchView: alwaysOn, clickActionCallBack: { isOn in
          sLogger.log("alwaysOn isOn: \(isOn)")
          nightTime.isEnabled = !isOn
          self.sheetSelectValueChanged.onNext((.init(row: 0, section: 0), isOn ? 1 : 0, "hdrAlwaysTime"))
        }),
        LKPopupAction(with: nightTime) {},
        LKPopupAction(with: test, autoDismiss: true, clickActionCallBack: { }),

//        LKPopupAction(with: nil, subTitle: L.hdr_timer_description.localized, clickActionCallBack: { }),
        LKPopupAction(with: emptyView) {},
    ]
    return hdrSection
  }
}

extension SettingViewController: UITableViewDelegate, UITableViewDataSource {
  public func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    if self.listItem.value.count < 1 { return UITableViewCell() }
    let sectionItems = self.listItem.value
    let sectionModel = sectionItems[indexPath.section]

    let cell = self.tableView.dequeueReusableCell(for: indexPath, cellType: SettingCell.self)
    cell.selectionStyle = .none
    let model = sectionModel.items[indexPath.row]

    // MARK: 저전력모드 disable
    if sectionModel.items.first(where: { $0.title == L.low_power_mode.localized })?.rowIntValue
        == 1
    {
      if model.title == L.record_voltage_limit.localized {
        sectionItems[indexPath.section].items[indexPath.row].enable = false
        self.listItem.accept(sectionItems)
        cell.disableData(model: model, value: L.setting_detail_off_value.localized)
        return cell
      }
      if model.title == L.record_time_lapse.localized {
        sectionItems[indexPath.section].items[indexPath.row].enable = false
        self.listItem.accept(sectionItems)
        cell.disableData(model: model, value: L.setting_time_lapse_value01.localized)
        return cell
      }
    }

//    // MARK: 주차 녹화방식 disable
//    if model.title == L.setting_parkfps_value00.localized {
//     if sectionModel.items.first(where: { $0.title == L.parking_recording_isp_mode_txt.localized })?
//        .rowIntValue == 2 ||
//          //  4K 및 QHD 60F 설정 일시 주차녹화방식 disable
//        wifiViewModel?.dictionaryForSetting?["frontResolution"] as! Int != 3 {
//
//        sectionItems[indexPath.section].items[indexPath.row].enable = false
//        self.listItem.accept(sectionItems)
//        cell.disableData(model: model, value: L.setting_parkfps_value02.localized)
//        return cell
//      }
//    }

    // MARK: change password disable
    if model.title == L.setting_changepassword.localized &&
       wifiViewModel?.dictionaryForSetting?["secretmode"] as! Int == 0 {
      sectionItems[indexPath.section].items[indexPath.row].enable = false
      self.listItem.accept(sectionItems)
      cell.setData(model: model, isMakeDot: isFirmwareExist)
      cell.disableData(model: model, value: model.value)
      return cell
    }

    // MARK: 60Fps 설정 주차 녹화 방식 설정(park-isp-mode) disable
    if model.title == L.s1_setting_park_isp_mode.localized {
      if wifiViewModel?.dictionaryForSetting?["frontResolution"] as! Int == 0 ||
         wifiViewModel?.dictionaryForSetting?["frontResolution"] as! Int == 2 {
        sectionItems[indexPath.section].items[indexPath.row].enable = false
        self.listItem.accept(sectionItems)
        cell.setData(model: model, isMakeDot: isFirmwareExist)
        cell.disableData(model: model, value: L.setting_drive_isp_mode_value01.localized)
        return cell
      }
    }

    // MARK: 60Fps 설정 주차 상시 방식 설정 disable
    if model.title == L.s1_setting_drive_isp.localized {
      if wifiViewModel?.dictionaryForSetting?["frontResolution"] as! Int == 0 ||
          wifiViewModel?.dictionaryForSetting?["frontResolution"] as! Int == 2 {
        sectionItems[indexPath.section].items[indexPath.row].enable = false
        self.listItem.accept(sectionItems)
        cell.setData(model: model, isMakeDot: isFirmwareExist)
        cell.disableData(model: model, value: L.setting_drive_isp_mode_value01.localized)
        return cell
      }
    }

    if checkDisableSettingItem(model: model) {
      sectionItems[indexPath.section].items[indexPath.row].enable = false
      sLogger.info("title  \(model.title), enable: \(sectionItems[indexPath.section].items[indexPath.row].enable)")
      self.listItem.accept(sectionItems)
      cell.setData(model: model, isMakeDot: isFirmwareExist)
      cell.disableData(model: model, value: model.value)
      return cell
    }

    if model.type == .scUpdate {
      cell.setData(model: model, isMakeDot: isSafetyDBExist)
    } else {
      cell.setData(model: model, isMakeDot: isFirmwareExist)
    }

    sectionItems[indexPath.section].items[indexPath.row].enable = true
    self.listItem.accept(sectionItems)
    return cell
  }

  func checkDisableSettingItem(model: SettingCellPresentable) -> Bool {
    // MARK: channel 수 에 따른 후방카메라, 내부카메라 옵션 설정
    let currentChannels = AppManager.shared.deviceInfo?.vchannelbits?.attachChannel
    if currentChannels == .F {
      if model.key == "ParkRearCam3chOnOff" {
        return true
      }
    }

    if currentChannels == .FR {
      if model.key == "ParkRearCam3chOnOff" {
        return true
      }
    }

    // MARK: 녹화할당중행전용 (memoryassign 3) 일때 주차모드 설정 disable
    if wifiViewModel?.dictionaryForSetting?["memoryassign"] as! Int == 3 {
      if model.key == "parkmode" || model.key == "voltagelimit" || model.key == "parkrectime" || model.key == "wintermode" ||
          model.key == "park_delay" || model.key == "park-isp-mode" || model.key == "sleepmode" || model.key == "timelapse" ||
          model.key == "ParkRearCam3chOnOff"
      {
        sLogger.info("key: \(model.key)")
        return true
      }
    }


    // MARK: 주차모드시 format 항목 disable
    if let driveMode = AppManager.shared.initInfo?.driveMode,
       let state = DashcamState(rawValue: driveMode),
       state == .Parking {

      if model.resetType == .Format ||
          model.type == .sdcardformat || model.type == .factoryreset || model.type == .fwUpdate || model.type == .initialize ||
          model.key == "datetype" || model.key == "summertime" || model.key == "wifibandwidth" || model.key == "parkmode"
      {
        return true
      }
    }
    return false
  }

  public func numberOfSections(in tableView: UITableView) -> Int {
    let sectionItems = self.listItem.value  //viewModel?.listItem else { return 1 }
    return sectionItems.count
  }

  public func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    //guard let sectionItems = viewModel?.listItem else { return 1 }
    let sectionItems = self.listItem.value
    if sectionItems.count < 1 { return 1 }

    if sectionItems[section].isFold == true {
      return 1
    } else {
      return sectionItems[section].items.count
    }
  }

  public func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    return 60
  }
}


// MARK: show Popup
extension SettingViewController {
  func showInputAlert(message: String) {
    let alert = UIAlertController(
      title: L.dialog_wifi_ap_title.localized,
      message: message,
      preferredStyle: UIAlertController.Style.alert
    )
    alert.addTextField(configurationHandler: { textField in
      textField.placeholder = L.dialog_wifi_ap_msg.localized
    })

    let action = UIAlertAction(title: L.confirm.localized, style: UIAlertAction.Style.default) {
      [weak alert] action in
      if let first = alert?.textFields?.first,
         let ssid = first.text
      {
        Log.debug(category: .Setting, to: ssid)
      }
    }

    let cancel = UIAlertAction(title: L.cancel.localized, style: UIAlertAction.Style.cancel)
    alert.addAction(action)
    alert.addAction(cancel)
    self.present(alert, animated: true)
  }

  func showInitialAlert(confirm: UIAlertAction) {
    let alert = UIAlertController(
      title: "warning".localized,
      message: L.msg_setting_alert3.localized,
      preferredStyle: UIAlertController.Style.alert
    )

    alert.addAction(confirm)
    self.present(alert, animated: true)
  }

  func showInformation(deviceInfo: [String?]) {
    guard let model = deviceInfo[0], let fw = deviceInfo[1], let sdinfo = deviceInfo[2]  else {
      return
    }

    // sdinfo 가 250 일경우 256 으로 표시
    let sdinfoValue = Int(sdinfo) ?? 0
    let sdCardInfo: Int = sdinfoValue == 250 ? 256 : sdinfoValue
    let sdCardString =  "\(sdCardInfo) GB"

    let titleView = LKPopupTitleActionView(title: L.system_info.localized)
    let emptyView = LKPopupEmptyActionView()

    let modelName = LKPopupDefaultActionView(title: L.model_name_title.localized, value: model)
    let firmwareVersion = LKPopupDefaultActionView(title: L.firmware_version_title.localized, value: fw)
    let sdcard = LKPopupDefaultActionView(title: L.sd_card_information_title.localized, value: sdCardString)

    LKPopupView.popup.actionSheet {[
      LKPopupAction(with: titleView, autoDismiss: true, clickActionCallBack: {}),

      LKPopupAction(with: modelName) {
      },
      LKPopupAction(with: firmwareVersion) {
      },
      LKPopupAction(with: sdcard) {
      },

      LKPopupAction(with: emptyView, autoDismiss: false, clickActionCallBack: { }),
    ]}
  }

  func showVoltageLimitWarningPopup() {
    LKPopupView.popup.alert {[
      .subTitle(L.setting_vehicle_type.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({ })
      ])
    ]}
  }

  func showResetConfirmationPopup(completion: @escaping () -> Void) {
    LKPopupView.popup.alert {[
      .title(L.notification_txt.localized),
      .subTitle(L.dashcam_reset_start_msg.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.simple_text_exit.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          completion()
        })
      ])
    ]}
  }

  func showLcdAlwaysOnWarningPopup(confirmAction: @escaping () -> Void, cancelAction: @escaping () -> Void) {
    LKPopupView.popup.alert {[
      .subTitle(L.lcd_always_on_alert_message.localized),
      .showCancel(true),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          confirmAction()
        })
      ]),
      .cancelAction([
        .text(L.cancel.localized),
        .tapActionCallback({
          cancelAction()
        })
      ])
    ]}
  }
}
