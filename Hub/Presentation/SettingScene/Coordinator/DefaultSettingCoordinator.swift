//
//  DefaultSettingCoordinator.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import UIKit

class DefaultSettingCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: SettingCoordinatorFinishDelegate?
  var childCoordinators = [SettingChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func start(with step: SettingSteps) {
    navigate(to: step)
  }
}

extension DefaultSettingCoordinator {
  func make() -> SettingViewController {
    let vc = SettingViewController()
    vc.viewModel = SettingViewModel()
    vc.wifiViewModel = configWifiModel()
    return vc
  }

  private func configWifiModel() -> SettingWifiViewModel {
    sLogger.trace("configWifiModel")
    return SettingWifiViewModel(
      useCase: Composers.settingUseCase,
      commandUseCase: Composers.commandUseCase,
      coordinator: self
    )
  }
}

extension DefaultSettingCoordinator: SettingCoordinator {
  func navigate(to step: SettingSteps) {
    switch step {
    case .showSetting:
      showSetting()
    case .cloudSetting(target: _):
      break
    case .settingDidFinish:
      DispatchQueue.main.async {
        self.delegate?.settingCoordinatorDidFinish()

        self.navigationController.popViewController(animated: true)
        Log.info(category: .App, to: "pop view controller")
      }
    }
  }
  
  func showSetting() {
    let vc = make()
    vc.hidesBottomBarWhenPushed = true
    self.navigationController.pushViewController(vc, animated: true)
  }

//  func finishToMode() {
//    if let del = delegate {
//      if let coordi = del as? DefaultMenuContainerCoordinator {
//        coordi.finish()
//      }
//    }
//  }
}

// MARK: Finish Delegate
