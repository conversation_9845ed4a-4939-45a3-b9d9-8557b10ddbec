//
//  SettingWifiViewModel.swift
//  Hub
//
//  Created by ncn on 2023/04/24.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit
import ZIPFoundation

public class SettingWifiViewModel: BaseViewModel {
  let useCase: SettingCommandUseCase
  let commandUseCase: CommandUseCase
  let coordinator: SettingCoordinator

  private var selectedIndexPath: IndexPath?
  var dictionaryForSetting: [String: Any]?

  public var isUpdateValue: Bool = false
  private var isSetWifiRange: Bool = false
  private var fileSettingType: FileSettingType = .None

  var listItem = BehaviorSubject<[SettingCellSectionModel]>(value: [])
  let rxSelectedAlert = PublishSubject<(Bool, Int)>()
  let rxDidPopNavigation = BehaviorSubject<Bool>(value: false)
  var alertState: [String: Bool] = [:]

  struct Input {
    let willAppearEvent: Observable<Bool>
    let itemSelectedEvent: ControlEvent<IndexPath>
    let switchValueChanged: Observable<(IndexPath, Bool)>
    let sheetSelectedEvent: ControlEvent<IndexPath>
    let sheetSelectValueChanged: PublishSubject<(IndexPath, Int?, String?)>
    let selectedDateEvent: Observable<Date>
    let closeSheetViewEvent: Observable<Bool>
  }

  struct Output {
    let rxReloadSection = PublishSubject<IndexPath>()
    let rxSelectedIndexPath = PublishSubject<SettingCellPresentable>()
    let rxSectionItemsShowMessage = PublishSubject<(FileSettingType, Int)>()

    let rxWifiSetting = PublishSubject<Void>()
    let rxIsShowFirmware = PublishSubject<Void>()
    let rxIsShowSafetyCam = PublishSubject<Void>()

    let rxShowInfo = PublishSubject<[String?]>()
    let rxIsInitialized = PublishSubject<Void>()
    let rxIsFactoryReset = PublishSubject<Void>()
    let rxIsSdCardFormat = PublishSubject<Void>()
    let rxIsFormat = PublishSubject<Void>()

    let rxAlertMessage = PublishSubject<String>()
    let rxIsConnect = PublishSubject<Bool>()
    let rxCloseSheet = PublishSubject<Void>()

    let rxIsShowLoading = PublishSubject<Bool>()
    let rxShowLoadingWithTime = PublishSubject<(Bool, Int)>()
    let rxDisConnect = PublishSubject<Void>()

    let rxRemoveSafetyFile = PublishSubject<Void>()
    let rxLcdAlwaysOnWarning = PublishSubject<(IndexPath, [SettingCellSectionModel])>()
  }

  init(
    useCase: SettingCommandUseCase, commandUseCase: CommandUseCase, coordinator: SettingCoordinator
  ) {
    self.useCase = useCase
    self.commandUseCase = commandUseCase
    self.coordinator = coordinator
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

//    input.wiillAppearEvent
//      .compactMap { _ in AppManager.shared.dashcamWifiConfig }
//      .rxDebug("willAppearEvent1")
//      .map { GetConfiguration.Response(getconfiguration: $0).toDashcamDomain() }
//      .rxDebug("willAppearEvent1_1")
//      .bind(to: listItem)
//      .disposed(by: disposedBag)

    input.willAppearEvent
      .subscribe(onNext: { [weak self] _ in
        sLogger.log("willAppearEvent")
        guard let self = self else { return }
        self.connect(output: output)
      })
      .disposed(by: disposedBag)

    input.itemSelectedEvent
      .withLatestFrom(listItem, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] (indexPath, sectionItems) in
        guard let self else { return }
        sLogger.info("Selected at \(indexPath)")
        self.selectedIndexPath = indexPath
        let settingItem = sectionItems[indexPath.section].items[indexPath.row]
        if settingItem.viewType == .sheetSelect ||
            settingItem.viewType == .sheetInfo ||
            settingItem.viewType == .sheetSelectNoValue {
          self.setSheetItems(sections: sectionItems, indexPath: indexPath, output: output)
        } else if settingItem.viewType == .textField, settingItem.enable == true {
          sLogger.info("change password, rowInt:\(settingItem.rowIntValue ?? -1)")
          LKPopupView.popup.alertWithTextField {[
            .title(L.setting_changepassword.localized),
            .subTitle(L.dialog_password_content.localized),
            .textFieldPlaceholder("\(settingItem.value)"),
            .confirmActionWithText({ text in
              self.updateValue(listItem: sectionItems, indexPath: indexPath, value: Int(text ?? ""), output: output)
            }),
            .showCancel(true)
          ]}
        }
      })
      .disposed(by: disposedBag)

    input.switchValueChanged
      .withLatestFrom(listItem) { ($0.0, $0.1, $1) }
      .subscribe(onNext: { [weak self] (indexPath, isOn, sections) in
        guard let self = self,
          let section = sections[safe: indexPath.section],
          let row: SettingCellModel = section.items[safe: indexPath.row] as? SettingCellModel
        else {
          sLogger.error("Section or row is nil")
          return
        }
        
        if row.type == .format {
          sLogger.info("format switch")
        }
        
        self.selectedIndexPath = indexPath
        let value = isOn ? 1 : 0
        
        self.updateValue(
          listItem: sections,
          indexPath: IndexPath(row: value, section: 0),
          output: output
        )
      })
      .disposed(by: disposedBag)

    input.sheetSelectedEvent
      .withLatestFrom(listItem, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] (indexPath, sectionItems) in
        guard let self = self else { return }
        //TODO: - 키로 필터링을 하나 맞는 코드인가 싶습니다.
        if let selectedIndexPath = self.selectedIndexPath,
           let row = sectionItems[safe: selectedIndexPath.section]?.items[safe: selectedIndexPath.row],
           row.key == "lcd-ontime" && indexPath.row == 0 {
          output.rxLcdAlwaysOnWarning.onNext((indexPath, sectionItems))
        } else {
          self.updateValue(listItem: sectionItems, indexPath: indexPath, output: output)
          output.rxCloseSheet.onNext(())
        }
      })
      .disposed(by: disposedBag)

    input.sheetSelectValueChanged
      .withLatestFrom(listItem.asObservable()) { (event, sectionItems) in (event, sectionItems) }
      .subscribe(onNext: { [weak self] (event, sectionItems) in
        // event: sheetSelectValueChanged의 값
        // sectionItems: listItem의 최신 값
        let indexPath = event.0
        sLogger.info("sheetSelectValueChanged indexPath: \(indexPath)")
        if let changedValue = event.1 {
          sLogger.info("sheetSelectValueChanged changedValue: \(changedValue)")
          
          if let subkey = event.2 {
            sLogger.info("sheetSelectValueChanged subkey: \(subkey)")
            self?.updateValue(listItem: sectionItems, indexPath: indexPath, subKey: subkey, value: changedValue, output: output)
          } else {
            if let selectedIndexPath = self?.selectedIndexPath,
               let _ = sectionItems[safe: selectedIndexPath.section]?.items[safe: selectedIndexPath.row] {
              self?.updateValue(listItem: sectionItems, indexPath: indexPath, value: changedValue, output: output)
            } else {
              self?.updateValue(listItem: sectionItems, indexPath: indexPath, value: changedValue, output: output)
            }
          }
        }
        else {
          self?.updateValue(listItem: sectionItems, indexPath: indexPath, output: output)
        }
      })
      .disposed(by: disposedBag)
    
    input.selectedDateEvent
      .withLatestFrom(listItem, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] (date, sectionItems) in
        guard let self = self else { return }
        self.updateDateValue(listItem: sectionItems, date: date, output: output)
      })
      .disposed(by: disposedBag)

    input.closeSheetViewEvent
      .asObservable()
      .subscribe(onNext: { [weak self] ret in
        guard self != nil else { return }
      })
      .disposed(by: disposedBag)

    self.rxDidPopNavigation
      .filter { $0 == true }
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        let (hasChanges, _, _) = isUpdateSettingConfig()
        if hasChanges {
          requestTestVolume(setConfig: true, output: output) { [weak self] in
            self?.requestStartRecord(output: output)
          }
        } else {
          self.requestStartRecord(output: output)
        }
      })
      .disposed(by: disposedBag)

    self.rxSelectedAlert
      .filter { $0.0 == true }
      .withLatestFrom(listItem, resultSelector: { ($0.1, $1) })
      .subscribe(onNext: { [weak self] (selectedRow, listItem) in
        guard let selectedIndexPath = self?.selectedIndexPath,
          let row = listItem[safe: selectedIndexPath.section]?.items[
            safe: selectedIndexPath.row]
        else {
          sLogger.error("selectedIndexPath.section, selectedIndexPath.row is nil")
          return
        }

        if row.rowIntValue != nil {
          listItem[selectedIndexPath.section].items[selectedIndexPath.row].rowIntValue =
            selectedRow
          listItem[selectedIndexPath.section].items[selectedIndexPath.row].value =
            row.selectionValues[selectedRow]
          self?.dictionaryForSetting?[row.key] = selectedRow
          self?.listItem.onNext(listItem)
          output.rxReloadSection.onNext(selectedIndexPath)
        }

        #if HUB
        self?.requestTestVolume(
          setConfig: true, output: output,
          completion: {
            self?.useCase.disConnect()
            output.rxDisConnect.onNext(())
          }
        )
        #endif
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension SettingWifiViewModel {
  func setSheetItems(sections: [SettingCellSectionModel], indexPath: IndexPath, output: Output) {
    guard let section = sections[safe: indexPath.section],
      let row: SettingCellPresentable = section.items[safe: indexPath.row]
    else {
      sLogger.error("Section or row is nil")
      return
    }
    sLogger.info("setSheetItems row:  \(row.title), enable: \(row.enable)")
    switch row.type {
    case .select, .date:
      if row.enable == true {
        output.rxSelectedIndexPath.onNext(row)
      }
    case .format:
      output.rxIsFormat.onNext(())
    case .wifi:
      output.rxWifiSetting.onNext(())
    case .fwUpdate:
      if row.enable == true {
        output.rxIsShowFirmware.onNext(())
      }
    case .scUpdate:
        output.rxIsShowSafetyCam.onNext(())
    case .noti:
      let model: String? = AppManager.shared.deviceInfo?.model
      let version: String? = AppManager.shared.deviceInfo?.version
      let sdinfo: Int? = AppManager.shared.deviceInfo?.sdtotalSize
      let deviceInfo = [model, version, "\(sdinfo ?? 0)"]
      sLogger.info("deviceInfo: \(deviceInfo)")
      output.rxShowInfo.onNext(deviceInfo)
    case .initialize:
      if row.enable == true {
        output.rxIsInitialized.onNext(())
      }
    case .factoryreset:
      if row.enable == true {
        output.rxIsFactoryReset.onNext(())
      }
    case .sdcardformat:
      if row.enable == true {
        output.rxIsSdCardFormat.onNext(())
      }
    default:
      break
    }
  }

  func updateValue(
    listItem: [SettingCellSectionModel],
    indexPath: IndexPath,
    subKey: String? = nil,
    value: Int? = 0,
    output: Output
  ) {
    guard let cellSectionIndex = selectedIndexPath?.section,
      let cellRowIndex = selectedIndexPath?.row,
      let cellSection = listItem[safe: cellSectionIndex],
      let row = cellSection.items[safe: cellRowIndex]
    else {
      sLogger.error("cellSectionIndex, cellRowIndex, cellSection, row is nil")
      return
    }

    isUpdateValue = true
    fileSettingType = row.resetType
    sLogger.info("title: \(row.title) start: \(row.rowIntValue ?? -1) -> to: \(indexPath.row)")
    
    if row.resetType == .None {
      if row.key == "sleepmode" {
        if var recTypeitem = listItem[cellSectionIndex].items[cellRowIndex] as? SettingRectypeCellModel,
           let changeValue = value {
          sLogger.info("select row: \(indexPath.row), changeValue: \(changeValue)")
          if indexPath.row == 0 {
            recTypeitem.rowIntValue = changeValue
            self.dictionaryForSetting?[row.key] = changeValue
          } else if indexPath.row == 1 { // Impact + Motion
            recTypeitem.subRowIntValue = changeValue == 1 ? 0 : 1
            self.dictionaryForSetting?["timelapse"] = changeValue == 1 ? 0 : 1
          } else if indexPath.row == 2 { // Motion + Impact
            recTypeitem.subRowIntValue = changeValue == 1 ? 1 : 0
            self.dictionaryForSetting?["timelapse"] = changeValue == 1 ? 1 : 0
          }
          sLogger.info("recTypeitem: rowIntValue:\(recTypeitem.rowIntValue ?? 0) subRowIntValue: \(recTypeitem.subRowIntValue)")
          listItem[cellSectionIndex].items[cellRowIndex] = recTypeitem
        }
      } else if row.key == "drive-isp-mode" {
        if let changeValue = value {
          if subKey == "hdrSunriseTime" || subKey == "hdrSunSetTime" || subKey == "hdrAlwaysTime" {
//            listItem[cellSectionIndex].items[cellRowIndex].subRowIntValue = changeValue
//            listItem[cellSectionIndex].items[cellRowIndex].value = row.selectionValues[changeValue]
            sLogger.info("subkey changeValue: \(changeValue)")
            self.dictionaryForSetting?[subKey!] = changeValue
          } else {
            sLogger.info("drive-isp-mode select row: \(indexPath.row), changeValue: \(changeValue)")
            listItem[cellSectionIndex].items[cellRowIndex].rowIntValue = changeValue
            listItem[cellSectionIndex].items[cellRowIndex].value = row.selectionValues[changeValue]
            self.dictionaryForSetting?[row.key] = changeValue
          }
        }
      } else if row.key == "secretpass" {
        if let changeValue = value {
          sLogger.info("secretpass select row: \(indexPath.row), changeValue: \(changeValue)")
          listItem[cellSectionIndex].items[cellRowIndex].rowIntValue = changeValue
          listItem[cellSectionIndex].items[cellRowIndex].value = String(format: "%04d", changeValue)
          self.dictionaryForSetting?[row.key] = changeValue
        }
      } else if row.key == "frontRotate" {
        if let changeValue = value, let subKey = subKey {
          sLogger.info("frontRotate select row: \(indexPath.row), changeValue: \(changeValue), subkey: \(subKey)")
          if subKey == "Initialization" {
            self.dictionaryForSetting?["frontRotate"] = 0
            self.dictionaryForSetting?["frontMirror"] = 0
            self.dictionaryForSetting?["RearRotate"] = 0
            self.dictionaryForSetting?["RearMirror"] = 0
            self.dictionaryForSetting?["InteriorRotate"] = 0
            self.dictionaryForSetting?["InteriorMirror"] = 0
          } else {
            self.dictionaryForSetting?[subKey] = changeValue
          }
        }
      } else {
        listItem[cellSectionIndex].items[cellRowIndex].rowIntValue = indexPath.row
        listItem[cellSectionIndex].items[cellRowIndex].value = row.selectionValues[indexPath.row]
        
        if row.key == "parkrectime" {
          let value = ConfigurationModel.ParkRECTimeType.fromViewIndex(indexPath.row).rawValue
          self.dictionaryForSetting?[row.key] = value
        }
        else {
          self.dictionaryForSetting?[row.key] = indexPath.row
        }
      }
      #if HUB
      self.requestTestVolume(
        setConfig: true, output: output,
        completion: { [weak self] in
          self?.listItem.onNext(listItem)
        }
      )
      #endif
      self.listItem.onNext(listItem)
    } else {
      output.rxSectionItemsShowMessage.onNext((fileSettingType, indexPath.row))
    }
  }

  func updateDateValue(listItem: [SettingCellSectionModel], date: Date, output: Output) {
    guard let sectionIndex = selectedIndexPath?.section,
          let rowIndex = selectedIndexPath?.row,
          let row = listItem[safe: sectionIndex]?.items[safe: rowIndex]
    else {
      return
    }

    let value = date.toString(format: "yyyy-MM-dd HH:mm:ss")
    listItem[sectionIndex].items[rowIndex].rowStringValue = value
    listItem[sectionIndex].items[rowIndex].value = value
    self.listItem.onNext(listItem)
    self.dictionaryForSetting?[row.key] = value
    self.isUpdateValue = true
  }
}

// MARK: - Socket Connection
extension SettingWifiViewModel {
  func connect(output: Output) {
    output.rxIsShowLoading.onNext(true)
    useCase.connect { [weak self] response, error in
      guard let self = self else { return }
      output.rxIsShowLoading.onNext(false)
      if let e = error {
        output.rxIsConnect.onNext(false)
        output.rxAlertMessage.onNext(e.localizedDescription)
        coordinator.navigate(to: .settingDidFinish)
      } else {
        output.rxIsConnect.onNext(true)
        self.requestStopRecord(output: output)
      }
    }
  }

  func requestStopRecord(output: Output) {
    output.rxIsShowLoading.onNext(true)
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstop", param: 2)
    let send = SetCommand.Send(header: header, setcommand: command)
    useCase.send(to: send) { [weak self] response, error in
      guard let self = self else { return }
      if let e = error {
        output.rxIsConnect.onNext(false)
        coordinator.navigate(to: .settingDidFinish)
      } else {
        output.rxIsShowLoading.onNext(false)
        self.requestGetConfiguaration(output: output)
      }
    }
  }

  func requestStartRecord(output: Output) {
    output.rxIsShowLoading.onNext(true)
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstart", param: 2)
    let send = SetCommand.Send(header: header, setcommand: command)
    useCase.send(to: send) { response, error in
      LKPopupView.popup.hideLoading()
      output.rxIsShowLoading.onNext(false)
      self.dismiss()
    }
  }

  func requestGetConfiguaration(output: Output) {
    output.rxIsShowLoading.onNext(true)
    let header = HeaderModel(cmd: Command.getconfiguration)
    let send = GetConfiguration.Send(header: header)
    useCase.send(to: send) { [weak self] response, error in
      guard let self = self else { return }
      LKPopupView.popup.hideLoading()
      output.rxIsShowLoading.onNext(false)
      if let e = error {
        output.rxAlertMessage.onNext(e.localizedDescription)
        output.rxIsConnect.onNext(false)
        coordinator.navigate(to: .settingDidFinish)
      }
      if let value = response {
        let obj = value.toDashcamDomain()
        let dic = value.getconfiguration.dictionary
        AppManager.shared.dashcamWifiConfig = value.getconfiguration
        self.listItem.onNext(obj)
        self.dictionaryForSetting = dic
      }
    }
  }

  func requestSetConfiguaration(output: Output, completion: @escaping () -> Void = {}) {
    sLogger.info("@@## requestSetConfiguaration")
    output.rxIsShowLoading.onNext(true)
    guard let dic = self.dictionaryForSetting else { return }
    let ret = dic.dictionaryToObject(objectType: ConfigurationModel.self)
    if let config = ret.obj {
      let header = HeaderModel(cmd: Command.setconfiguration)
      let send = SetConfiguration.Send(header: header, setconfiguration: config)
      useCase.send(to: send) { [weak self] reponse, error in
        LKPopupView.popup.hideLoading()
        output.rxIsShowLoading.onNext(false)
        if self?.fileSettingType == .ResetNetwork {
          self?.requestRestartNetwork(output: output, completion: completion)
          return
        }
        self?.fileSettingType = .None
        completion()
      }
    } else {
      output.rxIsShowLoading.onNext(false)
      sLogger.error("Fail to make object for set configuration.")
    }
  }

  func requestTestVolume(setConfig: Bool, output: Output, completion: @escaping () -> Void = {}) {
    sLogger.info("@@## requestTestVolume")
    output.rxIsShowLoading.onNext(true)
    let volume = self.dictionaryForSetting?["speakervolume"] as? Int ?? 0
    let header = HeaderModel(cmd: Command.testvolume)
    let command = TestVolumeModel(volume: volume)
    let send = TestVolume.Send(header: header, testvolume: command)
    useCase.send(to: send) { [weak self] response, error in
      guard let self = self else { return }
      output.rxIsShowLoading.onNext(false)
      if setConfig {
        self.requestSetConfiguaration(output: output, completion: completion)
      } else {
        completion()
      }
    }
  }

  func requestFormat(output: Output) {
    requestTestVolume(setConfig: false, output: output) { [weak self] in
      let header = HeaderModel(cmd: Command.setcommand)
      let command = SetCommandModel(command: "format", param: 2)
      let send = SetCommand.Send(header: header, setcommand: command)
      output.rxIsShowLoading.onNext(true)
      self?.useCase.send(to: send) { response, error in
        output.rxIsShowLoading.onNext(false)
        self?.useCase.disConnect()
        output.rxDisConnect.onNext(())
      }
    }
  }

  func requestInitial(output: Output) {
    requestTestVolume(setConfig: false, output: output) { [weak self] in
      let header = HeaderModel(cmd: Command.setcommand)
      let command = SetCommandModel(command: "confreset", param: 4)
      let send = SetCommand.Send(header: header, setcommand: command)
      output.rxIsShowLoading.onNext(true)
      self?.useCase.send(to: send) { response, error in
//        output.rxIsShowLoading.onNext(false)
//        self?.useCase.disConnect()
        output.rxDisConnect.onNext(())
      }
    }
  }

  func requestFactoryReset(output: Output) {
    requestTestVolume(setConfig: false, output: output) { [weak self] in
      let header = HeaderModel(cmd: Command.setcommand)
      let command = SetCommandModel(command: "factoryreset", param: 5)
      let send = SetCommand.Send(header: header, setcommand: command)
      output.rxIsShowLoading.onNext(true)
      self?.useCase.send(to: send) { response, error in
//        output.rxIsShowLoading.onNext(false)
//        self?.useCase.disConnect()
        output.rxDisConnect.onNext(())
      }
    }
  }

  func requestRestartNetwork(output: Output, completion: @escaping () -> Void) {
    output.rxIsShowLoading.onNext(true)
    let header = HeaderModel(cmd: Command.restartnetwork)
    let send = RestartNetwork.Send(header: header)
    useCase.send(to: send) { [weak self] response, error in
      output.rxIsShowLoading.onNext(false)
      self?.fileSettingType = .None
      completion()
    }
  }

  func unzipFirmware() -> URL? {
    guard let path = UrlList.tempPath(),
      let zipFileUrl = FileManager.loadFileAtFolder(path: path).filter({
        $0.lastPathComponent.contains(".zip")
      }).first
    else {
      Log.warning(to: "Fail to get path.")
      return nil
    }

    do {
      try FileManager().unzipItem(at: zipFileUrl, to: path)
      FileManager.remove(file: zipFileUrl)
      let datFileUrl = FileManager.loadFileAtFolder(path: path).filter({
        $0.lastPathComponent.contains(".dat")
      }).first
      return datFileUrl
    } catch {
      sLogger.error("Extraction of ZIP archive failed with error: \(error)")
      return nil
    }
  }

  func updateFirmware(output: Output) {
    output.rxIsShowLoading.onNext(true)
    guard let datFileUrl = unzipFirmware() else {
      output.rxIsShowLoading.onNext(false)
      return
    }
    useCase.stopKeepAlive()

    // send newFirmware
    let header = HeaderModel(cmd: "newfirmware")
    let newFirmware = NewFirmwareModel.Send.Newfirmware(file: datFileUrl.lastPathComponent)
    let firmwareModel = NewFirmwareModel.Send(header: header, newfirmware: newFirmware)
    useCase.send(to: firmwareModel) { [weak self] result in
      FileManager.remove(file: datFileUrl)
      self?.useCase.disConnect()
      output.rxDisConnect.onNext(())
      
      // 파일 전송 완료 후 성공 팝업 표시
//      DispatchQueue.main.async {
//        LKPopupView.popup.alert {[
//          .title(L.notification_txt.localized),
//          .subTitle(L.dashcam_reset_start_msg.localized),
//          .showCancel(false),
//          .confirmAction([
//            .text(L.simple_text_exit.localized),
//            .bgColor(.vueroidBlue),
//            .tapActionCallback({ })
//          ]),
//          .textAlignment(.left)
//        ]}
//      }
      
      switch result {
      case .success():
        Log.info(category: .WebSocket, to: "firmware update success")
      case .failure(let error):
        Log.error(category: .WebSocket, to: error)
      }
    }
    // send data to binary
    do {
      let datFileData = try Data(contentsOf: datFileUrl)
      useCase.send(to: datFileData) { result in
        FileManager.remove(file: datFileUrl)
        switch result {
        case .success():
          Log.info(category: .WebSocket, to: "firmware upload")
        case .failure(let error):
          Log.error(category: .WebSocket, to: error)
        }
      }
    } catch let error {
      sLogger.error("\(error.localizedDescription)")
    }
  }

  func updateSafetyCam(output: Output) {
    let showLoading = (true, 60)
    let hideLoading = (false, 60)
    output.rxShowLoadingWithTime.onNext(showLoading)
    guard let path = UrlList.safetyDBPath(),
      let safetyFileUrl = FileManager.loadFileAtFolder(path: path).filter({
        $0.lastPathComponent.contains(".dat")
      }).first
    else {
      output.rxShowLoadingWithTime.onNext(hideLoading)
      return
    }

    if safetyFileUrl.lastPathComponent == "others.dat" {
      AppManager.shared.deviceInfo?.countrycode = 256
      let header = HeaderModel(cmd: "setinformation")
      guard let setInfo = AppManager.shared.deviceInfo?.toSetInfo() else { return }
      let model = SetInformationModel.Send(header: header, setinformation: setInfo)
      commandUseCase.send(to: model) { result in
        output.rxShowLoadingWithTime.onNext(hideLoading)
        output.rxRemoveSafetyFile.onNext(())
        try? FileManager.default.removeItem(at: safetyFileUrl)
        Log.info(category: .WebSocket, to: "safetycam other update success")
      }
      return
    }
    // send new safetycam
    useCase.stopKeepAlive()
    let header = HeaderModel(cmd: "newbinaryfile")
    let newBinary = NewBinaryModel.Send.NewBinary(file: safetyFileUrl.lastPathComponent)
    let newBinaryModel = NewBinaryModel.Send(header: header, newbinaryfile: newBinary)
    useCase.send(to: newBinaryModel) { [weak self] result in
      switch result {
      case .success():
        let countryCode =
          safetyFileUrl.lastPathComponent.components(separatedBy: ".").first?.components(
            separatedBy: "_"
          ).last?.uppercased() ?? ""
        guard var setInfo = AppManager.shared.deviceInfo?.toSetInfo() else { return }
        setInfo.countrycode = PopupInitSettingCountryList(code: countryCode).rawValue
        let setInfoHeader = HeaderModel(cmd: "setinformation")
        let model = SetInformationModel.Send(header: setInfoHeader, setinformation: setInfo)
        self?.commandUseCase.send(to: model) { result in
          self?.useCase.startKeepAlive()
          output.rxShowLoadingWithTime.onNext(hideLoading)
          output.rxRemoveSafetyFile.onNext(())
          try? FileManager.default.removeItem(at: safetyFileUrl)
          Log.info(category: .WebSocket, to: "safetycam update success")
        }
        Log.info(category: .WebSocket, to: "safetycam update success")
      case .failure(let error):
        output.rxAlertMessage.onNext(error.localizedDescription)
      }
    }

    // send data to binary
    do {
      let datFileData = try Data(contentsOf: safetyFileUrl)
      useCase.send(to: datFileData) { result in
        FileManager.remove(file: safetyFileUrl)
        output.rxShowLoadingWithTime.onNext(hideLoading)
        switch result {
        case .success():
          Log.info(category: .WebSocket, to: "safetycam upload")
        case .failure(let error):
          output.rxAlertMessage.onNext(error.localizedDescription)
        }
      }
    } catch let error {
      output.rxShowLoadingWithTime.onNext(hideLoading)
      output.rxAlertMessage.onNext(error.localizedDescription)
      sLogger.error("\(error.localizedDescription)")
    }

    return

  }
}

// MARK: - Navigator
extension SettingWifiViewModel {
  func dismiss() {
    coordinator.navigate(to: .settingDidFinish)
  }
}

extension SettingWifiViewModel {
  public func isFormatAndResetUpdate() -> Bool {
    return fileSettingType == .Format
  }

  public func isUpdateSettingConfig() -> (Bool, String?, Int?) {
    if let currentConfig = self.dictionaryForSetting,
       let dashcamConfig = AppManager.shared.dashcamWifiConfig {
      
      // 비교할 주요 속성 목록
      let keysToCompare = [
        "infsensorsen", "parksensorsen", "frontmotionsensoren",
        "videobitrate", "voltagelimit", "parkrectime", "parkmode",
        "timelapse", "sleepmode", "drivetimelapse", "drive-isp-mode",
        "park-isp-mode", "rearmotionsensoren", "audiorec",
        "speakervolume", "alertevent", "timeregion", "summertime",
        "memoryassign", "secureled", "statusled", "parkled",
        "speedunit", "speedmode", "wifionauto", "wifibandwidth", "secretpass",
        "ongps-on", "gmt-index-time", "lcd-ontime", "secretmode",
        "frontResolution", "frequency", "wintermode", "audioguide",
        "frontRotate", "frontMirror", "RearRotate", "InteriorRotate",
        "language", "park_delay", "ParkRearCam3chOnOff", "RearMirror", "InteriorMirror",
        "eventProtection", "rearCamera", "interiorCamera", "datetype", "hdrAlwaysTime",
        "hdrSunriseTime", "hdrSunSetTime", "watermark"
      ]
      
      // ConfigurationModel의 속성 이름과 Dictionary 키 매핑
      let propertyMapping: [String: String] = [
        "driveispmode": "drive-isp-mode",
        "parkispmode": "park-isp-mode"
      ]
      
      // 각 속성 비교
      for key in keysToCompare {
        if let dictValue = currentConfig[key] as? Int {
          // ConfigurationModel에서 해당하는 속성 값 가져오기
          let configValue: Int
          
          if let propertyName = propertyMapping.first(where: { $0.value == key })?.key {
            configValue = getConfigValue(dashcamConfig, propertyName)
          } else {
            configValue = getConfigValue(dashcamConfig, key)
          }
          
          // 값이 다르면 설정이 변경된 것
          if dictValue != configValue {
            sLogger.debug("\(key) 변경: \(dictValue) != \(configValue)")
            return (true, key, dictValue)
          }
        }
      }
      
      return (false, nil, nil)
    }
    
    return (false, nil, nil)
  }
  
  /*
   "getconfiguration": {
       "videobitrate": 1,
       "voltagelimit": 3,
       "parkrectime": 6,
       "parkmode": 1,
       "timelapse": 0,
       "sleepmode": 0,
       "drivetimelapse": 0,
       "infsensorsen": 1,
       "parksensorsen": 1,
       "frontmotionsensoren": 2,
       "rearmotionsensoren": 2,
       "audiorec": 0,
       "speakervolume": 1,
       "alertevent": 0,
       "timeregion": 14,
       "summertime": 0,
       "time": "2025-05-21 05:04:22",
       "memoryassign": 0,
       "language": 0,
       "secureled": 1,
       "speedunit": 0,
       "speedmode": 1,
       "wifionauto": 0,
       "wifibandwidth": 1,
       "drive-isp-mode": 2,
       "park-isp-mode": 0,
       "lcd-ontime": 1,
       "secretmode": 1,
       "secretpass": 1234,
       "ongps-on": 0,
       "gmt-index-time": 0,
       "statusled": 1,
       "parkled": 1,
       "park_delay": 0,
       "ParkRearCam3chOnOff": 1,
       "frontResolution": 1,
       "frequency": 1,
       "wintermode": 0,
       "audioguide": 1,
       "frontRotate": 0,
       "frontMirror": 0,
       "RearRotate": 0,
       "InteriorRotate": 0,
       "RearMirror": 0,
       "InteriorMirror": 0,
       "eventProtection": 0,
       "rearCamera": 1,
       "interiorCamera": 1,
       "datetype": 1,
       "hdrAlwaysTime": 1,
       "hdrSunriseTime": 6,
       "hdrSunSetTime": 18,
       "watermark": 1
     }
   */
  private func getConfigValue(_ config: ConfigurationModel, _ propertyName: String) -> Int {
    switch propertyName {
    case "videobitrate":
      return config.videobitrate
    case "voltagelimit":
      return config.voltagelimit
    case "parkrectime":
      return config.parkrectime
    case "parkmode":
      return config.parkmode
    case "timelapse":
      return config.timelapse
    case "sleepmode":
      return config.sleepmode
    case "drivetimelapse":
      return config.drivetimelapse
    case "driveispmode", "drive-isp-mode":
      return config.driveispmode
    case "parkispmode", "park-isp-mode":
      return config.parkispmode
    case "infsensorsen":
      return config.infsensorsen
    case "parksensorsen":
      return config.parksensorsen
    case "frontmotionsensoren":
      return config.frontmotionsensoren
    case "rearmotionsensoren":
      return config.rearmotionsensoren
    case "audiorec":
      return config.audiorec
    case "speakervolume":
      return config.speakervolume
    case "alertevent":
      return config.alertevent
    case "timeregion":
      return config.timeregion
    case "summertime":
      return config.summertime
    case "memoryassign":
      return config.memoryassign
    case "language":
      return config.language
    case "secureled":
      return config.secureled
    case "statusled":
      return config.statusled
    case "parkled":
      return config.parkled
    case "speedunit":
      return config.speedunit
    case "speedmode":
      return config.speedmode
    case "wifionauto":
      return config.wifionauto
    case "wifibandwidth":
      return config.wifibandwidth
    case "secretpass":
      return config.secretpass
    case "secretmode":
      return config.secretmode
    case "lcd-ontime":
      return config.lcdOntime
    case "ongps-on":
      return config.ongpson
    case "gmt-index-time":
      return config.gmtIndexTime
    case "park_delay":
      return config.parkDelay
    case "eventProtection":
      return config.eventProtection
    case "rearCamera":
      return config.rearCamera
    case "interiorCamera":
      return config.interiorCamera
    case "datetype":
      return config.datetype
    case "hdrAlwaysTime":
      return config.hdrAlwaysTime
    case "hdrSunriseTime":
      return config.hdrSunriseTime
    case "hdrSunSetTime":
      return config.hdrSunSetTime
    case "watermark":
      return config.watermark
    case "frontResolution":
      return config.frontResolution
    case "frequency":
      return config.frequency
    case "wintermode":
      return config.wintermode
    case "audioguide":
      return config.audioguide
    case "frontRotate":
      return config.frontRotate
    case "frontMirror":
      return config.frontMirror
    case "RearRotate":
      return config.rearRotate
    case "InteriorRotate":
      return config.interiorRotate
    case "RearMirror":
      return config.rearMirror
    case "InteriorMirror":
      return config.interiorMirror
    case "ParkRearCam3chOnOff":
      return config.parkRearCam3chOnOff
      
    default:
      sLogger.warning("알 수 없는 속성: \(propertyName)")
      return -1
    }
  }
}

