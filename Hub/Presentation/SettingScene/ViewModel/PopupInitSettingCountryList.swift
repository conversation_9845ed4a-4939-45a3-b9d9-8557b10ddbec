
//
//  PopupInitSettingCountryList.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/30/24.
//

import Foundation

enum PopupInitSettingCountryList: Int, CaseIterable {
  case UAE = 1
  case Austria = 12
  case Autralia = 13
  case Bosnia = 17
  case Belgium = 20
  case Bulgaria = 22
  case Bahrain = 23
  case Brazil = 31
  case Belarus = 36
  case Canada = 38
  case China = 48
  case Czech_Republic = 56
  case Germany = 57
  case Denmark = 59
  case Estonia = 64
  case Spain = 68
  case Finland = 70
  case France = 75
  case UK = 77
  case Greece = 89
  case Croatia = 98
  case Hungary = 100
  case Ireland = 103
  case Israel = 104
  case Iceland = 110
  case Italy = 111
  case Japan = 115
  case Kuwait = 125
  //case Korea = 127
  case Lithuania = 135
  case Luxembourg = 136
  case Latvia = 137
  case Malta = 155
  case Malaysia = 160
  case Netherlands = 168
  case Norway = 169
  case Oman = 175
  case Poland = 182
  case Portugal = 187
  case Qatar = 190
  case Romania = 192
  case Serbia = 193
  case Russia = 194
  case Sweden = 200
  case Singapore = 201
  case Slovenia = 203
  case Slovakia = 205
  case Turkey = 228
  case USA = 236
  case Others = 256
  case Select = 257
  
  init(code: String) {
    switch code {
    case "AU":
      self.init(rawValue: 12)!
      
    case "AT":
      self.init(rawValue: 13)!
    case "BH":
      self.init(rawValue: 23)!
    case "BY":
      self.init(rawValue: 36)!
    case "BE":
      self.init(rawValue: 20)!
    case "BA":
      self.init(rawValue: 17)!
    case "BR":
      self.init(rawValue: 31)!
    case "BG":
      self.init(rawValue: 22)!
    case "CA":
      self.init(rawValue: 38)!
    case "CN":
      self.init(rawValue: 48)!
    case "HR":
      self.init(rawValue: 98)!
    case "CZ":
      self.init(rawValue: 56)!
    case "DK":
      self.init(rawValue: 59)!
    case "EE":
      self.init(rawValue: 64)!
    case "FI":
      self.init(rawValue: 70)!
    case "FR":
      self.init(rawValue: 75)!
    case "DE":
      self.init(rawValue: 57)!
    case "GR":
      self.init(rawValue: 89)!
    case "HU":
      self.init(rawValue: 100)!
    case "IS":
      self.init(rawValue: 110)!
    case "IE":
      self.init(rawValue: 103)!
    case "IL":
      self.init(rawValue: 104)!
    case "IT":
      self.init(rawValue: 111)!
    case "JP":
      self.init(rawValue: 115)!
    case "KW":
      self.init(rawValue: 125)!
    case "LV":
      self.init(rawValue: 137)!
    case "LT":
      self.init(rawValue: 135)!
    case "LU":
      self.init(rawValue: 136)!
    case "MT":
      self.init(rawValue: 155)!
    case "MY":
      self.init(rawValue: 160)!
    case "NL":
      self.init(rawValue: 168)!
    case "NO":
      self.init(rawValue: 169)!
    case "OM":
      self.init(rawValue: 175)!
    case "PL":
      self.init(rawValue: 182)!
    case "PT":
      self.init(rawValue: 187)!
    case "QA":
      self.init(rawValue: 190)!
    case "RO":
      self.init(rawValue: 192)!
    case "RU":
      self.init(rawValue: 194)!
    case "RS":
      self.init(rawValue: 193)!
    case "SG":
      self.init(rawValue: 201)!
    case "SK":
      self.init(rawValue: 205)!
    case "SI":
      self.init(rawValue: 203)!
    case "ES":
      self.init(rawValue: 68)!
    case "SE":
      self.init(rawValue: 200)!
    case "TR":
      self.init(rawValue: 228)!
    case "AE":
      self.init(rawValue: 1)!
    case "GB":
      self.init(rawValue: 77)!
    case "US":
      self.init(rawValue: 236)!
    case "Others":
      self.init(rawValue: 256)!
    default:
      self.init(rawValue: 1)!
    }
  }

  static var displayCases: [PopupInitSettingCountryList] = [
    .UAE, .Austria, .Autralia,
    .Bosnia, .Belgium, .Bulgaria, .Bahrain, .Brazil, .Belarus,
    .Canada, .China, .Czech_Republic, .Germany, .Denmark, .Estonia,
    .Spain, .Finland, .France, .UK, .Greece, .Croatia, .Hungary,
    .Ireland, .Israel, .Iceland, .Italy, .Japan, .Kuwait, /*.Korea,*/
    .Lithuania, .Luxembourg, .Latvia, .Malta, .Malaysia, .Netherlands,
    .Norway, .Oman, .Poland, .Portugal, .Qatar, .Romania,
    .Serbia, .Russia, .Sweden, .Singapore,
    .Slovenia, .Slovakia, .Turkey, .USA, .Others,
  ]

  var name: String {
    switch self {
    case .UAE:
      return "UAE"
    case .Austria:
      return "Austria"
    case .Autralia:
      return "Autralia"
    case .Bosnia:
      return "Bosnia"
    case .Belgium:
      return "Belgium"
    case .Bulgaria:
      return "Bulgaria"
    case .Bahrain:
      return "Bahrain"
    case .Brazil:
      return "Brazil"
    case .Belarus:
      return "Belarus"
    case .Canada:
      return "Canada"
    case .China:
      return "China"
    case .Czech_Republic:
      return "Czech Republic"
    case .Germany:
      return "Germany"
    case .Denmark:
      return "Denmark"
    case .Estonia:
      return "Estonia"
    case .Spain:
      return "Spain"
    case .Finland:
      return "Finland"
    case .France:
      return "France"
    case .UK:
      return "UK"
    case .Greece:
      return "Greece"
    case .Croatia:
      return "Croatia"
    case .Hungary:
      return "Hungary"
    case .Ireland:
      return "Ireland"
    case .Israel:
      return "Israel"
    case .Iceland:
      return "Iceland"
    case .Italy:
      return "Italy"
    case .Japan:
      return "Japan"
    case .Kuwait:
      return "Kuwait"
    //case .Korea:
    //    return "Korea"
    case .Lithuania:
      return "Lithuania"
    case .Luxembourg:
      return "Luxembourg"
    case .Latvia:
      return "Latvia"
    case .Malta:
      return "Malta"
    case .Malaysia:
      return "Malaysia"
    case .Netherlands:
      return "Netherlands"
    case .Norway:
      return "Norway"
    case .Oman:
      return "Oman"
    case .Poland:
      return "Poland"
    case .Portugal:
      return "Portugal"
    case .Qatar:
      return "Qatar"
    case .Romania:
      return "Romania"
    case .Serbia:
      return "Serbia"
    case .Russia:
      return "Russia"
    case .Sweden:
      return "Sweden"
    case .Singapore:
      return "Singapore"
    case .Slovenia:
      return "Slovenia"
    case .Slovakia:
      return "Slovakia"
    case .Turkey:
      return "Turkey"
    case .USA:
      return "USA"
    case .Others:
      return "Others"
    case .Select:
      return "Select"
    }
  }

  var code: String {
    switch self {
    case .Autralia:
      return "AU"
    case .Austria:
      return "AT"
    case .Bahrain:
      return "BH"
    case .Belarus:
      return "BY"
    case .Belgium:
      return "BE"
    case .Bosnia:
      return "BA"
    case .Brazil:
      return "BR"
    case .Bulgaria:
      return "BG"
    case .Canada:
      return "CA"
    case .China:
      return "CN"
    case .Croatia:
      return "HR"
    case .Czech_Republic:
      return "CZ"
    case .Denmark:
      return "DK"
    case .Estonia:
      return "EE"
    case .Finland:
      return "FI"
    case .France:
      return "FR"
    case .Germany:
      return "DE"
    case .Greece:
      return "GR"
    case .Hungary:
      return "HU"
    case .Iceland:
      return "IS"
    case .Ireland:
      return "IE"
    case .Israel:
      return "IL"
    case .Italy:
      return "IT"
    case .Japan:
      return "JP"
    //case .Korea:
    //    return "KO"
    case .Kuwait:
      return "KW"
    case .Latvia:
      return "LV"
    case .Lithuania:
      return "LT"
    case .Luxembourg:
      return "LU"
    case .Malta:
      return "MT"
    case .Malaysia:
      return "MY"
    case .Netherlands:
      return "NL"
    case .Norway:
      return "NO"
    case .Oman:
      return "OM"
    case .Poland:
      return "PL"
    case .Portugal:
      return "PT"
    case .Qatar:
      return "QA"
    case .Romania:
      return "RO"
    case .Russia:
      return "RU"
    case .Serbia:
      return "RS"
    case .Singapore:
      return "SG"
    case .Slovakia:
      return "SK"
    case .Slovenia:
      return "SI"
    case .Spain:
      return "ES"
    case .Sweden:
      return "SE"
    case .Turkey:
      return "TR"
    case .UAE:
      return "AE"
    case .UK:
      return "GB"
    case .USA:
      return "US"
    case .Others:
      return "Others"
    default:
      return ""
    }
  }
}
