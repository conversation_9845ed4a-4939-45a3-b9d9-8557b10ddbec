//
//  SettingViewController.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import RxRelay
import RxSwift
import UIKit
import WebKit

class TravelMapViewController: UIViewController, Orientable {
  let disposedBag = DisposeBag()
  
  let mapContainerView = TravelMapContainView()
  let travelInfoView = TravelInfoView()
  let controlView = MapControlView()
  
  
  var viewModel: TravelMapViewModel?
  var wifiViewModel: TravelMapWifiViewModel?

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setComponent()
    setAutoLayout()
    bindViewModel(to: viewModel)
    bindViewModel(to: wifiViewModel)
  }

  func setComponent() {
    self.title = L.driving_history.localized
    navigationItem.backButtonTitle = ""
    view.backgroundColor = .background
    view.addBody(mapContainerView)
    mapContainerView.addSubview(travelInfoView)
    mapContainerView.addSubview(controlView)
  }

  func setAutoLayout() {
    travelInfoView.pin.horizontally(offset: 15).height(174.rv).bottom(offset: -35.rv, safe: true).activate()
    controlView.pin.top(travelInfoView, offset: 14).horizontally(offset: 35).height(20.rv).activate()
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? TravelMapViewModel else { return }

    let traceButtonEvent = mapContainerView.traceButton.rx.tap.asObservable()
    let input = TravelMapViewModel.Input(
      traceButtonEvent: traceButtonEvent
    )
    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsLandscape
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isValue in
        guard let self = self else { return }
        AppUtility.lockOrientation(isValue ? .landscapeRight : .portrait)
        self.setOrientaion(isLandscape: isValue)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
          self.setOrientaionViews(isLandscape: isValue)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxTraceButtonState
      .bind(to: mapContainerView.traceButton.rx.isSelected)
      .disposed(by: self.disposedBag)
  }

  internal func setOrientaionViews(isLandscape: Bool) {
    travelInfoView.isHidden = isLandscape
  }
}

extension TravelMapViewController {
  func setInfo(model: TravelLogCellModel, count: String) {
    let string = String(format: "%@\n%@ ~ %@", model.date, model.startTm, model.endTm)
    mapContainerView.dateSelectView.dateLabel.text = string
    mapContainerView.countLabel.text = count
    
    // UIPageControl 업데이트
    mapContainerView.dateSelectView.updatePageControl(with: count)

    var topStack: [TravelInfoItemView] = []

    var isMile = Current.wifiHistorySpeedUnit == .mph

    switch model.type {
    case 1:
      let topItem1 = TravelInfoItemView()
      topItem1.itemLabel.text = L.distance_drive_txt.localized
      let distance = Double(model.distance) * (isMile ? 1.0 : 1.60934)
      let strDistance = String(format: "%.1f", distance)
      topItem1.valueLabel.text = strDistance
      topItem1.unitLabel.text = isMile ? L.unit_mile.localized : L.unit_kilo.localized
      
      
      let topItem2 = TravelInfoItemView()
      topItem2.itemLabel.text = L.average_speed_txt.localized
      
      let avg = Double(model.avgSpeed) * (isMile ? 1.0 : 1.60934)
      let strAvg = String(format: "%.1f", avg)
      topItem2.valueLabel.text = strAvg
      topItem2.unitLabel.text = isMile ? L.unit_mph.localized : L.unit_kmh.localized
      
      
      let bottomItem1 = TravelInfoItemView()
      bottomItem1.itemLabel.text = L.history_top_speed.localized
      let max = Double(model.maxSpeed) * (isMile ? 1.0 : 1.60934)
      let strMax = String(format: "%.1f", max)
      bottomItem1.valueLabel.text = strMax
      bottomItem1.unitLabel.text = isMile ? L.unit_mph.localized : L.unit_kmh.localized
      
      
      let bottomItem2 = TravelInfoItemView()
      bottomItem2.itemLabel.text = L.history_event_value01.localized
      bottomItem2.valueLabel.text = ""
      bottomItem2.unitLabel.text = String(
        format: L.shock_recording_value.localized, "\(model.evtNum)")
      
      topStack.append(topItem1)
      topStack.append(bottomItem1)
      topStack.append(bottomItem2)
      topStack.append(topItem2)
    default:
      let topItem1 = TravelInfoItemView()
      topItem1.itemLabel.text = L.history_park_recording_time.localized
      var endTm = model.endTm
      if model.endTm == "00:00:00" {
        endTm = "24:00:00"
      }
      let diffMinute = Int((endTm.toMinute() - model.startTm.toMinute()).rounded())
      let hourString = "\(diffMinute / 60)" + L.travel_detail_park_time_type01.localized
      let minuteString = "\(diffMinute % 60)" + L.travel_detail_park_time_type02.localized
      topItem1.valueLabel.text = diffMinute / 60 == 0 ? minuteString : hourString + " " + minuteString
      topStack.append(topItem1)
      
      let bottomItem1 = TravelInfoItemView()
      bottomItem1.itemLabel.text = L.history_park_shock_event.localized
      bottomItem1.valueLabel.text = ""
      bottomItem1.unitLabel.text = String(
        format: L.shock_recording_value.localized, "\(model.evtNum)")
      topStack.append(bottomItem1)

      let bottomItem2 = TravelInfoItemView()
      bottomItem2.itemLabel.text = L.history_park_motion_event.localized
      bottomItem2.valueLabel.text = ""
      bottomItem2.unitLabel.text = String(
        format: L.shock_recording_value.localized, "\(model.motNum)")
      topStack.append(bottomItem2)
    }

    travelInfoView.setInfoData(topStackData: topStack)
  }
}
