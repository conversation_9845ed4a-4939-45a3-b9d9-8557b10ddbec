//
//  DefaultTravelMapCoordinator.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import UIKit

final class DefaultTravelMapCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: TravelMapCoordinatorFinishDelegate?

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  func start(with step: TravelMapSteps) {
    navigate(to: step)
  }
}

extension DefaultTravelMapCoordinator: TravelMapCoordinator {
  func navigate(to step: TravelMapSteps) {
    switch step {
    case let .showTravelMap(items: items, index: index):
      travelMapWith(items: items, index: index)
    case .travelMapDidFinish:
      navigationController.popViewController(animated: true)
      delegate?.travelMapCoordinatorDidFinish()
    }
  }

  func travelMapWith(items: [TravelLogCellModel], index: Int) {
    let vc = TravelMapViewController()
    vc.viewModel = TravelMapViewModel(coordinator: self)
    configWifiModel(viewController: vc, item: items, index: index)
    navigationController.pushViewController(vc, animated: true)
  }

  func showTravelMap() {
    let vc = TravelMapViewController()
    vc.viewModel = TravelMapViewModel(coordinator: self)
    configWifiModel(viewController: vc)
    navigationController.pushViewController(vc, animated: true)
  }
}

extension DefaultTravelMapCoordinator {
  func configWifiModel(
    viewController: TravelMapViewController, item: [TravelLogCellModel]? = nil, index: Int = 0
  ) {
    viewController.wifiViewModel = TravelMapWifiViewModel(
      coordinator: self, dbUseCase: Composers.dbUsecase)
    viewController.wifiViewModel?.items = item
    viewController.wifiViewModel?.index = index
  }
}
