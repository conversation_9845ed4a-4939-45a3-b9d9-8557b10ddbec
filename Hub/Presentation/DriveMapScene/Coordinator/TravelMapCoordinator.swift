//
//  TravelMapCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

protocol TravelMapCoordinator: AnyObject {
  func navigate(to step: TravelMapSteps)
}

public protocol TravelMapCoordinatorFinishDelegate: AnyObject {
  func travelMapCoordinatorDidFinish()
}

public enum TravelMapSteps: Step {
  case showTravelMap(items: [TravelLogCellModel], index: Int)
  case travelMapDidFinish
}
