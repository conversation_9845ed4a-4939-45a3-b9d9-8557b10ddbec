//
//  SettingViewModel.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import RxRelay
import RxSwift
import UIKit

class TravelMapViewModel: BaseViewModel {
  weak var coordinator: TravelMapCoordinator?
  var isSelected: Bool = false

  init(coordinator: TravelMapCoordinator) {
    self.coordinator = coordinator
  }

  struct Input {
    let traceButtonEvent: Observable<Void>
  }

  struct Output {
    let rxIsLandscape = PublishRelay<Bool>()  // 0: port, 1: land
    let rxTraceButtonState = PublishRelay<Bool>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.traceButtonEvent
      .scan(false) { oldValue, newValue in
        !oldValue
      }
      .bind(to: output.rxTraceButtonState)
      .disposed(by: disposedBag)

    return output
  }
}

extension TravelMapViewModel {
  func dismiss() {
    coordinator?.navigate(to: .travelMapDidFinish)
  }
}
