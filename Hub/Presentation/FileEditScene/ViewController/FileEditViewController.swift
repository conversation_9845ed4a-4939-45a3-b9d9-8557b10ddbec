//
//  FileEditViewController.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import RxCocoa
import RxSwift
import SnapKit
import UIKit

public enum FileEditType {
  case privacyProtection
  case trim
}

class FileEditViewController: UIViewController {
  let menuView = FileEditMenuView()
  let playerContainerView = EditPlayerContainerView()
  let convertButton = UIButton.customStyleButton(title: L.trim.localized)
  var disposedBag = DisposeBag()
  
  var channelTitles: [String] = []
  lazy var channelSheetMenuViewController: SheetViewController = {
    let viewController = SheetViewController()
    viewController.viewModel = SheetMenuViewModel()
    return viewController
  }()

  var viewModel: FileEditViewModel?
  var playerViewModel: FilePlayerViewModel?
  var editType: FileEditType?

  deinit {
    removeChild(channelSheetMenuViewController)
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  override func viewDidLoad() {
    super.viewDidLoad()

    self.title = L.my_library_title.localized
    navigationItem.backButtonTitle = ""
    self.view.backgroundColor = .background

    setComponent()
    setAutoLayout()
    bindViewModel(to: viewModel)
    bindViewModel(to: playerViewModel)
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    addChildController(channelSheetMenuViewController)
  }

   func setComponent() {

    menuView.nameLabel.text = "20230314_103709_INF_1.mp4"
    //        menuView.trackItems = ["1", "2", "3"]
    //        menuView.channelItems = ["1ch", "2ch", "3ch"]
    view.addSubview(menuView)
    view.addSubview(playerContainerView)
    view.addSubview(convertButton)
  }

  func setAutoLayout() {
    let playerHeight = playerContainerView.baseHeight + 45 + 32 + 10 + 17
    
    Pin.activate([
      menuView.pin.top(safe:true).horizontally().height(64.rv),
      playerContainerView.pin.below(menuView, offset: 60.rv).horizontally().height(377.rv),
      convertButton.pin.height(60).horizontally(offset: 15).bottom(offset: -35, safe: true),
    ])
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? FileEditViewModel,
          let playerViewModel = playerViewModel
    else { return }
    
    //    let backEvent = navigationView.leftButton.rx.tap.asObservable()
    let viewWillAppear = self.rx.viewWillAppear.asObservable()
    let viewWillDisappearEvent = self.rx.viewWillDisappear.asObservable()
    //    let gpsTapEvent = self.editMenuView.gpsButton.rx.tapWithIsSelected.asObservable()
    //    let audioTapEvent = self.editMenuView.audioButton.rx.tapWithIsSelected.asObservable()
    let switchChannel = self.channelSheetMenuViewController.tableView.rx.itemSelected.asObservable()
    
    let convertButtonEvent = convertButton.rx.tap
      .map { [weak self] _ -> tapConvertBtn in
        let high = self?.playerContainerView.sliderContainerView.higherValueLabel.text ?? "00:00"
        let low = self?.playerContainerView.sliderContainerView.lowerValueLabel.text ?? "00:00"
        let type = self?.editType ?? .trim
        return (type: type, low: low, high: high)
      }
      .asObservable()
    
    let input = FileEditViewModel.Input(
      viewWillAppear: viewWillAppear,
      willDisappearEvent: viewWillDisappearEvent,
      switchChannel: switchChannel,
      convertButtonEvent: convertButtonEvent
    )
    
    let output = model.bind(input: input, disposedBag: self.disposedBag)
    
    output.rxLoadImages
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] images in
        guard let self = self else { return }
        self.playerContainerView.sliderContainerView.setThumbnail(images: images)
      })
      .disposed(by: self.disposedBag)
    
    output.rxLoadInfo
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] model in
        guard let self = self else { return }
        self.menuView.nameLabel.text = model.fileName
        self.menuView.dateLabel.text = model.date.toString(format: "yyyy/MM/dd HH:mm:ss")
        
        let channelCount = model.channelbits.channelBitMaskingCount
        for i in 0..<channelCount {
          self.channelTitles.append("\(i+1)ch")
        }
        
        let model = SheetMenuModel(title: L.channel_select_txt.localized, items: self.channelTitles)
        self.channelSheetMenuViewController.setItems(model: model)
      })
      .disposed(by: self.disposedBag)
    
    output.rxIsPause
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let player = self.playerContainerView.playerView
        player.pause()
        self.playerContainerView.handlerView.playButton.isSelected = true
      })
      .disposed(by: self.disposedBag)

    output.rxIsExccedAiUsageLimit
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] result in
        guard let self = self else { return }
        guard result else { return }
        LKPopupView.popup.alert {[
          .subTitle(L.ai_function_limit_msg.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({ })
          ])
        ]}
      }).disposed(by: disposedBag)
  
                 
    
    output.rxIsCompleteWithUrl
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] url in
        guard let self = self, let url = url else { return }
        playerViewModel.rxIsStop.onNext(true)
        switch self.editType {
        case .trim:
          mLogger.log("trim rxIsCompleteWithUrl")
          LKPopupView.popup.alert {[
            .title(L.dialog_save_title.localized),
            .subTitle(L.dialog_save_success.localized),
            .showCancel(false),
            .confirmAction([
              .text(L.ok.localized),
              .bgColor(.vueroidBlue),
              .tapActionCallback({
                self.viewModel?.coordinator?.navigate(to: .fileEditDidFinish)
              })
            ])
          ]}
          break
        case .privacyProtection:
          mLogger.log("privacyProtection rxIsCompleteWithUrl: \(url)")
          self.viewModel?.coordinator?.navigate(to: .upDownload(url: url, taskType: .deIdentified) )
          break
        default:
          break
        }

      })
      .disposed(by: disposedBag)

    output.rxIsIndicator
      .subscribe(onNext: { ret in
        ret ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)
#if false
    self.menuView.channelButton.rx.tap
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] in
        playerViewModel.rxIsPause.onNext(true)
        self?.channelSheetMenuViewController.show()
      })
      .disposed(by: disposedBag)
    
    output.rxChannelButtonState
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] row in
        guard let self = self else { return }
//        self.menuView.channelButton.setTitle(self.channelTitles[row], for: .normal)
        self.channelSheetMenuViewController.hide()
      })
      .disposed(by: disposedBag)
#endif
  }
}

extension FileEditViewController {
  func bindViewModel(to viewModel: FilePlayerViewModel?) {
    guard let playerViewModel = viewModel else { return }

    let viewWillAppear = self.rx.viewWillAppear.asObservable()
    let switchChannelEvent = self.channelSheetMenuViewController.tableView.rx.itemSelected
      .asObservable()

    let rxState = self.playerContainerView.playerView.rxIsState
    let playButtonEvent = playerContainerView.handlerView.playButton.rx.tapWithIsSelected
      .asObservable()
    let remainValue = playerContainerView.playerView.rxRemainingTime.asObserver()
    let progressValue = playerContainerView.playerView.rxTime.asObserver()
    let sliderValue = playerContainerView.sliderContainerView.rxSliderValue.asObservable()

    let input = FilePlayerViewModel.Input(
      viewWillAppear: viewWillAppear,
      rtspPlayerState: rxState,
      playButtonEvent: playButtonEvent,
      progressTime: progressValue,
      remainTime: remainValue,
      sliderValue: sliderValue,
      switchChannel: switchChannelEvent)

    let output = playerViewModel.bind(input: input, disposedBag: self.disposedBag)
    output.rxUrl
      .observe(on: MainScheduler.asyncInstance)
      .do(onNext: { [weak self] url, _ in
        self?.playerContainerView.openStreamWithoutPlay(url: url)
      })
      .delay(.milliseconds(500), scheduler: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] url, startTime in
        guard let self = self else { return }
        self.playerContainerView.playerView.startWith(sec: Double(startTime) / 1000.0)
        self.playerContainerView.handlerView.playButton.isSelected = false
        playerViewModel.isStop = false
      })
      .disposed(by: self.disposedBag)

    output.rxChangeChannel
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] channel, startTime in
        Log.message(category: .VLC, to: "channel: \(channel) > \(startTime)")
        if channel == 0 {
          self?.playerContainerView.playerView.mediaPlayer.stop()
        } else {
          self?.playerContainerView.playerView.mediaPlayer.currentVideoTrackIndex = Int32(channel)
        }
        DispatchQueue.main.asyncAfter(deadline: .now()+0.5) {
          playerViewModel.rxIsStart.onNext(true)
        }
      })
      .disposed(by: disposedBag)

    output.rxIsDisplay
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        if ret {
          self.playerContainerView.pause()
          self.playerContainerView.handlerView.playButton.isSelected = true
        }
      })
      .disposed(by: self.disposedBag)

    playerViewModel.rxIsPause
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let player = self.playerContainerView.playerView
        player.pause()
        self.playerContainerView.handlerView.playButton.isSelected = true
      })
      .disposed(by: self.disposedBag)

    playerViewModel.rxIsStart
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let player = self.playerContainerView.playerView
        if playerViewModel.isStop {
          let low = self.playerContainerView.sliderContainerView.slider.lowerValue
          let duration = self.playerContainerView.sliderContainerView.duration
          let interval = Double(duration) * low / 100.0 / 1000
          // TODO: 간헐적으로 구간 종료후 시작구간 0초 -> 종료구간 끝초 로 변경하면 이전의 값이 남아있는 경우가 있음
          player.startWith(sec: interval)
        } else {
          player.play()
        }
        self.playerContainerView.handlerView.playButton.isSelected = false
        playerViewModel.isStop = false
      })
      .disposed(by: self.disposedBag)

    playerViewModel.rxIsStop
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isPause in
        guard let self = self else { return }
        let player = self.playerContainerView.playerView
        playerViewModel.isStop = true
        player.stop()
        self.playerContainerView.handlerView.playButton.isSelected = true
      })
      .disposed(by: self.disposedBag)

    output.rxIsSlider
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] lowerValue in
        guard let self = self else { return }
        let player = self.playerContainerView.playerView
        player.pause()
        player.seek(value: Float(lowerValue))
        self.playerContainerView.handlerView.playButton.isSelected = true
      })
      .disposed(by: self.disposedBag)

    output.rxDuration
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] time in
        guard let self = self else { return }
        self.playerContainerView.sliderContainerView.duration = time
      })
      .disposed(by: self.disposedBag)

    output.rxTime
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] time in
        guard let self = self else { return }
        self.playerContainerView.sliderContainerView.changeTime(time: time)
      })
      .disposed(by: self.disposedBag)
  }
}
