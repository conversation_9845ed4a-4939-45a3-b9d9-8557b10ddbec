//
//  ImageCropViewController+CropFix.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import UIKit

/**
 * ImageCropView 크롭 영역 계산 수정 사용 예제
 * 
 * 수정된 cropAreaInImage 계산 로직을 포함한 ImageCropView의 사용법을 보여줍니다.
 * 이미지 확대/축소 및 드래그 후에도 정확한 크롭 영역을 계산합니다.
 */

extension UIViewController {
  
  // MARK: - 수정된 ImageCropView 사용 예제
  
  /// 수정된 크롭 계산 로직을 사용하는 ImageCropView 설정
  func setupFixedImageCropView() -> ImageCropView {
    let imageCropView = ImageCropView(frame: view.bounds)
    
    // 테스트용 이미지 설정
    if let sampleImage = UIImage(named: "sample_image") {
      imageCropView.image = sampleImage
    }
    
    view.addSubview(imageCropView)
    
    // Auto Layout 설정
    imageCropView.translatesAutoresizingMaskIntoConstraints = false
    NSLayoutConstraint.activate([
      imageCropView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
      imageCropView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
      imageCropView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
      imageCropView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -100)
    ])
    
    return imageCropView
  }
  
  /// 크롭 테스트 버튼들을 추가하는 메서드
  func addCropTestButtons(for imageCropView: ImageCropView) {
    let stackView = UIStackView()
    stackView.axis = .horizontal
    stackView.distribution = .fillEqually
    stackView.spacing = 10
    stackView.translatesAutoresizingMaskIntoConstraints = false
    
    view.addSubview(stackView)
    
    NSLayoutConstraint.activate([
      stackView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
      stackView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
      stackView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
      stackView.heightAnchor.constraint(equalToConstant: 44)
    ])
    
    // 버튼들 생성
    let logStateButton = createButton(title: "상태 로그", action: #selector(logTransformState))
    let validateButton = createButton(title: "계산 검증", action: #selector(validateCropCalculation))
    let testCropButton = createButton(title: "크롭 테스트", action: #selector(testCropArea))
    let demoButton = createButton(title: "변환 데모", action: #selector(demonstrateTransform))
    
    stackView.addArrangedSubview(logStateButton)
    stackView.addArrangedSubview(validateButton)
    stackView.addArrangedSubview(testCropButton)
    stackView.addArrangedSubview(demoButton)
    
    // ImageCropView를 저장 (실제 구현에서는 적절한 방법 사용)
    objc_setAssociatedObject(self, "fixedImageCropView", imageCropView, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
  }
  
  private func createButton(title: String, action: Selector) -> UIButton {
    let button = UIButton(type: .system)
    button.setTitle(title, for: .normal)
    button.backgroundColor = .systemBlue
    button.setTitleColor(.white, for: .normal)
    button.layer.cornerRadius = 8
    button.addTarget(self, action: action, for: .touchUpInside)
    return button
  }
  
  // MARK: - 버튼 액션 메서드들
  
  @objc private func logTransformState() {
    guard let imageCropView = objc_getAssociatedObject(self, "fixedImageCropView") as? ImageCropView else { return }
    
    imageCropView.logCurrentTransformState()
    
    let alert = UIAlertController(
      title: "상태 로그",
      message: "현재 변환 상태가 콘솔에 출력되었습니다.",
      preferredStyle: .alert
    )
    alert.addAction(UIAlertAction(title: "확인", style: .default))
    present(alert, animated: true)
  }
  
  @objc private func validateCropCalculation() {
    guard let imageCropView = objc_getAssociatedObject(self, "fixedImageCropView") as? ImageCropView else { return }
    
    let isValid = imageCropView.validateCropAreaCalculation()
    
    let alert = UIAlertController(
      title: "계산 검증 결과",
      message: isValid ? "✅ 크롭 영역 계산이 정확합니다" : "❌ 크롭 영역 계산에 문제가 있습니다",
      preferredStyle: .alert
    )
    alert.addAction(UIAlertAction(title: "확인", style: .default))
    present(alert, animated: true)
  }
  
  @objc private func testCropArea() {
    guard let imageCropView = objc_getAssociatedObject(self, "fixedImageCropView") as? ImageCropView else { return }
    
    let cropRect = imageCropView.cropAreaInImage
    
    let alert = UIAlertController(
      title: "크롭 영역 정보",
      message: """
      현재 크롭 영역 (이미지 좌표계):
      X: \(Int(cropRect.origin.x))
      Y: \(Int(cropRect.origin.y))
      Width: \(Int(cropRect.width))
      Height: \(Int(cropRect.height))
      
      이 값을 사용하여 실제 이미지를 크롭할 수 있습니다.
      """,
      preferredStyle: .alert
    )
    
    alert.addAction(UIAlertAction(title: "확인", style: .default))
    alert.addAction(UIAlertAction(title: "크롭 실행", style: .default) { _ in
      self.performActualCrop(cropRect: cropRect)
    })
    
    present(alert, animated: true)
  }
  
  @objc private func demonstrateTransform() {
    guard let imageCropView = objc_getAssociatedObject(self, "fixedImageCropView") as? ImageCropView else { return }
    
    let alert = UIAlertController(
      title: "변환 데모",
      message: "이미지를 자동으로 확대하고 이동한 후 크롭 영역을 확인합니다.",
      preferredStyle: .alert
    )
    
    alert.addAction(UIAlertAction(title: "시작", style: .default) { _ in
      imageCropView.demonstrateCropAfterTransform()
    })
    alert.addAction(UIAlertAction(title: "취소", style: .cancel))
    
    present(alert, animated: true)
  }
  
  // MARK: - 실제 크롭 실행 예제
  
  private func performActualCrop(cropRect: CGRect) {
    guard let imageCropView = objc_getAssociatedObject(self, "fixedImageCropView") as? ImageCropView,
          let originalImage = imageCropView.image else { return }
    
    // 실제 이미지 크롭 실행
    let croppedImage = cropImage(originalImage, to: cropRect)
    
    // 결과 표시
    showCroppedImageResult(original: originalImage, cropped: croppedImage, cropRect: cropRect)
  }
  
  private func cropImage(_ image: UIImage, to rect: CGRect) -> UIImage? {
    guard let cgImage = image.cgImage else { return nil }
    
    let cropRect = CGRect(
      x: rect.origin.x * image.scale,
      y: rect.origin.y * image.scale,
      width: rect.width * image.scale,
      height: rect.height * image.scale
    )
    
    guard let croppedCGImage = cgImage.cropping(to: cropRect) else { return nil }
    
    return UIImage(cgImage: croppedCGImage, scale: image.scale, orientation: image.imageOrientation)
  }
  
  private func showCroppedImageResult(original: UIImage, cropped: UIImage?, cropRect: CGRect) {
    let alert = UIAlertController(
      title: "크롭 결과",
      message: cropped != nil ? "✅ 크롭이 성공적으로 완료되었습니다" : "❌ 크롭에 실패했습니다",
      preferredStyle: .alert
    )
    
    if let croppedImage = cropped {
      alert.addAction(UIAlertAction(title: "결과 보기", style: .default) { _ in
        self.presentImageComparisonView(original: original, cropped: croppedImage)
      })
    }
    
    alert.addAction(UIAlertAction(title: "확인", style: .default))
    present(alert, animated: true)
  }
  
  private func presentImageComparisonView(original: UIImage, cropped: UIImage) {
    let comparisonVC = ImageComparisonViewController()
    comparisonVC.originalImage = original
    comparisonVC.croppedImage = cropped
    comparisonVC.modalPresentationStyle = .fullScreen
    present(comparisonVC, animated: true)
  }
}

// MARK: - 이미지 비교 화면

class ImageComparisonViewController: UIViewController {
  var originalImage: UIImage?
  var croppedImage: UIImage?
  
  private let scrollView = UIScrollView()
  private let contentView = UIView()
  private let originalImageView = UIImageView()
  private let croppedImageView = UIImageView()
  
  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
  }
  
  private func setupUI() {
    view.backgroundColor = .systemBackground
    
    // 닫기 버튼
    let closeButton = UIButton(type: .system)
    closeButton.setTitle("Close", for: .normal)
    closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
    closeButton.translatesAutoresizingMaskIntoConstraints = false
    
    // 스크롤뷰 설정
    scrollView.translatesAutoresizingMaskIntoConstraints = false
    contentView.translatesAutoresizingMaskIntoConstraints = false
    
    // 이미지뷰 설정
    originalImageView.contentMode = .scaleAspectFit
    originalImageView.image = originalImage
    originalImageView.translatesAutoresizingMaskIntoConstraints = false
    
    croppedImageView.contentMode = .scaleAspectFit
    croppedImageView.image = croppedImage
    croppedImageView.translatesAutoresizingMaskIntoConstraints = false
    
    // 레이블
    let originalLabel = UILabel()
    originalLabel.text = "원본 이미지"
    originalLabel.textAlignment = .center
    originalLabel.font = .boldSystemFont(ofSize: 16)
    originalLabel.translatesAutoresizingMaskIntoConstraints = false
    
    let croppedLabel = UILabel()
    croppedLabel.text = "크롭된 이미지"
    croppedLabel.textAlignment = .center
    croppedLabel.font = .boldSystemFont(ofSize: 16)
    croppedLabel.translatesAutoresizingMaskIntoConstraints = false
    
    // 뷰 계층 구성
    view.addSubview(closeButton)
    view.addSubview(scrollView)
    scrollView.addSubview(contentView)
    contentView.addSubview(originalLabel)
    contentView.addSubview(originalImageView)
    contentView.addSubview(croppedLabel)
    contentView.addSubview(croppedImageView)
    
    // 제약 조건 설정
    NSLayoutConstraint.activate([
      closeButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 10),
      closeButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
      
      scrollView.topAnchor.constraint(equalTo: closeButton.bottomAnchor, constant: 10),
      scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
      scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
      scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
      
      contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
      contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
      contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
      contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
      contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
      
      originalLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
      originalLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
      originalLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
      
      originalImageView.topAnchor.constraint(equalTo: originalLabel.bottomAnchor, constant: 10),
      originalImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
      originalImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
      originalImageView.heightAnchor.constraint(equalToConstant: 200),
      
      croppedLabel.topAnchor.constraint(equalTo: originalImageView.bottomAnchor, constant: 30),
      croppedLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
      croppedLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
      
      croppedImageView.topAnchor.constraint(equalTo: croppedLabel.bottomAnchor, constant: 10),
      croppedImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
      croppedImageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
      croppedImageView.heightAnchor.constraint(equalToConstant: 200),
      croppedImageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20)
    ])
  }
  
  @objc private func closeButtonTapped() {
    dismiss(animated: true)
  }
}
