//
//  ImageCropViewController.swift
//  Hub
//
//  Created by ncn on 5/27/25.
//

import UIKit

// MARK: - ImageCropViewControllerDelegate Protocol

public protocol ImageCropViewControllerDelegate: AnyObject {
  func imageCropViewControllerSuccess(_ controller: UIViewController, didFinishCroppingImage croppedImage: UIImage)
  func imageCropViewControllerDidCancel(_ controller: UIViewController)
}

// MARK: - ImageCropViewController

public enum CaptureCategory: String {
  case image = "image"
  case video = "video"
}

class ImageCropViewController: UIViewController {
  weak var delegate: ImageCropViewControllerDelegate?
  weak var coordinator: ImageCropCoordinator?
  var blurredBackground = false
  var image: UIImage?
  var cropView: ImageCropView?
  
  var originalFileName: String?
  var afterCropfileName: String?
  var category: CaptureCategory = .image
  private var _cropArea: CGRect = .zero

  private let popupAiLIcenseStartPopupKey = "popupAiLIcenseStartPopupKey"
  private let popupDismissedKey = "didDismissRestorePopup"

  var cropArea: CGRect {
    get {
      if let cropView = cropView {
        return cropView.cropAreaInImage
      } else {
        return .zero
      }
    }
    set {
      _cropArea = newValue
      if let cropView = cropView {
        cropView.cropAreaInImage = _cropArea
      }
    }
  }

  var infoButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "ai_info_icon.pdf"), for: .normal)
//    button.setImage(#imageLiteral(resourceName: "ai_info_icon.pdf"), for: .selected)
    button.backgroundColor = .white
    button.layer.cornerRadius = 18
    return button
  }()

  var areaButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "ai_area_icon.pdf"), for: .normal)
    button.setTitle("  \(L.ai_plate_setting_arrange.localized)", for: .normal)
    button.titleLabel?.font = .subText
    button.setTitleColor(.mainBlack, for: .normal)
    button.backgroundColor = .white
    button.layer.cornerRadius = 18
    return button
  }()

  var transferButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "ai_transper_icon.pdf"), for: .normal)
    button.isEnabled = false
    button.setTitle("  \(L.ai_plate_send.localized)", for: .normal)
    button.titleLabel?.font = .subText
    button.setTitleColor(.mainBlack, for: .normal)
    button.backgroundColor = .disable
    button.layer.cornerRadius = 18
    return button
  }()

  var backButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "ai_arrow_left.pdf"), for: .normal)
    button.setImage(#imageLiteral(resourceName: "ai_arrow_left.pdf"), for: .selected)
    return button
  }()

  init(image: UIImage, origFileName: String, category: CaptureCategory) {
    self.image = image.fixOrientation()
    self.originalFileName = origFileName
    self.category = category
    super.init(nibName: nil, bundle: nil)
  }

  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    
    // navigationBar hidden
    self.navigationController?.navigationBar.isHidden = true
    self.navigationController?.isNavigationBarHidden = true
    
    setupView()
    
  }
  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    setupStartPopup()
  }

  override func viewWillTransition(
    to size: CGSize,
    with coordinator: any UIViewControllerTransitionCoordinator
  ) {
    cLogger.info("ImageCropViewController viewWillTransition")
  }
  
  private func setupView() {
    let contentView = UIView()
    contentView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
    contentView.backgroundColor = .black

    let viewFrame = CGRect(
      x: 0,
      y: 0,
      width: view.bounds.size.height,
      height: view.bounds.size.width
    )

    cropView = ImageCropView(frame: viewFrame, blurOn: blurredBackground)
    cropView?.isCropAreaHidden = true
    
    view = contentView

    if let cropView = cropView {
      contentView.addSubview(cropView)
      if let image = image {
        cropView.setImage(image)
      }
      if _cropArea.size.width > 0 {
        cropView.cropAreaInImage = _cropArea
      }
    }
    
    setupButtonViews()
  }
  
  private func setupButtonViews() {
    view.addSubviews([backButton, infoButton, areaButton, transferButton])
    backButton.pin.size(36).top(offset: 30, safe: true).start(offset: 80).activate()
    areaButton.pin.height(36).width(188).centerX().bottom(offset: -20, safe: true) .activate()
    infoButton.pin.size(36).before(areaButton, offset: -12).bottom(offset: -20, safe: true) .activate()
    transferButton.pin.height(36).width(106).after(areaButton, offset: 12).bottom(offset: -20, safe: true) .activate()
    
    backButton.addAction(UIAction { [weak self] _ in
      self?.tapBackButton()
    }, for: .touchUpInside)
    
    infoButton.addAction(UIAction { [weak self] _ in
      self?.tapInfoButton()
    }, for: .touchUpInside)

    areaButton.addAction(UIAction { [weak self] _ in
      self?.tapAreaButton()
    }, for: .touchUpInside)

    transferButton.addAction(UIAction { [weak self] _ in
      self?.tapTransferButton()
    }, for: .touchUpInside)

    backButton.addAction(UIAction { [weak self] _ in
      self?.tapBackButton()
    }, for: .touchUpInside)
  }
  
  func setupStartPopup() {
    if !UserDefaults.standard.bool(forKey: popupAiLIcenseStartPopupKey) {
      LKPopupView.popup.alert {[
        .subTitle(L.restore_start_popup.localized),
          .showCancel(true),
          .confirmAction([
              .text(L.ok.localized),
              .bgColor(.vueroidBlue),
              .tapActionCallback({})
          ]),
          .cancelAction([
            .text(L.close_popup.localized),
              .tapActionCallback({
                UserDefaults.standard.set(true, forKey: self.popupAiLIcenseStartPopupKey)
              })
          ])
      ]}
    }
  }

  func tapInfoButton() {
    LKPopupView.popup.alert {[
      .title(L.plate_restore_info_title.localized),
      .subTitle(L.plate_restore_info_message.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({})
      ])
    ]}
  }
  
  
  func tapBackButton() {
    AppUtility.lockOrientation(.portrait)
    self.dismiss(animated: false) {
      self.delegate?.imageCropViewControllerDidCancel(self)
    }
  }

  func tapAreaButton() {
    if UserDefaults.standard.bool(forKey: popupDismissedKey) {
        guard let cropView = cropView else { return }
        cropView.isCropAreaHidden.toggle()
        if cropView.isCropAreaHidden {
            transferButton.isEnabled = false
            transferButton.backgroundColor = .disable
        } else {
            transferButton.isEnabled = true
            transferButton.backgroundColor = .white
        }
        return
    }

    LKPopupView.popup.alert {[
        .subTitle(L.restore_area_popup.localized),
        .showCancel(true),
        .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({})
        ]),
        .cancelAction([
          .text(L.close_popup.localized),
            .tapActionCallback({
                UserDefaults.standard.set(true, forKey: self.popupDismissedKey)
            })
        ])
    ]}
    guard let cropView = cropView else { return }
    cropView.isCropAreaHidden.toggle()
    if cropView.isCropAreaHidden {
      transferButton.isEnabled = false
      transferButton.backgroundColor = .disable
    } else {
      transferButton.isEnabled = true
      transferButton.backgroundColor = .white
    }
  }

  func tapTransferButton() {
    guard let image = image,
          let cropView = cropView else { return }

    guard UserDefaults.shared.incrementAiLicenseFeatureUsage() else {
      LKPopupView.popup.alert {[
        .subTitle(L.ai_function_limit_msg.localized),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({
          })
        ])
      ]}
      return
    }
      
    let cropRect = cropView.cropAreaInImage
    guard let imageRef = image.cgImage?.cropping(to: cropRect) else { return }
    let croppedImage = UIImage(cgImage: imageRef)
    
    MainAsync(after: 0.5) {
      LKPopupView.popup.hideLoading()
      AIServiceManager.shared.currentFileName = self.afterCropfileName
      mLogger.info("AIServiceManager - currentFileName: \(self.afterCropfileName ?? "nil")")
      self.transferButton.isUserInteractionEnabled = true
      self.coordinator?.navigate(to: .upProcessDownload(parent: self, cropedImage: croppedImage, url: nil, taskType: .restored))
    }

    delegate?.imageCropViewControllerSuccess(self, didFinishCroppingImage: croppedImage)
  }
}

