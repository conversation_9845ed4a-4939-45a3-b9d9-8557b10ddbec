//
//  FileAiViewModel.swift
//  Hub
//
//  Created by ncn on 4/10/25.
//

import UIKit

class FileAiViewModel: BaseViewModel {
  
  weak var coordinator: FileEditCoordinator?
  let fileModel: FileListCellModel

  init(coordinator: FileEditCoordinator, fileModel: FileListCellModel) {
    self.coordinator = coordinator
    self.fileModel = fileModel
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }

}

