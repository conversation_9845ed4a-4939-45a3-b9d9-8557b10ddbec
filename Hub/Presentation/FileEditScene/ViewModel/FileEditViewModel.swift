//
//  FileListViewModel.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import AVFoundation
import RxCocoa
import RxSwift
import UIKit
import VLCKitSPM

let _thumbnailCount = 6

typealias tapConvertBtn = (type: FileEditType, low: String, high: String)
class FileEditViewModel: BaseViewModel {
  weak var coordinator: FileEditCoordinator?
  let fileModel: FileListCellModel
  let aiUseCase: AIUseCase
  let editType: FileEditType
  var channel: Int? = 0
  var state = State()

  struct Input {
    let viewWillAppear: Observable<Bool>
    let willDisappearEvent: Observable<Bool>

//    let gpsTapEvent: Observable<Bool>
//    let audioTapEvent: Observable<Bool>
    let switchChannel: Observable<IndexPath>
    let convertButtonEvent: Observable<tapConvertBtn>
  }

  struct Output {
    let rxLoadImages = PublishSubject<[UIImage]>()
    let rxLoadInfo = PublishSubject<FileListCellModel>()
    let rxDuration = PublishSubject<Int>()
    let rxIsCompleteWithUrl = PublishSubject<URL?>()
    let rxIsExccedAiUsageLimit = PublishSubject<Bool>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxIsPause = PublishSubject<Bool>()  // 1: pause, 0: play
    let rxGpsButtonState = PublishSubject<Bool>()
    let rxAudioButtonState = BehaviorSubject<Bool>(value: true)
    let rxChannelButtonState = PublishSubject<Int>()
  }

  struct State {
  }

  init(
    coordinator: FileEditCoordinator,
    fileModel: FileListCellModel,
    aiUseCase: AIUseCase,
    editType: FileEditType
  ) {
    self.coordinator = coordinator
    self.fileModel = fileModel
    self.editType = editType
    self.aiUseCase = aiUseCase
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.viewWillAppear
      .map { _ in (0, _thumbnailCount) }
      .flatMapLatest(loadThumbnails)
      .bind(to: output.rxLoadImages)
      .disposed(by: disposedBag)

    input.viewWillAppear
      .compactMap { [weak self] _ in self?.fileModel }
      .bind(to: output.rxLoadInfo)
      .disposed(by: disposedBag)

    input.willDisappearEvent
      .subscribe(onNext: { _ in
        guard let folderPath = UrlList.vodTemp() else { return }
        FileManager.remove(folder: folderPath)
      })
      .disposed(by: disposedBag)

    input.convertButtonEvent
      .do(onNext: { _ in output.rxIsPause.onNext(true) })
      .map { data in
        return (data.type, Float(data.low.toSecond()), Float(data.high.toSecond()))
      }
      .withLatestFrom(
        output.rxAudioButtonState,
        resultSelector: { data, isAudio in
          return (data.0, data.1, data.2, isAudio)
        }
      )
      .do(onNext: { _ in output.rxIsIndicator.onNext(true) })
      .flatMapLatest { [weak self] (type, low, high, isAudio) in
        guard let self = self else { return Observable<URL?>.just(nil) }
        guard UserDefaults.shared.incrementAiPrivacyFeatureUsage() else {
          output.rxIsExccedAiUsageLimit.onNext(true)
          return Observable<URL?>.just(nil)
        }
        return self.cropVideo(startTime: low, endTime: high)
      }
      .do(onNext: { url in
        let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
        PhotoUtils.saveVideo(url: url, toAlbum: modelName, completion: { _, _ in })
      })
      .do(onNext: { _ in output.rxIsIndicator.onNext(false) })
      .bind(to: output.rxIsCompleteWithUrl)
      .disposed(by: disposedBag)

    input.switchChannel
      .map { ($0.row, _thumbnailCount) }
      .flatMapLatest(loadThumbnails)
      .bind(to: output.rxLoadImages)
      .disposed(by: disposedBag)

    input.switchChannel
      .map { $0.row }
      .do(onNext: { [weak self] channel in self?.channel = channel })
      .bind(to: output.rxChannelButtonState)
      .disposed(by: disposedBag)

    return output
  }
}

extension FileEditViewModel {

  func loadThumbnails(_ channel: Int = 0, _ thumbnailCount: Int) -> Observable<[UIImage]> {
    var arrayForImage: [UIImage] = []
    let path: URL
    let selectedThumbnailTrack: AVAssetTrack?
    let baseAsset: AVAsset
    switch self.editType {
    case .trim, .privacyProtection:
      guard let sharePath = fileModel.filePath else { return Observable.just([]) }
      path = sharePath
      baseAsset = AVAsset(url: path)
      selectedThumbnailTrack = baseAsset.tracks(withMediaType: .video)[safe: channel]
    }


    guard let thumbnailTrack = selectedThumbnailTrack else { return Observable.just([]) }

    let composition = AVMutableComposition()
    guard let compositionTrack = composition.addMutableTrack(
      withMediaType: .video,
      preferredTrackID: kCMPersistentTrackID_Invalid
    ) else {
      return Observable.just([])
    }

    try? compositionTrack.insertTimeRange(CMTimeRange(start: .zero, duration: baseAsset.duration), of: thumbnailTrack, at: .zero)

    let generate = AVAssetImageGenerator(asset: composition)
    generate.appliesPreferredTrackTransform = true
    generate.requestedTimeToleranceAfter = CMTime.zero
    generate.requestedTimeToleranceBefore = CMTime.zero

    let thumbTime: CMTime = composition.duration
    let thumbtimeSeconds = Int(CMTimeGetSeconds(thumbTime))
    let maxLength = "\(thumbtimeSeconds)" as NSString
    let thumbAvg = thumbtimeSeconds / thumbnailCount
    var startTime = 1

    for _ in 0..<thumbnailCount {
      do {
        let time = CMTimeMakeWithSeconds(
          Float64(startTime), preferredTimescale: Int32(maxLength.length))
        Log.message(to: time)
        let cgImage = try generate.copyCGImage(at: time, actualTime: nil)
        let image = UIImage(cgImage: cgImage)
        arrayForImage.append(image)
      } catch let e as NSError {
        Log.message(to: e.localizedDescription)
      }
      startTime = startTime + thumbAvg
    }
    return Observable.just(arrayForImage)
  }

  func saveThumbnail(image: UIImage, url: URL) {
    guard let data = image.jpegData(compressionQuality: 1) ?? image.pngData() else {
      Log.message(to: "Fail to convert data.")
      return
    }
    guard let path = UrlList.downloadedPath() else {
      Log.message(to: "Fail to get path.")
      return
    }
    let date = Date().toString(format: "yyyy_MM_dd_HH_mm_ss")
    let name = String(format: "%@.jpg", date)
    FileManager.writeToFile(data: data, atPath: path, name: name, isSaveToPhoto: false)

  }

//  func cropVideo(
//    avAseet: AVAsset,
//    startTime: Float,
//    endTime: Float,
//  ) -> Observable<URL?> {
//
////    let duration = CMTimeMakeWithSeconds(<#T##seconds: Float64##Float64#>, preferredTimescale: baseAsset.duration.timescale)
////    let timeRange = CMTimeRangeMake(start: startTime, duration: duration)
//    let start = CMTime(seconds: Double(startTime), preferredTimescale: 1000)
//    let end = CMTime(seconds: Double(endTime), preferredTimescale: 1000)
//    let timeRange = CMTimeRange(start: start, end: end)
//
//    VideoManager.exportEditVideo(for: avAseet, range: timeRange) { [weak self] url, error in
//      if let error = error {
//
//      } else if url != nil {
////        emitter.onNext(url)
////        mLogger.info("exported at \(url)")
//      }
//    }
//  }


  func cropVideo(startTime: Float, endTime: Float) -> Observable<URL?> {
    guard let downloadPath = UrlList.downloadedPath() else { return Observable.just(nil) }
    let path: URL

    let baseAsset: AVAsset

    switch self.editType {
    case .trim:
      guard let sharePath = fileModel.filePath else { return Observable.just(nil) }
      path = sharePath
      mLogger.debug("trim start: \(startTime),  end: \(endTime)")
      baseAsset = AVAsset(url: path)
    case .privacyProtection:
      mLogger.debug("privacyProtection start: \(startTime),  end: \(endTime)")
      guard let sharePath = fileModel.filePath else { return Observable.just(nil) }
      path = sharePath
      baseAsset = AVAsset(url: path)
    }


    let start = CMTime(seconds: Double(startTime), preferredTimescale: 1000)
    let end = CMTime(seconds: Double(endTime), preferredTimescale: 1000)
    let timeRange = CMTimeRange(start: start, end: end)

    guard let exportSession = AVAssetExportSession(asset: baseAsset, presetName: AVAssetExportPresetHighestQuality) else {
      return Observable.just(nil)
    }

    mLogger.log("beforeTrim fileName : \(self.fileModel.fileName)")
    let uniqueFileName = editType == .trim ? try! HubFileNameGenerator.generateTrimmedFileName(baseFileName: fileModel.fileName) :
                    try! HubFileNameGenerator.generatePrivacyBlurFileName(baseFileName: fileModel.fileName, stage: .beforeProcessing)
    mLogger.debug("trim video with name: \(uniqueFileName)")
    
    let uniqueFileURL = downloadPath.appendingPathComponent(uniqueFileName)
    exportSession.outputURL = uniqueFileURL
    exportSession.outputFileType = .mp4
    exportSession.timeRange = timeRange

    return Observable.create { emitter in
      MainAsync {
        exportSession.exportAsynchronously {
          switch exportSession.status {
          case .completed:
            emitter.onNext(uniqueFileURL)
            mLogger.info("exported at \(uniqueFileURL)")
          case .failed:
            emitter.onNext(nil)
            mLogger.info("failed \(exportSession.error?.localizedDescription ?? "")")
          case .cancelled:
            emitter.onNext(nil)
            mLogger.warning("cancelled \(exportSession.error?.localizedDescription ?? "")")
          default:
            emitter.onNext(nil)
            break
          }
        }
      }
      return Disposables.create()
    }
  }
}
