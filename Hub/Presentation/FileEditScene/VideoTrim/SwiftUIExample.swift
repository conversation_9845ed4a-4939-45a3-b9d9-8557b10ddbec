//
//  SwiftUIExample.swift
//  Hub
//
//  Created by ncn on 6/23/25.
//
import SwiftUI
import AVFoundation

struct VideoEditorView: UIViewControllerRepresentable {
  @Binding var isPresented: Bool
  @Binding var exportedURL: URL?
  
  func makeUIViewController(context: Context) -> VideoTrimViewController {
    let videoURL = Bundle.main.url(forResource: "sample", withExtension: "mp4")!
    let asset = AVAsset(url: videoURL)
    let editVC = VideoTrimViewController(avAsset: asset, animateDismiss: true)
    editVC.editFinishBlock = { url in
      self.exportedURL = url
      self.isPresented = false
    }
    editVC.cancelEditBlock = {
      self.isPresented = false
    }
    return editVC
  }
  
  func updateUIViewController(_ uiViewController: VideoTrimViewController, context: Context) {}
}
