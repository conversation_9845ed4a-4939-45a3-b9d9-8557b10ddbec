//
//  VideoEditConfiguration.swift
//  Hub
//
//  Created by ncn on 6/23/25.
//

import UIKit

// MARK: - Configuration Structure
public struct VideoTrimConfiguration {
  public var frameCount: Int = 10
  public var maxEditVideoTime: TimeInterval = 10.0
  public var minSelectVideoDuration: TimeInterval = 1.0
  public var maxSelectVideoDuration: TimeInterval = 60.0
  
  // UI Configuration
  public var bottomToolBtnHeight: CGFloat = 34.0
  public var bottomToolTitleFont: UIFont = UIFont.systemFont(ofSize: 17)
  public var bottomToolBtnCornerRadius: CGFloat = 5.0
  
  // Colors
  public var bottomToolViewBtnNormalTitleColor: UIColor = .white
  public var bottomToolViewBtnNormalBgColor: UIColor = UIColor(red: 80/255.0, green: 169/255.0, blue: 56/255.0, alpha: 1.0)
  
  // Localized Strings
  public var cancelButtonTitle = L.cancel.localized
  public var doneButtonTitle = L.trim.localized
  public var okButtonTitle = "OK"
  public var videoLoadFailedMessage: String = "Failed to load video"
  public var shorterThanMinDurationMessage = "Video duration is too short. Minimum duration is %.0f seconds."
  public var longerThanMaxDurationMessage = "Video duration is too long. Maximum duration is %.0f seconds."
  
  public init() {}
}
