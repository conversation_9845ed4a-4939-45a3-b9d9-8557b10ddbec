//
//  VideoTrimViewController.swift
//  Standalone Video Editor
//

import UIKit
import Photos
import AVFoundation

// MARK: - Weak Proxy for Timer
class WeakProxy: NSObject {
  private weak var target: NSObjectProtocol?
  
  init(target: NSObjectProtocol) {
    self.target = target
    super.init()
  }
  
  override func forwardingTarget(for aSelector: Selector!) -> Any? {
    return target
  }
  
  override func responds(to aSelector: Selector!) -> <PERSON><PERSON> {
    return target?.responds(to: aSelector) ?? false
  }
}

/// USAGE:
///
///    // Create video asset
///    let videoURL = Bundle.main.url(forResource: "sample", withExtension: "mp4")!
///    let asset = AVAsset(url: videoURL)
///
///    // Create editor
///    let editVC = StandaloneVideoEditViewController(avAsset: asset, animateDismiss: true)
///
///    // Handle completion
///    editVC.editFinishBlock = { exportedURL in
///        if let url = exportedURL {
///            print("Video exported to: \(url)")
///            // Save to photo library or handle as needed
///        }
///    }
///
///    editVC.cancelEditBlock = {
///        print("Editing cancelled")
///    }
///
///    present(editVC, animated: true)
///    ...
///
// MARK: - Video Export Manager
private class VideoExportManager {
  static func exportEditVideo(for asset: AVAsset, range: CMTimeRange, completion: @escaping (URL?, Error?) -> Void) {
    let outputURL = URL(fileURLWithPath: NSTemporaryDirectory().appending("\(UUID().uuidString).mp4"))
    
    guard let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetHighestQuality) else {
      completion(nil, NSError(domain: "VideoExportError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to create export session"]))
      return
    }
    
    exportSession.outputURL = outputURL
    exportSession.outputFileType = .mp4
    exportSession.timeRange = range
    
    exportSession.exportAsynchronously {
      DispatchQueue.main.async {
        switch exportSession.status {
        case .completed:
          completion(outputURL, nil)
        case .failed:
          completion(nil, exportSession.error)
        case .cancelled:
          completion(nil, NSError(domain: "VideoExportError", code: -2, userInfo: [NSLocalizedDescriptionKey: "Export cancelled"]))
        default:
          completion(nil, NSError(domain: "VideoExportError", code: -3, userInfo: [NSLocalizedDescriptionKey: "Unknown export error"]))
        }
      }
    }
  }
}

// MARK: - Collection View Flow Layout
private class VideoTrimCollectionViewFlowLayout: UICollectionViewFlowLayout {
  override func layoutAttributesForElements(in rect: CGRect) -> [UICollectionViewLayoutAttributes]? {
    let attributes = super.layoutAttributesForElements(in: rect)
    return attributes?.compactMap { $0.copy() as? UICollectionViewLayoutAttributes }
  }
}

// MARK: - Main Video Edit View Controller
public class VideoTrimViewController: UIViewController {
  
  // MARK: - Configuration
  public var configuration = VideoTrimConfiguration()
  
  // MARK: - Public Properties
  @objc public var editFinishBlock: ((URL?) -> Void)?
  @objc public var cancelEditBlock: (() -> Void)?
  
  // MARK: - Private Properties
  static let frameImageSize = CGSize(width: CGFloat(round(52.0 / 2.0)), height: 52.0)
  
  let avAsset: AVAsset
  let animateDismiss: Bool
  
  // MARK: - Constraint Properties
  var leftSideViewLeadingConstraint: NSLayoutConstraint!
  var rightSideViewTrailingConstraint: NSLayoutConstraint!
  var frameImageBorderViewCenterXConstraint: NSLayoutConstraint!
  var frameImageBorderViewWidthConstraint: NSLayoutConstraint!
  var indicatorLeadingConstraint: NSLayoutConstraint!
  var indicatorWidthConstraint: NSLayoutConstraint!
  
  // MARK: - UI Elements
  lazy var cancelBtn: UIButton = {
    let btn = UIButton(type: .custom)
    btn.setTitle(configuration.cancelButtonTitle, for: .normal)
    btn.setTitleColor(configuration.bottomToolViewBtnNormalTitleColor, for: .normal)
    btn.titleLabel?.font = configuration.bottomToolTitleFont
    btn.addTarget(self, action: #selector(cancelBtnClick), for: .touchUpInside)
    btn.translatesAutoresizingMaskIntoConstraints = false
    btn.setContentHuggingPriority(.required, for: .horizontal)
    btn.setContentCompressionResistancePriority(.required, for: .horizontal)
    return btn
  }()
  
  let trimButton = UIButton.customStyleButton(title: L.trim.localized)
  
  var timer: Timer?
  
  lazy var playerLayer: AVPlayerLayer = {
    let layer = AVPlayerLayer()
    layer.videoGravity = .resizeAspect
    return layer
  }()
  
  lazy var collectionView: UICollectionView = {
    let layout = VideoTrimCollectionViewFlowLayout()
    layout.itemSize = VideoTrimViewController.frameImageSize
    layout.minimumLineSpacing = 0
    layout.minimumInteritemSpacing = 0
    layout.scrollDirection = .horizontal
    
    let view = UICollectionView(frame: .zero, collectionViewLayout: layout)
    view.backgroundColor = .clear
    view.delegate = self
    view.dataSource = self
    view.showsHorizontalScrollIndicator = false
    view.register(VideoFrameImageCell.self, forCellWithReuseIdentifier: "VideoFrameImageCell")
    return view
  }()
  
  lazy var frameImageBorderView: VideoFrameImageBorderView = {
    let view = VideoFrameImageBorderView()
    view.isUserInteractionEnabled = false
    view.translatesAutoresizingMaskIntoConstraints = false
    return view
  }()
  
  lazy var leftSideView: UIImageView = {
    let view = UIImageView()
    view.backgroundColor = UIColor.mainYellow
    view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMinXMaxYCorner]
    view.layer.cornerRadius = 10
    view.isUserInteractionEnabled = true
    view.translatesAutoresizingMaskIntoConstraints = false
    return view
  }()
  
  lazy var rightSideView: UIImageView = {
    let view = UIImageView()
    view.backgroundColor = UIColor.mainYellow
    // view rightTop, rightBottom corner radius
    view.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
    view.layer.cornerRadius = 10
    view.isUserInteractionEnabled = true
    view.translatesAutoresizingMaskIntoConstraints = false
    return view
  }()
  
  lazy var leftSidePan: UIPanGestureRecognizer = {
    let pan = UIPanGestureRecognizer(target: self, action: #selector(leftSidePanAction(_:)))
    pan.delegate = self
    return pan
  }()
  
  lazy var rightSidePan: UIPanGestureRecognizer = {
    let pan = UIPanGestureRecognizer(target: self, action: #selector(rightSidePanAction(_:)))
    pan.delegate = self
    return pan
  }()
  
  lazy var indicator: UIView = {
    let view = UIView()
    view.backgroundColor = UIColor.white.withAlphaComponent(0.7)
    view.translatesAutoresizingMaskIntoConstraints = false
    view.isHidden = true
    return view
  }()
  
  var measureCount = 0
  
  lazy var interval: TimeInterval = {
    let assetDuration = round(self.avAsset.duration.seconds)
    return min(assetDuration, configuration.maxEditVideoTime) / Double(configuration.frameCount)
  }()
  
  lazy var requestFrameImageQueue: OperationQueue = {
    let queue = OperationQueue()
    queue.maxConcurrentOperationCount = 10
    return queue
  }()
  
  var frameImageCache: [Int: UIImage] = [:]
  var requestFailedFrameImageIndex: [Int] = []
  var shouldLayout = true
  
  lazy var generator: AVAssetImageGenerator = {
    let g = AVAssetImageGenerator(asset: self.avAsset)
    g.maximumSize = CGSize(width: VideoTrimViewController.frameImageSize.width * 3, height: VideoTrimViewController.frameImageSize.height * 3)
    g.appliesPreferredTrackTransform = true
    g.requestedTimeToleranceBefore = .zero
    g.requestedTimeToleranceAfter = .zero
    g.apertureMode = .productionAperture
    return g
  }()
  
  // MARK: - View Controller Lifecycle
  override public var prefersStatusBarHidden: Bool {
    return true
  }
  
  override public var supportedInterfaceOrientations: UIInterfaceOrientationMask {
    return UIDevice.current.userInterfaceIdiom == .phone ? .portrait : .all
  }
  
  deinit {
    print("StandaloneVideoEditViewController deinit")
    cleanTimer()
    requestFrameImageQueue.cancelAllOperations()
    try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
  }
  
  // MARK: - Initialization
  /// Initialize the video edit view controller
  /// - Parameters:
  ///   - avAsset: AVAsset object, requires local video, network video is not supported
  ///   - animateDismiss: Whether to show dismiss animation when exiting the interface
  @objc public init(avAsset: AVAsset, animateDismiss: Bool = false) {
    self.avAsset = avAsset
    self.animateDismiss = animateDismiss
    super.init(nibName: nil, bundle: nil)
  }
  
  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  override public func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    
    try? AVAudioSession.sharedInstance().setCategory(.playback)
    try? AVAudioSession.sharedInstance().setActive(true, options: .notifyOthersOnDeactivation)
    NotificationCenter.default.addObserver(self, selector: #selector(appWillResignActive), name: UIApplication.willResignActiveNotification, object: nil)
    NotificationCenter.default.addObserver(self, selector: #selector(appDidBecomeActive), name: UIApplication.didBecomeActiveNotification, object: nil)
  }
  
  override public func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    analysisAssetImages()
  }
  
  override public func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()
    guard shouldLayout else {
      return
    }
    shouldLayout = false
    
    print("edit video layout subviews")
    
    // Update player layer frame (AVPlayerLayer cannot use Auto Layout)
    updatePlayerLayerFrame()
    
    // Update frame border view's valid rect
    frameImageBorderView.validRect = frameImageBorderView.convert(clipRect(), from: view)
  }
  
  private func updatePlayerLayerFrame() {
    let safeArea = view.safeAreaInsets
    let playerLayerY = safeArea.top + 20
    let bottomBtnAndColSpacing: CGFloat = 20
    let frameImageHeight = VideoTrimViewController.frameImageSize.height
    let diffBottom = frameImageHeight + bottomBtnAndColSpacing + safeArea.bottom + 30
    
    playerLayer.frame = CGRect(
      x: 0,
      y: playerLayerY,
      width: view.bounds.width,
      height: view.bounds.height - playerLayerY - diffBottom
    )
  }
  
  private func setupUI() {
    title = L.my_library_title.localized
    navigationItem.backButtonTitle = ""
    view.backgroundColor = .background

    // Add subviews
    view.layer.addSublayer(playerLayer)
    view.addSubview(collectionView)
    view.addSubview(frameImageBorderView)
    view.addSubview(indicator)
    view.addSubview(leftSideView)
    view.addSubview(rightSideView)
    view.addSubview(trimButton)
    
    // Setup gesture recognizers
    view.addGestureRecognizer(leftSidePan)
    view.addGestureRecognizer(rightSidePan)
    
    collectionView.panGestureRecognizer.require(toFail: leftSidePan)
    collectionView.panGestureRecognizer.require(toFail: rightSidePan)
    rightSidePan.require(toFail: leftSidePan)
    
    setupConstraints()
  }
  
  private func setupConstraints() {
    let safeArea = view.safeAreaLayoutGuide
    let frameImageHeight = VideoTrimViewController.frameImageSize.height
    let frameImageWidth = VideoTrimViewController.frameImageSize.width
//    let bottomBtnAndColSpacing: CGFloat = 20
    
    // Bottom buttons constraints
    Pin.activate([
      trimButton.pin.height(60).horizontally(offset: 15).bottom(offset: -35, safe: true),
      collectionView.pin.horizontally().height(frameImageHeight).above(trimButton, offset: -84),
    ])
    
    // Frame border view constraints
    let frameViewWidth = frameImageWidth * CGFloat(configuration.frameCount)
    frameImageBorderViewCenterXConstraint = frameImageBorderView.centerXAnchor.constraint(equalTo: view.centerXAnchor)
    frameImageBorderViewWidthConstraint = frameImageBorderView.widthAnchor.constraint(equalToConstant: frameViewWidth)
    
    NSLayoutConstraint.activate([
      frameImageBorderViewCenterXConstraint,
      frameImageBorderViewWidthConstraint,
      frameImageBorderView.topAnchor.constraint(equalTo: collectionView.topAnchor),
      frameImageBorderView.heightAnchor.constraint(equalToConstant: frameImageHeight)
    ])
    
    // Side views constraints
    let leftRightSideViewWidth = 18.0
    leftSideViewLeadingConstraint = leftSideView.leadingAnchor.constraint(equalTo: frameImageBorderView.leadingAnchor)
    rightSideViewTrailingConstraint = rightSideView.trailingAnchor.constraint(equalTo: frameImageBorderView.trailingAnchor)
    
    NSLayoutConstraint.activate([
      // Left side view
      leftSideViewLeadingConstraint,
      leftSideView.topAnchor.constraint(equalTo: collectionView.topAnchor),
      leftSideView.widthAnchor.constraint(equalToConstant: leftRightSideViewWidth),
      leftSideView.heightAnchor.constraint(equalToConstant: frameImageHeight),
      
      // Right side view
      rightSideViewTrailingConstraint,
      rightSideView.topAnchor.constraint(equalTo: collectionView.topAnchor),
      rightSideView.widthAnchor.constraint(equalToConstant: leftRightSideViewWidth),
      rightSideView.heightAnchor.constraint(equalToConstant: frameImageHeight)
    ])
    
    // Indicator constraints
    indicatorLeadingConstraint = indicator.leadingAnchor.constraint(equalTo: leftSideView.leadingAnchor)
    indicatorWidthConstraint = indicator.widthAnchor.constraint(equalToConstant: 2)
    
    NSLayoutConstraint.activate([
      indicatorLeadingConstraint,
      indicatorWidthConstraint,
      indicator.topAnchor.constraint(equalTo: leftSideView.topAnchor),
      indicator.heightAnchor.constraint(equalTo: leftSideView.heightAnchor)
    ])
  }
  
  // MARK: - Button Actions
  @objc private func cancelBtnClick() {
    dismiss(animated: animateDismiss) {
      self.cancelEditBlock?()
    }
  }
  
  @objc private func doneBtnClick() {
    cleanTimer()
    
    let d = CGFloat(interval) * clipRect().width / VideoTrimViewController.frameImageSize.width
    if TimeInterval(round(d)) < configuration.minSelectVideoDuration {
      let message = String(format: configuration.shorterThanMinDurationMessage, configuration.minSelectVideoDuration)
      showAlert(message: message)
      return
    }
    if TimeInterval(round(d)) > configuration.maxSelectVideoDuration {
      let message = String(format: configuration.longerThanMaxDurationMessage, configuration.maxSelectVideoDuration)
      showAlert(message: message)
      return
    }
    
    // Max deviation is 0.01
    if abs(d - round(CGFloat(avAsset.duration.seconds))) <= 0.01 {
      dismiss(animated: animateDismiss) {
        self.editFinishBlock?(nil)
      }
      return
    }
    
    LKPopupView.popup.loading()
    VideoExportManager.exportEditVideo(for: avAsset, range: getTimeRange()) { [weak self] url, error in
      LKPopupView.popup.hideLoading()
      if let error = error {
        self?.showAlert(message: error.localizedDescription)
      } else if let url = url {
        self?.dismiss(animated: self?.animateDismiss ?? false) {
          self?.editFinishBlock?(url)
        }
      }
    }
  }
}
