//
//  VideoTrimViewController+CollectionView.swift
//  Standalone Video Editor Collection View Support
//

import UIKit
import AVFoundation

// MARK: - UIGestureRecognizerDelegate
extension VideoTrimViewController: UIGestureRecognizerDelegate {
  public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> <PERSON><PERSON> {
    if gestureRecognizer == leftSidePan {
      let point = gestureRecognizer.location(in: view)
      let frame = leftSideView.frame
      let outerFrame = frame.inset(by: UIEdgeInsets(top: -20, left: -40, bottom: -20, right: -20))
      return outerFrame.contains(point)
    } else if gestureRecognizer == rightSidePan {
      let point = gestureRecognizer.location(in: view)
      let frame = rightSideView.frame
      let outerFrame = frame.inset(by: UIEdgeInsets(top: -20, left: -20, bottom: -20, right: -40))
      return outerFrame.contains(point)
    }
    return true
  }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegateFlowLayout
extension VideoTrimViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
  public func scrollViewDidScroll(_ scrollView: UIScrollView) {
    cleanTimer()
    playerLayer.player?.seek(to: getStartTime(), toleranceBefore: .zero, toleranceAfter: .zero)
  }
  
  public func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
    if !decelerate {
      startTimer()
    }
  }
  
  public func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
    startTimer()
  }
  
  public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
    let w = VideoTrimViewController.frameImageSize.width * 10
    let leftRight = (collectionView.frame.width - w) / 2
    return UIEdgeInsets(top: 0, left: leftRight, bottom: 0, right: leftRight)
  }
  
  public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
    return measureCount
  }
  
  public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
    let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoFrameImageCell", for: indexPath) as! VideoFrameImageCell
    
    if let image = frameImageCache[indexPath.row] {
      cell.imageView.image = image
    }
    
    return cell
  }
  
  public func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
    if requestFailedFrameImageIndex.contains(indexPath.row) {
      let mes = TimeInterval(indexPath.row) * interval
      let time = CMTimeMakeWithSeconds(Float64(mes), preferredTimescale: avAsset.duration.timescale)
      
      let operation = VideoFrameImageOperation(generator: generator, time: time) { [weak self] image, _ in
        self?.frameImageCache[indexPath.row] = image
        let cell = self?.collectionView.cellForItem(at: IndexPath(row: indexPath.row, section: 0)) as? VideoFrameImageCell
        cell?.imageView.image = image
        if image != nil {
          self?.requestFailedFrameImageIndex.removeAll { $0 == indexPath.row }
        }
      }
      requestFrameImageQueue.addOperation(operation)
    }
  }
}

// MARK: - VideoFrameImageBorderView
class VideoFrameImageBorderView: UIView {
  var validRect: CGRect = .zero {
    didSet {
      self.setNeedsDisplay()
    }
  }
  
  override init(frame: CGRect) {
    super.init(frame: frame)
    layer.borderWidth = 5.5
    layer.borderColor = UIColor.mainYellow.cgColor
    backgroundColor = .clear
    isOpaque = false
  }
  
  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  override func draw(_ rect: CGRect) {
    let context = UIGraphicsGetCurrentContext()
    context?.setStrokeColor(UIColor.white.cgColor)
    context?.setLineWidth(4)
    
    context?.move(to: CGPoint(x: validRect.minX, y: 0))
    context?.addLine(to: CGPoint(x: validRect.minX + validRect.width, y: 0))
    
    context?.move(to: CGPoint(x: validRect.minX, y: rect.height))
    context?.addLine(to: CGPoint(x: validRect.minX + validRect.width, y: rect.height))
    
    context?.strokePath()
  }
}

// MARK: - VideoFrameImageCell
class VideoFrameImageCell: UICollectionViewCell {
  lazy var imageView: UIImageView = {
    let view = UIImageView()
    view.contentMode = .scaleAspectFill
    view.clipsToBounds = true
    view.translatesAutoresizingMaskIntoConstraints = false
    return view
  }()
  
  override init(frame: CGRect) {
    super.init(frame: frame)
    
    contentView.addSubview(imageView)
    imageView.pin.all().activate()
  }
  
  @available(*, unavailable)
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
