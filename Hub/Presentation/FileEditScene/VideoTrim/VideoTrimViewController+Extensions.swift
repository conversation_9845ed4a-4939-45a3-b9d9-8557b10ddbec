//
//  VideoTrimViewController+Extensions.swift
//  Standalone Video Editor Extensions
//

import UIKit
import AVFoundation

// MARK: - Gesture Handling
extension VideoTrimViewController {
  
  @objc func leftSidePanAction(_ pan: UIPanGestureRecognizer) {
    let point = pan.location(in: view)
    
    if pan.state == .began {
      frameImageBorderView.layer.borderColor = UIColor(white: 1, alpha: 0.4).cgColor
      cleanTimer()
    } else if pan.state == .changed {
      let minX = frameImageBorderView.frame.minX
      let maxX = rightSideView.frame.minX - leftSideView.frame.width
      
      let newX = min(maxX, max(minX, point.x))
      let offsetFromBorderView = newX - frameImageBorderView.frame.minX
      
      // Update constraint
      leftSideViewLeadingConstraint.constant = offsetFromBorderView
      view.layoutIfNeeded()
      
      frameImageBorderView.validRect = frameImageBorderView.convert(clipRect(), from: view)
      playerLayer.player?.seek(to: getStartTime(), toleranceBefore: .zero, toleranceAfter: .zero)
    } else if pan.state == .ended || pan.state == .cancelled {
      frameImageBorderView.layer.borderColor = UIColor.clear.cgColor
      startTimer()
    }
  }
  
  @objc func rightSidePanAction(_ pan: UIPanGestureRecognizer) {
    let point = pan.location(in: view)
    
    if pan.state == .began {
      frameImageBorderView.layer.borderColor = UIColor(white: 1, alpha: 0.4).cgColor
      cleanTimer()
    } else if pan.state == .changed {
      let minX = leftSideView.frame.maxX
      let maxX = frameImageBorderView.frame.maxX - rightSideView.frame.width
      
      let newX = min(maxX, max(minX, point.x))
      let offsetFromBorderViewEnd = frameImageBorderView.frame.maxX - (newX + rightSideView.frame.width)
      
      // Update constraint (trailing constraint uses negative values)
      rightSideViewTrailingConstraint.constant = -offsetFromBorderViewEnd
      view.layoutIfNeeded()
      
      frameImageBorderView.validRect = frameImageBorderView.convert(clipRect(), from: view)
      playerLayer.player?.seek(to: getStartTime(), toleranceBefore: .zero, toleranceAfter: .zero)
    } else if pan.state == .ended || pan.state == .cancelled {
      frameImageBorderView.layer.borderColor = UIColor.clear.cgColor
      startTimer()
    }
  }
  
  @objc func appWillResignActive() {
    cleanTimer()
    indicator.layer.removeAllAnimations()
  }
  
  @objc func appDidBecomeActive() {
    startTimer()
  }
}

// MARK: - Video Analysis and Processing
extension VideoTrimViewController {
  
  func analysisAssetImages() {
    let duration = round(avAsset.duration.seconds)
    guard duration > 0 else {
      showFetchFailedAlert()
      return
    }
    let item = AVPlayerItem(asset: avAsset)
    let player = AVPlayer(playerItem: item)
    playerLayer.player = player
    
    measureCount = Int(duration / interval)
    collectionView.reloadData()
    startTimer()
    requestVideoMeasureFrameImage()
  }
  
  private func requestVideoMeasureFrameImage() {
    for i in 0..<measureCount {
      let mes = TimeInterval(i) * interval
      let time = CMTimeMakeWithSeconds(Float64(mes), preferredTimescale: avAsset.duration.timescale)
      
      let operation = VideoFrameImageOperation(generator: generator, time: time) { [weak self] image, _ in
        self?.frameImageCache[Int(i)] = image
        let cell = self?.collectionView.cellForItem(at: IndexPath(row: Int(i), section: 0)) as? VideoFrameImageCell
        cell?.imageView.image = image
        if image == nil {
          self?.requestFailedFrameImageIndex.append(i)
        }
      }
      requestFrameImageQueue.addOperation(operation)
    }
  }
  
  @objc private func playPartVideo() {
    playerLayer.player?.seek(to: getStartTime(), toleranceBefore: .zero, toleranceAfter: .zero)
    if (playerLayer.player?.rate ?? 0) == 0 {
      playerLayer.player?.play()
    }
  }
}

// MARK: - Timer Management
extension VideoTrimViewController {
  
  func startTimer() {
    cleanTimer()
    let duration = interval * TimeInterval(clipRect().width / VideoTrimViewController.frameImageSize.width)
    
    timer = Timer.scheduledTimer(timeInterval: duration, target: WeakProxy(target: self), selector: #selector(playPartVideo), userInfo: nil, repeats: true)
    timer?.fire()
    RunLoop.main.add(timer!, forMode: .common)
    
    indicator.isHidden = false
    
    // Calculate animation range
    let clipRectWidth = clipRect().width
    let indicatorW: CGFloat = 2
    
    // Set initial position
    var fromOffset: CGFloat = 0
    var toOffset = clipRectWidth - indicatorW
    
    if isRTL() {
      swap(&fromOffset, &toOffset)
    }
    
    // Update constraints for animation
    indicatorLeadingConstraint.constant = fromOffset
    view.layoutIfNeeded()
    
    indicator.layer.removeAllAnimations()
    UIView.animate(withDuration: duration, delay: 0, options: [.allowUserInteraction, .curveLinear, .repeat], animations: {
      self.indicatorLeadingConstraint.constant = toOffset
      self.view.layoutIfNeeded()
    }, completion: nil)
  }
  
  func cleanTimer() {
    timer?.invalidate()
    timer = nil
    indicator.layer.removeAllAnimations()
    indicator.isHidden = true
    playerLayer.player?.pause()
  }
}

// MARK: - Helper Methods
extension VideoTrimViewController {
  
  func getStartTime() -> CMTime {
    var oneFrameDuration = interval
    if measureCount > configuration.frameCount {
      // If measureCount > 10, calculate the duration of each frame image outside the selected area
      oneFrameDuration = (avAsset.duration.seconds - configuration.maxEditVideoTime) / Double(measureCount - configuration.frameCount)
    }
    
    let offsetX = collectionView.contentOffset.x
    let previousSecond = offsetX / VideoTrimViewController.frameImageSize.width * oneFrameDuration
    
    // Starting duration within the selected area
    let innerRect = frameImageBorderView.convert(clipRect(), from: view)
    let innerPreviousSecond: TimeInterval
    if isRTL() {
      innerPreviousSecond = (frameImageBorderView.frame.width - innerRect.maxX) / VideoTrimViewController.frameImageSize.width * interval
    } else {
      innerPreviousSecond = innerRect.minX / VideoTrimViewController.frameImageSize.width * interval
    }
    
    let totalDuration = max(0, previousSecond + round(innerPreviousSecond))
    
    return CMTimeMakeWithSeconds(Float64(totalDuration), preferredTimescale: avAsset.duration.timescale)
  }
  
  func getTimeRange() -> CMTimeRange {
    let start = getStartTime()
    let d = CGFloat(interval) * clipRect().width / VideoTrimViewController.frameImageSize.width
    let duration = CMTimeMakeWithSeconds(Float64(round(d)), preferredTimescale: avAsset.duration.timescale)
    return CMTimeRangeMake(start: start, duration: duration)
  }
  
  func clipRect() -> CGRect {
    var frame = CGRect.zero
    frame.origin.x = leftSideView.frame.minX
    frame.origin.y = leftSideView.frame.minY
    frame.size.width = rightSideView.frame.maxX - frame.minX
    frame.size.height = leftSideView.frame.height
    return frame
  }
  
  private func showFetchFailedAlert() {
    showAlert(message: configuration.videoLoadFailedMessage)
  }
  
  func showAlert(message: String) {
    DispatchQueue.main.async {
      let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
      alert.addAction(UIAlertAction(title: self.configuration.okButtonTitle, style: .default, handler: nil))
      self.present(alert, animated: true, completion: nil)
    }
  }
  
  private func isRTL() -> Bool {
    return UIView.userInterfaceLayoutDirection(for: UIView.appearance().semanticContentAttribute) == .rightToLeft
  }
}
