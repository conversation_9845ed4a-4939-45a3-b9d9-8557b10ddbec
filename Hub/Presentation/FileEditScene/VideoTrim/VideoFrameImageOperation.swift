//
//  VideoFrameImageOperation.swift
//  Hub
//
//  Created by ncn on 6/23/25.
//

import UIKit
import AVFoundation

// MARK: - VideoFrameImageOperation
class VideoFrameImageOperation: Operation, @unchecked Sendable {
  private let generator: AVAssetImageGenerator
  private let time: CMTime
  let completion: (UIImage?, CMTime) -> Void
  
  private var _isExecuting = false {
    willSet {
      willChangeValue(forKey: "isExecuting")
    }
    didSet {
      didChangeValue(forKey: "isExecuting")
    }
  }
  
  override var isExecuting: Bool {
    return _isExecuting
  }
  
  private var _isFinished = false {
    willSet {
      willChangeValue(forKey: "isFinished")
    }
    didSet {
      didChangeValue(forKey: "isFinished")
    }
  }
  
  override var isFinished: Bool {
    return _isFinished
  }
  
  private var _isCancelled = false {
    willSet {
      willChangeValue(forKey: "isCancelled")
    }
    didSet {
      didChangeValue(forKey: "isCancelled")
    }
  }
  
  override var isCancelled: Bool {
    return _isCancelled
  }
  
  init(generator: AVAssetImageGenerator, time: CMTime, completion: @escaping ((UIImage?, CMTime) -> Void)) {
    self.generator = generator
    self.time = time
    self.completion = completion
    super.init()
  }
  
  override func start() {
    if isCancelled {
      fetchFinish()
      return
    }
    _isExecuting = true
    generator.generateCGImagesAsynchronously(forTimes: [NSValue(time: time)]) { _, cgImage, _, result, _ in
      if result == .succeeded, let cg = cgImage {
        let image = UIImage(cgImage: cg)
        DispatchQueue.main.async {
          self.completion(image, self.time)
        }
        self.fetchFinish()
      } else {
        self.fetchFinish()
      }
    }
  }
  
  override func cancel() {
    super.cancel()
    _isCancelled = true
  }
  
  private func fetchFinish() {
    _isExecuting = false
    _isFinished = true
  }
}
