//
//  DefaultFileListCoordinator.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import UIKit
import AVFoundation

final class DefaultFileEditCoordinator: NavigationCoordinator {
  public var navigationController: UINavigationController
  weak var delegate: FileEditCoordinatorFinishDelegate?

  var childCoordinators = [FileEditChildCoordinator: Coordinator]()
  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func start(with step: FileEditSteps) {
    navigate(to: step)
  }
}

extension DefaultFileEditCoordinator: FileEditCoordinator {
  func navigate(to step: FileEditSteps) {
    switch step {
    case let .showFileEdit(model: model, editType: type):
      if AppConfig.shared.hubAppConfig.isNewVideoTrimEnabled {
        let vc: VideoTrimViewController = make(fileModel: model, editType: type)
        vc.hidesBottomBarWhenPushed = true
        navigationController.pushViewController(vc, animated: true)
      } else {
        let vc: FileEditViewController = make(fileModel: model, editType: type)
        vc.hidesBottomBarWhenPushed = true
        navigationController.pushViewController(vc, animated: true)
      }
      
    case let .upDownload(url: url, taskType: taskType):
      let upDownloadCoordinator = DefaultPopupUpDownloadCoordinator(navigationController: navigationController)
      childCoordinators[.upDownload] = upDownloadCoordinator
      upDownloadCoordinator.delegate = self
      upDownloadCoordinator.start(with: .showPopupUpProcessingDownload(parent: nil, restoreImage: nil, url: url, taskType: taskType))

    case .fileEditDidFinish:
      // navigationController 에서 두단계 pop viewController를 한번에
      mLogger.info("fileEditDidFinish popToRoot")
      navigationController.popToRootViewController(animated: true)
      delegate?.fileEditCoordinatorDidFinish()
    }
  }
  
  func make(fileModel: FileListCellModel, editType: FileEditType) -> FileEditViewController {
    let viewModel = FileEditViewModel(
      coordinator: self,
      fileModel: fileModel,
      aiUseCase: Composers.aiUseCase,
      editType: editType
    )
    
    let playerModel = FilePlayerViewModel(fileModel: fileModel, editType: editType)
    let vc = FileEditViewController()
    vc.editType = editType
    vc.playerViewModel = playerModel
    vc.viewModel = viewModel
    return vc
  }

  func make(fileModel: FileListCellModel, editType: FileEditType) -> VideoTrimViewController {
    let viewModel = FileEditViewModel(
      coordinator: self,
      fileModel: fileModel,
      aiUseCase: Composers.aiUseCase,
      editType: editType
    )
    
    let path = viewModel.fileModel.filePath
    let asset = AVAsset(url: path!)
    let vc = VideoTrimViewController(avAsset: asset, animateDismiss: true)
    return vc
  }
}

extension DefaultFileEditCoordinator: PopupUpDownloadCoordinatorFinishDelegate {
  func popupUpDownloadCoordinatorDidFinish() {
    childCoordinators[.upDownload] = nil
    navigationController.popToRootViewController(animated: false)
  }
}
