//
//  FileEditCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

protocol FileEditCoordinator: AnyObject {
  func navigate(to step: FileEditSteps)
}

public protocol FileEditCoordinatorFinishDelegate: AnyObject {
  func fileEditCoordinatorDidFinish()
}

public enum FileEditSteps: Step {
  case showFileEdit(model: FileListCellModel, editType: FileEditType)
  case upDownload(url: URL, taskType: TaskType)
  case fileEditDidFinish
}

public enum FileEditChildCoordinator {
  case upDownload
}
