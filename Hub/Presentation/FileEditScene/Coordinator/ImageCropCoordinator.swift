//
//  ImageCropCoordinator.swift
//  Hub
//
//  Created by ncn on 6/24/25.
//

import UIKit

protocol ImageCropCoordinator: AnyObject {
  func navigate(to step: ImageCropSteps)
}

public protocol ImageCropCoordinatorFinishDelegate: AnyObject {
  func ImageCropCoordinatorDidFinish()
}

public enum ImageCropSteps: Step {
  case showImageCrop(parentController: ImageCropViewControllerDelegate, image: UIImage, fileName: String, category: CaptureCategory)
  case upProcessDownload(parent: UIViewController, cropedImage: UIImage, url: URL?, taskType: TaskType)
  case ImageCropDidFinish
}

public enum ImageCropChildCoordinator {
  case upProcessDownload
}

