//
//  DefaultImageCropCoordinator.swift
//  Hub
//
//  Created by ncn on 6/24/25.
//

import UIKit

final class DefaultImageCropCoordinator: NavigationCoordinator {
  public var navigationController: UINavigationController
  weak var delegate: ImageCropCoordinatorFinishDelegate?
  
  var childCoordinators = [ImageCropChildCoordinator: Coordinator]()
  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func start(with step: ImageCropSteps) {
    navigate(to: step)
  }
}

extension DefaultImageCropCoordinator: ImageCropCoordinator {
  func navigate(to step: ImageCropSteps) {
    switch step {
    case let .showImageCrop(parentController: controller, image: image, fileName: fileName, category: category):
      let cropController = ImageCropViewController(image: image, origFileName: fileName, category: category)
      cropController.delegate = controller
      cropController.coordinator = self
      cropController.blurredBackground = false
      cropController.modalPresentationStyle = .fullScreen
      navigationController.present(cropController, animated: false)
      
    case let .upProcessDownload(parent: parent, cropedImage: image, url: url, taskType: taskType):
      let upDownloadCoordinator = DefaultPopupUpDownloadCoordinator(navigationController: navigationController)
      childCoordinators[.upProcessDownload] = upDownloadCoordinator
      upDownloadCoordinator.delegate = self
      upDownloadCoordinator.start(with: .showPopupUpProcessingDownload(parent: parent, restoreImage: image, url: url, taskType: taskType))
      
    case .ImageCropDidFinish:
      mLogger.info("ImageCropDidFinish popToRoot")
      navigationController.popToRootViewController(animated: true)
      delegate?.ImageCropCoordinatorDidFinish()
    }
  }
}

extension DefaultImageCropCoordinator: PopupUpDownloadCoordinatorFinishDelegate {
  func popupUpDownloadCoordinatorDidFinish() {
    mLogger.info("popupUpDownloadCoordinatorDidFinish")
    childCoordinators[.upProcessDownload] = nil
    navigate(to: .ImageCropDidFinish)
  }
}
