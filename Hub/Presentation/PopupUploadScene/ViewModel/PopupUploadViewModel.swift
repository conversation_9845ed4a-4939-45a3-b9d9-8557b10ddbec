//
//  PopupUploadViewModel.swift
//  Hub
//
//  Created by leej<PERSON><PERSON><PERSON> on 12/5/23.
//

import Foundation
import RxCocoa
import RxSwift

final class PopupUploadViewModel: BaseViewModel {
  let useCase: VodUseCase
  let fileModel: [FileListCellModel]

  init(useCase: VodUseCase, fileModel: [FileListCellModel]) {
    self.useCase = useCase
    self.fileModel = fileModel
  }

  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxUploadEnd = PublishRelay<Bool>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.viewDidLoad
      .subscribe(onNext: { [weak self] in
        guard let self = self else { return }
        self.uploadFiles(cellModels: self.fileModel, output: output)
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupUploadViewModel {
  private func uploadFiles(cellModels: [FileListCellModel], output: Output) {
    guard let serial = AppManager.shared.dashcam?.serial else { return }
    if cellModels.isEmpty {
      output.rxUploadEnd.accept(true)
      return
    }
    var cellModels = cellModels
    let cellModel = cellModels.removeFirst()
    let header = HeaderCloud(type: "uploadfile", serial: serial)
    let data = UploadFileCloud.Send.SendData(
      filename: cellModel.fileName, type: cellModel.vodType, videotime: 10000, to: "Cloud")
    let model = UploadFileCloud.Send(header: header, data: data)
    useCase.send(uploadFile: model) { [weak self] result in
      switch result {
      case .success(_):
        self?.uploadFiles(cellModels: cellModels, output: output)
      case .failure(let connectError):
        Log.warning(to: connectError)
        output.rxUploadEnd.accept(false)
        return
      }
    }
  }
}
