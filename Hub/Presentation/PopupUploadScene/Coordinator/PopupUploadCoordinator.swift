//
//  PopupUploadCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

//protocol PopupUploadCoordinator: AnyObject {
//  func start(model: [FileListCellModel])
//}


protocol PopupUploadCoordinator: AnyObject {
  func navigate(to step: PopupUploadSteps)
}

public protocol PopupUploadCoordinatorFinishDelegate: AnyObject {
  func popupUploadCoordinatorDidFinish()
}

public enum PopupUploadSteps: Step {
  case showPopupUpload(models: [FileListCellModel])
  case popupUploadDidFinish
}



