//
//  MoreViewController.swift
//  Hub
//
//  Created by ncn on 12/1/24.
//

import UIKit
import RxSwift

public class MoreViewController: UITableViewController {
  var datas = [L.dialog_history_title.localized, L.action_settings.localized]
  weak var coordinator: MoreCoordinator?
  private let repo = Composers.socketRepository
  
  var dbBottomSheet: SheetHistoryViewController = {
    let historyViewModel = SheetHistoryViewModel()
    let viewController = SheetHistoryViewController(vm: historyViewModel)
    return viewController
  }()
  
  private var disposedBag = DisposeBag()
  
  init(coordinator: MoreCoordinator) {
    super.init(style: .plain)
    self.coordinator = coordinator
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  public override func viewDidLoad() {
    super.viewDidLoad()
    self.title = L.menu_more_title.localized
    navigationItem.backButtonTitle = ""
    AppManager.shared.mode = .wifi
    tableView.backgroundColor = .background
    tableView.register(CustomMoreViewCell.self, forCellReuseIdentifier: CustomMoreViewCell.identifier)
    tableView.tableFooterView = UIView()
    
    tableView.rowHeight = UITableView.automaticDimension
    tableView.rowHeight = 70
    
    tableView.separatorStyle = .none
  }
  
  public override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    tabBarController?.toggleTabbar(isShow: true)
    addChildController(dbBottomSheet)
  }
  
  func showSheetMenu(model: BottomSheetModel) {
    dbBottomSheet.setItems(model: model)
    dbBottomSheet.show()
  }
  
  public override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    return datas.count
  }
  
  public override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    guard let cell = tableView.dequeueReusableCell(withIdentifier: CustomMoreViewCell.identifier, for: indexPath) as? CustomMoreViewCell else {
      return UITableViewCell()
    }
    
    cell.selectionStyle = .none
    cell.configure(title: datas[indexPath.row])
    return cell
  }
  
  public override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    // Prevent multiple selections while processing
//    guard !isProcessingRequest else { return }
    sLogger.info("didSelectRowAt")
    
    switch indexPath.row {
    case 0:
      AppManager.shared.mode = Current.isConnect ? .wifi : .file
      if AppManager.shared.mode == .file {
        readLocalHistoryDB()
      } else {
        coordinator?.navigate(to: .pushHistoryScene)
      }
      
    case 1:
      if Current.isConnect == false {
        LKPopupView.popup.alert {[
          .subTitle(L.blackbox_disconnect.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              self.tabBarController?.selectedIndex = HubChildCoordinator.home.rawValue
            })
          ])
        ]}
      } else if let driveMode = AppManager.shared.initInfo?.driveMode,
              let state = DashcamState(rawValue: driveMode), state == .Parking &&
                  AppManager.shared.dashcamWifiConfig?.sleepmode == 1 {
        LKPopupView.popup.alert {[
          .subTitle(L.s1_disable_setting_in_parking.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({

            })
          ])
        ]}
      } else {
        self.coordinator?.navigate(to: .pushSettingScene)
      }
    default:
      break
    }
  }
  
  func readLocalHistoryDB() {
    let cellModels = SqlQuery.getDeviceList().map {
      SheetHistoryCellModel(ssid: $0.ssid, macAddress: $0.macAdr)
    }
    if cellModels.isEmpty {
      LKPopupView.popup.toast(hit: "\(L.history_empty_msg.localized)")
      return
    }
    
    let sheetModel = BottomSheetModel(
      title: L.local_history_sheet_title.localized, cellModel: cellModels
    )
    self.showSheetMenu(model: sheetModel)
    
    dbBottomSheet.tableView.rx.itemSelected
      .take(1)
      .subscribe(onNext: { [weak self] indexPath in
        
        guard let self else { return }
        let cellModel = sheetModel.cellModel[indexPath.row]
        let dashcamModel = HomeDashcamModel(
          model: "", serial: "", mdName: "",
          macaddress: cellModel.macAddress,
          voltage: "", drivemode: "", status: nil, netStatus: "",
          ssid: cellModel.ssid,
          netpasswd: "", imageUrl: "", dvcAls: "", timeZone: "", time: "",
          udG: nil
        )
        
        let deviceInfo = InformationModel(
          name: "", company: "", model: "", version: "", osversion: nil, vchannels: "",
          vchannelbits: 0b111,
          protocol: "",
          macaddress: cellModel.macAddress,
          ongpsver: nil,
          ssid: cellModel.ssid,
          netpasswd: nil, userinitsetting: nil, userlanguage: nil, userlanguagever: nil,
          countrycode: nil, gmt: nil
        )
        AppManager.shared.dashcam = dashcamModel
        AppManager.shared.deviceInfo = deviceInfo
        
        coordinator?.navigate(to: .pushHistoryScene)
        dbBottomSheet.hide()
      })
      .disposed(by: disposedBag)
  }
  
}

extension MoreViewController {
  func requestStopRecord() {
    LKPopupView.popup.loading()
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstop", param: 1)
    let send = SetCommand.Send(header: header, setcommand: command)
    
    repo.send(to: send) { [weak self] response, error in
      guard let self else { return }
      
      if let error = error {
        hLogger.error("error: \(error.localizedDescription)")
        return
      }
      
      if response?.header.rmsg == "OK" {
        DispatchQueue.main.async {
          LKPopupView.popup.hideLoading()
          self.coordinator?.navigate(to: .pushSettingScene) 
        }
      }
    }
  }
}
