//
//  SettingViewController.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import RxRelay
import RxSwift
import UIKit

class TravelLogViewController: UIViewController {
  let dateSelectView = ChartMenuView()
  let tableView = UITableView(frame: .zero, style: .plain)
  var mainViewModel: TravelLogViewModel?
  var wifiViewModel: TravelLogWifiViewModel?

  lazy var emptyLabel: UILabel = {
    let label = UILabel()
    label.text = L.history_chart_empty_msg.localized
    label.textAlignment = .center
    label.font = .h2Regular
    label.textColor = .grayText
    return label
  }()

  var disposedBag = DisposeBag()

  override func viewDidLoad() {
    super.viewDidLoad()
    self.navigationItem.title = L.driving_history.localized
    navigationItem.backButtonTitle = ""

    setTableView()
    setupView()

    bindViewModel(to: mainViewModel)
    bindViewModel(to: wifiViewModel)
  }

  func setupView() {
    view.addSubviews([dateSelectView, tableView])

    dateSelectView.pin.top(safe: true).horizontally().height(64).activate()
    tableView.pin.below(dateSelectView).horizontally().bottom(safe: false).activate()
  }

  private func setTableView() {
    tableView.separatorStyle = .none
    tableView.backgroundColor = .background
    tableView.translatesAutoresizingMaskIntoConstraints = false
    tableView.allowsSelection = true
    tableView.rowHeight = 65.0
    tableView.register(cellType: TravelLogCell.self)

    // RxSwift 바인딩 설정
    tableView.rx.setDelegate(self).disposed(by: disposedBag)

    // 스와이프 삭제 기능 설정
    tableView.rx.itemDeleted
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        let currentItems = self.wifiViewModel?.initState.listItem.value ?? []
        let itemToDelete = currentItems[indexPath.row]

        self.wifiViewModel?.dbUseCase.deleteTravelLog(items: [itemToDelete]) { result in
          switch result {
          case .success(_):
            var updatedItems = currentItems
            updatedItems.remove(at: indexPath.row)
            self.wifiViewModel?.initState.listItem.accept(updatedItems)
          case .failure(let error):
            print("삭제 실패: \(error.localizedDescription)")
          }
        }
      })
      .disposed(by: disposedBag)
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let mainViewModel = viewModel as? TravelLogViewModel else { return }

    let input = TravelLogViewModel.Input()
    let _ = mainViewModel.bind(input: input, disposedBag: self.disposedBag)
  }
}

extension TravelLogViewController: UITableViewDelegate {
  func setDataSoruce(listItem: BehaviorRelay<[TravelLogCellModel]>) {
    guard let mainViewModel = mainViewModel else { return }
    // add empty label when listItem is empty
    if listItem.value.isEmpty {
      tableView.backgroundView = emptyLabel
    }
      
    
    listItem.asObservable()
      .bind(to: tableView.rx.items) { [weak self] (tableView, row, element) in
        guard let self = self else { return UITableViewCell() }
        tableView.backgroundView = nil
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.tableView.dequeueReusableCell(
          for: indexPath,
          cellType: TravelLogCell.self)

        if mainViewModel.deleteIndexList.contains(where: { $0 == indexPath }) {
          cell.setDelete(isSelect: true)
        } else {
          cell.setDelete(isSelect: false)
        }
        cell.selectionStyle = .none
        cell.tag = row
        cell.setData(model: element)

        return cell
      }
      .disposed(by: self.disposedBag)

    tableView.reloadData()
  }

  func tableView(_ tableView: UITableView, canFocusRowAt indexPath: IndexPath) -> Bool {
    return true
  }

  func tableView(_ tableView: UITableView, editingStyleForRowAt indexPath: IndexPath)
    -> UITableViewCell.EditingStyle
  {
    return .delete
  }

  func tableView(
    _ tableView: UITableView,
    trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath
  ) -> UISwipeActionsConfiguration? {
    let deleteAction = UIContextualAction(style: .destructive, title: nil) {
      [weak self] (action, view, completion) in
      guard let self = self else { return }
      let currentItems = self.wifiViewModel?.initState.listItem.value ?? []
      let itemToDelete = currentItems[indexPath.row]

      self.wifiViewModel?.dbUseCase.deleteTravelLog(items: [itemToDelete]) { result in
        switch result {
        case .success(_):
          var updatedItems = currentItems
          updatedItems.remove(at: indexPath.row)
          self.wifiViewModel?.initState.listItem.accept(updatedItems)
          completion(true)
        case .failure(let error):
          print("삭제 실패: \(error.localizedDescription)")
          completion(false)
        }
      }
    }

    deleteAction.image = UIImage(named: "travel_trash_ic")
    return UISwipeActionsConfiguration(actions: [deleteAction])
  }
}
