//
//  TravelLogCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

//protocol TravelLogCoordinator {
//  func pushTravelMap(items: [TravelLogCellModel], index: Int)
//  func dissmiss()
//
//  func start(
//    _ chartItem: EventChartModel?, _ dayItem: CalanderDayModel?, _ dateItem: SearchDateModel?)
//}

protocol TravelLogCoordinator: AnyObject {
  func navigate(to step: TravelLogSteps)
}

public protocol TravelLogCoordinatorFinishDelegate: AnyObject {
  func travelLogCoordinatorDidFinish()
}

public enum TravelLogSteps: Step {
  case showTravelLog
  case travelLogWith(
    _ chartItem: EventChartModel? = nil,
    _ dayItem: CalanderDayModel? = nil,
    _ dateItem: SearchDateModel? = nil
  )
  case travelMap(items: [TravelLogCellModel], index: Int)
  case travelLogDidFinish
}

public enum TravelLogChildCoordinator {
  case travelMap
}


