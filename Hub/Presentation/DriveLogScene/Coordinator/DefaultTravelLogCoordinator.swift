//
//  DefaultTravelLogCoordinator.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import UIKit

final class DefaultTravelLogCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: TravelLogCoordinatorFinishDelegate?
  var childCoordinators = [TravelLogChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  func start(with step: TravelLogSteps) {
    navigate(to: step)
  }
}

extension DefaultTravelLogCoordinator {
  func configWifiModel(
    viewController: TravelLogViewController,
    chartModel: EventChartModel?,
    dayModel: CalanderDayModel?,
    dateModel: SearchDateModel?
  ) {
    //S: db usecase
    viewController.wifiViewModel = TravelLogWifiViewModel(coordinator: self, dbUseCase: Composers.dbUsecase)
    if let dateModel = dateModel {
      viewController.wifiViewModel?.initState.rxSearchDate.onNext(dateModel)
    }
    
    if let dayModel = dayModel?.date.intDate() {
      viewController.wifiViewModel?.initState.rxSearchDate.onNext(
        SearchDateModel(year: dayModel.year, month: dayModel.month, day: dayModel.day))
    }
    
    if let chartModel = chartModel {
      let date = chartModel.originDate.toDate(format: "yyyy/MM/dd") ?? Date()
      let ret = date.intDate()
      viewController.wifiViewModel?.initState.rxSearchDate.onNext(
        SearchDateModel(year: ret.year, month: ret.month, day: ret.day)
      )
    }
  }
}

extension DefaultTravelLogCoordinator: TravelLogCoordinator {
  func navigate(to step: TravelLogSteps) {
    switch step {
    case .showTravelLog:
      showTravelLog()
    case let .travelLogWith(chartItem, dayItem, dateItem):
      travelLogWith(chartItem, dayItem, dateItem)
    case let .travelMap(items: items, index: index):
      pushTravelMap(items: items, index: index)
    case .travelLogDidFinish:
      navigationController.popViewController(animated: true)
      delegate?.travelLogCoordinatorDidFinish()
    }
  }
  

  func showTravelLog() {
    let vc = TravelLogViewController()
    vc.hidesBottomBarWhenPushed = true
    vc.mainViewModel = TravelLogViewModel(coordinator: self)

    switch AppManager.shared.mode {
    case .wifi:
      configWifiModel(viewController: vc, chartModel: nil, dayModel: nil, dateModel: nil)
      break
    case .file:
      break
    }
    navigationController.pushViewController(vc, animated: true)
  }

  func travelLogWith(
    _ chartItem: EventChartModel? = nil, _ dayItem: CalanderDayModel? = nil,
    _ dateItem: SearchDateModel? = nil
  ) {
    let vc = TravelLogViewController()
    vc.hidesBottomBarWhenPushed = true
    vc.mainViewModel = TravelLogViewModel(coordinator: self)
    configWifiModel(
      viewController: vc, chartModel: chartItem, dayModel: dayItem, dateModel: dateItem
    )

    navigationController.pushViewController(vc, animated: true)
  }


  func pushTravelMap(items: [TravelLogCellModel], index: Int) {
    let mapCoordinator = DefaultTravelMapCoordinator(navigationController: navigationController)
    mapCoordinator.delegate = self
    childCoordinators[.travelMap] = mapCoordinator
    mapCoordinator.start(with: .showTravelMap(items: items, index: index))
  }
}

extension DefaultTravelLogCoordinator: TravelMapCoordinatorFinishDelegate {
  func travelMapCoordinatorDidFinish() {
    childCoordinators[.travelMap] = nil
  }
}
