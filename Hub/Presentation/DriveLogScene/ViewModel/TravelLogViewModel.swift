//
//  SettingViewModel.swift
//  Hub
//
//  Created by ncn on 2022/12/05.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

class TravelLogViewModel: BaseViewModel {
  weak var coordinator: TravelLogCoordinator?

  init(coordinator: TravelLogCoordinator) {
    self.coordinator = coordinator
  }

  let isDeleteMode = BehaviorSubject<Bool>(value: false)
  var deleteIndexList: [IndexPath] = []

  struct Input {
    //    let backButtonEvent: Observable<Void>
    //    let checkButtonEvent: Observable<Bool>
  }

  struct Output {
    let rxCheckButtonState = PublishSubject<Bool>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    //    input.backButtonEvent
    //      .subscribe(onNext: { [weak self] event in
    //        guard let self = self else { return }
    //        self.dismiss()
    //      })
    //      .disposed(by: disposedBag)

    //    let checkButton = input.checkButtonEvent
    //      .map { !$0 }
    //      .share()
    //    checkButton
    //      .bind(to: isDeleteMode)
    //      .disposed(by: disposedBag)
    //
    //    checkButton
    //      .bind(to: output.rxCheckButtonState)
    //      .disposed(by: disposedBag)

    return output
  }
}

extension TravelLogViewModel {
  func dismiss() {
    coordinator?.navigate(to: .travelLogDidFinish)
  }
}
