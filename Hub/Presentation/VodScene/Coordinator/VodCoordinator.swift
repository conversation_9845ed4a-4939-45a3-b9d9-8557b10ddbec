//
//  VodCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import UIKit

protocol VodCoordinator: AnyObject {
  func navigate(to step: VodSteps)
}

public protocol VodCoordinatorFinishDelegate: AnyObject {
  func vodCoordinatorDidFinish()
}

public enum VodSteps: Step {
  case showVod(type: PlayerViewType)
  case showLocalVod(model: VodLocalModel, type: PlayerViewType, delegate: DismissDelegate?)
  case showStreamVod(model: VodStreamModel, type: PlayerViewType, delegate: DismissDelegate?)
  case edit(model: FileListCellModel, editType: FileEditType)
  case download(models: [FileListCellModel], type: FileStorageType)
  case share(models: [FileListCellModel], type: FileStorageType)
  case imageCrop(image: UIImage?, model: FileListCellModel, type: CaptureCategory)
  case vodDidFinish
}

public enum VodChildCoordinator {
  case download
  case edit
  case imageCrop
}
