//
//  DefaultVodCoordinator.swift
//  Hub
//
//  Created by ncn on 2023/03/02.
//

import UIKit

class DefaultVodCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: VodCoordinatorFinishDelegate?

  var childCoordinators = [VodChildCoordinator: Coordinator]()
  var localVodController: VodViewController?
  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func start(with step: VodSteps) {
    navigate(to: step)
  }
}

extension DefaultVodCoordinator {
  private func configWifiModel(streamModel: VodStreamModel, vodType: PlayerViewType) -> VodWifiViewModel {
    let streamViewModel = VodWifiViewModel(
      coordinator: self,
      streamUseCase: Composers.streamUseCase,
      fileUseCase: Composers.fileCommandUseCase,
      vodType: vodType
    )
    streamViewModel.vodStreamModel = streamModel
    streamViewModel.selectedIndex = streamModel.selectedIndex

    return streamViewModel
  }

  private func configLocalModel(fileModel: VodLocalModel, vodType: PlayerViewType)
    -> VodLocalViewModel
  {
    let localViewModel = VodLocalViewModel(
      coordinator: self,
      fileModel: fileModel,
      vodType: vodType
    )
    return localViewModel
  }
}

extension DefaultVodCoordinator: VodCoordinator {
  func navigate(to step: VodSteps) {
    switch step {
    case let .showVod(type):
      showVod(vodType: type)
    case let .showLocalVod(model, type, delegate):
      AppManager.shared.mode = .file
      showLoadVod(model, vodType: type, dismissDelegate: delegate)
    case let .showStreamVod(model, type, delegate):
      showStreamVod(model, vodType: type, dismissDelegate: delegate)
    case let .edit(models, editType):
      pushEdit(model: models, editType: editType)
    case let .download(models, type):
      pushDownload(model: models, type: type)
    case .share(let models, let type):
      pushShare(model: models, type: type)
    case let .imageCrop(image, model, type):
      presentImageCrop(image: image, model: model, type: type)
    case .vodDidFinish:
      DispatchQueue.main.async { [weak self] in
        self?.navigationController.popViewController(animated: true)
        self?.delegate?.vodCoordinatorDidFinish()
      }
    }
  }

  func showVod(vodType: PlayerViewType) {
    let streamViewModel = VodWifiViewModel(
      coordinator: self,
      streamUseCase: Composers.streamUseCase,
      fileUseCase: Composers.fileCommandUseCase,
      vodType: vodType
    )

    let viewModel = VodViewModel(coordinator: self)

    let vc = VodViewController()
    vc.viewModel = viewModel
    vc.streamViewModel = streamViewModel
    navigationController.pushViewController(vc, animated: true)
  }

  func showLoadVod(
    _ model: VodLocalModel, vodType: PlayerViewType, dismissDelegate: DismissDelegate?
  ) {
    localVodController = VodViewController()
    guard let vc = localVodController else { return }
    vc.viewModel = VodViewModel(coordinator: self)
    vc.vodLocalViewModel = configLocalModel(fileModel: model, vodType: vodType)
    vc.dismissDelegate = dismissDelegate

    if model.items[model.selectedIndex].type == .screenshot {
      vc.playerContainerView = VodPlayerContainerView(type: .snapShot)
    } else {
      vc.playerContainerView = VodPlayerContainerView(type: .localVod)
    }
    vc.hidesBottomBarWhenPushed = true
    navigationController.pushViewController(vc, animated: true)
  }

  func showStreamVod(_ model: VodStreamModel, vodType: PlayerViewType, dismissDelegate: DismissDelegate?) {
    let vc = VodViewController()
    vc.playerContainerView = VodPlayerContainerView(type: vodType)
    vc.viewModel = VodViewModel(coordinator: self)
    vc.dismissDelegate = dismissDelegate

    if case .wifi = AppManager.shared.mode {
      vc.streamViewModel = configWifiModel(streamModel: model, vodType: vodType)
    }

    vc.hidesBottomBarWhenPushed = true
    navigationController.pushViewController(vc, animated: true)
  }

  func pushEdit(model: FileListCellModel, editType: FileEditType) {
    let editCoordinator = DefaultFileEditCoordinator(navigationController: navigationController)
    childCoordinators[.edit] = editCoordinator
    editCoordinator.delegate = self
    editCoordinator.start(with: .showFileEdit(model: model, editType: editType))
  }

  func pushDownload(model: [FileListCellModel], type: FileStorageType) {
    let downloadCoordinator = DefaultPopupDownloadCoordinator(
      navigationController: navigationController)
    downloadCoordinator.delegate = self
    childCoordinators[.download] = downloadCoordinator
    downloadCoordinator.start(
      with: .showPopupDownload(model: model, editType: .trim, type: type, isToEdit: false))
  }

  func pushShare(model: [FileListCellModel], type: FileStorageType) {
    let downloadCoordinator = DefaultPopupDownloadCoordinator(
      navigationController: navigationController)
    childCoordinators[.download] = downloadCoordinator
    downloadCoordinator.delegate = self
    downloadCoordinator.start(
      with: .showPopupDownload(model: model, editType: .trim, type: type, isToEdit: false))
  }
  
  func presentImageCrop(image: UIImage?, model: FileListCellModel, type: CaptureCategory) {
    let imageCropCoordinator = DefaultImageCropCoordinator(navigationController: navigationController)
    childCoordinators[.imageCrop] = imageCropCoordinator
    imageCropCoordinator.delegate = self
    guard let cellImage = model.thumbimage, let vc = localVodController else { return }
    if let image = image {
      do {
        let snapshotName = try HubFileNameGenerator.generateLicensePlateFileName(baseFileName: model.fileName, stage: .original)
        mLogger.info("Snapshot file name: \(snapshotName)")
        imageCropCoordinator.navigate(to: .showImageCrop(parentController: vc, image: image, fileName: snapshotName, category: type))
      } catch {
        mLogger.error("Failed to generate file name for image crop: \(error.localizedDescription)")
      }
    } else {
      imageCropCoordinator.navigate(to: .showImageCrop(parentController: vc, image: cellImage, fileName: model.fileName, category: type))
    }
  }
}

// MARK: Finish Delegate

extension DefaultVodCoordinator: FileEditCoordinatorFinishDelegate {
  func fileEditCoordinatorDidFinish() {
    childCoordinators[.edit] = nil
  }
}

extension DefaultVodCoordinator: PopupDownloadCoordinatorFinishDelegate {
  func popupDownloadCoordinatorDidFinish() {
    
    childCoordinators[.download] = nil
  }
}

extension DefaultVodCoordinator: ImageCropCoordinatorFinishDelegate {
  func ImageCropCoordinatorDidFinish() {
    childCoordinators[.imageCrop] = nil
  }
}

