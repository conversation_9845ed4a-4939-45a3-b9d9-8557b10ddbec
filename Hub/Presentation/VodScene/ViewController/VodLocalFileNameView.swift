//
//  VodLocalFileNameView.swift
//  Hub
//
//  Created by ncn on 1/22/25.
//
import UIKit

class VodLocalFileNameView: UIView {

  var nameLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 13)
    label.textColor = .mainBlack
    label.textAlignment = .left
    label.text = "DEFAULT_20240904_101501_INF_3.mp4"
    return label
  }()

  override init(frame: CGRect) {
    super.init(frame: frame)
    backgroundColor = .clear
    setup()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  private func setup() {
    addSubview(nameLabel)
    nameLabel.pin.horizontally(offset: 15).height(16).centerY().activate()
  }
}
