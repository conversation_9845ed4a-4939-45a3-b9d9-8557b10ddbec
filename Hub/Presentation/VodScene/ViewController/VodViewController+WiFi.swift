//
//  VodViewController+WiFi.swift
//  Hub
//
//  Created by ncn on 2023/06/01.
//

import RxCocoa
import RxSwift
import UIKit

extension VodViewController {
  func updateWifiConstraint() {
    if streamViewModel?.vodStreamModel?.items.count == 1 || playerContainerView.handlerView.type == .snapShot || playerContainerView.handlerView.type == .localVod {
      playerContainerView.handlerView.hideMoveButton()
    }
    let height = playerContainerView.baseHeight + 40  // menu
    playerContainerView.snp.makeConstraints { make in
      make.height.equalTo(height)
    }
  }

  func bindViewmodel(to streamViewModel: VodWifiViewModel?) {
    guard let model = streamViewModel else { return }
    model.vlcPlayerView = playerContainerView.playerView

    updateWifiConstraint()

    let handlerView = playerContainerView.handlerView
    let playButtonEvent = handlerView.playButton.rx.tapWithIsSelected.asObservable()
//    let backButtonEvent = navigationView.leftButton.rx.tap.asObservable()
//    let menuChannelButtonEvent = playerContainerView.menuView.channelButton.rx.tap.asObservable()
    let channelButtonEvent = playerContainerView.handlerView.controlBarView.channelButton.rx
      .tap.asObservable()
    let changeChannelEvent = sheetMenuViewController.tableView.rx.itemSelected.map { $0.row }
      .asObservable()

    let snapShotButtonLandscapeEvent = playerContainerView.handlerView.controlBarView.snapButton.rx
      .tap.asObservable()
    let snapShotButtonEvent = Observable.merge([
      snapShotButtonLandscapeEvent,
    ]).map { [weak self] in self?.playerContainerView.playerView.takeScreenshot() }
      .asObservable()

    let forwardEvent = handlerView.forwardButton.rx.tap.asObservable()
    let backwardEvent = handlerView.backwardButton.rx.tap.asObservable()
    let nextEvent = handlerView.nextButton.rx.tap.asObservable()
    let previousEvent = handlerView.previousButton.rx.tap.asObservable()
    let rxState = self.playerContainerView.playerView.rxIsState
    let downloadButtonEvent = playerContainerView.handlerView.controlBarView.downloadButton.rx.tap.asObservable()
    let deleteButtonEvent = playerContainerView.handlerView.controlBarView.deleteButton.rx.tap.asObservable()
    
    let fileSelectedEvent = fileListView.zoomView.rxSelectedTooltip.asObservable()
    let playerTime = self.playerContainerView.playerView.rxTime.asObservable()
    let playerRemainTime = self.playerContainerView.playerView.rxRemainingTime.asObservable()

    let input = VodWifiViewModel.Input(
      willAppearEvent: self.rx.viewWillAppear,
      willDisappearEvent: self.rx.viewDidDisappear,
//      backButtonEvent: backButtonEvent,
      playButtonEvent: playButtonEvent,
      channelButtonEvent: channelButtonEvent,
      changeChannelEvent: changeChannelEvent,
      snapShotButtonEvent: snapShotButtonEvent,
      forwardEvent: forwardEvent,
      backwardEvent: backwardEvent,
      nextEvent: nextEvent,
      previousEvent: previousEvent,
      playerState: rxState,
      playerTime: playerTime,
      playerRemainTime: playerRemainTime,
      downloadButtonEvent: downloadButtonEvent,
      deleteButtonEvent: deleteButtonEvent,
      fileSelectedEvent: fileSelectedEvent
    )

    // url
    let (output, state) = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxStreamUrl
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] url in
        DispatchQueue.main.async {
          guard let self else { return }
          self.highlightLineX = 0
          self.playerContainerView.openStream(url: url)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxStreamUrlWithChange
      .observe(on: MainScheduler.asyncInstance)
      .withLatestFrom(self.playerContainerView.playerView.rxTime)
      .subscribe(onNext: { [weak self] time in
        guard let self else { return }

        self.currentGSensorData = nil
        self.graphContainView.resetData()

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
          vLogger.log("rxStreamUrlWithChange: time \(Double(time) / 1000.0)")
          let url = "rtsp://192.168.111.1:9010/vod"
          self.playerContainerView.playerView.pause()
          LKPopupView.popup.loading()
          self.highlightLineX = 0
          self.playerContainerView.openStream(url: url)
          state.isChannelChangedSeek.accept(true)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxFileUrl
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] url in
        guard let self = self else { return }
        DispatchQueue.main.async {
          self.highlightLineX = 0
          self.playerContainerView.openFile(path: url)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxCurrentItem
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] cellModel in
        let channelItems = AppManager.shared.deviceInfo?.vchannelbits?.attachChannel.displayNames ?? [L.channel_front.localized]
        let sheetItem = SheetMenuModel(title: L.channel_select_txt.localized, items: channelItems)
        self?.sheetMenuViewController.setItems(model: sheetItem)
        vLogger.info("current channel \(state.currentChannel.value)")
        self?.sheetMenuViewController.tableView.selectRow(
          at: IndexPath(row: state.currentChannel.value - 1, section: 0),
          animated: false,
          scrollPosition: .none
        )
      })
      .disposed(by: self.disposedBag)

    output.rxFilelistSetting
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] array in
        guard let self = self else { return }
        let row = model.selectedIndex
        let indexPath = IndexPath(row: row, section: 0)
        self.fileListView.zoomView.list = array
        self.fileListView.zoomView.viewModel.selectedIndex = indexPath
        Log.message(to: indexPath)
      })
      .disposed(by: self.disposedBag)

    output.rxChangeSelectIndex
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] row in
        guard let self = self else { return }
        let indexPath = IndexPath(row: row, section: 0)
        self.fileListView.zoomView.viewModel.selectedIndex = indexPath
        self.fileListView.zoomView.collectionView.reloadData()
        self.fileListView.zoomView.collectionView.scrollToItem(at: indexPath, at: .centeredVertically, animated: true)
      })
      .disposed(by: self.disposedBag)

    output.rxPlayerState
      .bind(onNext: { playerState in
        switch playerState {
        case .paused:
          debugPrint("##@@ rxPlayerState: paused")
          if state.isForward.value {
            state.isForward.accept(false)
            debugPrint("##@@ rxPlayerState: isForward")
            self.playerContainerView.playerView.jumpForward(interval: 5)
          }
          
          if state.isBackward.value {
            state.isBackward.accept(false)
            debugPrint("##@@ rxPlayerState: isBackward")
            self.playerContainerView.playerView.jumpBackworad(interval: 5)
          }

          if state.isChannelChangedSeek.value {
            state.isChannelChangedSeek.accept(false)
            LKPopupView.popup.hideLoading()
            debugPrint("##@@ rxPlayerState: pause isChannelChangedSeek")
            self.playerContainerView.playerView.seekToPause()
            self.playerContainerView.playerView.play()
          }

          handlerView.playButton.isSelected = true
        case .stopped, .ended, .error , .esdeleted, .changed:
          handlerView.playButton.isSelected = true
        case .playing:
          if state.isChannelChangedSeek.value {
            state.isChannelChangedSeek.accept(false)
            debugPrint("##@@ rxPlayerState playing: isChannelChangedSeek")
            self.playerContainerView.playerView.seekToPause()
          }

          handlerView.playButton.isSelected = false
        case .esadded, .buffering, .opening:
          handlerView.playButton.isSelected = false
        }
      })
      .disposed(by: disposedBag)

    output.rxIsPause
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isPause in
        guard let self else { return }
        handlerView.playButton.isSelected = isPause
        let player = self.playerContainerView.playerView
        debugPrint("##@@ rxIsPause: isPause:\(isPause), remainTime: \(player.remainTime)")
        
        if !isPause && player.remainTime < 500
            || player.mediaPlayer.media == nil {
          let url = URL(string: "rtsp://192.168.111.1:9010/vod")!
          self.playerContainerView.openFile(path: url)
          return
        }
        
        if isPause {
          player.pause()
        } else {
//          if let pauseTime = player.pauseTime {
//            debugPrint("##@@ rxIsPause: pauseTime: \(pauseTime)")
//            player.mediaPlayer.time = pauseTime
//          }
          player.resume()
//          player.play()
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsForward
      .asObserver()
      .subscribe(onNext: { [weak self] isFoward in
        guard let self = self else { return }

        let view = self.playerContainerView.playerView
        view.showIndicator()
        view.pause()
        isFoward ? state.isForward.accept(true) : state.isBackward.accept(true)
//        isFoward ? view.jumpForward(interval: 5) : view.jumpBackworad(interval: 5)
//        isFoward ? view.seekToTimestamp(seconds: 5) : view.seekToTimestamp(seconds: -5)
      })
      .disposed(by: self.disposedBag)

    // state
    output.rxIsStart
      .asObserver()
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxFileImage
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] image in
        guard let self = self else { return }
        self.playerContainerView.openScreenShot(image: image)
        LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxIsStop
      .asObserver()
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsError
      .asObserver()
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsPlayerIndicator
      .asObserver()
      .subscribe(onNext: { [weak self] isValue in
        guard let self = self else { return }

        isValue
          ? self.playerContainerView.playerView.showIndicator()
          : self.playerContainerView.playerView.closeIndicator()

      })
      .disposed(by: self.disposedBag)

    output.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { isValue in
        isValue ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxIsCloseStream
      .asObservable()
      .subscribe(onNext: { [weak self] isClose in
        guard let self else { return }
        if isClose {
          playerContainerView.stop()
          viewModel?.dismiss()
        }
      })
      .disposed(by: self.disposedBag)

    output.rxDeletePopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] cellModel in
        guard let self else { return }
        let confirmAction: (() -> Void)? = {
          model.deleteFile(model: cellModel, output: output) {
            DispatchQueue.main.async {
              // 상위 화면에 삭제된 데이터 전달
              self.dismissDelegate?.dismissWithData(data: cellModel)
              
              // 삭제 완료 팝업 표시
               LKPopupView.popup.alert {[
                  .subTitle(L.dialog_delete_success.localized),
                  .showCancel(false),
                  .confirmAction([
                    .text(L.ok.localized),
                    .textColor(.white),
                    .bgColor(.vueroidBlue),
                    .tapActionCallback({
                      if AppUtility.getOrientation() != .portrait {
                        AppUtility.lockOrientation(.portrait)
                        self.setOrientaion(isLandscape: false)
                        self.setOrientaionViews(isLandscape: false)
                      }

                      self.viewModel?.dismiss()
                    })
                  ])
               ]}
            }
          }
        }
        self.popup.showPopup(
          title: L.popup_delete_title.localized, desc: L.popup_delete_desc.localized,
          isCancel: true, confirmAction: confirmAction
        )
      })
      .disposed(by: disposedBag)

    output.rxGsensorStream
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] gsensor in
        guard let self = self else { return }
        self.currentGSensorData = gsensor
        self.graphContainView.setData(wifiVod: gsensor)
        self.highlightedLine(time: 0)
      })
      .disposed(by: disposedBag)

    output.rxChangeTrack
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        self?.playerContainerView.playerView.play()
      })
      .disposed(by: disposedBag)

    state.currentChannel
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] channel in
        guard let self else { return }
        
        var changedChannel = channel
        if self.streamViewModel?.selectedFileCellModel?.channelbits.attachChannel == .FI, changedChannel == 2 {
          changedChannel += 1
        }

        vLogger.info("vod channel: \(channel) changedChannel: \(changedChannel)")
        replaceVodFileNameWith(channel: changedChannel)
        playerContainerView.handlerView.controlBarView.currentChannel = changedChannel
        playerContainerView.handlerView.updateChannelImage(channel: changedChannel)
      })
      .disposed(by: disposedBag)
    
    
    state.isShowChannel
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isShow in
        isShow ? self?.sheetMenuViewController.show() : self?.sheetMenuViewController.hide()
      })
      .disposed(by: disposedBag)

    output.rxToastSnapShot
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: L.viewer_snapshot_save.localized)
      })
      .disposed(by: disposedBag)

    self.playerContainerView.playerView.rxTime
      .map { CGFloat($0) }
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] time in
        guard let self else { return }
        self.highlightedLine(time: time)
      })
      .disposed(by: disposedBag)    
  }
  
  private func replaceVodFileNameWith(channel: Int) {
    guard let viewModel = streamViewModel , let vodModel = viewModel.vodStreamModel else { return }
    let item = vodModel.items[viewModel.selectedIndex]
    let newName = item.fileName.toChannelFileName(channel: channel)
    vLogger.info("oldName: \(item.fileName) newName: \(newName)")
    vodModel.items[viewModel.selectedIndex].fileName = newName
  }
}
