//
//  VodViewController+Local.swift
//  Hub
//
//  Created by ncn on 2023/05/24.
//

import RxCocoa
import RxSwift
import UIKit

extension VodViewController {
  
  func updateVodConstraints() {
    if vodLocalViewModel?.fileModel.items.count == 1  || playerContainerView.handlerView.type == .snapShot || playerContainerView.handlerView.type == .localVod {
      playerContainerView.handlerView.hideMoveButton()
    }
  }

  func bindViewModel(to fileModel: VodLocalViewModel?) {
    guard let model = fileModel else { return }
    updateVodConstraints()

    let handlerView = self.playerContainerView.handlerView
    let playButtonEvent = handlerView.playButton.rx.tapWithIsSelected.asObservable()
    let forwardEvent = handlerView.forwardButton.rx.tap.asObservable()
    let backwardEvent = handlerView.backwardButton.rx.tap.asObservable()
    let nextEvent = handlerView.nextButton.rx.tap.asObservable()
    let previousEvent = handlerView.previousButton.rx.tap.asObservable()
//    let backButtonEvent = self.navigationView.leftButton.rx.tap.asObservable()
    let rxState = self.playerContainerView.playerView.rxIsState
//    let editMenuButtonEvent = self.descriptionContainerView.editMenuView.rxSelectedType
//      .asObservable()
    let fileSelectedEvent = fileListView.zoomView.rxSelectedTooltip.asObservable()
    let channelButtonEvent = self.playerContainerView.handlerView.controlBarView.channelButton.rx.tap.asObservable()
    let changeChannelEvent = sheetMenuViewController.tableView.rx.itemSelected.map { $0.row }.asObservable()
    let snapShotButtonLandscapeEvent = playerContainerView.handlerView.controlBarView.snapButton.rx.tap.asObservable()
    let snapShotButtonEvent = Observable.merge([
      snapShotButtonLandscapeEvent,
    ]).map { [weak self] in self?.playerContainerView.playerView.takeScreenshot() }
      .asObservable()
    let deleteButtonEvent = playerContainerView.handlerView.controlBarView.deleteButton.rx.tap.asObservable()
    
    let playTime = playerContainerView.playerView.rxTime.asObserver()

    let input = VodLocalViewModel.Input(
      didAppearEvent: self.rx.viewDidAppear,
      playButtonEvent: playButtonEvent,
      forwardEvent: forwardEvent,
      backwardEvent: backwardEvent,
      nextEvent: nextEvent,
      previousEvent: previousEvent,
//      backButtonEvent: backButtonEvent,
      channelButtonEvent: channelButtonEvent,
      changeChannelEvent: changeChannelEvent,
      snapShotButtonEvent: snapShotButtonEvent,
      deleteButtonEvent: deleteButtonEvent,
      playerState: rxState,
      playTime: playTime,
//      editMenuButtonEvent: editMenuButtonEvent,
      fileSelectedEvent: fileSelectedEvent
    )

    // url
    let (output, state) = model.bind(input: input, disposedBag: self.disposedBag)
    output.rxVodType
      .bind(onNext: { [weak self] type in
        handlerView.playButton.isHidden = type == .snapShot
        handlerView.forwardButton.isHidden = type == .snapShot
        handlerView.backwardButton.isHidden = type == .snapShot
        handlerView.controlBarView.durationLabel.isHidden = type == .snapShot

        self?.graphContainView.isHidden = true
        self?.graphContainView.snp.updateConstraints { make in
          make.height.equalTo(0)
        }
      })
      .disposed(by: disposedBag)

    output.rxFileSetting
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] cellModel in
        guard let self = self else { return }
//        self.descriptionContainerView.descriptionView.setData(model: cellModel)
        let channelItems = AppManager.shared.deviceInfo?.vchannelbits?.attachChannel.displayNames ?? [L.channel_front.localized]
        let sheetItem = SheetMenuModel(title: L.channel_select_txt.localized, items: channelItems)
        self.sheetMenuViewController.setItems(model: sheetItem)
        self.fileLocalNameView.nameLabel.text = cellModel.fileName
        self.updateChannelImageFromFileName()
        self.sheetMenuViewController.tableView.selectRow(
          at: IndexPath(row: 0, section: 0),
          animated: false,
          scrollPosition: .none
        )
      })
      .disposed(by: self.disposedBag)

    output.rxFilelistSetting
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] array in
        guard let self = self else { return }
        let row = model.fileModel.selectedIndex
        let indexPath = IndexPath(row: row, section: 0)
        self.fileListView.zoomView.list = array
        self.fileListView.zoomView.viewModel.selectedIndex = indexPath
        Log.message(to: indexPath)
      })
      .disposed(by: self.disposedBag)

    output.rxChangeSelectIndex
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] row in
        guard let self = self else { return }
        let indexPath = IndexPath(row: row, section: 0)
        self.fileListView.zoomView.viewModel.selectedIndex = indexPath
        self.fileListView.zoomView.collectionView.reloadData()
        self.fileListView.zoomView.collectionView.scrollToItem(at: indexPath, at: .centeredVertically, animated: true)
        if let selectedFile = self.vodLocalViewModel?.fileModel.items[safe: row] {
          self.fileLocalNameView.nameLabel.text = selectedFile.fileName
          self.updateChannelImageFromFileName()
        }
      })
      .disposed(by: self.disposedBag)

    output.rxFileUrl
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] url in
        guard let self = self else { return }
        DispatchQueue.main.async {
          if self.playerContainerView.playerView.state == .paused {
            return
          }
          self.playerContainerView.openFile(path: url)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxFileImage
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] image in
        guard let self = self else { return }
        self.playerContainerView.openScreenShot(image: image)
      })
      .disposed(by: self.disposedBag)

    output.rxDeleteLocalPopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] cellModel in
        let confirmAction: (() -> Void)? = {
                      model.deleteLocalFile(model: cellModel, output: output) { [weak self] in
              guard let self = self else { return }
              self.dismissDelegate?.dismissWithData(data: cellModel)
    
             LKPopupView.popup.alert {[
               .subTitle(L.dialog_delete_success.localized),
               .showCancel(false),
               .confirmAction([
                 .text(L.ok.localized),
                 .textColor(.white),
                 .bgColor(.vueroidBlue),
                 .tapActionCallback({ 
                   self.viewModel?.dismiss()
                 })
               ])
             ]}
            }
        }
        self?.popup.showPopup(
          title: L.popup_delete_title.localized, desc: L.popup_delete_desc.localized,
          isCancel: true, confirmAction: confirmAction
        )
      })
      .disposed(by: disposedBag)

    output.rxPlayerState
      .bind(onNext: { state in
        switch state {
        case .stopped, .ended, .error, .paused, .esdeleted, .changed:
          handlerView.playButton.isSelected = true
        case .esadded, .playing, .buffering, .opening:
          handlerView.playButton.isSelected = false
          DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            self.aiRestorationButton.isEnabled = true
            self.aiPrivacyButton.isEnabled = true
          }
        }
      })
      .disposed(by: disposedBag)

    // play, pause
    output.rxIsPause
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isPause in
        guard let self = self else { return }
        handlerView.playButton.isSelected = isPause
        let player = self.playerContainerView.playerView
        if player.state == .paused && player.remainTime < 100 {
          player.stop()
        }
        if player.state == .stopped {
          let index = model.fileModel.selectedIndex
          let url = model.fileModel.items[index].filePath
          self.playerContainerView.openFile(path: url!)
          return
        }
        isPause ? player.pause() : player.play()
      })
      .disposed(by: self.disposedBag)

    // forward, backward
    output.rxIsForward
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isFoward in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        let playerState = view.mediaPlayer.state
        if playerState == .paused {
          view.mediaPlayer.play()
        }
        isFoward ? view.mediaPlayer.jumpForward(5) : view.mediaPlayer.jumpBackward(5)
      })
      .disposed(by: self.disposedBag)

    // state
    output.rxIsStart
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] value in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsStop
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] value in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsError
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] value in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsCloseStream
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isClose in
        guard let self = self else { return }
        if isClose {
          self.playerContainerView.stop()
          DispatchQueue.main.async {
//            self.dismissController()
            self.viewModel?.dismiss()
          }
        }
      })
      .disposed(by: self.disposedBag)
    output.rxShare
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] shareUrl in
        guard let self = self else { return }
        let activityViewController = UIActivityViewController(
          activityItems: [shareUrl], applicationActivities: nil)
        activityViewController.popoverPresentationController?.sourceView = self.view  // so that iPads won't crash
        activityViewController.excludedActivityTypes = [.message]
        self.present(activityViewController, animated: true)
      })
      .disposed(by: disposedBag)

    output.rxChangeChannel
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] channel in
        Log.message(to: "--video-track : \(channel)")
        // 플레이어 채널 변경
        if channel == 0 {
          self?.playerContainerView.playerView.mediaPlayer.stop()
          let nowTime = Int(self?.playerContainerView.playerView.mediaPlayer.time.intValue ?? 0)
          let startTime = Double(nowTime) / 1000.0
          self?.playerContainerView.playerView.startWith(sec: startTime)
        } else {
          self?.playerContainerView.playerView.mediaPlayer.currentVideoTrackIndex = Int32(channel)
        }
        
        // UI 업데이트
        self?.playerContainerView.handlerView.controlBarView.currentChannel = channel
      })
      .disposed(by: disposedBag)

    output.rxToastSnapShot
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: L.viewer_snapshot_save.localized)
      })
      .disposed(by: disposedBag)
    
    state.isShowChannel
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isShow in
        isShow ? self?.sheetMenuViewController.show() : self?.sheetMenuViewController.hide()
      })
      .disposed(by: disposedBag)
  }
}
