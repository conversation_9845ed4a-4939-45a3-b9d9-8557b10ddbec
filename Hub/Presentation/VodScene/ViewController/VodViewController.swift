//
//  VodViewController.swift
//  Hub
//
//  Created by ncn on 2023/03/02.
//

import RxCocoa
import RxSwift
import UIKit
import AVFoundation

public protocol DismissDelegate: AnyObject {
  func dismissWithData(data: Any?)
}

class VodViewController: UIViewController, Orientable {
  lazy var scrollView: UIScrollView = {
    let view = UIScrollView()
    view.showsHorizontalScrollIndicator = false
    view.alwaysBounceHorizontal = false
    view.zoomScale = 1.0
    view.translatesAutoresizingMaskIntoConstraints = false
    view.backgroundColor = .background
    return view
  }()

  var fileLocalNameView = VodLocalFileNameView()

  var aiRestorationButton = MyLibraryAiButton(leftImage: #imageLiteral(resourceName: "ai_restoration.pdf"), title: "\(L.driving_recording_isp_mode_license_plate_txt.localized)")
  var aiPrivacyButton = MyLibraryAiButton(leftImage: #imageLiteral(resourceName: "ai_protection"), title: "\(L.ai_privacy_protection.localized)")
  let shareButton = MyLibraryAiButton( leftImage: #imageLiteral(resourceName: "share.pdf"), title: "\(L.dialog_share_title.localized)")
  let videoEditButton = MyLibraryAiButton( leftImage: #imageLiteral(resourceName: "video_trim"), title: "\(L.video_edit.localized.localized)")

  var playerContainerView: VodPlayerContainerView!
//  let descriptionContainerView = VodDescriptionContainView()
  let graphContainView = GSensorContainView()
  let graphLineView = UIView()
  //let mapView = MapContainView()
  let fileListView = FileListContainerView()

  let sheetMenuViewController = SheetViewController()

  var viewModel: VodViewModel?
  var streamViewModel: VodWifiViewModel?
  var vodLocalViewModel: VodLocalViewModel?

  weak var dismissDelegate: DismissDelegate?

  var isButtonAction = false
  var sx: CGFloat = 1
  var sy: CGFloat = 1
  var disposedBag = DisposeBag()

  var currentGSensorData: GSensorWifiVodModel?
  private let popupAiPirvacyStartPopupKey = "popupAiPirvacyStartPopupKey"
  private let popupAiLicenseStartPopupKey = "popupAiLicenseStartPopupKey"
  var highlightLineX: CGFloat = 0
  
  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
    self.playerContainerView.stop()
    self.playerContainerView.timer?.invalidate()
    self.playerContainerView.timer = nil
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    self.title = Current.mode == .wifi ? L.fileviewer_title.localized : L.my_library_title.localized
    navigationItem.backButtonTitle = ""
    self.view.backgroundColor = .background

    setComponent()
    setAutoLayout()

    // 채널 이미지 초기화
    playerContainerView.handlerView.updateChannelImage(channel: 1)

    bindViewModel(to: viewModel)
    bindViewmodel(to: streamViewModel)
    bindViewModel(to: vodLocalViewModel)
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)

    AppUtility.lockOrientation(.portrait)
    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      self.scrollView.updateContentSize()
    }
    addChildController(sheetMenuViewController)

    tabBarController?.toggleTabbar(isShow: false)
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    if vodLocalViewModel?.vodType == .snapShot {
      playerContainerView.handlerView.channelInfoImage.isHidden = true
    } else {
      if AppManager.shared.mode == .file {
        playerContainerView.handlerView.channelInfoImage.isHidden = false
        updateChannelImageFromFileName()
      } else {
        playerContainerView.handlerView.channelInfoImage.isHidden = true
      }
    }
  }

  override func viewDidDisappear(_ animated: Bool) {
    super.viewDidDisappear(animated)
    try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
    self.playerContainerView.stop()
    guard case .file = AppManager.shared.mode else {
//      self.playerContainerView.stop()
      self.playerContainerView.timer?.invalidate()
      self.playerContainerView.timer = nil
      return
    }
  }

  func setComponent() {
    self.view.addSubview(self.scrollView)

    let selectIndex = vodLocalViewModel?.fileModel.selectedIndex ?? 0
    let currentItem = vodLocalViewModel?.fileModel.items[selectIndex]
    let fileName = currentItem?.fileName ?? "none"
    fileLocalNameView.nameLabel.text = "\(fileName)"

    aiRestorationButton.isEnabled = false
    aiRestorationButton.addAction(UIAction { [weak self] _ in
      self?.didTapRestorationButton(item: currentItem)
    }, for: .touchUpInside)

    aiPrivacyButton.addAction(UIAction { [weak self] _ in
      self?.didTapPrivacyButton(item: currentItem)
      if !UserDefaults.standard.bool(forKey: self?.popupAiPirvacyStartPopupKey ?? "") {
        LKPopupView.popup.alert {[
          .subTitle(L.ai_privacy_popup.localized),
          .showCancel(true),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({})
          ]),
          .cancelAction([
            .text(L.close_popup.localized),
            .tapActionCallback({ [weak self] in
              UserDefaults.standard.set(true, forKey: self?.popupAiPirvacyStartPopupKey ?? "")
            })
          ])
        ]}
      }
    }, for: .touchUpInside)

    shareButton.addAction(UIAction { [weak self] _ in
      self?.didTapShareButton(item: currentItem)
    }, for: .touchUpInside)

    videoEditButton.addAction(UIAction { [weak self] _ in
      self?.didTapTrimButton(item: currentItem)
    }, for: .touchUpInside)


    let viewModel = SheetMenuViewModel()
    sheetMenuViewController.viewModel = viewModel

    if AppManager.shared.mode == .file {
      fileLocalNameView.tag = _tag_scrollView_size
      scrollView.addSubview(fileLocalNameView)
      playerContainerView.tag = _tag_scrollView_size
      scrollView.addSubview(playerContainerView)

      if vodLocalViewModel?.vodType == .snapShot {
        if fileName.contains("LB") || fileName.contains("LA") {
          scrollView.addSubviews([shareButton])
        } else {
          scrollView.addSubviews([aiRestorationButton, shareButton])
        }

        aiRestorationButton.isEnabled = true
        aiRestorationButton.tag = _tag_scrollView_size
        shareButton.tag = _tag_scrollView_size
        playerContainerView.screenShotView.isHidden = false
        playerContainerView.handlerView.playButton.isHidden = true
        playerContainerView.handlerView.backwardButton.isHidden = true
        playerContainerView.handlerView.forwardButton.isHidden = true
        playerContainerView.handlerView.nextButton.isHidden = false
        playerContainerView.handlerView.previousButton.isHidden = false
        playerContainerView.handlerView.channelInfoImage.isHidden = true
        playerContainerView.handlerView.controlBarView.durationLabel.isHidden = true
        playerContainerView.handlerView.controlBarView.slider.isHidden = true
      } else {
        scrollView.addSubviews([aiRestorationButton, aiPrivacyButton, shareButton, videoEditButton])
        playerContainerView.screenShotView.isHidden = true
        playerContainerView.handlerView.sizeButton.isHidden = false
        playerContainerView.handlerView.playButton.isHidden = false
        playerContainerView.handlerView.backwardButton.isHidden = false
        playerContainerView.handlerView.forwardButton.isHidden = false
        playerContainerView.handlerView.nextButton.isHidden = false
        playerContainerView.handlerView.previousButton.isHidden = false
        playerContainerView.handlerView.channelInfoImage.isHidden = false
        aiRestorationButton.tag = _tag_scrollView_size
        aiPrivacyButton.tag = _tag_scrollView_size
        shareButton.tag = _tag_scrollView_size
        videoEditButton.tag = _tag_scrollView_size
      }

      graphContainView.isHidden = true
      fileListView.isHidden = true
    } else {
      if streamViewModel?.vodType != .bookmark {
        graphContainView.tag = _tag_scrollView_size
        scrollView.addSubview(graphContainView)
        graphLineView.backgroundColor = .vueroidBlue
        graphContainView.chartView.addSubview(graphLineView)
        graphContainView.isHidden = false
      }

      playerContainerView.tag = _tag_scrollView_size
      scrollView.addSubview(playerContainerView)
      fileListView.zoomView.isToolTip = false
      fileListView.tag = _tag_scrollView_size
      scrollView.addSubview(fileListView)
      fileListView.isHidden = false
      playerContainerView.handlerView.isHidden = false
      playerContainerView.handlerView.channelInfoImage.isHidden = false
    }

//    if vodLocalViewModel?.vodType != .snapShot {
//      updateChannelImageFromFileName()
//    }
  }

  func setAutoLayout() {
    let height = playerContainerView.baseHeight

    self.scrollView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalToSuperview()
      make.left.right.equalTo(self.view)
      make.bottom.equalTo(self.view.safeAreaLayoutGuide)
    }

    if AppManager.shared.mode == .file {
      fileLocalNameView.pin.height(34).top().horizontally().activate()
      playerContainerView.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.top.equalTo(fileLocalNameView.snp.bottom)
        make.left.right.equalTo(self.view)
        make.height.equalTo(height)
      }

      if vodLocalViewModel?.vodType == .snapShot {
        let selectIndex = vodLocalViewModel?.fileModel.selectedIndex ?? 0
        let currentItem = vodLocalViewModel?.fileModel.items[selectIndex]
        let fileName = currentItem?.fileName ?? "none"

        if fileName.contains("LB") || fileName.contains("LA") {
          shareButton.snp.makeConstraints { [weak self] make in
            guard let self = self else { return }
            make.top.equalTo(playerContainerView.snp.bottom).offset(20)
            make.height.equalTo(65)
            make.left.equalTo(self.view).offset(15)
            make.right.equalTo(self.view).offset(-15)
            make.centerX.equalTo(self.view)
          }
        } else {
          aiRestorationButton.snp.makeConstraints { [weak self] make in
            guard let self = self else { return }
            make.top.equalTo(playerContainerView.snp.bottom).offset(20)
            make.height.equalTo(65)
            make.left.equalTo(self.view).offset(15)
            make.right.equalTo(self.view).offset(-15)
            make.centerX.equalTo(self.view)
          }

          shareButton.snp.makeConstraints { [weak self] make in
            guard let self = self else { return }
            make.top.equalTo(aiRestorationButton.snp.bottom).offset(20)
            make.height.equalTo(65)
            make.left.equalTo(self.view).offset(15)
            make.right.equalTo(self.view).offset(-15)
            make.centerX.equalTo(self.view)
          }
        }
      } else {
        aiRestorationButton.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(playerContainerView.snp.bottom).offset(20)
          make.height.equalTo(65)
          make.left.equalTo(self.view).offset(15)
          make.right.equalTo(self.view).offset(-15)
          make.centerX.equalTo(self.view)
        }

        aiPrivacyButton.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(aiRestorationButton.snp.bottom).offset(20)
          make.height.equalTo(65)
          make.left.equalTo(self.view).offset(15)
          make.right.equalTo(self.view).offset(-15)
          make.centerX.equalTo(self.view)
        }

        shareButton.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(aiPrivacyButton.snp.bottom).offset(20)
          make.height.equalTo(65)
          make.left.equalTo(self.view).offset(15)
          make.right.equalTo(self.view).offset(-15)
          make.centerX.equalTo(self.view)
        }

        videoEditButton.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(shareButton.snp.bottom).offset(20)
          make.height.equalTo(65)
          make.left.equalTo(self.view).offset(15)
          make.right.equalTo(self.view).offset(-15)
          make.centerX.equalTo(self.view)
        }
      }
    } else {
      playerContainerView.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.top.equalTo(self.scrollView)
        make.left.right.equalTo(self.view)
        make.height.equalTo(height)
      }

      if streamViewModel?.vodType != .bookmark {
        graphContainView.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(self.playerContainerView.snp.bottom)
          make.left.equalTo(self.view).offset(0)
          make.right.equalTo(self.view).offset(0)
          make.height.equalTo(69.rv)
        }

        fileListView.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(self.graphContainView.snp.bottom).offset(3)
          make.left.right.equalTo(self.view)
          make.bottom.equalTo(self.scrollView.snp.bottom)
          make.height.equalTo(400)
        }

      } else {
        fileListView.snp.makeConstraints { [weak self] make in
          guard let self = self else { return }
          make.top.equalTo(self.playerContainerView.snp.bottom).offset(3)
          make.left.right.equalTo(self.view)
          make.bottom.equalTo(self.scrollView.snp.bottom)
          make.height.equalTo(400)
        }
      }

    }
  }

  // MARK: - VodViewModel
  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? VodViewModel else { return }

    let orientationEvent = playerContainerView.handlerView.sizeButton.rx.tap.asObservable()
    let remianValue = playerContainerView.playerView.rxRemainingTime.asObserver()
    let progressValue = playerContainerView.playerView.rxTime.asObserver()

    let input = VodViewModel.Input(
      orientainTouchEvent: orientationEvent,
      progressTime: progressValue,
      reamingTime: remianValue
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsLandscape
      .asObservable()
      .subscribe(onNext: { [weak self] isValue in
        guard let self else { return }

        self.isButtonAction = true
        AppUtility.lockOrientation(isValue ? .landscapeRight : .portrait)
        self.setOrientaion(isLandscape: isValue)
        self.setOrientaionViews(isLandscape: isValue)
      })
      .disposed(by: self.disposedBag)

    output.rxDuration
      .asObservable()
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        let duration = self.playerContainerView.playerView.mediaPlayer.media?.length.intValue.asInt ?? 0
        self.playerContainerView.handlerView.controlBarView.duration = duration
      })
      .disposed(by: self.disposedBag)

    output.rxTime
      .asObservable()
      .subscribe(onNext: { [weak self] time in
        guard let self else { return }
        playerContainerView.playerView.closeIndicator()
        playerContainerView.handlerView.controlBarView.progressTime = time
      })
      .disposed(by: self.disposedBag)

    output.rxToastSnapShot
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: L.viewer_snapshot_save.localized)
      })
      .disposed(by: disposedBag)

    playerContainerView.handlerView.controlBarView
      .hReversButton
      .rx.tap
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        self.sx = self.sx * -1
        playerContainerView.playerView.rotation(sx: self.sx, sy: self.sy)
        playerContainerView.screenShotView.rotation(sx: self.sx, sy: self.sy)
        fLogger.info("Vod hReverseButton Tap")
      })
      .disposed(by: disposedBag)

    playerContainerView.handlerView.controlBarView
      .vReversButton
      .rx.tap
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        self.sy *= -1
        playerContainerView.playerView.rotation(sx: self.sx, sy: self.sy)
        playerContainerView.screenShotView.rotation(sx: self.sx, sy: self.sy)
        fLogger.info("Vod vReverseButton Tap")
      })
      .disposed(by: disposedBag)
  }
}


// MARK: - Hub Button Action
extension VodViewController {
  func didTapRestorationButton(item: FileListCellModel?) {
    guard let cellModel = item  else { return }
    playerContainerView.playerView.pause()
    AppUtility.lockOrientation(.landscapeRight)

    if cellModel.type == .screenshot {
      viewModel?.coordinator?.navigate(to: .imageCrop(image: nil, model: cellModel, type: .image))
    } else {
      LKPopupView.popup.loading()
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        guard let pauseTime = self.playerContainerView.playerView.pauseTime,
              let filePath = cellModel.filePath,
              let vlcSnapshotImage = AVFoundationUtils.generateThumbnail(path: filePath, vlcTime: pauseTime)
        else {
          LKPopupView.popup.hideLoading()
          return
        }

        mLogger.info("vlcSnapshotImage success")
        self.viewModel?.coordinator?.navigate(to: .imageCrop(image: vlcSnapshotImage, model: cellModel, type: .video))
        LKPopupView.popup.hideLoading()
      }
    }
  }

  func didTapPrivacyButton(item: FileListCellModel?) {
    guard let cellModel = item else { return }
    playerContainerView.playerView.mediaPlayer.stop()
    viewModel?.coordinator?.navigate(to: .edit(model: cellModel, editType: .privacyProtection))
  }

  func didTapTrimButton(item: FileListCellModel?) {
    guard let cellModel = item else { return }
    playerContainerView.playerView.mediaPlayer.stop()
    viewModel?.coordinator?.navigate(to: .edit(model: cellModel, editType: .trim))
  }

  func didTapShareButton(item: FileListCellModel?) {
    guard let url = item?.filePath else { return }
    let player = self.playerContainerView.playerView
    player.pause()
    let activityViewController = UIActivityViewController(activityItems: [url], applicationActivities: nil)
    activityViewController.popoverPresentationController?.sourceView = self.view  // so that iPads won't crash
    activityViewController.excludedActivityTypes = [.message]
    self.present(activityViewController, animated: true, completion: nil)

    activityViewController.completionWithItemsHandler = {
      (activityType, completed, returnedItems, error) in
      self.navigationController?.popViewController(animated: true)
    }
  }
}


// MARK: - ImageCropViewControllerDelegate
extension VodViewController: ImageCropViewControllerDelegate {
  func imageCropViewControllerSuccess(
    _ controller: UIViewController,
    didFinishCroppingImage croppedImage: UIImage
  ) {
    guard let screenshotPath = UrlList.screenshotPath(),
          let cropController = controller as? ImageCropViewController,
          let fileName = cropController.originalFileName,
          let origImage = cropController.image,
          let imageData = croppedImage.jpegData(compressionQuality: 0.8)
    else { return }

    mLogger.info("after crop: \(cropController.category.rawValue)")
    
    do {
      let origImageData = origImage.jpegData(compressionQuality: 0.8)
      if cropController.category == .video {
        let origCaptureName = try HubFileNameGenerator.generateLicensePlateFileName(baseFileName: fileName, stage: .original)
        FileManager.writeToFile(data: origImageData, atPath: screenshotPath, name: origCaptureName)
      }
      let saveName = try HubFileNameGenerator.generateLicensePlateFileName(baseFileName: fileName, stage: .beforeRestoration)
      cropController.afterCropfileName = saveName
      FileManager.writeToFile(data: imageData, atPath: screenshotPath, name: saveName)
    } catch {
      mLogger.error("Error generating AI service file names: \(error)")
    }
    
  }

  func imageCropViewControllerDidCancel(_ controller: UIViewController) {
    AppUtility.lockOrientation(.portrait)
    let selectIndex = vodLocalViewModel?.fileModel.selectedIndex ?? 0
    let currentItem = vodLocalViewModel?.fileModel.items[selectIndex]
    let fileName = currentItem?.fileName ?? "none"
    if fileName.contains("mp4") {
      self.playerContainerView.playerView.resume()
    }
  }
}

extension VodViewController {
  func setOrientaionViews(isLandscape: Bool) {
    self.navigationController?.isNavigationBarHidden = isLandscape
    self.tabBarController?.toggleTabbar(isShow: false)
    playerContainerView.handlerView.updateLayout(isLandscape)

    // aiRestorationButton이 눌렸을 때는 controlBarView를 숨김
    if isButtonAction {
      playerContainerView.handlerView.controlBarView.isHidden = true
    }

    self.scrollView.isScrollEnabled = !isLandscape
    if AppManager.shared.mode != .file {
      graphContainView.isHidden = isLandscape
      fileListView.isHidden = isLandscape
    }

    scrollView.snp.updateConstraints { [weak self] make in
      guard let self = self else { return }
      make.bottom.equalTo(self.view.safeAreaLayoutGuide).offset(isLandscape ? 34 : 0)
    }

    let height = isLandscape ? playerContainerView.baseWidth : playerContainerView.baseHeight

    if isLandscape == true {
      playerContainerView.snp.remakeConstraints { [weak self] make in
        guard let self = self else { return }
        make.top.equalTo(self.view)
        make.left.right.equalTo(self.view)
        make.bottom.equalTo(self.view)
      }
    } else {
      playerContainerView.snp.remakeConstraints { [weak self] make in
        guard let self = self else { return }
        make.top.equalTo(self.scrollView)
        make.left.right.equalTo(self.view)
        make.height.equalTo(height)
      }
    }
    playerContainerView.setOrientationViews(isLandscape: isLandscape)
  }

  override func viewWillTransition(
    to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator
  ) {
    if isButtonAction == true {
      isButtonAction = false
      return
    }

    let orientation = UIDevice.current.orientation
    switch orientation {
    case .unknown, .portrait, .portraitUpsideDown, .faceUp, .faceDown:
      setOrientaionViews(isLandscape: false)
      self.viewModel?.isSelected = false
      break
    case .landscapeLeft, .landscapeRight:
      setOrientaionViews(isLandscape: true)
      self.viewModel?.isSelected = true
      break
    default:
      break
    }
  }

  func highlightedLine(time: CGFloat) {
    let fullTime = CGFloat(playerContainerView.playerView.mediaPlayer.media?.length.intValue ?? 20000)
    if fullTime == 0 { return }
    let fullLen = self.graphContainView.chartView.contentRect.width
    var x = self.graphContainView.chartView.contentRect.origin.x + (time / fullTime * fullLen)
    let y = self.graphContainView.chartView.contentRect.origin.y
    
    if x < highlightLineX {
      // 현재 시간이 이전보다 짧으면 이전 시간을 유지
      vLogger.debug("x: \(x), highlightLineX: \(self.highlightLineX)")
      x = highlightLineX
    } else {
      highlightLineX = x
    }

    let width = 1.0
    let height = self.graphContainView.chartView.contentRect.height
    UIView.animate(
      withDuration: 0.3,
      animations: { [weak self] in
        guard let self else { return }
        self.graphLineView.frame = CGRect(x: x, y: y, width: width, height: height)
        self.graphLineView.layoutIfNeeded()
      })

    // G-sensor 데이터를 찾아 XYZ 값 업데이트
    if let gsensorData = currentGSensorData {
      if fullTime > 0 {
        let timeRatio = time / fullTime
        let dataIndex = Int(timeRatio * CGFloat(gsensorData.gSensorData.count))
        if dataIndex < gsensorData.gSensorData.count && dataIndex >= 0 {
          let data = gsensorData.gSensorData[dataIndex]
          let gsensorData = Gsensor.GsensorData(x: data.x, y: data.y, z: data.z)
          self.graphContainView.setDataLabel(data: gsensorData)
        }
      }
    }
  }

  func updateChannelImageFromFileName() {
    guard let fileName = fileLocalNameView.nameLabel.text else { return }

    if let channelInfo = extractChannelInfo(from: fileName) {
      switch channelInfo {
      case "F":
        playerContainerView.handlerView.updateChannelImage(channel: 1) // 전방 카메라
      case "R":
        playerContainerView.handlerView.updateChannelImage(channel: 2) // 후방 카메라
      case "I":
        playerContainerView.handlerView.updateChannelImage(channel: 3) // 실내 카메라
      default:
        playerContainerView.handlerView.updateChannelImage(channel: 1) // 기본값
      }
    }
  }

  private func extractChannelInfo(from fileName: String) -> String? {
    let nameWithoutExtension = fileName.split(separator: ".").first ?? ""
    let components = nameWithoutExtension.split(separator: "_")

    if components.count >= 3 {
        let channelComponent = components[components.count - 2]
        if channelComponent == "R" || channelComponent == "F" || channelComponent == "I" {
            return String(channelComponent)
        }
    }
    return nil
  }
}
