//
//  VodStreamViewModel.swift
//  Hub
//
//  Created by ncn on 2023/03/03.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

/* Stream, player */
class VodWifiViewModel: BaseViewModel {
  let streamUseCase: StreamUseCase
  let fileUseCase: FileCommandUseCase
  weak var coordinator: VodCoordinator?

  let vodType: PlayerViewType
  var vodStreamModel: VodStreamModel?
  var selectedFileCellModel: FileListCellModel?
  var selectedIndex = 0
  var channelPausedTime: Int32 = 0
  var selectedVodModel: StartRecordVodModel?
  var isClose = false
  var vlcPlayerView: VLCPlayerView?
  
  struct State {
    let currentChannel = BehaviorRelay<Int>(value: 1)
    let isShowChannel = BehaviorRelay<Bool>(value: false)
    let isForward = BehaviorRelay<Bool>(value: false)
    let isBackward = BehaviorRelay<Bool>(value: false)
    let isChannelChangedSeek = BehaviorRelay<Bool>(value: false)
  }

  struct Input {
    let willAppearEvent: ControlEvent<Bool>
    let willDisappearEvent: ControlEvent<Bool>
//    let backButtonEvent: Observable<Void>
    let playButtonEvent: Observable<Bool>
    let channelButtonEvent: Observable<Void>
    let changeChannelEvent: Observable<Int>

    let snapShotButtonEvent: Observable<UIImage?>

    let forwardEvent: Observable<Void>
    let backwardEvent: Observable<Void>
    let nextEvent: Observable<Void>
    let previousEvent: Observable<Void>
    let playerState: Observable<RTSPPlayerState>
    let playerTime: Observable<Int>
    let playerRemainTime: Observable<Int>

//    let editMenuButtonEvent: Observable<(FileStorageType, FileActionType)>
    let downloadButtonEvent: Observable<Void>
    let deleteButtonEvent: Observable<Void>
    let fileSelectedEvent: Observable<FileListCellModel>
  }

  struct Output {
    let rxStreamUrl = PublishSubject<String>()
    let rxStreamUrlWithChange = PublishSubject<Void>()
    let rxCurrentItem = PublishSubject<FileListCellModel>()
    let rxFileImage = PublishSubject<UIImage>()
    let rxFilelistSetting = PublishSubject<[FileListSectionModel]>()
    let rxChangeSelectIndex = PublishSubject<Int>()

    let rxIsPause = PublishRelay<Bool>()  // 1: pause, 0: play
    let rxIsForward = PublishSubject<Bool>()  // 1: forward, 0: backward
    let rxIsNext = PublishSubject<Bool>()  // 1: next, 0: previous

    let rxIsStart = PublishSubject<Bool>()
    let rxIsStop = PublishSubject<Bool>()
    let rxIsError = PublishSubject<Bool>()  // state
    let rxPlayerState = PublishSubject<RTSPPlayerState>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxIsPlayerIndicator = PublishSubject<Bool>()
    let rxFileUrl = PublishSubject<URL>()
    let rxError = PublishSubject<Error>()  // message
    let rxIsCloseStream = PublishSubject<Bool>()

    let rxDeletePopup = PublishSubject<FileListCellModel>()
    let rxGsensorStream = PublishSubject<GSensorWifiVodModel?>()
    let rxChangeTrack = PublishSubject<Bool>()
    let rxToastSnapShot = PublishSubject<Void>()
  }

  init(coordinator: VodCoordinator, streamUseCase: StreamUseCase, fileUseCase: FileCommandUseCase, vodType: PlayerViewType) {
    vLogger.info("VodWifiViewModel init")
    self.coordinator = coordinator
    self.streamUseCase = streamUseCase
    self.fileUseCase = fileUseCase
    self.vodType = vodType
  }

  deinit {
    vLogger.info("deinit \(Self.self)")
  }
  
  func bind(input: Input, disposedBag: DisposeBag) -> (Output, State) {
    let output = Output()
    let state = State()
    /* Life cycle */
    input.willAppearEvent
      .take(1)
      .do(onNext: { [weak self] _ in
        guard let self else { return }
        guard let vodStreamModel = self.vodStreamModel else { return }
        let model = FileListSectionModel(
          items: vodStreamModel.items,
          header: vodStreamModel.items.first!.date.toString(format: "yyyy / MM / dd"))
        output.rxFilelistSetting.onNext([model])
      })
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.openStreaming(output: output)
      })
      .disposed(by: disposedBag)

    input.willDisappearEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
//        self.stopStreaming(output: output)
        closeStreaming(output: output)
        isClose = true
      })
      .disposed(by: disposedBag)

//    input.backButtonEvent
//      .subscribe(onNext: { [weak self] _ in
//        guard let self = self else { return }
//        self.closeStreaming(output: output)
//        self.isClose = true
//      })
//      .disposed(by: disposedBag)

    /* Button event */
    input.playButtonEvent
      .map { !$0 }
      .bind(to: output.rxIsPause)
      .disposed(by: disposedBag)

    input.channelButtonEvent
      .map { _ in true }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    let channelChanged = input.changeChannelEvent
      .map { $0 + 1 }
      .do(onNext: { _ in
        output.rxIsIndicator.onNext(true)
      })
      .flatMap { [weak self] channel -> Observable<Result<Int, Error>> in
        guard let self = self else { return .just(.failure(CustomError.SelfIsNil)) }
          // 현재 선택된 파일 모델 가져오기
          guard let vodModel = self.vodStreamModel,
                let _ = self.selectedFileCellModel else {
              return .just(.failure(NCError(title: "Model", description: "No selected model", code: 999)))
          }
          
          // 채널 정보 저장
          return self.rxStopStreaming()
              .flatMap { _ -> Observable<Result<Int, Error>> in
                  // 채널 변경 후 스트리밍 다시 시작
                  self.selectedIndex = vodModel.selectedIndex
                  self.openStreaming(output: output, channel: channel, isSwitch: true)
                  return .just(.success(channel))
              }
              .catch { error -> Observable<Result<Int, Error>> in
                  vLogger.error("Channel change error: \(error.localizedDescription)")
                  return .just(.failure(error))
              }
      }
      .filter { result in
//          output.rxIsIndicator.onNext(false)
          switch result {
          case .success(_):
              return true
          case .failure(let error):
              vLogger.error("connectError : \(error.localizedDescription)")
              return false
          }
      }
      .share()

    channelChanged
      .compactMap { try? $0.get() }
      .bind(to: state.currentChannel)
      .disposed(by: disposedBag)

    channelChanged
      .map { _ in false }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    input.forwardEvent
      .subscribe(onNext: { _ in
        output.rxIsForward.onNext(true)
      })
      .disposed(by: disposedBag)

    input.backwardEvent
      .subscribe(onNext: { _ in
        output.rxIsForward.onNext(false)
      })
      .disposed(by: disposedBag)

    input.nextEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        // 소팅 이슈로
        self.previousPlayItem(output: output, state: state)
      })
      .disposed(by: disposedBag)

    input.previousEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        self.nextPlayItem(output: output, state: state)
      })
      .disposed(by: disposedBag)

    input.downloadButtonEvent
      .subscribe(onNext: { [weak self] in
        guard let self, let cellModel = self.selectedFileCellModel else { return }
        output.rxIsPause.accept(true)
        vLogger.info("downloadButtonEvent: file: \(cellModel.fileName)")
        self.coordinator?.navigate(to: .download(models: [cellModel], type: .dashcam))
      })
      .disposed(by: disposedBag)

    input.deleteButtonEvent
      .subscribe(onNext: { [weak self] in
        guard let self, let cellModel = self.selectedFileCellModel else { return }
        output.rxIsPause.accept(true)
        if cellModel.fileName.contains("_L.") {
          LKPopupView.popup.toast(hit: L.delete_protect_file_txt.localized)
        } else {
          output.rxDeletePopup.onNext(cellModel)
        }
      })
      .disposed(by: disposedBag)

    input.snapShotButtonEvent
      .withLatestFrom(state.currentChannel, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] image, channel in
        guard let self,
          let index = self.vodStreamModel?.selectedIndex,
          let cellModel = self.vodStreamModel?.items[index]
        else { return }

        if let playerView = vlcPlayerView {
          do {
            let fileName = try HubFileNameGenerator.generateImageCaptureFileName(baseFileName: cellModel.fileName)
            guard let path = UrlList.screenshotPath() else {
              aLogger.error("Fail to get path.")
              return
            }
            let uniquePath = path.appendingPathComponent(fileName)
            vLogger.info("VodCapture name: \(uniquePath.path)")
            playerView.mediaPlayer.saveVideoSnapshot(at: uniquePath.path, withWidth: 0, andHeight: 0)
            MainAsync(after: 0.5) {
              self.saveSnapshotToPhotoApp(fileurl: uniquePath)
            }
            output.rxToastSnapShot.onNext(())
          } catch {
            vLogger.error("writeSnapshnot exception")
          }
        }
      })
      .disposed(by: disposedBag)

    /* State */
    input.playerState
      .bind(to: output.rxPlayerState)
      .disposed(by: disposedBag)
    
    input.playerState
      .withLatestFrom(input.playerRemainTime, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] playerState, remainTime in
        guard let self = self else { return }
//        debugPrint("##@@ playerState: \(playerState), remainTime: \(remainTime)")
        if remainTime < 1500 {
          if (playerState == .ended) && self.isClose != true {
            self.previousPlayItem(output: output, state: state)
          }
        }
      })
      .disposed(by: disposedBag)

    input.fileSelectedEvent
      .do(onNext: { _ in output.rxIsIndicator.onNext(true) })
      .flatMap { [weak self] cellModel -> Observable<FileListCellModel?> in
          guard let self = self else { return .just(nil) }
          // Skip stream stopping for bookmark type
          if self.vodType == .bookmark {
              return .just(cellModel)
          } else {
              return self.rxStopStreaming(cellModel)
          }
      }
      .subscribe(onNext: { [weak self] cellModel in
        guard let self = self,
          let vodModel = self.vodStreamModel,
          let cellModel = cellModel,
          let index: Int = vodModel.items.firstIndex(of: cellModel)
        else { return }
        self.selectedIndex = index
        self.vodStreamModel?.selectedIndex = index
        self.openStreaming(output: output, channel: state.currentChannel.value)
      })
      .disposed(by: disposedBag)
    return (output, state)
  }
  
  func saveSnapshotToPhotoApp(fileurl: URL) {
    let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
    vLogger.info("saved photo app modelName: \(modelName)")
    guard let image = vlcPlayerView?.mediaPlayer.lastSnapshot else {
      vLogger.warning("vlcPlayer lastSnapshot empty")
      return
    }
    PhotoUtils.savePhoto(image: image, toAlbum: modelName, completion: { error in
      guard let error = error else {
        vLogger.info("image photoapp saved: \(fileurl.absoluteString)")
        return
      }
      vLogger.error("error: \(error.localizedDescription)")
    })
  }
}

extension VodWifiViewModel {
  func openStreaming(output: Output, channel: Int = 1, isSwitch: Bool = false) {
    guard let vodModel = vodStreamModel else {
      return
    }
    let item = vodModel.items[selectedIndex]
    vLogger.info("openStreaming: file: \(item.fileName), channel: \(channel)")
    selectedFileCellModel = item
    
    let fileExtension = item.fileName.fileExtension.lowercased()
    if (fileExtension == "png") || (fileExtension == "jpg") || (fileExtension == "jpeg") {
      guard let image = item.thumbimage else { return }
      vLogger.info("open BookmarkTab: image: \(item.fileName)")
      output.rxFileImage.onNext(image)
      return
    }
    
    if let url = item.filePath {
      output.rxFileUrl.onNext(url)
      return
    }
    output.rxIsIndicator.onNext(true)
    output.rxIsPlayerIndicator.onNext(true)
    output.rxCurrentItem.onNext(item)

    // 채널에 맞게 파일명 변경
    var changedChannel = channel
    if item.channelbits.attachChannel == .FI, changedChannel == 2 {
      changedChannel += 1
    }
    let fileName = item.fileName.toChannelFileName(channel: changedChannel)
    vLogger.info("Using filename for channel \(changedChannel): \(fileName)")
    selectedFileCellModel?.fileName = fileName
    
    let header = HeaderModel(cmd: Command.startrecordvod)
    let model = StartRecordVodModel(
      path: item.path ?? "/mnt/mmc/INF",
      name: fileName,
      port: 9010,
      ipaddress: "*************",
      video: 1, //channel,
      audio: 1
    )
    self.selectedVodModel = model
    let message = VodStart.Send(header: header, startrecordvod: model)
    streamUseCase.send(to: message) { [weak self] response, error in
      if let e = error {
        output.rxError.onNext(e)
//        output.rxIsIndicator.onNext(false)
        output.rxIsPlayerIndicator.onNext(false)
      } else if let res = response {
        output.rxGsensorStream.onNext(res.startrecordvod)
        DispatchQueue.main.asyncAfter(
          deadline: .now() + 1,
          execute: {
            if isSwitch {
              output.rxStreamUrlWithChange.onNext(())
            } else {
              let string = "rtsp://*************:9010/vod"
              output.rxStreamUrl.onNext(string)
            }
//            output.rxIsIndicator.onNext(false)
            output.rxIsPlayerIndicator.onNext(false)
          }
        )
        output.rxChangeSelectIndex.onNext(self?.selectedIndex ?? 0)
      }
    }
  }

  func stopAndStart(params: (output: Output, channel: Int)) -> Observable<Result<Int, Error>> {
    guard let selectedVodModel = self.selectedVodModel else {
      return Observable.just(
        .failure(NCError(title: "vodModel", description: "no selected model", code: 999))
      )
    }

    params.output.rxIsPause.accept(true)
    params.output.rxIsStop.onNext(true)
    params.output.rxIsIndicator.onNext(true)
    params.output.rxIsPlayerIndicator.onNext(true)

    let startHeader = HeaderModel(cmd: Command.startrecordvod)

    let videoName = selectedVodModel.name.toChannelFileName(channel: params.channel)
    vLogger.info("#@ vod play Name: \(videoName) channel: \(1)")

    let startModel = StartRecordVodModel(
      path: selectedVodModel.path,
      name: videoName,
      port: 9010,
      ipaddress: "*************",
      video: 1,
      audio: 1
    )

    self.selectedVodModel = startModel
    let startMessage = VodStart.Send(header: startHeader, startrecordvod: startModel)

    let stopHeader = HeaderModel(cmd: Command.stoprecordvod)
    let stopMessage = VodStop.Send(header: stopHeader)

    return Observable.create { [weak self] emitter in
      self?.streamUseCase.send(to: stopMessage) { response, error in
        vLogger.info("stopAndStart, VodStop")
        self?.streamUseCase.send(to: startMessage) { response, error in
          vLogger.info("stopAndStart VodStart")
          if let e = error {
            params.output.rxError.onNext(e)
            emitter.onNext(.failure(e))
          } else if let res = response {
            params.output.rxGsensorStream.onNext(res.startrecordvod)
            params.output.rxStreamUrlWithChange.onNext(())
            params.output.rxIsIndicator.onNext(false)
            params.output.rxIsPlayerIndicator.onNext(false)
            emitter.onNext(.success(params.channel))
          }
        }
      }
      return Disposables.create()
    }
  }

  func stopStreaming(output: Output) {
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    streamUseCase.send(to: message) { response, error in
      vLogger.info("stopStreaming")
    }
  }

  func rxStopStreaming(_ cellModel: FileListCellModel? = nil) -> Observable<FileListCellModel?> {
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    // output.rxIsStop.onNext(true) // This should be handled by the caller if needed for UI state
    return Observable.create { [weak self] emitter in
      self?.streamUseCase.send(to: message) { response, error in
        if let error = error {
          vLogger.error("rxStopStreaming failed: \(error.localizedDescription)")
          emitter.onError(error)
        } else {
          vLogger.info("rxStopStreaming successful: cellModel: \(cellModel?.fileName ?? "N/A")")
          emitter.onNext(cellModel) // Pass cellModel through, or nil if not provided
          emitter.onCompleted()
        }
      }
      return Disposables.create()
    }
  }

  func closeStreaming(output: Output) {
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    streamUseCase.send(to: message) { response, error in
      vLogger.info("\(String(describing: response)) error: \(String(describing: error))")
      output.rxIsCloseStream.onNext(true)
    }
  }

  func nextPlayItem(output: Output, state: State) {
    guard let vodModel = vodStreamModel else {
      return
    }
    selectedIndex += 1
    if vodModel.items.count <= selectedIndex {
      selectedIndex = 0
    }
    #if false
    if vodModel.items.count == 1 {
      return
    }
    #endif

    output.rxIsPlayerIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    streamUseCase.send(to: message) { [weak self] response, error in
      guard let self else { return }
      if let e = error {
        output.rxIsPlayerIndicator.onNext(false)
        output.rxError.onNext(e)
      } else {
        self.openStreaming(output: output, channel: state.currentChannel.value)
      }
    }
  }

  func selectPlayItem(output: Output, index: Int?) {
    guard let index = index else { return }
    selectedIndex = index
    output.rxIsPlayerIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    streamUseCase.send(to: message) { [weak self] response, error in
      guard let self = self else { return }
      if let e = error {
        output.rxIsPlayerIndicator.onNext(false)
        output.rxError.onNext(e)
      } else {
        self.openStreaming(output: output)
      }
    }
  }

  func previousPlayItem(output: Output, state: State) {
    guard let vodModel = vodStreamModel else {
      return
    }
    selectedIndex -= 1
    if 0 > selectedIndex {
      selectedIndex = vodModel.items.count - 1
    }
    self.vodStreamModel?.selectedIndex = selectedIndex
    vLogger.info("##@@ selectedIndex: \(self.selectedIndex) streamModel.selectIndex:\(self.vodStreamModel?.selectedIndex ?? -1)")
#if false
    if vodModel.items.count == 1 {
      return
    }
#endif
    output.rxIsPlayerIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.stoprecordvod)
    let message = VodStop.Send(header: header)
    streamUseCase.send(to: message) { [weak self] response, error in
      guard let self = self else { return }
      if let e = error {
        output.rxIsPlayerIndicator.onNext(false)
        output.rxError.onNext(e)
      } else {
        
        self.openStreaming(output: output, channel: state.currentChannel.value)
      }

    }
  }

  func deleteFile(model: FileListCellModel, output: Output, completion: @escaping () -> Void) {
    output.rxIsIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.deletefile)
    let command = DeleteFileModel.Send.DeleteFile(
      path: model.path ?? "/mnt/mmc/INF", name: model.fileName)
    let send = DeleteFileModel.Send(header: header, deletefile: command)

    fileUseCase.send(to: send) { [weak self] response, error in
      output.rxIsIndicator.onNext(false)
      guard self != nil else { return }
      completion()
    }
  }
}


// MARK: Navigation
extension VodWifiViewModel {
  func dismiss() {
    coordinator?.navigate(to: .vodDidFinish)
  }
}
