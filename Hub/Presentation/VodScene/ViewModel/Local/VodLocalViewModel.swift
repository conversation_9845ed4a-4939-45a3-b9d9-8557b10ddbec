//
//  VodLocalViewModel.swift
//  Hub
//
//  Created by ncn on 2023/05/24.
//

import Foundation
import RxCocoa
import RxSwift
import RxRelay
import UIKit

class VodLocalViewModel: BaseViewModel {
  private weak var coordinator: VodCoordinator?
  let vodType: PlayerViewType
  var fileModel: VodLocalModel

  var isClose = false
  //  var selectedIndex = 0
  var selectedFileCellModel: FileListCellModel?

  struct State {
    let nowChannel = BehaviorSubject<Int>(value: 0)
    let isShowChannel = BehaviorRelay<Bool>(value: false)
  }

  struct Input {
    let didAppearEvent: ControlEvent<Bool>
    let playButtonEvent: Observable<Bool>
    let forwardEvent: Observable<Void>
    let backwardEvent: Observable<Void>
    let nextEvent: Observable<Void>
    let previousEvent: Observable<Void>
//    let backButtonEvent: Observable<Void>
    let channelButtonEvent: Observable<Void>
    let changeChannelEvent: Observable<Int>
    let snapShotButtonEvent: Observable<UIImage?>
    let deleteButtonEvent: Observable<Void>
    let playerState: Observable<RTSPPlayerState>
    let playTime: Observable<Int>

//    let editMenuButtonEvent: Observable<(FileStorageType, FileActionType)>
    let fileSelectedEvent: Observable<FileListCellModel>
  }

  struct Output {
    let rxVodType = PublishSubject<PlayerViewType>()
    let rxFileSetting = PublishSubject<FileListCellModel>()
    let rxFilelistSetting = BehaviorSubject<[FileListSectionModel]>(value: [])
    let rxChangeSelectIndex = PublishSubject<Int>()

    let rxPlayerState = PublishSubject<RTSPPlayerState>()
    let rxPlayerStop = PublishRelay<Void>()
    let rxIsPause = PublishRelay<Bool>()  // 1: pause, 0: play
    let rxIsForward = PublishSubject<Bool>()  // 1: forward, 0: backward
    let rxIsNext = PublishSubject<Bool>()  // 1: next, 0: previous
    let rxIsStart = PublishSubject<Bool>()
    let rxIsStop = PublishSubject<Bool>()
    let rxIsError = PublishSubject<Bool>()  // state

    let rxChangeChannel = PublishSubject<Int>()  // state

    let rxFileUrl = PublishSubject<URL>()
    let rxFileImage = PublishSubject<UIImage>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxError = PublishSubject<Error>()  // message
    let rxIsCloseStream = PublishSubject<Bool>()
    let rxDeleteLocalPopup = PublishSubject<FileListCellModel>()

    let rxShare = PublishSubject<URL>()
    let rxToastSnapShot = PublishSubject<Void>()
  }

  init(coordinator: VodCoordinator, fileModel: VodLocalModel, vodType: PlayerViewType) {
    self.coordinator = coordinator
    self.fileModel = fileModel
    self.vodType = vodType
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func bind(input: Input, disposedBag: DisposeBag) -> (Output, State) {
    let state = State()
    let output = Output()

    input.didAppearEvent
      .compactMap { [weak self] _ in self?.vodType }
      .bind(to: output.rxVodType)
      .disposed(by: disposedBag)

    input.didAppearEvent
      .take(1)
      .do(onNext: { [weak self] _ in
        guard let self = self else { return }
        let model = FileListSectionModel(
          items: self.fileModel.items,
          header: self.fileModel.items.first!.date.toString(format: "yyyy / MM / dd"))
        output.rxFilelistSetting.onNext([model])
      })
      .do(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.openFile(output: output)
      })
      .compactMap { [weak self] _ in
        return self?.fileModel.items[self?.fileModel.selectedIndex ?? 0]
      }
      .bind(to: output.rxFileSetting)
      .disposed(by: disposedBag)

    /* Button event */
    input.playButtonEvent
      .map { !$0 }
      .bind(to: output.rxIsPause)
      .disposed(by: disposedBag)

    input.forwardEvent
      .subscribe(onNext: { event in
        output.rxIsForward.onNext(true)
      })
      .disposed(by: disposedBag)

    input.backwardEvent
      .subscribe(onNext: { event in
        output.rxIsForward.onNext(false)
      })
      .disposed(by: disposedBag)

    input.nextEvent
      .subscribe(onNext: { [weak self] event in
        guard let self else { return }
        self.openPreviousFile(output: output)
      })
      .disposed(by: disposedBag)

    input.previousEvent
      .subscribe(onNext: { [weak self] event in
        guard let self else { return }
        self.openNextFile(output: output)
      })
      .disposed(by: disposedBag)

    input.deleteButtonEvent
      .subscribe(onNext: { [weak self] in
        guard let self, let cellModel = self.selectedFileCellModel else { return }
        output.rxIsPause.accept(true)
        output.rxDeleteLocalPopup.onNext(cellModel)
      })
      .disposed(by: disposedBag)

//    input.backButtonEvent
//      .subscribe(onNext: { [weak self] event in
//        guard let self = self else { return }
//        output.rxIsCloseStream.onNext(true)
//        self.isClose = true
//      })
//      .disposed(by: disposedBag)

    input.channelButtonEvent
      .map { _ in true }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    let changeChannel = input.changeChannelEvent
      .map { $0 + 1 }
      .share()

    // 현재 채널 상태 업데이트
    changeChannel
      .bind(to: state.nowChannel)
      .disposed(by: disposedBag)

    // PlayerControlBarView의 currentChannel 업데이트를 위한 바인딩
    changeChannel
      .bind(to: output.rxChangeChannel)
      .disposed(by: disposedBag)

    // 채널 선택 UI 숨기기
    changeChannel
      .map { _ in false }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    /* State */
    input.playerState
      .bind(to: output.rxPlayerState)
      .disposed(by: disposedBag)

    input.playerState
      .subscribe(onNext: { [weak self] state in
        guard let self = self else { return }
        debugPrint("### player state: \(state)")
        if state == .ended && self.isClose != true {
          if self.fileModel.items.count == 1 {
            output.rxIsPause.accept(true)
          }
        }
      })
      .disposed(by: disposedBag)

    input.fileSelectedEvent
      .do(onNext: { _ in output.rxIsIndicator.onNext(true) })
      .subscribe(onNext: { [weak self] cellModel in
        guard let self = self,
          let index: Int = self.fileModel.items.firstIndex(of: cellModel)
        else { return }

        self.fileModel.selectedIndex = index
        self.openFile(output: output)
        output.rxFileSetting.onNext(cellModel)
      })
      .disposed(by: disposedBag)

    input.snapShotButtonEvent
      .withLatestFrom(state.nowChannel, resultSelector: { ($0, $1) })
      .subscribe(onNext: { [weak self] image, channel in
        guard let self,
          let image = image
        else { return }

        let index = self.fileModel.selectedIndex
        let fileName = self.fileModel.items[safe: index]?.fileName
        let channelCount = self.fileModel.items[safe: index]?.channelbits.channelBitMaskingCount ?? 3

        FileManager.default.writeSnapshot(
          image: image,
          fileName: fileName,
          channel: (channelCount == 1 ? -1 : channel)
        )
        output.rxToastSnapShot.onNext(())
      })
      .disposed(by: disposedBag)

    return (output, state)
  }
}

extension VodLocalViewModel {
  func openFile(output: Output) {

    let index = fileModel.selectedIndex
    selectedFileCellModel = fileModel.items[index]
    output.rxChangeSelectIndex.onNext(index)

    let fileExtension = fileModel.items[index].filePath?.lastPathComponent.fileExtension
      .lowercased()
    if (fileExtension == "mp4") || (fileExtension == "avi") || (fileExtension == "mov") {
      guard let url = fileModel.items[index].filePath else { return }
      output.rxFileUrl.onNext(url)
    } else if (fileExtension == "png") || (fileExtension == "jpg") || (fileExtension == "jpeg") {
      guard let image = fileModel.items[index].thumbimage else { return }
      output.rxFileImage.onNext(image)
    }
  }

  func openNextFile(output: Output) {
    var index = fileModel.selectedIndex + 1
    if index >= fileModel.items.count {
      index = 0
    }

    #if false
    if fileModel.items.count == 1 {
      return
    }
    #endif
    fileModel.selectedIndex = index
    openFile(output: output)
    guard let selectedCellModel = try? output.rxFilelistSetting.value()[0].items[safe: index] else {
      return
    }
    output.rxFileSetting.onNext(selectedCellModel)
  }

  func openPreviousFile(output: Output) {
    var index = fileModel.selectedIndex - 1
    if index < 0 {
      index = fileModel.items.count - 1
    }

    #if false
    if fileModel.items.count == 1 {
      return
    }
    #endif

    fileModel.selectedIndex = index
    openFile(output: output)
    guard let selectedCellModel = try? output.rxFilelistSetting.value()[0].items[safe: index] else {
      return
    }
    output.rxFileSetting.onNext(selectedCellModel)
  }

  func deleteLocalFile(model: FileListCellModel, output: Output, completetion: @escaping () -> Void)
  {
    output.rxIsIndicator.onNext(true)
    guard let path = model.filePath,
      let thumbFolderPath = UrlList.downloadedThumbPath()
    else {
      output.rxIsIndicator.onNext(false)
      return
    }
    let thumbFileName = path.lastPathComponent.fileName + ".jpg"
    let thumbPath = thumbFolderPath.appendingPathComponent(thumbFileName)

    Log.message(to: path)
    FileManager.remove(file: path)

    Log.message(to: thumbPath)
    FileManager.remove(file: thumbPath)

    output.rxIsIndicator.onNext(false)
    completetion()
  }
}
