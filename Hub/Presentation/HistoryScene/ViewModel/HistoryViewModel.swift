//
//  HistoryViewModel.swift
//  Hub
//
//  Created by ncn on 2023/03/16.
//

import RxCocoa
import RxSwift
import UIKit

class HistoryViewModel: BaseViewModel {
  weak var coordinator: HistoryCoordinator?

  struct Input {
    let logButtonEvent: Observable<SearchDateModel>
    let rxChartItem: Observable<EventChartModel>
    let rxDayItem: Observable<CalanderDayModel>
  }

  struct Output {
    let rxIsFlip = PublishRelay<Bool>()
  }

  init(coordinator: HistoryCoordinator) {
    self.coordinator = coordinator
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.logButtonEvent
      .subscribe(onNext: { [weak self] dateItem in
        guard let self = self else { return }

        self.pushLog(with: dateItem)
      })
      .disposed(by: disposedBag)

    input.rxChartItem
      .subscribe(onNext: { [weak self] event in
        guard let self = self else { return }
        self.pushLog(with: event)
      })
      .disposed(by: disposedBag)

    input.rxDayItem
      .subscribe(onNext: { [weak self] event in
        guard let self = self else { return }
        self.pushLog(with: event)
      })
      .disposed(by: disposedBag)

    return output
  }
}


extension HistoryViewModel {
  func pushLog(with item: SearchDateModel) {
    coordinator?.navigate(to: .pushLogWith(nil, nil, item))
  }

  func pushLog(with item: EventChartModel) {
    coordinator?.navigate(to: .pushLogWith(item, nil, nil))
  }

  func pushLog(with item: CalanderDayModel) {
    coordinator?.navigate(to: .pushLogWith(nil, item, nil))
  }

}
