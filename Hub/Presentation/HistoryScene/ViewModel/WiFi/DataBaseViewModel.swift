//
//  DataBaseViewModel.swift
//  Hub
//
//  Created by ncn on 2023/03/16.
//

import Foundation
import RxCocoa
import RxSwift

class DataBaseViewModel: BaseViewModel {
  let useCase: DataBaseUseCase

  var selectedDate: SearchDateModel

  struct State {
    let listItem = BehaviorRelay<[DrivingCellSectionModel]>(value: [])
    let listHistoryItem = BehaviorRelay<[HistoryCellSectionModel]>(value: [])
    let periodType = BehaviorSubject<PeriodType>(value: .day)
    let speedType = BehaviorSubject<SpeedType>(value: Current.speedUnit)
    let selectCellType = BehaviorSubject<HistoryCellType>(value: .Distance)
    let chartEventModels = BehaviorSubject<[EventChartModel]>(value: [])
  }

  struct Input {
    let dbReady: Observable<Void>
    let searchDateEvent: Observable<SearchDateModel>
//    let selectCellEvent: Observable<DriveingCellModel>
    let selectHistoryCellEvent: Observable<HistoryCellModel>
    let switchUnitEvent: Observable<SpeedType>
  }

  struct Output {
    let rxDrivedDate = PublishSubject<[Date]>()
    let rxChartData = PublishSubject<([EventChartModel], HistoryCellType, SpeedType)>()
    let rxSwitchUnit = PublishSubject<Bool>()
  }

  init(useCase: DataBaseUseCase) {
    self.useCase = useCase

    let ret = Date().intDate()
    self.selectedDate = SearchDateModel(year: ret.year, month: ret.month, day: ret.day)
  }

  func bind(input: Input, disposedBag: DisposeBag) -> (State, Output) {
    let output = Output()
    let state = State()

    input.dbReady
      .flatMapLatest(migration)
      .withLatestFrom(state.periodType)
      .withLatestFrom(state.speedType, resultSelector: { ($0, $1) })
      .do(onNext: { Current.wifiHistorySpeedUnit = $0.1 })
      .flatMapLatest(rxMakeChartAndTable(params:))
      .bind(to: state.chartEventModels)
      .disposed(by: disposedBag)

    input.searchDateEvent
      .do(onNext: { [weak self] date in self?.selectedDate = date })
      .withLatestFrom(state.periodType)
      .withLatestFrom(state.speedType, resultSelector: { ($0, $1) })
      .flatMapLatest(rxMakeChartAndTable(params:))
      .bind(to: state.chartEventModels)
      .disposed(by: disposedBag)

    input.switchUnitEvent
      .do(onNext: { state.speedType.onNext($0) })
      .withLatestFrom(state.chartEventModels)
      .bind(to: state.chartEventModels)
      .disposed(by: disposedBag)

    input.switchUnitEvent
      .do(onNext: { Current.wifiHistorySpeedUnit = $0 })
      .map { $0 == .mph }
      .bind(to: output.rxSwitchUnit)
      .disposed(by: disposedBag)

    input.selectHistoryCellEvent
      .map { $0.type }
      .do(onNext: { state.selectCellType.onNext($0) })
      .withLatestFrom(state.chartEventModels)
      .bind(to: state.chartEventModels)
      .disposed(by: disposedBag)
//    input.selectCellEvent
//      .map { $0.type }
//      .do(onNext: { state.selectCellType.onNext($0) })
//      .withLatestFrom(state.chartEventModels)
//      .bind(to: state.chartEventModels)
//      .disposed(by: disposedBag)

    // ----- State
    state.chartEventModels
      .withLatestFrom(state.periodType, resultSelector: { ($0, $1) })
      .withLatestFrom(state.speedType, resultSelector: { ($0.0, $0.1, $1) })
      .flatMapFirst(rxReqeustDrivingInfo(params:))
      .bind(to: state.listHistoryItem)
      .disposed(by: disposedBag)

    state.chartEventModels
      .withLatestFrom(state.selectCellType, resultSelector: { ($0, $1) })
      .withLatestFrom(state.speedType, resultSelector: { ($0.0, $0.1, $1) })
      .bind(to: output.rxChartData)
      .disposed(by: disposedBag)

    return (state, output)
  }

}

extension DataBaseViewModel {
  func migration() -> Observable<Void> {
    let result = useCase.migration()
    switch result {
    case .success(_):
      return Observable.just(())
    case .failure(let error):
      Log.error(to: error)
      return Observable.just(())
    }
  }

  func rxMakeChartAndTable(params: (type: PeriodType, speedType: SpeedType)) -> Observable<
    [EventChartModel]
  > {
    let chartDataModel: [EventChartModel]
    let days: [Date]
    switch params.type {
    case .day:  // 일
      days = self.makeDays(selectedDate: self.selectedDate)
    case .week:  // 주
      days = self.makeWeek(selectedDate: self.selectedDate)
    case .month:  // 월
      days = self.makeMonth(selectedDate: self.selectedDate)
    case .year:  // 연
      days = self.makeYear(selectedDate: self.selectedDate)
    }
    chartDataModel = self.excuteTo(type: params.type, days: days, speedType: params.speedType)
    return Observable.just(chartDataModel)
  }

  func excuteTo(type: PeriodType, days: [Date], speedType: SpeedType) -> [EventChartModel] {
    var data: [EventChartModel] = []
    for i in days.indices {
      iLogger.info("days : \(days[i])")
      if days[safe: i + 1] != nil {
        let modelList = SqlQuery.selectHistoryFromMain(
          startDate: days[i], endDate: days[i + 1],
          macAddress: AppManager.shared.deviceInfo?.macaddress ?? "")
        let chartModelList = modelList.map {
          $0.toDomain(day: days[i], type: type, speedType: speedType)
        }
        
        var sumChartModel = chartModelList.reduce(EventChartModel(day: days[i], type: type)) {
          oldModel, newModel in
          return oldModel + newModel
        }
        if chartModelList.count != 0 {
          let filteredChartModelList = chartModelList.filter { $0.avgspeed != 0 && $0.distance != 0 }
          iLogger.debug("chartModelList: \(chartModelList.count), filterList: \(filteredChartModelList.count)")

//          let intAvgspeed = Int(round(Double(sumChartModel.avgspeed) / Double(filteredChartModelList.count)))
//          sumChartModel.avgspeed = intAvgspeed
          sumChartModel.avgspeed = sumChartModel.avgspeed / chartModelList.count
        }
        data.append(sumChartModel)
      }
    }
    return data
  }
}

extension DataBaseViewModel {
  // 원본은 CalendarViewModel에 있음
  func makeDays(selectedDate: SearchDateModel) -> [Date] {
    var days: [Date] = []
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: selectedDate.year,
        month: selectedDate.month,
        day: selectedDate.day))
    {
      if let ret = date.adding(day: -2) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: -1) {
        days.append(ret.startOfDay)
      }
      days.append(date.startOfDay)
      if let ret = date.adding(day: 1) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: 2) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: 3) {
        days.append(ret.startOfDay)
      }
    }
    return days
  }

  func makeWeek(selectedDate: SearchDateModel) -> [Date] {
    var weeks: [Date] = []
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: selectedDate.year,
        month: selectedDate.month,
        day: selectedDate.day))
    {
      // MARK: 일:1~토:7 -> 월:1~일:7 로 변환
      guard
        let sunday = calendar.dateComponents(
          [.calendar, .yearForWeekOfYear, .weekOfYear], from: date
        ).date
      else { return [] }
      for i in 0..<8 {
        Log.message(to: Calendar.current.date(byAdding: .day, value: i, to: sunday) ?? Date())
        guard let weekDate = Calendar.current.date(byAdding: .day, value: i, to: sunday) else {
          continue
        }
        weeks.append(weekDate)
      }
    }
    return weeks
  }

  func makeMonth(selectedDate: SearchDateModel) -> [Date] {
    var months: [Date] = []
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: selectedDate.year,
        month: selectedDate.month,
        day: selectedDate.day))
    {
      guard let startMonth = date.toString(format: "yyyy-MM-01").toDate(format: "yyyy-MM-dd"),
        let nextMonth = calendar.date(byAdding: .month, value: +1, to: startMonth),
        let endOfMonth = calendar.date(byAdding: .day, value: -1, to: nextMonth)
      else { return [] }
      let days = Calendar.current.dateComponents([.day], from: startMonth, to: endOfMonth)

      guard let dayCount = days.day else { return [] }
      for i in stride(from: 0, through: dayCount + 1, by: 7) {
        guard let monthDate = Calendar.current.date(byAdding: .day, value: i, to: startMonth) else {
          continue
        }
        months.append(monthDate)
      }
      months.append(endOfMonth)
    }
    return months
  }

  func makeYear(selectedDate: SearchDateModel) -> [Date] {
    var years: Array = [Date]()
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: selectedDate.year,
        month: selectedDate.month,
        day: selectedDate.day))
    {
      guard let startMonth = date.toString(format: "yyyy-01-01").toDate(format: "yyyy-MM-dd") else {
        return []
      }
      let startYear = startMonth
      for i in 0..<13 {
        guard let yearDate = startYear.adding(month: i) else { continue }
        years.append(yearDate)
      }
    }
    return years
  }

}

//MARK: 차트 하단 리스트 세팅
extension DataBaseViewModel {
  func rxReqeustDrivingInfo(
    params: (datas: [EventChartModel], type: PeriodType, speedType: SpeedType)
  ) -> Observable<[HistoryCellSectionModel]> {
    if params.datas.count < 2 { return Observable.just([]) }
    let data: [EventChartModel]
    switch params.type {
    case .day:
      data = [params.datas[2]]
    default:
      data = params.datas
    }

    let sumDatas = data.reduce(EventChartModel(day: Date(), type: params.type), +)

    var strDistance = ""
    var strAvg = ""
    var strMax = ""
    switch params.speedType {
    case .mph:
      strDistance = String(format: "%d " + L.unit_mile.localized, sumDatas.distance)
      strAvg = String(format: "%d " + L.unit_mph.localized, sumDatas.avgspeed)
      strMax = String(format: "%d " + L.unit_mph.localized, sumDatas.maxspeed)
    case .kph:
      strDistance = String(
        format: "%d " + L.unit_kilo.localized, NCUtil.mileToKilo(mile: sumDatas.distance))
      strAvg = String(
        format: "%d " + L.unit_kmh.localized, NCUtil.mileToKilo(mile: sumDatas.avgspeed))
      strMax = String(
        format: "%d " + L.unit_kmh.localized, NCUtil.mileToKilo(mile: sumDatas.maxspeed))
    case .off: break
    }

    let strCnt1 = String(format: L.shock_recording_value.localized, "\(sumDatas.cnt1)")
    let strCnt2 = String(format: L.shock_recording_value.localized, "\(sumDatas.cnt2)")
    let strCnt3 = String(format: L.shock_recording_value.localized, "\(sumDatas.cnt3)")

    let drive = String(format: "%@", sumDatas.sumtime1)
    let park = String(format: "%@", sumDatas.sumtime2)

    let drivingItems = [
      HistoryCellModel(type: .Distance, item: L.distance_drive_txt.localized, value: strDistance),
      HistoryCellModel(type: .DrivingTime, item: L.history_always_recording_time.localized, value: drive),
      HistoryCellModel(type: .ParkingTime, item: L.history_park_recording_time.localized, value: park)
    ]
    
    let eventItems = [
      HistoryCellModel(type: .DrivingEvent, item: L.history_permanent_shock_event.localized, value: strCnt1),
      HistoryCellModel(type: .ParkingMotion, item: L.history_park_motion_event.localized, value: strCnt2),
      HistoryCellModel(type: .ParkingEvent, item: L.history_park_shock_event.localized, value: strCnt3)
    ]
    
    let averegeItems = [
      HistoryCellModel(type: .AvgSpeed, item: L.history_average_speed.localized, value: strAvg),
      HistoryCellModel(type: .MaxSpeed, item: L.history_max_speed.localized, value: strMax)
    ]
    
    let sections = [
      HistoryCellSectionModel(sectionType: .history, items: drivingItems),
      HistoryCellSectionModel(sectionType: .history, items: eventItems),
      HistoryCellSectionModel(sectionType: .history, items: averegeItems)
    ]
    
    return Observable.just(sections)    
  }
}
