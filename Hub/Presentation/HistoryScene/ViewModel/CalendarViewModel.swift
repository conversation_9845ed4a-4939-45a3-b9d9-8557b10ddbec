//
//  CalendarViewModel.swift
//  Hub
//
//  Created by ncn on 2023/03/28.
//

import Foundation
import RxCocoa
import RxSwift

enum PeriodType: Int {
  case day = 0
  case week = 1
  case month = 2
  case year = 3
}

public struct SearchDateModel {
  var year: Int
  var month: Int
  var day: Int

  /// yyyy-MM-dd
  func getDate() -> Date {
    let calendar = Calendar.defaultCalendar()
    let dateComponent = DateComponents(year: self.year, month: self.month, day: self.day)
    guard let date = calendar.date(from: dateComponent) else { return Date() }
    return date
  }

  /// yyyy-MM-dd
  func getDay() -> String {
    let calendar = Calendar.defaultCalendar()
    let dateComponent = DateComponents(year: self.year, month: self.month, day: self.day)
    guard let date = calendar.date(from: dateComponent) else { return "" }
    let yearMonthDayString = date.toString(format: "yyyy/MM/dd")
    return yearMonthDayString
  }

  /// yyyy-MM-dd ~ yyyy-MM-dd
  func getWeekRange() -> String {
    let calendar = Calendar.defaultCalendar(local: "GMT", timeZone: "GMT")
    let dateComponent = DateComponents(year: self.year, month: self.month, day: self.day)
    guard let date = calendar.date(from: dateComponent),
      let start = date.addingFromSunday(add: 0),
      let end = date.addingFromSunday(add: 6)
    else { return "" }
    let string = start.toString(format: "yyyy/MM/dd") + " ~ " + end.toString(format: "yyyy/MM/dd")
    return string
  }
  /// yyyy-MM
  func getMonth() -> String {
    let calendar = Calendar.defaultCalendar()
    let dateComponent = DateComponents(year: self.year, month: self.month, day: self.day)
    guard let date = calendar.date(from: dateComponent) else { return "" }
    let yearMonthString = date.toString(format: "yyyy/MM")
    return yearMonthString
  }
  /// yyyy
  func getYear() -> String {
    let calendar = Calendar.defaultCalendar()
    let dateComponent = DateComponents(year: self.year, month: self.month, day: self.day)
    guard let date = calendar.date(from: dateComponent) else { return "" }
    let yearString = date.toString(format: "yyyy")
    return yearString
  }

}

class CalendarViewModel: BaseViewModel {
  var periodType: PeriodType = .day

  private var currentMonth: Date!
  private let calendar = Calendar(identifier: .gregorian)
  private var days: [CalanderDayModel] = []

  var drivedDate: [Date]?

  var rxSearcDate = PublishSubject<SearchDateModel>()
  var rxSearcDateWithType = PublishSubject<(SearchDateModel, PeriodType)>()
  var searchDate: SearchDateModel {
    didSet {
      rxSearcDate.onNext(searchDate)
      rxSearcDateWithType.onNext((searchDate, periodType))
    }
  }

  private var currentMonthIndex: Int = 0 {
    didSet {
      //_ =  makeCalendarDay()
      //collectionView.reloadData()
    }
  }

  struct Input {
    let willAppearEvent: ControlEvent<Bool>
    let calendarSeletedEvent: Observable<IndexPath>
    let previousButtonEvent: Observable<Void>
    let previousMonthButtonEvent: Observable<Void>
    let nextButtonEvent: Observable<Void>
    let nextMonthButtonEvent: Observable<Void>
  }

  struct Output {
    let rxSearchDay = PublishSubject<[Date]>()
    let rxSearchWeek = PublishSubject<[Date]>()
    let rxSearchMonth = PublishSubject<[Date]>()
    let rxSearchYear = PublishSubject<[Date]>()
    let rxSelectedDate = PublishSubject<String>()

    let rxCalendarDays = PublishSubject<[CalanderDayModel]>()
  }

  init() {
    let ret = Date().intDate()
    self.searchDate = SearchDateModel(year: ret.year, month: ret.month, day: ret.day)
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.willAppearEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.makeDate(type: self.periodType, output: output)
        let ret = self.makeCalendarDay(true)
        output.rxCalendarDays.onNext(ret)
      })
      .disposed(by: disposedBag)

    input.calendarSeletedEvent
      .subscribe(onNext: { indexPath in

      })
      .disposed(by: disposedBag)

    input.previousButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.decrease()
        let ret = self.makeCalendarDay()
        output.rxCalendarDays.onNext(ret)
        self.makeDate(type: self.periodType, output: output)
        //output.rxIsForward.onNext(true)
      })
      .disposed(by: disposedBag)

    input.previousMonthButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        searchDate.month -= 1
        let ret = self.makeCalendarDay()
        output.rxCalendarDays.onNext(ret)
        self.makeDate(type: self.periodType, output: output)
        //output.rxIsForward.onNext(true)
      })
      .disposed(by: disposedBag)

    input.nextButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.increase()
        let ret = self.makeCalendarDay()
        output.rxCalendarDays.onNext(ret)
        self.makeDate(type: self.periodType, output: output)
      })
      .disposed(by: disposedBag)

    input.nextMonthButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        searchDate.month += 1
        let ret = self.makeCalendarDay()
        output.rxCalendarDays.onNext(ret)
        self.makeDate(type: self.periodType, output: output)
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension CalendarViewModel {
  private func decrease() {
    switch periodType {
    case .day:
      searchDate.day -= 1
      break
    case .week:
      searchDate.day -= 7
      break
    case .month:
      searchDate.month -= 1
      break
    case .year:
      searchDate.year -= 1
      break
    }
  }

  private func increase() {
    switch periodType {
    case .day:
      searchDate.day += 1
      break
    case .week:
      searchDate.day += 7
      break
    case .month:
      searchDate.month += 1
      break
    case .year:
      searchDate.year += 1
      break
    }
  }

  func makeDate(type: PeriodType, output: Output) {
    switch type {
    case .day:
      makeDays(output: output)
      break
    case .week:
      makeWeek(output: output)
      break
    case .month:
      makeMonth(output: output)
      break
    case .year:
      makeYear(output: output)
      break
    }
  }
}

// MARK: - For Database
extension CalendarViewModel {
  func makeDays(output: Output) {
    var days: [Date] = []
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: searchDate.year,
        month: searchDate.month,
        day: searchDate.day))
    {
      let string = date.toString(format: "yyyy/MM/dd")
      output.rxSelectedDate.onNext(string)
      if let ret = date.adding(day: -2) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: -1) {
        days.append(ret.startOfDay)
      }
      days.append(date.startOfDay)
      if let ret = date.adding(day: 1) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: 2) {
        days.append(ret.startOfDay)
      }
      if let ret = date.adding(day: 3) {
        days.append(ret.startOfDay)
      }
    }

    output.rxSearchDay.onNext(days)
  }

  func makeWeek(output: Output) {
    let calendar = Calendar.defaultCalendar()
    if let date = calendar.date(
      from: DateComponents(
        year: searchDate.year,
        month: searchDate.month,
        day: searchDate.day))
    {
      var days: [Date] = []
      for idx in 0..<8 {
        if let ret = date.addingFromSunday(add: idx + 1) {
          days.append(ret)
        }
      }

      let string =
        days[0].toString(format: "yyyy/MM/dd") + " ~ " + days[6].toString(format: "yyyy/MM/dd")
      output.rxSelectedDate.onNext(string)
      let result = days.map { $0.startOfDay }
      output.rxSearchWeek.onNext(result)
    }
  }

  func makeMonth(output: Output) {
    let calendar = Calendar.defaultCalendar()
    let year = searchDate.year
    let month = searchDate.month
    let day = searchDate.day
    var lastDay = 31
    _ = searchDate.getMonth()
    if let date = calendar.date(
      from: DateComponents(
        year: year,
        month: month,
        day: day))
    {
      let string = date.toString(format: "yyyy-MM")
      output.rxSelectedDate.onNext(string)
    }

    if let nextMonth = calendar.date(
      from: DateComponents(
        year: year,
        month: month + 1,
        day: day))
    {
      lastDay = calendar.component(.day, from: nextMonth)
    }

    var days: Array = [Date]()
    for i in 1...lastDay {
      if let date = calendar.date(
        from: DateComponents(
          year: year,
          month: month,
          day: i))
      {
        days.append(date)
      }
    }
    //basicDate = startMonth
    output.rxSearchMonth.onNext(days)
  }

  func makeYear(output: Output) {
    let calendar = Calendar.defaultCalendar()
    _ = searchDate.year
    var months: Array = [Date]()

    if let date = calendar.date(
      from: DateComponents(
        year: searchDate.year,
        month: searchDate.month,
        day: searchDate.day))
    {
      let yearString = date.toString(format: "yyyy")
      output.rxSelectedDate.onNext(yearString)
      for i in 1...12 {
        if let date = "\(yearString)-\(i)-01".toDate(format: "yyyy/MM/dd") {
          months.append(date)
        }
      }
    }
    output.rxSearchYear.onNext(months)
  }
}

// MARK: - For Calendar
extension CalendarViewModel {
  func getDateWidthMonth(search: Int) -> [Date] {
    var array: [Date] = []
    if let dates = drivedDate {
      for date in dates {
        let comp = calendar.dateComponents([.month], from: date)
        if comp.month == search {
          array.append(date)
        }
      }
    }
    return array
  }

  func makeCalendarDay(_ isDefault: Bool = false) -> [CalanderDayModel] {
    let initialDate = calendar.date(
      from: DateComponents(
        year: searchDate.year,
        month: searchDate.month,
        day: searchDate.day))!
    currentMonth = initialDate

    let components = calendar.dateComponents([.year, .month], from: initialDate)
    let firstDayOfMonth = calendar.date(from: components)!
    let firstWeek = calendar.dateComponents([.weekday], from: firstDayOfMonth).weekday ?? 0
    let totalDay = calendar.range(of: .day, in: .month, for: initialDate)?.count ?? 0

    let nextDate = calendar.date(
      from: DateComponents(
        year: searchDate.year,
        month: searchDate.month + 1,
        day: searchDate.day))!
    let nextComponents = calendar.dateComponents([.year, .month], from: nextDate)
    let dayOfNextMonth = calendar.date(from: nextComponents)!

    let today = calendar.startOfDay(for: initialDate)
    let dayOfWeek = calendar.component(.weekday, from: today)
    let dates = calendar.range(of: .weekday, in: .weekOfYear, for: today)!
      .compactMap { calendar.date(byAdding: .day, value: $0 - dayOfWeek, to: today) }

    let drived = getDateWidthMonth(search: initialDate.intDate().month)

    days.removeAll()
    var offset: Int = 42
    for i in 1..<firstWeek {
      let date = firstDayOfMonth.addingByDay(day: -(firstWeek - i))
      let model = CalanderDayModel(
        date: date.addingByMonth(month: 0),
        dayNumber: date.getTheDayIndex,
        isPreviousMonth: true,
        type: periodType)
      if periodType == .week {
        if dates.contains(date) {
          //model.isCurrent = true
          //model.isFirst = date.shortDateFormat == dates[0].shortDateFormat
        }
      }
      days.append(model)
      offset -= 1
    }

    for i in 0..<totalDay {
      let date = firstDayOfMonth.addingByDay(day: i)
      var model = CalanderDayModel(date: date, dayNumber: date.getTheDayIndex, type: periodType)

      switch periodType {
      case .day:
        if date.shortDateFormat == initialDate.shortDateFormat {
          model.isCurrent = true
        }
        break
      case .week:
        break
      default:
        break
      }

      if drived.contains(where: { $0.shortDateFormat == date.shortDateFormat }) {
        model.isDrive = true
      }

      days.append(model)
      offset -= 1
    }

    let lastDate = dayOfNextMonth.addingByDay(day: 0)
    var lastContains = calendar.range(of: .weekday, in: .weekOfYear, for: lastDate)!
      .compactMap { calendar.date(byAdding: .day, value: $0 - dayOfWeek, to: lastDate) }
    lastContains.removeLast()

    if offset > 0 {
      for i in 0..<offset {
        //let date = firstDayOfMonth.addingByDay(day: i)
        let date = dayOfNextMonth.addingByDay(day: i)
        let model = CalanderDayModel(
          date: date.addingByMonth(month: 1),
          dayNumber: date.getTheDayIndex,
          isNextMonth: true,
          type: periodType)
        if periodType == .week && isDefault == false {
          //if dates.contains(date) {
          if lastContains.contains(date) {
            //model.isCurrent = true
            //model.isLast = date.shortDateFormat == lastContains.last!.shortDateFormat
            //model.isLast = date.shortDateFormat == dates.last!.shortDateFormat
          }
        }
        days.append(model)
        offset -= 1
      }
    }
    return days
  }
}
