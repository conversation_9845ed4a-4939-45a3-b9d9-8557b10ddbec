//
//  HistoryCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

//protocol HistoryCoordinator: NavigationCoordinator {
//  func make() -> HistoryViewController
//  //    func pushLog()
//  func pushLog(with item: SearchDateModel)
//  func pushLog(with item: EventChartModel)
//  func pushLog(with item: CalanderDayModel)
//}

protocol HistoryCoordinator: AnyObject {
  func navigate(to step: HistorySteps)
}

public protocol HistoryCoordinatorFinishDelegate: AnyObject {
  func historyCoordinatorDidFinish()
}

public enum HistorySteps: Step {
  case showHistory
  case pushLogWith(
    _ chartItem: EventChartModel? = nil,
    _ dayItem: CalanderDayModel? = nil,
    _ dateItem: SearchDateModel? = nil
  )
  case HistoryDidFinish
}

public enum HistoryChildCoordinator {
  case travelLog
}
