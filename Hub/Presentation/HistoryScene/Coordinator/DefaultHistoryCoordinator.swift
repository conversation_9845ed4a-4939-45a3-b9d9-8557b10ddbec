//
//  DefaultHistoryCoordinator.swift
//  Hub
//
//  Created by ncn on 2023/03/15.
//

import UIKit

final class DefaultHistoryCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: HistoryCoordinatorFinishDelegate?
  var childCoordinators = [HistoryChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  func start() {
    navigate(to: .showHistory)
  }
  
  func start(with step: HistorySteps) {
    navigate(to: step)
  }
}

extension DefaultHistoryCoordinator {
  func make() -> HistoryViewController {

    let vc = HistoryViewController()
    vc.viewModel = HistoryViewModel(coordinator: self)
    vc.calendarViewModel = CalendarViewModel()

    switch AppManager.shared.mode {
    case .wifi:
      configWifiModel(viewController: vc)
      break
    case .file:
      configLocalModel(viewController: vc)
      break
    }
    return vc
  }

  private func configWifiModel(viewController: HistoryViewController) {
    let wifiViewModel = HistoryWifiViewModel(
      useCase: Composers.historyUseCase,
      dbUseCase: Composers.dbUsecase
    )
    let dbViewModel = DataBaseViewModel(useCase: Composers.dbUsecase)

    viewController.wifiViewModel = wifiViewModel
    viewController.dbViewModel = dbViewModel
  }

  private func configLocalModel(viewController: HistoryViewController) {
    let dbViewModel = DataBaseViewModel(useCase: Composers.dbUsecase)
    viewController.dbViewModel = dbViewModel
  }

}

extension DefaultHistoryCoordinator: HistoryCoordinator {
  func navigate(to step: HistorySteps) {
    switch step {
    case .showHistory:
      showHistory()
    case let .pushLogWith(chartItem, dayItem, dateItem):
      pushLog(chartItem, dayItem, dateItem)
      
    case .HistoryDidFinish:
      
      delegate?.historyCoordinatorDidFinish()
    }
  }

  func showHistory() {
    let vc = make()
    vc.hidesBottomBarWhenPushed = true
    self.navigationController.pushViewController(vc, animated: true)
  }

  func pushLog(
    _ chartItem: EventChartModel? = nil,
    _ dayItem: CalanderDayModel? = nil,
    _ dateItem: SearchDateModel? = nil
  ) {
    let logCoordinator = DefaultTravelLogCoordinator(navigationController: navigationController)
    childCoordinators[.travelLog] = logCoordinator
    logCoordinator.delegate = self
    logCoordinator.start(with: .travelLogWith(chartItem, dayItem, dateItem))
  }
}

extension DefaultHistoryCoordinator: TravelLogCoordinatorFinishDelegate {
  func travelLogCoordinatorDidFinish() {
    childCoordinators[.travelLog] = nil
  }
}
