//
//  SpeedUnitButton.swift
//  Hub
//
//  Created by ncn on 3/11/25.
//
import UIKit

class SpeedUnitButton: UIButton {
  private let activationFeedBackGenerator = UIImpactFeedbackGenerator(style: .heavy)
  
  private let stackView: UIStackView = {
    let stack = UIStackView()
    stack.axis = .vertical
    stack.alignment = .center
    stack.distribution = .fillProportionally
    stack.spacing = 0
    stack.isUserInteractionEnabled = false
    return stack
  }()
  
  private let imageContainer: UIImageView = {
    let imageView = UIImageView()
    imageView.contentMode = .scaleAspectFit
    imageView.image = UIImage(named: "moreDistanceChange")?.withTintColor(.iconDark)
    imageView.pin.size(20).activate()
    return imageView
  }()
  
  private let unitLabel: UILabel = {
    let label = UILabel()
    label.font = .body1
    label.textColor = .mainBlack
    label.text = "km/h"
    return label
  }()
  
  override init(frame: CGRect) {
    super.init(frame: frame)
    setupButton()
  }
  
  required init?(coder: NSCoder) {
    super.init(coder: coder)
    setupButton()
  }
  
  private func setupButton() {
    isEnabled = true
    isUserInteractionEnabled = true
    backgroundColor = .clear
    layer.cornerRadius = 10
    layer.borderWidth = 1
    layer.borderColor = UIColor.line.cgColor
    
    addSubview(stackView)
    stackView.addArrangedSubview(imageContainer)
    stackView.addArrangedSubview(unitLabel)
    stackView.pin.center().height(38).width(50).activate()
  }
  
  override var isSelected: Bool {
    didSet {
      activationFeedBackGenerator.impactOccurred()
      unitLabel.text = isSelected ? SpeedType.mph.title : SpeedType.kph.title
      Current.wifiHistorySpeedUnit = isSelected ? .mph : .kph
    }
  }
}
