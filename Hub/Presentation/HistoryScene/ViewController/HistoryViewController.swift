//
//  HistoryViewController.swift
//  Hub
//
//  Created by ncn on 2023/03/15.
//

import RxCocoa
import RxSwift
import RxGesture
import UIKit

class HistoryViewController: UIViewController {
  let charContainerView = ChartContainerView()
  let drivingView = DrivingInfoView(section: [])

  var viewModel: HistoryViewModel?
  var calendarViewModel: CalendarViewModel?

  // Hub UI
  var logButton: UIButton = {
    let button = UIButton()
    button.setTitle(L.driving_record.localized, for: .normal)
    button.setTitleColor(.white, for: .normal)
    button.titleLabel?.font = .pretendard(ofSize: 16, weight: .bold)
    button.setBackgroundColor(.vueroidBlue, for: .normal)
    button.setBackgroundColor(.vueroidBlue, for: .highlighted)
    button.contentHorizontalAlignment = .left
    button.titleEdgeInsets = .init(top: 0, left: 15, bottom: 0, right: 0)
    return button
  }()
  
  var logButtonImage: UIImageView = {
    let imageView = UIImageView()
    imageView.image = UIImage(systemName: "chevron.right")
    imageView.tintColor = .white
    return imageView
  }()
  
  var switchButton = SpeedUnitButton()

  /* wifi */
  var wifiViewModel: HistoryWifiViewModel?
  var dbViewModel: DataBaseViewModel?

  lazy var scrollView: UIScrollView = {
    let view = UIScrollView()
    view.showsHorizontalScrollIndicator = false
    view.alwaysBounceHorizontal = false
    view.zoomScale = 1.0
    view.translatesAutoresizingMaskIntoConstraints = false
    view.backgroundColor = .background
    return view
  }()
  var disposedBag = DisposeBag()

  override func viewDidLoad() {
    super.viewDidLoad()
    self.title = L.history_dashboard.localized
    navigationItem.backButtonTitle = ""
    navigationController?.delegate = self
    
    setComponent()
    setAutoLayout()
    bindViewModel(to: viewModel)
    bindForWifi(historyVM: wifiViewModel, dataBaseVM: dbViewModel)
    bindForLocal(dataBaseVM: dbViewModel)
    bindViewModel(to: calendarViewModel)
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    logButton.roundCorners(.allCorners, radius: 10)
    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      self.scrollView.updateContentSize()
    }
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    tabBarController?.toggleTabbar(isShow: false)
  }

  func setComponent() {
    self.view.addSubview(self.scrollView)
    charContainerView.tag = _tag_scrollView_size
    self.scrollView.addSubview(charContainerView)

    logButton.tag = _tag_scrollView_size
    switchButton.tag = _tag_scrollView_size
    
    self.scrollView.addSubview(logButton)
    self.scrollView.addSubview(logButtonImage)
    self.scrollView.addSubview(switchButton)

    switchButton.isSelected = Current.speedUnit == .mph
    drivingView.tag = _tag_scrollView_size
    self.scrollView.addSubview(drivingView)
  }

  func setAutoLayout() {
    self.scrollView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.view.safeAreaLayoutGuide)
      make.bottom.equalTo(self.view)
      make.left.right.equalTo(self.view)
    }

    charContainerView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.view).offset(0)
      make.right.equalTo(self.view).offset(0)
      make.top.equalToSuperview()
      make.height.equalTo(264.rv)
    }

    logButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.charContainerView.snp.bottom).offset(18)
      make.left.equalTo(self.view).offset(15)
      make.height.equalTo(54)
      make.width.equalTo(268.wrv)
    }
    
    logButtonImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self.logButton.snp.right).offset(-15) // 버튼 오른쪽 내부에서 15만큼 떨어짐
      make.centerY.equalTo(self.logButton) // Y축 중심 정렬
//      make.width.height.equalTo(20) // 이미지 크기 설정 (필요에 따라 조정)
    }

    switchButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.logButton.snp.top)
      make.right.equalTo(self.view).offset(-15)
      make.left.equalTo(logButton.snp.right).offset(10.wrv)
      make.height.equalTo(54)
      make.width.equalTo(52)
    }

    drivingView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.logButton.snp.bottom).offset(18)
      make.left.right.equalTo(self.view).offset(0)
      make.height.equalTo(356.rv)
    }
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? HistoryViewModel else { return }
    guard let calendarViewModel = calendarViewModel else { return }
    let calendarButtonEvent = charContainerView.menuView.dateLabel.rx.tapGesture().asObservable()
    
    let logButtonEvent = logButton.rx.tap
      .map { calendarViewModel.searchDate }
      .asObservable()

    let input = HistoryViewModel.Input(
      logButtonEvent: logButtonEvent,
      rxChartItem: charContainerView.rxSelectChartItem,
      rxDayItem: charContainerView.calendarView.rxSelectedDay
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)
    output.rxIsFlip
      .asObservable()
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.charContainerView.flip()
      })
      .disposed(by: self.disposedBag)

    calendarButtonEvent
      .observe(on: MainScheduler.asyncInstance)
      .skip(1)
      .bind(onNext: { [weak self] _ in
        let vc = CalenderViewController(calendarViewModel: calendarViewModel)
        vc.modalTransitionStyle = .crossDissolve
        vc.modalPresentationStyle = .overCurrentContext
        self?.present(vc, animated: true)
      })
      .disposed(by: disposedBag)
    
    calendarViewModel.rxSearcDateWithType
      .map { date, type in
        switch type {
        case .day:
          return date.getDay()
        case .week:
          return date.getWeekRange()
        case .month:
          return date.getMonth()
        case .year:
          return date.getYear()
        }
      }
      .observe(on: MainScheduler.asyncInstance)
      .bind(to: charContainerView.menuView.dateLabel.rx.text)
      .disposed(by: disposedBag)
  }
}

// MARK: - CalendarViewModel
extension HistoryViewController {
  func bindViewModel(to viewModel: CalendarViewModel?) {
    guard let model = viewModel else { return }

    let calanderSelectEvent = charContainerView.calendarView.collectionView.rx.itemSelected
      .asObservable()
    let previousButtonEvent = charContainerView.menuView.previousButton.rx.tap.asObservable()
    let previousMonthButtonEvent = charContainerView.menuView.previousMonthButton.rx.tap.asObservable()
    let nextButtonEvent = charContainerView.menuView.nextButton.rx.tap.asObservable()
    let nextMonthButtonEvent = charContainerView.menuView.nextMonthButton.rx.tap.asObservable()

    let input = CalendarViewModel.Input(
      willAppearEvent: self.rx.viewWillAppear,
      calendarSeletedEvent: calanderSelectEvent,
      previousButtonEvent: previousButtonEvent,
      previousMonthButtonEvent: previousMonthButtonEvent,
      nextButtonEvent: nextButtonEvent,
      nextMonthButtonEvent: nextMonthButtonEvent
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxSelectedDate
      .asObserver()
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        self.charContainerView.menuView.dateLabel.text = ret
      })
      .disposed(by: self.disposedBag)

    output.rxSearchDay
      .asObserver()
      .subscribe(onNext: { [weak self] days in
        guard let self = self else { return }
        if self.dbViewModel != nil {
          Log.message(to: "days: \(days)")
        }
      })
      .disposed(by: self.disposedBag)

    output.rxSearchWeek
      .asObserver()
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        if self.dbViewModel != nil {
        }
      })
      .disposed(by: self.disposedBag)

    output.rxSearchMonth
      .asObserver()
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        if self.dbViewModel != nil {
        }
      })
      .disposed(by: self.disposedBag)

    output.rxSearchYear
      .asObserver()
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        if self.dbViewModel != nil {
        }
      })
      .disposed(by: self.disposedBag)

    output.rxCalendarDays
      .asObserver()
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }
        self.charContainerView.calendarView.menuItems = ret
      })
      .disposed(by: self.disposedBag)

  }
}

