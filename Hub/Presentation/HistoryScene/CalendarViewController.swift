//
//  CalendarViewController.swift
//  Hub
//
//  Created by le<PERSON>ung<PERSON><PERSON> on 2023/10/19.
//

import CVCalendar
import Foundation
import RxCocoa
import RxSwift
import SnapKit
import UIKit

class CalenderViewController: BaseViewController {

  let containerView = UIView()

  let menuContainerView = UIView()
  let selectDateLabel = UILabel()

  let calendarContainerView = UIView()
  let menuView = CVCalendarMenuView()
  let calendarView = CVCalendarView()

  let buttonContainerView = UIStackView()
  let okButton = UIButton()
  let cancelButton = UIButton()

  let calendarViewModel: CalendarViewModel

  var calendarSelectDate: SearchDateModel?

  init(calendarViewModel: CalendarViewModel) {
    self.calendarViewModel = calendarViewModel
    super.init(nibName: nil, bundle: nil)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func viewDidLoad() {
    super.viewDidLoad()

    self.setComponent()
    self.setAutoLayout()
    self.bind()

  }

  override func viewDidLayoutSubviews() {
    super.viewDidLayoutSubviews()
    menuView.commitMenuViewUpdate()
    calendarView.commitCalendarViewUpdate()
  }

  override func setComponent() {

    view.backgroundColor = UIColor.black.withAlphaComponent(0.64)

    containerView.backgroundColor = .white
    containerView.roundCorners(.allCorners, radius: 20)
    containerView.clipsToBounds = true
    self.view.addSubview(containerView)

    menuContainerView.backgroundColor = .white
    containerView.addSubview(menuContainerView)

    selectDateLabel.textColor = .customSoftBlue
    selectDateLabel.font = .MontserratBold(ofSize: 24)
    menuContainerView.addSubview(selectDateLabel)

    calendarContainerView.backgroundColor = .white
    containerView.addSubview(calendarContainerView)

    // Menu delegate [Required]
    menuView.menuViewDelegate = self
    calendarContainerView.addSubview(menuView)

    // Appearance delegate [Unnecessary]
    calendarView.calendarAppearanceDelegate = self
    // Calendar delegate [Required]
    calendarView.calendarDelegate = self
    // Animator delegate [Unnecessary]
    calendarView.animatorDelegate = self
    calendarContainerView.addSubview(calendarView)

    buttonContainerView.alignment = .center
    buttonContainerView.distribution = .fillEqually
    buttonContainerView.axis = .horizontal
    buttonContainerView.spacing = 10
    buttonContainerView.backgroundColor = .clear
    containerView.addSubview(buttonContainerView)

    cancelButton.backgroundColor = .grayButton
    cancelButton.setTitleColor(.mainBlack, for: .normal)
    cancelButton.setTitle(L.cancel.localized, for: .normal)
    cancelButton.titleLabel?.font = .sub2
    cancelButton.roundCorners(.allCorners, radius: 5)
    buttonContainerView.addArrangedSubview(cancelButton)
    
    okButton.backgroundColor = .vueroidBlue
    okButton.setTitleColor(.white, for: .normal)
    okButton.setTitle(L.confirm.localized, for: .normal)
    okButton.titleLabel?.font = .sub2
    okButton.roundCorners(.allCorners, radius: 5)
    buttonContainerView.addArrangedSubview(okButton)
  }

  override func setAutoLayout() {
    containerView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      make.leading.equalToSuperview().inset(40)
      make.top.equalToSuperview().inset(100)
    }

    menuContainerView.snp.makeConstraints { make in
      make.leading.trailing.top.equalToSuperview()
      make.height.equalTo(60)
    }
    selectDateLabel.snp.makeConstraints { make in
      make.top.bottom.equalToSuperview()
      make.leading.equalToSuperview().inset(20)
    }

    calendarContainerView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview()
      make.top.equalTo(menuContainerView.snp.bottom)
    }
    menuView.snp.makeConstraints { make in
      make.leading.trailing.top.equalToSuperview()
      make.height.equalTo(40)
    }
    calendarView.snp.makeConstraints { make in
      make.leading.trailing.bottom.equalToSuperview()
      make.top.equalTo(menuView.snp.bottom)
    }

    buttonContainerView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(13)
      make.top.equalTo(calendarContainerView.snp.bottom).offset(15)
      make.bottom.equalToSuperview().inset(15)
      make.height.equalTo(48)
    }
    
    okButton.snp.makeConstraints { make in
      make.top.bottom.equalToSuperview()
    }
    cancelButton.snp.makeConstraints { make in
      make.top.bottom.equalToSuperview()
    }
  }

  func bind() {
    Observable.just(calendarViewModel.searchDate)
      .map { $0.getDay() }
      .bind(to: selectDateLabel.rx.text)
      .disposed(by: disposedBag)

    Observable.just(calendarViewModel.searchDate)
      .map { $0.getDate() }
      .bind(onNext: { [weak self] date in
        self?.calendarView.toggleViewWithDate(date)
      })
      .disposed(by: disposedBag)

    cancelButton.rx.tap
      .bind(onNext: { [weak self] in
        self?.dismiss(animated: true)
      })
      .disposed(by: disposedBag)

    okButton.rx.tap
      .compactMap { [weak self] in self?.calendarSelectDate }
      .bind(onNext: { [weak self] date in
        self?.calendarViewModel.searchDate = date
        self?.dismiss(animated: true)
      })
      .disposed(by: disposedBag)

  }
}

extension CalenderViewController: CVCalendarViewDelegate, CVCalendarMenuViewDelegate {

  func presentationMode() -> CalendarMode {
    return .monthView
  }

  func firstWeekday() -> Weekday {
    return .monday
  }

  func didSelectDayView(_ dayView: DayView, animationDidFinish: Bool) {
    let date = dayView.date.convertedDate() ?? Date()
    calendarSelectDate = SearchDateModel(
      year: date.intDate().year, month: date.intDate().month, day: date.intDate().day)
  }

  func presentedDateUpdated(_ date: CVDate) {
    var month = "\(date.month)"
    var day = "\(date.day)"
    if date.month < 10 { month = "0" + month }
    if date.day < 10 { day = "0" + day }
    selectDateLabel.text = "\(date.year)-\(month)-\(day)"
  }
}

extension CalenderViewController: CVCalendarViewAppearanceDelegate {
  func dayOfWeekFont() -> UIFont {
    return .body1
  }

  func dayOfWeekTextColor() -> UIColor {
    return .mainBlack
  }

  func dayLabelWeekdayFont() -> UIFont {
    return .sub1
  }

  func dayLabelWeekdayInTextColor() -> UIColor {
    return .mainBlack
  }
}
