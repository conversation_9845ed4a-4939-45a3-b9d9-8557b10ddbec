//
//  HubDrivingInfoView.swift
//  Hub
//
//  Created by ncn on 2/25/25.
//
import UIKit

class HubDrivingInfoView: UIView {
  override init(frame: CGRect) {
    super.init(frame: .zero)
    setupView()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  func setupView() {
    self.backgroundColor = .white
    
    VStackView(spacing: 5) {
      HStackView(spacing: 5) {
        InfoValueView(type: .distance).withSize(.init(width: 161, height: 30))
        InfoValueView(type: .topSpeed).withSize(.init(width: 161, height: 30))
      }
      
      HStackView(spacing: 5) {
        InfoValueView(type: .impactRecord).withSize(.init(width: 161, height: 30))
        InfoValueView(type: .avgSpeed).withSize(.init(width: 161, height: 30))
      }
    }
  }
  
  
}

class RandomView: UIView {
  override init(frame: CGRect) {
    super.init(frame: frame)
    backgroundColor = .random
    self.roundCorners(.allCorners, radius: 6)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
    
}


class InfoValueView: UIView {
  enum InfoType {
    case distance
    case topSpeed
    case avgSpeed
    case impactRecord
    
    var title: String {
      switch self {
      case .distance:
        L.distance_drive_txt.localized
      case .topSpeed:
        L.top_speed_txt.localized
      case .avgSpeed:
        L.average_speed_txt.localized
      case .impactRecord:
        L.shock_recording_txt.localized
      }
    }
    
    var value: String {
      let speedUnit = Current.speedUnit
      
      switch self {
      case .distance:
        return "\(0) " + (speedUnit == .kph ? L.unit_kilo.localized : L.unit_mile.localized)
      case .topSpeed:
        return "\(0) " + (speedUnit == .kph ? L.unit_kmh.localized : L.unit_mph.localized)
      case .avgSpeed:
        return "\(0) " + (speedUnit == .kph ? L.unit_kmh.localized : L.unit_mph.localized)
      case .impactRecord:
        return String(format: L.shock_recording_value.localized, "\(0)")
      }
    }
  }

  lazy var titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .body5
    label.text = L.history_top_speed.localized
    label.sizeToFit()
    label.textAlignment = .left
    return label
  }()

  lazy var valueLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .body5
    label.text = "100 km/h"
    label.sizeToFit()
    label.textAlignment = .right
    return label
  }()

  
  public init(type: InfoType) {
    super.init(frame: .zero)
    backgroundColor = .background
    self.roundCorners(.allCorners, radius: 6)
    
    titleLabel.text = type.title
    valueLabel.text = type.value
    titleLabel.sizeToFit()
    valueLabel.sizeToFit()
    
    lLogger.debug("titleLabel: \(self.titleLabel.text ?? "")")
    
    HStackView {
      titleLabel
      HSpacer()
      valueLabel
    }.withMargins(.horizontal(6))
  }
    
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

