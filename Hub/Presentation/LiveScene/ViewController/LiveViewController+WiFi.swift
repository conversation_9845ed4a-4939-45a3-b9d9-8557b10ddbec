//
//  LiveViewController+WiFi.swift
//  Hub
//
//  Created by ncn on 2023/02/24.
//

import RxCocoa
import RxSwift
import RxDataSources
import UIKit

import OSLog

extension LiveViewController {
  func updateWifiConstraint() {
    let height = playerContainerView.baseHeight + 60  // menu
    playerContainerView.snp.makeConstraints { make in
      make.height.equalTo(height)
    }
    
    let channelItems = AppManager.shared.deviceInfo?.vchannelbits?.attachChannel.displayNames ?? [L.channel_front.localized]
    lLogger.info("channelItems: \(channelItems)")
    let sheetItem = SheetMenuModel(title: L.channel_select_txt.localized, items: channelItems)
    sheetMenuViewController.setItems(model: sheetItem)
    sheetMenuViewController.tableView.selectRow(
      at: IndexPath(row: 0, section: 0),
      animated: false,
      scrollPosition: .none
    )
  }

  func showCloseMessage(title: String, message: String) {
    let alert = UIAlertController(
      title: title,
      message: message,
      preferredStyle: UIAlertController.Style.alert)

    let action = UIAlertAction(title: L.exit_app.localized, style: .default) { action in
      self.viewModel?.dismiss()
    }

    let cancel = UIAlertAction(title: L.cancel.localized, style: UIAlertAction.Style.cancel)
    alert.addAction(action)
    alert.addAction(cancel)
    self.present(alert, animated: true)
  }

}

extension LiveViewController {
  func bindViewModel(to streamViewModel: LiveStreamViewModel?) {
    guard let model = self.streamViewModel else { return }

    if let playerView = playerContainerView.playerView as? VLCPlayerView {
      model.vlcPlayerView = playerView
    }
    
    let willAppearEvent = self.rx.viewWillAppear
    let disAppearEvent = self.rx.viewWillDisappear

    let button = self.playerContainerView.handlerView.playButton
    let playButtonEvent = button.rx.tap.asObservable()
    let channelButtonEvent = playerContainerView.handlerView.controlBarView.channelButton.rx
      .tap.asObservable()
    let changeChannelEvent = sheetMenuViewController.tableView.rx.itemSelected.map { $0.row }
      .asObservable()


    let snapShotButtonLandscapeEvent = playerContainerView.handlerView.controlBarView.snapButton.rx
      .tap.asObservable()
    let snapShotButtonEvent = Observable.merge([
      snapShotButtonLandscapeEvent,
    ]).map { [weak self] in self?.playerContainerView.playerView?.takeScreenshot() }
      .asObservable()

    let playerView = self.playerContainerView.playerView as! VLCPlayerView
    let rxState = playerView.rxIsState
    let progressValue = playerView.rxTime.asObserver()

//    let traceButtonEvent = mapView.titleView.refreshButton.rx.tap.asObservable()

    let input = LiveStreamViewModel.Input(
      willAppearEvent: willAppearEvent,
      willDisappearEvent: disAppearEvent,
      playButtonEvent: playButtonEvent,
      channelButtonEvent: channelButtonEvent,
      changeChannelEvent: changeChannelEvent,
      snapShotButtonEvent: snapShotButtonEvent,
      rtspPlayerState: rxState,
      progressTime: progressValue
//      traceButtonEvent: traceButtonEvent
    )

    let (output, state) = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxUrl
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] url in
        guard let self = self else { return }
        DispatchQueue.main.async {
          self.playerContainerView.openStream(url: url)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsPause
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isPause in
        guard let self = self else { return }

        let player = self.playerContainerView.playerView
        isPause ? player?.pause() : player?.play()
        button.isSelected = isPause

      })
      .disposed(by: self.disposedBag)

    output.rxIsStart
      .observe(on: MainScheduler.asyncInstance)
      .do(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view?.closeIndicator()
      })
      .delay(.milliseconds(3500), scheduler: MainScheduler.asyncInstance)
      .subscribe(onNext: { _ in
        LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxIsBuffering
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view?.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsStop
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view?.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxIsError
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
        view?.closeIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxError
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] error in
        guard let self = self,
          let customError = error as? NCError
        else { return }
        let message = customError.failureReason
        Log.message(category: .App, to: error)
        DispatchQueue.main.async {
          self.playerContainerView.playerView?.closeIndicator()
          LKPopupView.popup.hideLoading()
          LKPopupView.popup.toast(hit: message)
        }

      })
      .disposed(by: self.disposedBag)

    output.rxIsVCIndicator
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { isShow in
        if isShow {
//          LKPopupView.popup.loading()
        } else {
          LKPopupView.popup.hideLoading()
        }
      })
      .disposed(by: disposedBag)

    output.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        let view = self.playerContainerView.playerView
//        LKPopupView.popup.loading()
        view?.showIndicator()
      })
      .disposed(by: self.disposedBag)

    output.rxTime
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] time in
        guard let self = self else { return }
//        self.playerContainerView.handlerView.controlBarView.progressTime = time
      })
      .disposed(by: self.disposedBag)

    output.rxIsSelectMessage
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        DispatchQueue.main.async {
          self.showCloseMessage(title: "", message: msg)
        }

      })
      .disposed(by: self.disposedBag)

    output.rxIsCloseStream
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isClose in
        guard let self = self else { return }
        if isClose {
          self.playerContainerView.closeStream()
          DispatchQueue.main.async {
            self.viewModel?.dismiss()
          }
        }
      })
      .disposed(by: self.disposedBag)

    output.rxGsensorStream
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] (gsensor, isTrace) in
        DispatchQueue.main.async {
//          self?.mapView.titleView.refreshButton.isSelected = isTrace
//          self?.mapView.disableView.isHidden = !isTrace
          self?.mapView.setLocation(model: gsensor.gps.toUdgModel(), isTrace: isTrace)
          self?.graphContainView.setData(wifiLive: gsensor)
        }
      })
      .disposed(by: disposedBag)

    state.currentChannel
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] channel in
        guard let self else { return }
        lLogger.info("channel: \(channel)")
        playerContainerView.handlerView.controlBarView.currentChannel = channel
      })
      .disposed(by: disposedBag)

    state.isShowChannel
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isShow in
        guard let self else { return }
        
        // 채널 선택 시트를 보여주려고 할 때 채널이 1개만 있는지 확인
        if isShow && Current.s1Channel == .F {
          LKPopupView.popup.hideLoading()
          LKPopupView.popup.toast(hit: L.video_channels_error.localized)
          return
        }
        
        isShow ? self.sheetMenuViewController.show() : self.sheetMenuViewController.hide()
      })
      .disposed(by: disposedBag)

    self.rx.viewWillDisappear
      .subscribe(onNext: { [weak self] _ in
        self?.disposedBag = DisposeBag()
      })
      .disposed(by: self.disposedBag)

    output.rxToastSnapShot
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: L.viewer_snapshot_save.localized)
      })
      .disposed(by: disposedBag)
  }
}

extension LiveViewController {
  func bindViewModel(to viewModel: LiveCommandViewModel?) {
    guard let model = viewModel else { return }
    let input = LiveCommandViewModel.Input(
      viewDidLoadEvent: Observable.just(())
    )

    let output = model.bind(input: input, disposedBag: disposedBag)
    
    output.listItem
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] value in
        DispatchQueue.main.async {
          if value.count > 0 {
            // TODO(HUB): 주행정보 업데이트
            lLogger.trace("# live info: \(value[0].items[0].item) ,\(value[0].items[0].value)")
            lLogger.trace("# live info: \(value[0].items[1].item) ,\(value[0].items[1].value)")
            lLogger.trace("# live info: \(value[0].items[2].item) ,\(value[0].items[2].value)")
          }
//          self?.drivingInfoView.
        }
      })
      .disposed(by: self.disposedBag)
  }
}

// MARK: - ADAS
#if HUB
extension LiveViewController {
  func bindViewModel(to viewModel: AdasViewModel?) {
    guard let model = viewModel else { return }

    let landscapeAdasButtonEvent = playerContainerView.handlerView.controlBarView.adasButton.rx.tap
      .asObservable()
    let okButtonEvent = adasView.menuView.checkButton.rx.tap
      .compactMap { [weak self] in self?.adasView.getData() }
      .asObservable()
    let helpButtonEvent = adasView.menuView.helpButton.rx.tap.asObservable()
    let closeButtonEvent = adasView.menuView.backButton.rx.tap.asObservable()

    let showSettingEvent = adasView.menuView.settingButton.rx.tap.asObservable()
    let isAllEvent = adasView.settingView.rxIsAllOn.asObservable()

    let okSettingEvent = adasView.settingView.okbutton.rx.tap
      .withLatestFrom(adasView.settingView.isOnSwitch.rx.isOn)
      .asObservable()
    let closeSettingEvent = adasView.settingView.cancelButton.rx.tap.asObservable()

    let input = AdasViewModel.Input(
      adasButtonEvent: landscapeAdasButtonEvent,
      landscapeAdasButtonEvent: landscapeAdasButtonEvent,
      adasOkButtonEvent: okButtonEvent,
      adasCloseButtonEvent: closeButtonEvent,
      adasHelpButtonEvent: helpButtonEvent,
      showAdasTriggerEvent: showSettingEvent,
      triggerOkButtonEventWithIsAll: okSettingEvent,
      closeAdasTriggerEvent: closeSettingEvent,
      rxUpdateMovePositionWithChannel: adasView.rxMoveRatioWithChannel.asObservable(),
      isAllEvent: isAllEvent,
      rxUpdateSettingModel: adasView.settingView.rxUpdate.asObserver())

    let output = model.bind(input: input, disposedBag: self.disposedBag)
    output.rxIsShowAdas
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isValue in
        guard let self = self else { return }

          self.navigationController?.isNavigationBarHidden = isValue
          self.tabBarController?.tabBar.isHidden = isValue

          self.playerContainerView.handlerView.isHidden = true
          AppUtility.lockOrientation(isValue ? .landscapeRight : .portrait)
          self.setOrientaion(isLandscape: isValue)
          self.setOrientaionViews(isLandscape: isValue)
          self.showAdasView(isShow: isValue)
      })
      .disposed(by: self.disposedBag)

    output.rxIsSaveAdas
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: showAdasView)
      .disposed(by: disposedBag)

    output.rxShowAdasHelp
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] in
        guard let self = self else { return }
        self.adasHelpController.start(in: .currentWindow(of: self))
      })
      .disposed(by: disposedBag)

    output.rxIsShowMessage
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] msg in
//        self?.showToast(message: msg)
        LKPopupView.popup.debugToast(hit: msg)
      })
      .disposed(by: disposedBag)

    output.rxShowCompletePopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] msg in
        self?.popup.showPopup(
          title: L.notification_text.localized, desc: msg, isCancel: false, confirmAction: {}
        )
      })
      .disposed(by: disposedBag)

    output.rxAdasSetupModel
      .asObservable()
      .subscribe(onNext: { [weak self] model in
        guard let self = self else { return }
        self.currentChannel = model.channel
        DispatchQueue.main.async {
          self.adasView.setData(model: model, currentChannel: model.channel)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsShowAdasTrigger
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isValue in
        guard let self = self else { return }
        isValue
          ? self.adasView.showSettingView()
          : self.adasView.closeSettingView()

      })
      .disposed(by: self.disposedBag)

    output.rxUpdateItems
      .do(onNext: { [weak self] data in
        self?.adasView.settingView.listItem = data
      })
      .bind(onNext: { data in
        output.rxReloadTable.onNext(())
      })
      .disposed(by: disposedBag)

    output.rxReloadTable
      .bind(to: adasView.settingView.rxUpdateTable)
      .disposed(by: self.disposedBag)

    output.rxReloadCell
      .do(onNext: { [weak self] _ in
        guard let viewModel = viewModel else { return }
        self?.adasView.settingView.listItem = viewModel.settings
      })
      //            .delay(.milliseconds(100), scheduler: MainScheduler.asyncInstance)
      .observe(on: MainScheduler.asyncInstance)
      .bind(to: adasView.settingView.rxUpdateCell)
      .disposed(by: self.disposedBag)

    output.rxIsIndicator
      .bind(onNext: { value in
        value ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

  }

  func showAdasView(isShow: Bool) {
    adasView.channel = currentChannel
    adasView.isHidden = !isShow
    isAdas = isShow
    if currentChannel == 1 {  // 전면
      //            adasView.centerTouchView.
    } else {  // 후면

    }
  }
}

extension LiveViewController: CoachMarksControllerDataSource, CoachMarksControllerDelegate {
  func coachMarksController(
    _ coachMarksController: Instructions.CoachMarksController, coachMarkViewsAt index: Int,
    madeFrom coachMark: Instructions.CoachMark
  ) -> (
    bodyView: (UIView & Instructions.CoachMarkBodyView),
    arrowView: (UIView & Instructions.CoachMarkArrowView)?
  ) {
    switch index {
    case 0:
      let coachViews = coachMarksController.helper.makeDefaultCoachViews(
        withArrow: false, hintText: L.msg_adas_1.localized)
      return coachViews
    case 1:
      let coachViews = coachMarksController.helper.makeDefaultCoachViews(
        withArrow: false, hintText: L.msg_adas_5.localized)
      let height = min(UIScreen.width, UIScreen.height)
      let move = CGAffineTransform(translationX: 0, y: -height / 2)
      coachViews.bodyView.transform = move
      return coachViews
    case 2:
      let coachViews = coachMarksController.helper.makeDefaultCoachViews(
        withArrow: false, hintText: L.msg_adas_7.localized)
      return coachViews
    default:
      let coachViews = coachMarksController.helper.makeDefaultCoachViews(
        withArrow: false, hintText: L.msg_adas_1.localized)
      return coachViews
    }
  }

  func coachMarksController(
    _ coachMarksController: Instructions.CoachMarksController, coachMarkAt index: Int
  ) -> Instructions.CoachMark {
    switch index {
    case 0:
      return coachMarksController.helper.makeCoachMark(for: adasView.menuView.helpButton)
    case 1:
      // centerImage의 기준으로 만들기
      return coachMarksController.helper.makeCoachMark(for: adasView.centerImage)

    case 2:
      return coachMarksController.helper.makeCoachMark(for: adasView.bonnetImage)
    default:
      return coachMarksController.helper.makeCoachMark(for: adasView.menuView.helpButton)
    }
  }

  func numberOfCoachMarks(for coachMarksController: Instructions.CoachMarksController) -> Int {
    return 3
  }

}
#endif
