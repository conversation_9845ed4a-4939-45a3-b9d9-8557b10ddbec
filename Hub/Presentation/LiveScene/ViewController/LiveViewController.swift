//
//  MainViewController.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import CoreLocation
import RxCocoa
import RxRelay
import RxSwift
import UIKit

class LiveViewController: UIViewController, Orientable {
  let streamType: StreamType
  var playerContainerView: LivePlayerContainerView
  let mapView = MapContainView()
  
  let toggleInfoButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "live_info.pdf"), for: .normal)
    button.isHidden = false
    return button
  }()

  let liveInfoContainerView: UIView = {
    let view = UIView()
    view.backgroundColor = .white
    view.isUserInteractionEnabled = true
    view.roundCorners(.allCorners, radius: 5)
    return view
  }()
  
  let drivingInfoView = HubDrivingInfoView()
  let graphContainView = GSensorContainView()
  let titleView = ContentTitleView(type: .driveInfo)
  let closeGraphViewButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "circle_close.pdf"), for: .normal)
    button.isHidden = false
    return button
  }()
  
  lazy var sheetMenuViewController: SheetViewController = {
    let viewController = SheetViewController()
    let viewModel = SheetMenuViewModel()
    viewController.viewModel = viewModel
    return viewController
  }()

  var viewModel: LiveViewModel?

  var streamViewModel: LiveStreamViewModel?
  var commandViewModel: LiveCommandViewModel?

  var isAdas = false
  var isButtonAction = false
  var isFirst = false
  var currentChannel = 1

  var sx: CGFloat = 1
  var sy: CGFloat = 1

  var progressTimer: Timer?
  var progressSeconds: Int = 0

  init(_ streamType: StreamType = .hls) {
    self.streamType = streamType
    self.playerContainerView = LivePlayerContainerView(type: streamType)
    super.init(nibName: nil, bundle: nil)
  }

  var disposedBag = DisposeBag()

  required init?(coder aDecoder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  deinit {
    if progressTimer != nil {
      progressTimer?.invalidate()
      progressTimer = nil
      progressSeconds = 0
    }

    aLogger.info("deinit \(Self.self)")
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    self.navigationItem.title = "Live View"
    navigationItem.backButtonTitle = ""

    setComponent()
    setAutoLayout()

    bindViewModel(to: viewModel)
#if HUB
    bindViewModel(to: rtcViewModel)
    bindViewModel(to: signalingViewModel)
    bindViewModel(to: cloudViewModel)
    bindViewModel(to: adasViewModel)
#endif
    if Current.isConnect {
      bindViewModel(to: streamViewModel)
      bindViewModel(to: commandViewModel)
    }
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    tabBarController?.toggleTabbar(isShow: true)
  }
  
  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    
    AppUtility.lockOrientation(.portrait)
    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      if self.isFirst == true {
        return
      }
      self.isFirst = true
//      self.scrollView.updateContentSize()
    }
    Log.message(to: streamType)

    switch streamType {
    case .hls, .rtc:
      updateWifiConstraint()
      break
    default:
      break
    }
  }

  func setComponent() {
    view.addSubview(playerContainerView)
    view.addSubview(mapView)
    
    mapView.addSubview(toggleInfoButton)
    mapView.addSubview(liveInfoContainerView)
    
    liveInfoContainerView.addSubview(titleView)
    liveInfoContainerView.addSubview(graphContainView)
    liveInfoContainerView.addSubview(drivingInfoView)
    liveInfoContainerView.addSubview(closeGraphViewButton)
    
    /// add action
    titleView.drivingTitleButton.addAction(UIAction { [weak self] _ in
      self?.titleView.type = .driveInfo
      self?.graphContainView.isHidden = true
      self?.drivingInfoView.isHidden = false
    }, for: .touchUpInside)

    titleView.gSensorTitleButton.addAction(UIAction { [weak self] _ in
      self?.titleView.type = .gSensor
      self?.graphContainView.isHidden = false
      self?.drivingInfoView.isHidden = true
    }, for: .touchUpInside)

    toggleInfoButton.addAction(UIAction { [weak self] _ in
      self?.liveInfoContainerView.isHidden = false
    }, for: .touchUpInside)
    
    closeGraphViewButton.addAction(UIAction { [weak self] _ in
      self?.liveInfoContainerView.isHidden = true
    }, for: .touchUpInside)
  }

  func setAutoLayout() {
    let height = playerContainerView.baseHeight
    playerContainerView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top)
      make.left.right.equalToSuperview()
      make.height.equalTo(height)
    }

    mapView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.playerContainerView.snp.bottom).offset(0)
      make.left.equalTo(self.view).offset(0)
      make.right.equalTo(self.view).offset(0)
      make.bottom.equalTo(self.self.view.safeAreaLayoutGuide.snp.bottom)
    }

    Pin.activate([
      toggleInfoButton.pin.size(48).start(offset: 14).bottom(offset: -45.rv),
      liveInfoContainerView.pin.height(110.rv).horizontally(offset: 12).bottom(offset: -12),
      titleView.pin.top(offset: 8.rv).start(offset: 8).height(24.rv).width(146.wrv),
      closeGraphViewButton.pin.size(20).top(offset: 8.rv).end(offset: -8),
      graphContainView.pin.below(titleView, offset: -6.rv).horizontally(offset: 5).bottom(offset: -8.rv),
      drivingInfoView.pin.below(titleView, offset: 8.rv).horizontally(offset: 5).bottom(offset: -8.rv),
    ])
  }

  // MARK: - LiveViewModel
  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? LiveViewModel else { return }
    let orientationEvent = playerContainerView.handlerView.sizeButton.rx.tap.asObservable()

    let input = LiveViewModel.Input(
      willDisappearEvent: self.rx.viewWillDisappear,
      orientationTouchEvent: orientationEvent
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsBack
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        self.viewModel?.dismiss()
      })
      .disposed(by: self.disposedBag)

    output.rxIsLandscape
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] isValue in
        guard let self = self else { return }

        self.isButtonAction = true
        AppUtility.lockOrientation(isValue ? .landscapeRight : .portrait)
        self.setOrientaion(isLandscape: isValue)
        self.setOrientaionViews(isLandscape: isValue)

      })
      .disposed(by: self.disposedBag)


    output.rxToastPaste
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        LKPopupView.popup.toast(hit: msg)
      })
      .disposed(by: disposedBag)

    output.rxToastSnapShot
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: L.viewer_snapshot_save.localized)
      })
      .disposed(by: disposedBag)

    // revers
    playerContainerView.handlerView
      .controlBarView
      .hReversButton
      .rx.tap
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.sx = self.sx * -1
        self.playerContainerView.playerView?.rotation(sx: self.sx, sy: self.sy)
      })
      .disposed(by: disposedBag)

    playerContainerView.handlerView
      .controlBarView
      .vReversButton
      .rx.tap
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.sy = self.sy * -1
        self.playerContainerView.playerView?.rotation(sx: self.sx, sy: self.sy)
      })
      .disposed(by: disposedBag)
  }

}

extension LiveViewController {
  internal func setOrientaionViews(isLandscape: Bool) {

    self.navigationController?.isNavigationBarHidden = isLandscape
    tabBarController?.toggleTabbar(isShow: !isLandscape)
    playerContainerView.handlerView.updateLayout(isLandscape)

    mapView.isHidden = isLandscape

    let height = isLandscape ? playerContainerView.baseWidth : playerContainerView.baseHeight

    if isLandscape == true {
      playerContainerView.snp.remakeConstraints { [weak self] make in
        guard let self = self else { return }
        make.edges.equalTo(self.view)
      }
    } else {
      playerContainerView.snp.remakeConstraints { [weak self] make in
        guard let self = self else { return }
        make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top)
        make.left.right.equalTo(self.view)
        make.height.equalTo(height)
      }
    }

    playerContainerView.setOrientaionViews(isLandscape: isLandscape)
  }

  override func viewWillTransition(
    to size: CGSize,
    with coordinator: UIViewControllerTransitionCoordinator
  ) {
    if isAdas == true { return }
    if isButtonAction == true {
      isButtonAction = false
      return
    }

    let orientation = UIDevice.current.orientation
    switch orientation {
    case .unknown, .portrait, .portraitUpsideDown, .faceUp, .faceDown:
      setOrientaionViews(isLandscape: false)
      self.viewModel?.isSelected = false
      break
    case .landscapeLeft, .landscapeRight:
      setOrientaionViews(isLandscape: true)
      self.viewModel?.isSelected = true
      break
    default:
      break
    }
  }
}
