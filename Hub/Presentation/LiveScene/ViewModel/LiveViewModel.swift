//
//  LiveViewModel.swift
//  Hub
//
//  Created by ncn on 2023/02/21.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

class LiveViewModel: BaseViewModel {
  weak var coordinator: LiveCoordinator?

  var isSelected: Bool = false

  struct Input {
    let willDisappearEvent: ControlEvent<Bool>
    let orientationTouchEvent: Observable<Void>
  }

  struct Output {
    let rxIsBack = PublishRelay<Bool>()
    let rxIsLandscape = PublishRelay<Bool>()  // 0: port, 1: land
    let rxToastPaste = PublishSubject<String>()
    let rxToastSnapShot = PublishSubject<Void>()
  }

  init(coordinator: LiveCoordinator) {
    self.coordinator = coordinator
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.willDisappearEvent
      .scan(false) { (lastState, newValue) in
        !lastState
      }
      .bind(to: output.rxIsBack)
      .disposed(by: disposedBag)

    input.orientationTouchEvent
      .scan(into: false) { [weak self] (lastState, newValue) in
        lastState = !lastState
        //self.isSelected = lastState
        if self?.isSelected == lastState {
          lastState = !lastState
          self?.isSelected = lastState
        } else {
          self?.isSelected = lastState
        }
      }
      .bind(to: output.rxIsLandscape)
      .disposed(by: disposedBag)

    return output
  }
}

// MARK: - Navigation
extension LiveViewModel {
  func dismiss() {
    aLogger.info("@@ liveDidFinish")
    coordinator?.navigate(to: .liveDidFinish)
  }
}
