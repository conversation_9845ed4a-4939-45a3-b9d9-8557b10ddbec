//
//  LiveCommandViewModel.swift
//  Hub
//
//  Created by ncn on 2023/04/06.
//

import CoreLocation
import RxCocoa
import RxSwift
import UIKit

class LiveCommandViewModel: NSObject {
  private let useCase: LiveUseCase
  private let homeUseCase: HomeUseCase

  private let locationManager = CLLocationManager()

  //  var drivingItem: [DrivingCellSectionModel]?
  //  var listItem = BehaviorRelay<[DrivingCellSectionModel]>(value: [])

  let myLocation = PublishSubject<CLLocationCoordinate2D>()

  init(useCase: LiveUseCase, homeUseCase: HomeUseCase) {
    self.useCase = useCase
    self.homeUseCase = homeUseCase
  }

  struct Input {
    let viewDidLoadEvent: Observable<Void>
//    let tapUpdateLocation: Observable<Void>
  }

  struct Output {
    let listItem = BehaviorSubject<[DrivingCellSectionModel]>(value: [])
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    locationManager.delegate = self
    let output = Output()

    let viewDidLoad = input.viewDidLoadEvent
      .share()

    viewDidLoad
      .bind(onNext: { [weak self] in
        self?.locationManager.requestLocation()
      })
      .disposed(by: disposedBag)

    viewDidLoad
      .delay(.seconds(2), scheduler: MainScheduler.instance)
      .flatMapLatest { _ in
        Observable<Int>.timer(.seconds(0), period: .seconds(5), scheduler: MainScheduler.instance)
      }
      .map { _ in return }
      .flatMapLatest(rxRequestGetLiveInfo)
      .compactMap { [weak self] history in self?.makeDrivingInfo(history: history) }
      .bind(to: output.listItem)
      .disposed(by: disposedBag)

//    input.tapUpdateLocation
//      .compactMap { [weak self] in self?.locationManager.location?.coordinate }
//      .bind(onNext: { [weak self] coordinate in
//        self?.myLocation.onNext(coordinate)
//        self?.locationManager.requestLocation()
//      })
//      .disposed(by: disposedBag)

    return output
  }

}
extension LiveCommandViewModel: CLLocationManagerDelegate {
  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    if let location = locations.first {
      self.myLocation.onNext(location.coordinate)
    }
  }

  func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    Log.warning(to: "Failed to find user's location: \(error.localizedDescription)")
  }
}

// MARK: - call socket
extension LiveCommandViewModel {
  func requestGetInfo() {
    let header = HeaderModel(cmd: Command.getInformation)
    let send = GetInformation.Send(header: header)
    self.useCase.send(to: send) { response, error in
      Log.message(to: "\(String(describing: response)), error: \(String(describing: error))")
    }
  }

//  func rxRequestGetInitInfo() -> Observable<GetInitInfoModel?> {
//    let header = HeaderModel(cmd: Command.getInitInfo)
//    let send = GetInitInfo.Send(header: header)
//    return Observable.create { [weak self] emitter in
//      self?.homeUseCase.send(to: send) { response, error in
//        if let error = error {
//          emitter.onError(error)
//        } else {
//          emitter.onNext(response?.getInitInfo)
//          emitter.onCompleted()
//        }
//      }
//      return Disposables.create()
//    }
//  }

  func rxRequestGetLiveInfo() -> Observable<GetInitInfoHistoryModel?> {
    let header = HeaderModel(cmd: Command.getLiveInfo)
    let send = GetLiveInfoModel.Send(header: header)
    return Observable.create { [weak self] emitter in
      self?.homeUseCase.send(to: send) { result in
        switch result {
        case .success(let model):
          Log.message(
            category: .WebSocket, to: "model: \(model.getliveinfo?.history.debugDescription ?? "")")
          emitter.onNext(model.getliveinfo?.history)
        case .failure(let error):
          Log.error(category: .WebSocket, to: "error: \(error)")
          emitter.onNext(nil)
        }
        emitter.onCompleted()
      }
      return Disposables.create()
    }
  }
}

extension LiveCommandViewModel {
  func makeDrivingInfo(history: GetInitInfoHistoryModel?)
    -> [DrivingCellSectionModel]
  {
    guard let duration = history?.duration,
      let distance = history?.distance,
      let speedMax = history?.speedMax,
      let speedAvg = history?.speedAvg,
      let eventNum = history?.eventNum
    else { return [] }
    
    let durationString = duration.getDurationString()
    let distanceString = "\(distance) " + Current.speedUnit.title
    let speedMaxString = "\(speedMax) " + Current.speedUnit.title
    let speedAvgString = "\(speedAvg) " + Current.speedUnit.title
    let eventNumString = String(format: L.shock_recording_value.localized, "\(eventNum)")

    var array: [DriveingCellModel] = []
    if AppManager.shared.dashcam?.drivemode == "Parking" {
      array.append(contentsOf: [
        DriveingCellModel(
          image: "03_icon1", item: L.history_park_recording_time.localized, value: durationString),
        DriveingCellModel(
          image: "03_icon4", item: L.history_park_shock_event.localized, value: eventNumString),
      ])
    } else {
      array.append(contentsOf: [
        DriveingCellModel(
          image: "03_icon1", item: L.driving_time_txt.localized, value: durationString),
        DriveingCellModel(
          image: "03_icon2", item: L.distance_drive_txt.localized, value: distanceString),
        DriveingCellModel(
          image: "03_icon3", item: L.top_speed_txt.localized, value: speedMaxString),
        DriveingCellModel(
          image: "03_icon3", item: L.average_speed_txt.localized, value: speedAvgString),
        DriveingCellModel(
          image: "03_icon4", item: L.shock_recording_txt.localized, value: eventNumString),
      ])
    }
    let section = DrivingCellSectionModel(sectionType: .driving, items: array)
    return [section]
  }
}
