//
//  LiveStreamViewModel.swift
//  Hub
//
//  Created by ncn on 2023/02/24.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

// 에러 발생시 인디게이터 처리
class LiveStreamViewModel: BaseViewModel {
  let useCase: StreamUseCase
  var currentChannel: Int = 1  // 1: 전방, 2: 후방, 3: 실내
  var startTime: Int = -1
  var vlcPlayerView: VLCPlayerView?
  
  struct State {
    let currentChannel = BehaviorRelay<Int>(value: 1)
    let isShowChannel = BehaviorRelay<Bool>(value: false)
    let isMapTrace = BehaviorRelay<Bool>(value: true)
  }

  struct Input {
    let willAppearEvent: ControlEvent<Bool>
    let willDisappearEvent: ControlEvent<Bool>

    let playButtonEvent: Observable<Void>

    let channelButtonEvent: Observable<Void>
    let changeChannelEvent: Observable<Int>

    let snapShotButtonEvent: Observable<UIImage?>

    let rtspPlayerState: Observable<RTSPPlayerState>
    let progressTime: Observable<Int>
//    let traceButtonEvent: Observable<Void>
  }

  struct Output {
    let rxIsPause = PublishRelay<Bool>()  // 1: pause, 0: play
    let rxIsStart = PublishSubject<Bool>()
    let rxIsBuffering = PublishSubject<Bool>()
    let rxIsStop = PublishSubject<Bool>()
    let rxIsError = PublishSubject<Bool>()  // state
    let rxUrl = PublishSubject<String>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxError = PublishSubject<Error>()  //player message

    let rxCurrentChannel = PublishSubject<Int>()  //player message

    let rxIsVCIndicator = PublishSubject<Bool>()

    let rxIsSelectMessage = PublishSubject<String>()
    let rxIsShowMessage = PublishSubject<String>()
    let rxIsCloseStream = PublishSubject<Bool>()
    let rxTime = PublishSubject<Int>()
    let rxGsensorStream = PublishSubject<(GSensorWifiLiveModel, Bool)>()

    let rxToastSnapShot = PublishSubject<Void>()
  }

  init(useCase: StreamUseCase) {
    self.useCase = useCase
  }

  func bind(input: Input, disposedBag: DisposeBag) -> (Output, State) {
    let state = State()
    let output = Output()

    input.willAppearEvent
      .delay(.seconds(1), scheduler: MainScheduler.instance)
      .subscribe(onNext: { [weak self] _ in
        lLogger.info("willAppearEvent")
        guard let self = self else { return }
        output.rxIsIndicator.onNext(true)
        self.openStreaming(output: output, state: state)
      })
      .disposed(by: disposedBag)

    input.willDisappearEvent
      .subscribe(onNext: { [weak self]_ in
        lLogger.info("willDisappearEvent")
        self?.closeStreaming(output: output)
      })
      .disposed(by: disposedBag)

    input.playButtonEvent
      .scan(false) { (lastValue, newValue) in
        !lastValue
      }
      .bind(to: output.rxIsPause)
      .disposed(by: disposedBag)

    input.channelButtonEvent
      .map { _ in true }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    let channelChanged = input.changeChannelEvent
      .map { $0 + 1 }
      .do(onNext: { _ in output.rxIsVCIndicator.onNext(true) })
      .flatMapLatest(rxChangeChannel)
      .filter { result in
        output.rxIsVCIndicator.onNext(false)
        switch result {
        case .success(_):
          return true
        case .failure(let error):
          lLogger.error("\(error.localizedDescription)")
          return false
        }
      }
      .share()

    channelChanged
      .compactMap { try? $0.get() }
      .bind(to: state.currentChannel)
      .disposed(by: disposedBag)

    channelChanged
      .map { _ in false }
      .bind(to: state.isShowChannel)
      .disposed(by: disposedBag)

    input.rtspPlayerState
      .subscribe(onNext: { [weak self] state in
        guard let self = self else { return }

        self.logging(state: state, output: output)

      })
      .disposed(by: disposedBag)

    input.progressTime
      .subscribe(onNext: { [weak self] time in
        guard let self = self else { return }

        if self.startTime < 0 {
          self.startTime = time
        }
        let ret = time - self.startTime
        output.rxTime.onNext(ret)
      })
      .disposed(by: disposedBag)

//    input.traceButtonEvent
//      .withLatestFrom(state.isMapTrace)
//      .map { !$0 }
//      .bind(to: state.isMapTrace)
//      .disposed(by: disposedBag)

    input.snapShotButtonEvent
      .withLatestFrom(state.currentChannel, resultSelector: { ($0, $1) })
      .subscribe(
        onNext: { [weak self] _, channel in
          guard let self else { return }
          
          if let playerView = vlcPlayerView {
            let liveCaptureName = HubFileNameGenerator.generateLiveFileName(channel: channel)
            guard let path = UrlList.screenshotPath() else {
              lLogger.error("Fail to get path.")
              return
            }
            let capturePath = path.appendingPathComponent(liveCaptureName)
            lLogger.info("LiveCapture channell: \(channel), name: \(capturePath.path)")
            playerView.mediaPlayer.saveVideoSnapshot(at: capturePath.path, withWidth: 0, andHeight: 0)
            
            MainAsync(after: 0.5) {
              self.saveSnapshotToPhotoApp(fileurl: capturePath)
            }
            output.rxToastSnapShot.onNext(())
        }
      })
      .disposed(by: disposedBag)

    return (output, state)
  }
  
  func saveSnapshotToPhotoApp(fileurl: URL) {
    let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
    lLogger.info("saved photo app modelName: \(modelName)")
    guard let image = vlcPlayerView?.mediaPlayer.lastSnapshot else {
      lLogger.warning("vlcPlayer lastSnapshot empty")
      return
    }
    
    PhotoUtils.savePhoto(image: image, toAlbum: modelName, completion: { error in
      guard let error = error else {
        lLogger.info("image photoapp saved: \(fileurl.absoluteString)")
        return
      }
      lLogger.error("error: \(error.localizedDescription)")
    })
  }
  
  func getLiveUniqueName(fileName: String) -> String {
    guard let path = UrlList.screenshotPath() else {
      aLogger.error("Fail to get path.")
      return ""
    }

    let extensionName = fileName.components(separatedBy: ".").last ?? "jpg"

    let noExtensionName =
      fileName
      .replacingOccurrences(of: ".avi", with: "")
      .replacingOccurrences(of: ".mp4", with: "")
      .replacingOccurrences(of: ".png", with: "")
      .replacingOccurrences(of: ".jpg", with: "")
      .replacingOccurrences(of: ".jpeg", with: "")
      .replacingOccurrences(of: ".", with: "")

    var newFileName = noExtensionName + ".\(extensionName)"

    var uniqueFileURL = path.appendingPathComponent(newFileName)
    // 중복 파일 처리
    var index = 1
    while FileManager.default.fileExists(atPath: uniqueFileURL.path) {
      // 중복 파일이 이미 존재하는 경우, 새로운 파일 이름을 생성.
      if noExtensionName.contains("(") {
        let noBracketName = noExtensionName.components(separatedBy: "(").first ?? ""
        newFileName = noExtensionName + "(\(index)).\(extensionName)"
      } else {
        newFileName = noExtensionName + "(\(index)).\(extensionName)"
      }
      uniqueFileURL = path.appendingPathComponent(newFileName)
      index += 1
    }

    return uniqueFileURL.path
  }

}

extension LiveStreamViewModel {
  /*
     d21 로직은 기존동일, URL :  "rtsp://*************:9010/live" 변경, Option: "--rtsp-tcp" 추가, SetProtocol : "RTSP" 변경
     */
  func openStreaming(output: Output, state: State) {
    let header = HeaderModel(cmd: Command.startliveview)
    let message = StartLiveView.Send(header: header, startliveview: StartLiveViewModel())
    useCase.send(to: message) { response, error in
      if let e = error {
        output.rxError.onNext(e)
      } else if let gsensor = response?.gsensorData {
        output.rxGsensorStream.onNext((gsensor, state.isMapTrace.value))
      } else {
        output.rxUrl.onNext("rtsp://*************:9010/live")
      }
    }
  }

  func closeStreaming(output: Output) {
    output.rxIsIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.stopliveview)
    let message = StopLiveView.Send(header: header)
    useCase.send(to: message) { response, error in
      output.rxIsIndicator.onNext(false)
      if let e = error {
        Log.error(category: .WebSocket, to: "\(e.localizedDescription)")
        output.rxIsCloseStream.onNext(false)
        output.rxIsSelectMessage.onNext("스트리밍 종료 실패 되었습니다. 그래도 종료 하시겠습니까?")
      } else {
        lLogger.info("closeStreaming success")
        output.rxIsCloseStream.onNext(true)
      }
    }
  }

  func rxChangeChannel(channel: Int) -> Observable<Result<Int, Error>> {
    //    output.rxIsVCIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.changeliveview)
    let model = ChangeLiveViewModel(video: channel, dewarp: 0)
    let message = ChangeLiveView.Send(header: header, changeliveview: model)
    return Observable.create { [weak self] emitter in
      self?.useCase.send(to: message) { result in
        switch result {
        case .success(_):
          emitter.onNext(.success(channel))
        case .failure(let error):
          emitter.onNext(.failure(error))
        }
      }
      return Disposables.create()
    }
  }
}

extension LiveStreamViewModel {
  private func logging(state: RTSPPlayerState, output: Output) {
    switch state {
    case .playing:
      lLogger.debug("VLCMediaPlayerDelegate: PLAYING")
      output.rxIsStart.onNext(true)
      break
    case .opening:
      lLogger.debug("VLCMediaPlayerDelegate: OPENING")
      break
    case .error:
      output.rxIsError.onNext(true)
      lLogger.debug("VLCMediaPlayerDelegate: ERROR")
      break
    case .buffering:
      output.rxIsBuffering.onNext(true)
      lLogger.debug("VLCMediaPlayerDelegate: BUFFERING")
      break
    case .stopped:
      lLogger.debug("VLCMediaPlayerDelegate: STOPPED")
      output.rxIsStop.onNext(true)
      break
    case .paused:
      lLogger.debug("VLCMediaPlayerDelegate: PAUSED")
      break
    case .ended:
      output.rxIsStop.onNext(true)
      lLogger.debug("VLCMediaPlayerDelegate: ENDED")
      break
    case .esadded:
      output.rxIsStart.onNext(true)
      lLogger.debug("VLCMediaPlayerDelegate: ESADDED")
      break
    default:
      Log.message(to: "state: \(state)")
      break
    }
  }
}
