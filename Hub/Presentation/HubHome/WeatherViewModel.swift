//
//  WeatherViewModel.swift
//  Hub
//
//  Created by ncn on 2023/07/12.
//

import RxCocoa
import RxSwift
import UIKit

// MARK: 영어처리
//let _home_weather_type1 = "드라이브 하기 좋은 쾌적한 날씨입니다."
//let _home_weather_type2 = "비가 예상됩니다. 안전 운행하세요."
//let _home_weather_type3 = "폭우가 예상됩니다. 시야확보에 유의하세요."
//let _home_weather_type4 = "눈이 예상됩니다. 감속 운전하세요."
//let _home_weather_type5 = "구름이 많은 날씨입니다. 안전 운행하세요."
//let _home_weather_type6 = "드라이브 하기 좋은 대체적으로 맑은 날씨입니다."

//let _home_weather_type1 = "It's a nice day to drive."
//let _home_weather_type2 = "Rain is expected. Drive safely."
//let _home_weather_type3 = "We expect heavy rain. Pay attention to securing visibility."
//let _home_weather_type4 = "We expect snow. Slow down."
//let _home_weather_type5 = "It's a cloudy day. Drive safely."
//let _home_weather_type6 = "It's generally sunny for a drive."

enum Weather {
  struct Response: Codable {
    let name: String
    let main: Main
    let wind: Wind
    let weather: [Weather]
  }

  struct Main: Codable {
    let temp: Double
    let humidity: Int
  }

  struct Wind: Codable {
    let speed: Double
    let deg: Int
  }

  struct Weather: Codable {
    let id: Int
    let main: String
    let description: String
    let icon: String
  }
}

struct WeatherModel {
  var image: UIImage?
  let temp: String
  let text: String
  let humidity: String
  let speed: String
}

class WeatherViewModel: NSObject {
  var latitude: Double = 0.0
  var longtitude: Double = 0.0

  var isFirstCall = false

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  struct Input {
    let willApearEvent: ControlEvent<Bool>
    let rxIsUpdateWeather: PublishSubject<Bool>
  }

  struct Output {
    let rxWeatherModel = PublishSubject<WeatherModel>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()
    input.willApearEvent
      .subscribe(onNext: { [weak self] _ in
        self?.requestWeather(output: output)
      })
      .disposed(by: disposedBag)

    input.rxIsUpdateWeather
      .subscribe(onNext: { isValue in
        //        guard let self = self else { return }
        //        if isValue {
        //                    self.requestWeather(output: output)
        //        }
      })
      .disposed(by: disposedBag)
    return output
  }
}

extension WeatherViewModel {
  func toDamin(response: Weather.Response) -> WeatherModel? {
    guard let obj = response.weather.first else {
      Log.message(to: "Not exist weather.")
      return nil
    }

    var image = ""
    var text = ""

    switch obj.icon {
    case "01d", "01n":  // 맑음
      text = L.home_weather_type1.localized
      image = "weather_1"
      break
    case "02d", "02n":  // 구름 조금
      text = L.home_weather_type6.localized
      image = "weather_6"
      break
    case "03d", "03n":  // 흐림
      text = L.home_weather_type5.localized
      image = "weather_5"
      break
    case "04d", "04n":  // 매우 흐림
      text = L.home_weather_type5.localized
      image = "weather_5"
      break
    case "09d", "09n":  // 비
      text = L.home_weather_type2.localized
      image = "weather_2"
      break
    case "10d", "10n":  // 소나기
      text = L.home_weather_type2.localized
      image = "weather_2"
      break
    case "11d", "11n":  // 폭우
      text = L.home_weather_type3.localized
      image = "weather_3"
      break
    case "13d", "13n":  // 눈
      text = L.home_weather_type4.localized
      image = "weather_4"
      break
    case "50d", "50n":  // 안개
      text = L.home_weather_type5.localized
      image = "weather_5"
      break
    default:
      break
    }

    let temp = String(format: "%d", Int(response.main.temp))
    let humidity = String(format: "%d", response.main.humidity)
    let speed = String(format: "%d", Int(response.wind.speed))
    let img = UIImage(named: image)

    let model = WeatherModel(image: img, temp: temp, text: text, humidity: humidity, speed: speed)
    return model
  }

  func requestWeather(output: Output) {
    // TODO: 횟수 제안때문에 일단 테스트 코드 수행
    #if DEVELOP
      let sjonString =
        "{\"coord\":{\"lon\":127.1127,\"lat\":37.3958},\"weather\":[{\"id\":500,\"main\":\"Rain\",\"description\":\"light rain\",\"icon\":\"10d\"}],\"base\":\"stations\",\"main\":{\"temp\":28.87,\"feels_like\":33.18,\"temp_min\":28.53,\"temp_max\":29.83,\"pressure\":1005,\"humidity\":74},\"visibility\":10000,\"wind\":{\"speed\":1.54,\"deg\":120},\"rain\":{\"1h\":0.29},\"clouds\":{\"all\":75},\"dt\":1689141208,\"sys\":{\"type\":1,\"id\":8096,\"country\":\"KR\",\"sunrise\":1689106795,\"sunset\":1689159229},\"timezone\":32400,\"id\":1897000,\"name\":\"Seongnam-si\",\"cod\":200}"

      var dicData: [String: Any] = [String: Any]()
      do {
        dicData =
          try JSONSerialization.jsonObject(with: Data(sjonString.utf8), options: [])
          as! [String: Any]
      } catch {
        Log.error(to: error.localizedDescription)
      }

      let data = Data(sjonString.utf8)
      data.parsing(type: Weather.Response.self) { data, error in
        if let value = data,
          let model = self.toDamin(response: value)
        {
          output.rxWeatherModel.onNext(model)
        }
      }
    #else

      var latitude = self.latitude
      var longtitude = self.longtitude
      if latitude <= 0.0 || longtitude <= 0.0 {
        if let value = AppManager.shared.locationValue {
          latitude = value.latitude
          longtitude = value.longitude
        }
      }

      let param: [String: Any] = [
        "lat": latitude,
        "lon": longtitude,
        "units": "metric",
        "appid": _weather_api_key,
      ]

      let baseUrl = "https://api.openweathermap.org/data/2.5/weather"
      var components = URLComponents(string: baseUrl)!
      components.queryItems = param.map {
        URLQueryItem(name: $0, value: "\($1)")
      }

      var request = URLRequest(url: components.url!)
      request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
      request.setValue("application/json", forHTTPHeaderField: "Accept")
      request.httpMethod = "GET"

      let task = URLSession.shared.dataTask(with: request) { data, response, error in
        guard let data = data,
          let response = response as? HTTPURLResponse,
          error == nil
        else {
          Log.error(to: "error", error ?? URLError(.badServerResponse))
          return
        }

        guard (200...299) ~= response.statusCode else {
          Log.debug(to: "statusCode should be 2xx, but is \(response.statusCode)")
          Log.debug(to: "response = \(response)")
          return
        }

        if let responseString = String(data: data, encoding: .utf8) {
          Log.debug(to: "responseString = \(responseString)")
          //self.setMessage(message: responseString)

          data.parsing(type: Weather.Response.self) { data, error in
            if let value = data,
              let model = self.toDamin(response: value)
            {
              output.rxWeatherModel.onNext(model)
            }
          }
        } else {
          Log.warning(to: "unable to parse response as string")
        }
      }

      task.resume()
    #endif
  }
}
