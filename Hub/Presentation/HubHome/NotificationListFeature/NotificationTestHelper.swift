//
//  NotificationTestHelper.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import Foundation
import UIKit

/**
 * Remote Notification 시스템 테스트 헬퍼
 * 
 * 개발 및 테스트 목적으로 사용하는 유틸리티 클래스입니다.
 */

class NotificationTestHelper {
  
  // MARK: - Test Data Generation
  
  /// 테스트용 알림 데이터 생성
  static func createTestNotifications() -> [NotificationModel] {
    return [
      NotificationModel(
        title: "[test_video_001.mp4] Your request has been processed.",
        content: "AI processing completed successfully",
        fileUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
        fileName: "test_video_001.mp4",
        receivedDate: Date().addingTimeInterval(-3600) // 1시간 전
      ),
      NotificationModel(
        title: "[image_restore_002.jpg] Image restoration completed.",
        content: "License plate restoration finished",
        fileUrl: "https://via.placeholder.com/800x600.jpg",
        fileName: "image_restore_002.jpg",
        receivedDate: Date().addingTimeInterval(-7200) // 2시간 전
      ),
      NotificationModel(
        title: "[parking_video_003.mp4] De-identification process done.",
        content: "Video anonymization completed",
        fileUrl: "https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4",
        fileName: "parking_video_003.mp4",
        receivedDate: Date().addingTimeInterval(-86400) // 1일 전
      )
    ]
  }
  
  /// 테스트용 UserInfo 데이터 생성
  static func createTestUserInfo() -> [AnyHashable: Any] {
    return [
      AnyHashable("fileUrl"): "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
      AnyHashable("title"): "[test_notification.mp4] Your request has been processed.",
      AnyHashable("content"): "Test notification content",
      AnyHashable("fileName"): "test_notification.mp4",
      AnyHashable("aps"): [
        "alert": [
          "body": "Test notification content",
          "title": "[test_notification.mp4] Your request has been processed."
        ]
      ]
    ]
  }
  
  // MARK: - Test Methods
  
  /// UserDefaults에 테스트 알림 추가
  static func addTestNotificationsToUserDefaults() {
    let testNotifications = createTestNotifications()
    var existingNotifications = UserDefaults.shared.notifications
    
    // 중복 제거하면서 추가
    for notification in testNotifications {
      if !existingNotifications.contains(where: { $0.fileUrl == notification.fileUrl }) {
        existingNotifications.append(notification)
      }
    }
    
    UserDefaults.shared.notifications = existingNotifications
    aLogger.info("Added \(testNotifications.count) test notifications to UserDefaults")
  }
  
  /// UserDefaults의 모든 알림 삭제
  static func clearAllNotifications() {
    UserDefaults.shared.notifications = []
    aLogger.info("Cleared all notifications from UserDefaults")
  }
  
  /// 현재 저장된 알림 개수 확인
  static func getNotificationCount() -> Int {
    return UserDefaults.shared.notifications.count
  }
  
  /// 저장된 알림 목록 로그 출력
  static func logAllNotifications() {
    let notifications = UserDefaults.shared.notifications
    aLogger.info("=== Stored Notifications (\(notifications.count)) ===")
    
    for (index, notification) in notifications.enumerated() {
      aLogger.info("[\(index + 1)] \(notification.fileName)")
      aLogger.info("    Title: \(notification.title)")
      aLogger.info("    URL: \(notification.fileUrl)")
      aLogger.info("    Date: \(notification.receivedDate)")
    }
    
    aLogger.info("=====================================")
  }
  
  // MARK: - Simulation Methods
  
  /// Remote Notification 수신 시뮬레이션
  static func simulateRemoteNotification(in viewController: UIViewController) {
    let userInfo = createTestUserInfo()
    
    // NotificationModel 생성
    guard let notification = NotificationModel(from: userInfo) else {
      aLogger.error("Failed to create NotificationModel from userInfo")
      return
    }
    
    // UserDefaults에 저장
    var notifications = UserDefaults.shared.notifications
    if !notifications.contains(where: { $0.fileUrl == notification.fileUrl }) {
      notifications.insert(notification, at: 0)
      UserDefaults.shared.notifications = notifications
      
      aLogger.info("Simulated remote notification: \(notification.fileName)")
      
      // 성공 알림 표시
      let alert = UIAlertController(
        title: "알림 시뮬레이션",
        message: "테스트 알림이 추가되었습니다.\n\(notification.fileName)",
        preferredStyle: .alert
      )
      
      alert.addAction(UIAlertAction(title: "확인", style: .default))
      alert.addAction(UIAlertAction(title: "알림 목록 보기", style: .default) { _ in
        let notificationVC = NotificationListViewController()
        let navController = UINavigationController(rootViewController: notificationVC)
        viewController.present(navController, animated: true)
      })
      
      viewController.present(alert, animated: true)
    } else {
      aLogger.info("Duplicate notification ignored in simulation")
    }
  }
  
  /// NotificationListViewController 직접 표시
  static func presentNotificationList(from viewController: UIViewController) {
    let notificationVC = NotificationListViewController()
    let navController = UINavigationController(rootViewController: notificationVC)
    viewController.present(navController, animated: true)
  }
  
  // MARK: - Debug Methods
  
  /// 알림 시스템 상태 체크
  static func checkNotificationSystemStatus() -> [String: Any] {
    let notificationCount = getNotificationCount()
    let notifications = UserDefaults.shared.notifications
    
    let status: [String: Any] = [
      "notificationCount": notificationCount,
      "hasNotifications": notificationCount > 0,
      "latestNotification": notifications.first?.fileName ?? "None",
      "oldestNotification": notifications.last?.fileName ?? "None",
      "userDefaultsKey": Current.notificationModelsKey
    ]
    
    aLogger.info("Notification System Status: \(status)")
    return status
  }
  
  /// 테스트 다운로드 URL 검증
  static func validateTestDownloadURLs() {
    let testNotifications = createTestNotifications()
    
    for notification in testNotifications {
      guard let url = URL(string: notification.fileUrl) else {
        aLogger.error("Invalid URL: \(notification.fileUrl)")
        continue
      }
      
      // URL 접근 가능성 체크 (간단한 HEAD 요청)
      var request = URLRequest(url: url)
      request.httpMethod = "HEAD"
      request.timeoutInterval = 10
      
      URLSession.shared.dataTask(with: request) { _, response, error in
        if let error = error {
          aLogger.error("URL validation failed for \(notification.fileName): \(error.localizedDescription)")
        } else if let httpResponse = response as? HTTPURLResponse {
          aLogger.info("URL validation for \(notification.fileName): HTTP \(httpResponse.statusCode)")
        }
      }.resume()
    }
  }
}

// MARK: - UIViewController Extension for Testing

extension UIViewController {
  
  /// 개발자 메뉴에서 알림 테스트 기능 추가
  func addNotificationTestMenu() {
    let alert = UIAlertController(
      title: "알림 시스템 테스트",
      message: "테스트할 기능을 선택하세요",
      preferredStyle: .actionSheet
    )
    
    alert.addAction(UIAlertAction(title: "테스트 알림 추가", style: .default) { _ in
      NotificationTestHelper.addTestNotificationsToUserDefaults()
      self.showSimpleAlert(title: "완료", message: "테스트 알림이 추가되었습니다.")
    })
    
    alert.addAction(UIAlertAction(title: "알림 수신 시뮬레이션", style: .default) { _ in
      NotificationTestHelper.simulateRemoteNotification(in: self)
    })
    
    alert.addAction(UIAlertAction(title: "알림 목록 보기", style: .default) { _ in
      NotificationTestHelper.presentNotificationList(from: self)
    })
    
    alert.addAction(UIAlertAction(title: "모든 알림 삭제", style: .destructive) { _ in
      NotificationTestHelper.clearAllNotifications()
      self.showSimpleAlert(title: "완료", message: "모든 알림이 삭제되었습니다.")
    })
    
    alert.addAction(UIAlertAction(title: "시스템 상태 확인", style: .default) { _ in
      let status = NotificationTestHelper.checkNotificationSystemStatus()
      let count = status["notificationCount"] as? Int ?? 0
      self.showSimpleAlert(title: "시스템 상태", message: "저장된 알림: \(count)개")
    })
    
    alert.addAction(UIAlertAction(title: "취소", style: .cancel))
    
    present(alert, animated: true)
  }
  
  private func showSimpleAlert(title: String, message: String) {
    let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
    alert.addAction(UIAlertAction(title: "확인", style: .default))
    present(alert, animated: true)
  }
}
