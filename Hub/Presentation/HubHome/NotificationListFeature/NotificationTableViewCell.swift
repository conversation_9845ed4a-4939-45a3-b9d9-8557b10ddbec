//
//  NotificationTableViewCell.swift
//  Hub
//
//  Created by ncn on 5/26/25.
//

import UIKit
import SnapKit

protocol NotificationTableViewCellDelegate: AnyObject {
  func didTapDownloadButton(for notification: NotificationModel, at indexPath: IndexPath)
  func didTapDeleteButton(for notification: NotificationModel, at indexPath: IndexPath)
}

class NotificationTableViewCell: UITableViewCell {
  
  static let identifier = "NotificationTableViewCell"
  
  weak var delegate: NotificationTableViewCellDelegate?
  private var notification: NotificationModel?
  private var indexPath: IndexPath?
  
  // MARK: - UI Components
  
  private let containerView: UIView = {
    let view = UIView()
    view.backgroundColor = .white
    view.layer.cornerRadius = 12
    view.layer.shadowColor = UIColor.black.cgColor
    view.layer.shadowOffset = CGSize(width: 0, height: 2)
    view.layer.shadowRadius = 4
    view.layer.shadowOpacity = 0.1
    return view
  }()
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 16, weight: .bold)
    label.textColor = .mainBlack
    label.numberOfLines = 2
    return label
  }()
    
  private let fileNameLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 12, weight: .regular)
    label.textColor = .secondText
    label.numberOfLines = 1
    return label
  }()
  
  private let dateLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 12, weight: .regular)
    label.textColor = .secondText
    label.textAlignment = .right
    return label
  }()
  
  private let downloadButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "icon_download.pdf"), for: .normal)
    button.setImage(#imageLiteral(resourceName: "icon_download_select.pdf"), for: .selected)
    button.tintColor = .vueroidBlue
    return button
  }()
  
  private let deleteButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "icon_delete.pdf"), for: .normal)
    button.setImage(#imageLiteral(resourceName: "icon_delete_select.pdf"), for: .selected)
    button.tintColor = .mainRed
    return button
  }()
  
  private let buttonStackView: UIStackView = {
    let stackView = UIStackView()
    stackView.axis = .horizontal
    stackView.spacing = 8
    stackView.distribution = .fillEqually
    return stackView
  }()
  
  // MARK: - Initialization
  
  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setupUI()
    setupConstraints()
    setupActions()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  // MARK: - Setup Methods
  
  private func setupUI() {
    backgroundColor = .clear
    selectionStyle = .none
    
    contentView.addSubview(containerView)
    
    containerView.addSubview(titleLabel)
    containerView.addSubview(fileNameLabel)
    containerView.addSubview(dateLabel)
    containerView.addSubview(buttonStackView)
    
    buttonStackView.addArrangedSubview(downloadButton)
    buttonStackView.addArrangedSubview(deleteButton)
  }
  
  private func setupConstraints() {
    containerView.snp.makeConstraints { make in
      make.top.equalToSuperview().offset(8)
      make.leading.equalToSuperview().offset(16)
      make.trailing.equalToSuperview().offset(-16)
      make.bottom.equalToSuperview().offset(-8)
    }
    
    titleLabel.snp.makeConstraints { make in
      make.top.equalToSuperview().offset(16)
      make.leading.equalToSuperview().offset(16)
      make.trailing.equalTo(buttonStackView.snp.leading).offset(-12)
    }
        
    fileNameLabel.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(8)
      make.leading.equalToSuperview().offset(16)
      make.trailing.equalTo(dateLabel.snp.leading).offset(-8)
    }
    
    dateLabel.snp.makeConstraints { make in
      make.top.equalTo(titleLabel.snp.bottom).offset(8)
      make.trailing.equalTo(buttonStackView.snp.leading).offset(-12)
      make.width.greaterThanOrEqualTo(80)
    }
    
    buttonStackView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.trailing.equalToSuperview().offset(-16)
      make.width.equalTo(88) // 40 + 40 + 8 spacing
      make.height.equalTo(40)
    }
    
    downloadButton.snp.makeConstraints { make in
      make.width.height.equalTo(40)
    }
    
    deleteButton.snp.makeConstraints { make in
      make.width.height.equalTo(40)
    }
    
    // Container view height constraint
    containerView.snp.makeConstraints { make in
      make.height.greaterThanOrEqualTo(100)
    }
  }
  
  private func setupActions() {
    downloadButton.addTarget(self, action: #selector(downloadButtonTapped), for: .touchUpInside)
    deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
  }
  
  // MARK: - Actions
  
  @objc private func downloadButtonTapped() {
    guard let notification = notification,
          let indexPath = indexPath else { return }
    
    // 버튼 애니메이션
    UIView.animate(withDuration: 0.1, animations: {
      self.downloadButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
    }) { _ in
      UIView.animate(withDuration: 0.1) {
        self.downloadButton.transform = .identity
      }
    }
    
    delegate?.didTapDownloadButton(for: notification, at: indexPath)
  }
  
  @objc private func deleteButtonTapped() {
    guard let notification = notification,
          let indexPath = indexPath else { return }
    
    // 버튼 애니메이션
    UIView.animate(withDuration: 0.1, animations: {
      self.deleteButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
    }) { _ in
      UIView.animate(withDuration: 0.1) {
        self.deleteButton.transform = .identity
      }
    }
    
    delegate?.didTapDeleteButton(for: notification, at: indexPath)
  }
  
  // MARK: - Configuration
  
  func configure(with notification: NotificationModel, at indexPath: IndexPath) {
    self.notification = notification
    self.indexPath = indexPath
    
    titleLabel.text = notification.title
    fileNameLabel.text = "📁 \(notification.fileName)"
    
    // 날짜 포맷팅
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .short
    dateLabel.text = formatter.string(from: notification.receivedDate)
  }
  
  // MARK: - Download State Management
  
  func setDownloadState(_ isDownloading: Bool) {
    downloadButton.isEnabled = !isDownloading
    
    if isDownloading {
      // 다운로드 중 상태
//      downloadButton.setImage(UIImage(systemName: "arrow.down.circle.fill"), for: .normal)
      downloadButton.tintColor = .systemGray
      
      // 로딩 애니메이션 추가
      let activityIndicator = UIActivityIndicatorView(style: .medium)
      activityIndicator.tag = 999
      activityIndicator.startAnimating()
      downloadButton.addSubview(activityIndicator)
      activityIndicator.snp.makeConstraints { make in
        make.center.equalToSuperview()
      }
    } else {
      // 일반 상태
//      downloadButton.setImage(UIImage(systemName: "arrow.down.circle"), for: .normal)
      downloadButton.tintColor = .systemBlue
      
      // 로딩 애니메이션 제거
      downloadButton.viewWithTag(999)?.removeFromSuperview()
    }
  }
  
  func setDownloadProgress(_ progress: Double) {
    // 진행률 표시 (선택사항)
    if progress > 0 && progress < 1.0 {
      downloadButton.setTitle("\(Int(progress * 100))%", for: .normal)
      downloadButton.titleLabel?.font = .systemFont(ofSize: 10, weight: .bold)
    } else {
      downloadButton.setTitle("", for: .normal)
    }
  }
}
