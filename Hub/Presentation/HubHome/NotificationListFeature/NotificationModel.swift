//
//  NotificationModels.swift
//  Hub
//
//  Created by ncn on 4/10/25.
//

import Foundation

/*
 {
     "title" : "제목",
     "body" : :"내용",
     "putData" : {
         "fileUrl" : "S3 DownURL" ,
         "title": "제목",  -- IOS만
         "content" :  "내용"  -- IOS만
     }
 }
 
 userInfo : [
  AnyHashable("google.c.fid"): dcmuq9mS4U_7qcQj9nTqN_,
  AnyHashable("gcm.message_id"): 1750227864787520,
  AnyHashable("fileUrl"): https://ncn-super-resolution.s3.amazonaws.com/file/5973de1e-5030-47d0-965e-576e68ba16fa/out_5973de1e-5030-47d0-965e-576e68ba16fa.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250618T062424Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=AKIAYI2VV35DKA7DULUZ%2F20250618%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=30f6f09db288c32830cd657d9a9f692b3b1a557421c09fff6daa61033ca5d4ca,
  AnyHashable("title"): [20250509_124930_INF_FPB.mp4] Your request has been processed.,
  AnyHashable("google.c.a.e"): 1,
  AnyHashable("google.c.sender.id"): 214610897257,
  AnyHashable("content"): content,
  AnyHashable("fileName"): 20250509_124930_INF_FPB.mp4,
  AnyHashable("aps"): {
     alert =     {
         body = content;
         title = "[20250509_124930_INF_FPB.mp4] Your request has been processed.";
     };
 }]
 */
struct NotificationModel: Codable, Equatable {
  let title: String
  let content: String
  let fileUrl: String
  let fileName: String
  let receivedDate: Date

  init(title: String, content: String, fileUrl: String, fileName: String, receivedDate: Date = Date()) {
    self.title = title
    self.content = content
    self.fileUrl = fileUrl
    self.fileName = fileName
    self.receivedDate = receivedDate
  }

  // UserInfo에서 NotificationModel 생성
  init?(from userInfo: [AnyHashable: Any]) {
    guard let title = userInfo["title"] as? String,
          let content = userInfo["content"] as? String,
          let fileUrl = userInfo["fileUrl"] as? String,
          let fileName = userInfo["fileName"] as? String else {
      return nil
    }

    self.title = title
    self.content = content
    self.fileUrl = fileUrl
    self.fileName = fileName
    self.receivedDate = Date()
  }
}

