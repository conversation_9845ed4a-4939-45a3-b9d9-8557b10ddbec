//
//  NotificationListViewController.swift
//  Hub
//
//  Created by ncn on 4/10/25.
//

import UIKit
import RxSwift
import RxCocoa

class NotificationListViewController: UIViewController, UITableViewDelegate, UITableViewDataSource, NotificationTableViewCellDelegate {

  // MARK: - Properties

  private var notifications: [NotificationModel] = []
  private let disposeBag = DisposeBag()
  private var downloadingIndexPaths: Set<IndexPath> = []

  // MARK: - UI Components

  private let tableView: UITableView = {
    let tableView = UITableView()
    tableView.backgroundColor = .background
    tableView.separatorStyle = .none
    tableView.register(NotificationTableViewCell.self, forCellReuseIdentifier: NotificationTableViewCell.identifier)
    tableView.rowHeight = UITableView.automaticDimension
    tableView.estimatedRowHeight = 120
    return tableView
  }()

  lazy var emptyLabel: UILabel = {
    let label = UILabel()
    label.text = L.history_chart_empty_msg.localized
    label.textAlignment = .center
    label.font = .h2Regular
    label.textColor = .grayText
    return label
  }()
  // MARK: - Lifecycle

  override func viewDidLoad() {
    super.viewDidLoad()
    setupUI()
    setupConstraints()
    loadNotifications()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    loadNotifications()
  }

  // MARK: - Setup Methods

  private func setupUI() {
    view.backgroundColor = .background
    self.title = L.dialog_info_title.localized
    tableView.delegate = self
    tableView.dataSource = self

    tableView.tableFooterView = UIView()
    
    tableView.rowHeight = UITableView.automaticDimension
    tableView.rowHeight = 120
    
    tableView.separatorStyle = .none
    
    if notifications.isEmpty {
      tableView.backgroundView = emptyLabel
    } else {
      tableView.backgroundView = nil
    }

    view.addSubview(tableView)
    
    navigationItem.backButtonTitle = ""
    let closeImage = #imageLiteral(resourceName: "close.pdf").withRenderingMode(.alwaysOriginal)
    navigationItem.rightBarButtonItem = UIBarButtonItem(
      image: closeImage,
      style: .plain,
      target: self,
      action: #selector( close )
    )
  }

  @objc
  func close() {
    self.dismiss(animated: true, completion: nil)
  }
  
  private func setupConstraints() {
    tableView.pin.all().activate()
  }

  // MARK: - Data Management

  private func loadNotifications() {
    notifications = UserDefaults.shared.notifications
    updateUI()
    aLogger.info("Loaded \(self.notifications.count) notifications")
  }

  private func saveNotifications() {
    UserDefaults.shared.notifications = notifications
    updateUI()
  }

  private func updateUI() {
    DispatchQueue.main.async {
      self.tableView.reloadData()
      self.emptyLabel.isHidden = !self.notifications.isEmpty
    }
  }
}

// MARK: - UITableViewDataSource

extension NotificationListViewController {

  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    return notifications.count
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    guard let cell = tableView.dequeueReusableCell(withIdentifier: NotificationTableViewCell.identifier, for: indexPath) as? NotificationTableViewCell else {
      return UITableViewCell()
    }

    let notification = notifications[indexPath.row]
    cell.configure(with: notification, at: indexPath)
    cell.delegate = self

    // 다운로드 중인 상태 표시
    if downloadingIndexPaths.contains(indexPath) {
      cell.setDownloadState(true)
    } else {
      cell.setDownloadState(false)
    }

    return cell
  }
}

// MARK: - UITableViewDelegate

extension NotificationListViewController {

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    tableView.deselectRow(at: indexPath, animated: true)

    let notification = notifications[indexPath.row]

    // 알림 탭 시 자동 다운로드
    downloadFile(for: notification, at: indexPath)
  }

  func tableView(_ tableView: UITableView, canEditRowAt indexPath: IndexPath) -> Bool {
    return true
  }

  func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
    if editingStyle == .delete {
      deleteNotification(at: indexPath)
    }
  }

  func tableView(_ tableView: UITableView, titleForDeleteConfirmationButtonForRowAt indexPath: IndexPath) -> String? {
    return L.dialog_delete_title.localized
  }
}

// MARK: - NotificationTableViewCellDelegate

extension NotificationListViewController {

  func didTapDownloadButton(for notification: NotificationModel, at indexPath: IndexPath) {
    downloadFile(for: notification, at: indexPath)
  }

  func didTapDeleteButton(for notification: NotificationModel, at indexPath: IndexPath) {
    deleteNotification(at: indexPath)
  }
}

// MARK: - File Download Management

extension NotificationListViewController {

  private func downloadFile(for notification: NotificationModel, at indexPath: IndexPath) {
    // 이미 다운로드 중인지 확인
    guard !downloadingIndexPaths.contains(indexPath) else {
      aLogger.warning("File is already downloading")
      return
    }

    // 다운로드 시작
    downloadingIndexPaths.insert(indexPath)
//    updateCellDownloadState(at: indexPath, isDownloading: true)

    aLogger.info("Starting download for file: \(notification.fileName)")
    popupDownload(model: notification)
    // 파일 다운로드 실행
    #if false
    downloadFileFromURL(notification.fileUrl, fileName: notification.fileName) { [weak self] result in
      DispatchQueue.main.async {
        self?.downloadingIndexPaths.remove(indexPath)
        self?.updateCellDownloadState(at: indexPath, isDownloading: false)

        switch result {
        case .success(let savedURL):
          aLogger.info("Download completed: \(savedURL)")
          self?.showDownloadSuccessAlert(fileName: notification.fileName, savedURL: savedURL)

        case .failure(let error):
          aLogger.error("Download failed: \(error.localizedDescription)")
          self?.showDownloadErrorAlert(error: error)
        }
      }
    }
    #endif
  }

  private func popupDownload(model: NotificationModel) {
    do {
      let savefilename = try HubFileNameGenerator.generatePushResultFileName(baseFileName: model.fileName)
      fLogger.info("##@@@ push input: \(model.fileName) download filename: \(savefilename)")
      
      let subTitle = "\n\(L.ask_download_file_msg.localized)\n[\(savefilename)]"
      LKPopupView.popup.alert {[
        .subTitle(subTitle),
        .showCancel(true),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({
            let vm = PopupNotificationDownloadViewModel(useCase: Composers.aiUseCase, model: model, saveFileName: savefilename)
            let vc = PopupWifiDownloadViewController(
              coordinator: nil,
              editType: .trim,
              fileName: savefilename,
              totalCount: -1
            )
            vc.wifiViewModel = nil
            vc.notificationDownlaodViewModel = vm
            self.present(vc, animated: true)
          })
        ])
      ]}

    } catch {
      aLogger.error("parseFileName error")
    }
  }
  
  private func updateCellDownloadState(at indexPath: IndexPath, isDownloading: Bool) {
    guard let cell = tableView.cellForRow(at: indexPath) as? NotificationTableViewCell else { return }
    cell.setDownloadState(isDownloading)
  }

  private func showDownloadSuccessAlert(fileName: String, savedURL: URL) {
    LKPopupView.popup.alert {[
      .title(L.dialog_save_title.localized),
      .subTitle(L.dialog_save_success.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
        })
      ])
    ]}
  }

  private func showDownloadErrorAlert(error: Error) {
    LKPopupView.popup.toast(hit: "Download Failed", icon: .toastFail, position: .center )
  }
}

// MARK: - Notification Management

extension NotificationListViewController {

  private func deleteNotification(at indexPath: IndexPath) {
    let notification = notifications[indexPath.row]

    self.notifications.remove(at: indexPath.row)
    self.saveNotifications()
    self.tableView.deleteRows(at: [indexPath], with: .fade)
    aLogger.info("Notification deleted: \(notification.fileName)")
    LKPopupView.popup.toast(hit: L.dialog_delete_success.localized)
  }

  func addNotification(_ notification: NotificationModel) {
    // 중복 확인 (같은 fileUrl이 있는지 확인)
    if !notifications.contains(where: { $0.fileUrl == notification.fileUrl }) {
      notifications.insert(notification, at: 0) // 최신 알림을 맨 위에 추가
      saveNotifications()

      DispatchQueue.main.async {
        self.tableView.insertRows(at: [IndexPath(row: 0, section: 0)], with: .top)
      }

      aLogger.info("New notification added: \(notification.fileName)")
    } else {
      aLogger.info("Duplicate notification ignored: \(notification.fileName)")
    }
  }
}
