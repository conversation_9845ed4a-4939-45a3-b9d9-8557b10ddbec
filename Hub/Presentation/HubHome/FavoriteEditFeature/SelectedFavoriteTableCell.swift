//
//  SelectedFavoriteTableCell.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON> won on 2/20/25.
//

import UIKit

class SelectedFavoriteTableCell: UITableViewCell {
  lazy var counterLabel: UILabel = {
    let label = UILabel()
    let count = UserDefaults.shared.favoriteFastSettingItems.filter { $0.title != "empty" }.count
    let fullText = "\(L.text_favorites.localized) \(count)/3"
    let attributedString = NSMutableAttributedString(string: fullText)
    let range = (fullText as NSString).range(of: "\(count)")
    attributedString.addAttribute(.foregroundColor, value: UIColor.vueroidBlue, range: range)
    label.attributedText = attributedString
    label.font = .sub1
    label.textColor = .subText
    label.sizeToFit()
    return label
  }()

  let buttonStackView: UIStackView = {
    let stackView = UIStackView(arrangedSubviews: [
      FavoriteItemView(),
      FavoriteItemView(),
      FavoriteItemView()
    ])
    stackView.axis = .horizontal
    stackView.spacing = 10
    stackView.distribution = .fillEqually
    return stackView
  }()

  
  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupUI() {
    selectionStyle = .none
    backgroundColor = .background
    contentView.addSubviews([counterLabel, buttonStackView])
    
    Pin.activate([
      counterLabel.pin.top().start().height(18.rv),
      buttonStackView.pin.below(counterLabel, offset: 10.rv).horizontally().bottom()
    ])
  }

  func updateCounter(items: [FavoriteFastSettingItem]) {
    let count = items.compactMap { $0 }.count
    let fullText = "\(L.text_favorites.localized) \(count)/3"
    let attributedString = NSMutableAttributedString(string: fullText)
    let range = (fullText as NSString).range(of: "\(count)")
    attributedString.addAttribute(.foregroundColor, value: UIColor.vueroidBlue, range: range)
    
    counterLabel.attributedText = attributedString
  }

  func configure(with items: [FavoriteFastSettingItem]) {
    buttonStackView.removeAllSubviews()
    
    items.forEach { item in
      buttonStackView.addArrangedSubview(FavoriteItemView(type: item))
    }

    let remainingSpots = 3 - items.count
    for _ in 0..<remainingSpots {
      buttonStackView.addArrangedSubview(FavoriteItemView())
    }
  }
}

