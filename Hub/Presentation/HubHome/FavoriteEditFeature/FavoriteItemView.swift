//
//  FavoriteItemView.swift
//
//
//  Created by ncn on 10/31/24.
//

import UIKit
/*
 1. 음성녹음 - ON/OFF/이벤트만ON
 2. 상시 타임랩스 - ON/OFF
 3. 주행충격감도 - OFF/LOW/MID/HIGH
 4. 이벤트 영상 보호 - ON/OFF
 5. 자동주차 - ON/OFF
 6. 주차모드지연 - OFF/90초/3분
 7. 저전력모드 = ON/OFF
 8. 스피커볼륨 - OFF/1/2/3
 9. 음성안내 - ON/OFF
 10. 이벤트녹화알림 - ON/OFF
 11. 속도단위 - km/h, mph
 12. 속도표시 - ON/OFF
 */

public class FavoriteItemView: UIView {
  
  private let iconImageView: UIImageView = {
    let iv = UIImageView()
    iv.contentMode = .scaleAspectFit
    return iv
  }()
  
  private let favoriteTitleLabel: UILabel = {
    let label = UILabel()
    label.font = .label
    label.textColor = .black
    label.textAlignment = .center
    label.sizeToFit()
    label.adjustsFontSizeToFitWidth = true
    label.numberOfLines = 2
    return label
  }()
  
  var type: FavoriteFastSettingItem? {
    didSet {
      if let type = type {
        iconImageView.image = UIImage(named: type.imageName)
        favoriteTitleLabel.text = type.getCurrentLocalTitle()
        favoriteTitleLabel.isHidden = false
        print("type.title: \(type.title)")
      } else {
        iconImageView.image = UIImage(named: "favorities_bg.pdf")
        favoriteTitleLabel.isHidden = true
      }
    }
  }

  public init(type: FavoriteFastSettingItem? = nil) {
    self.type = type
    super.init(frame: .zero)
//    self.frame.size = CGSize(width: 100.rv, height: 100.rv)
    layer.cornerRadius = 10

    if let type = type {
      backgroundColor = .white
      iconImageView.image = UIImage(named: type.imageName)
      favoriteTitleLabel.text = type.getCurrentLocalTitle()
      favoriteTitleLabel.isHidden = false
      
      let stackView = VStackView(spacing: 4) {
        iconImageView.withHeight(50.rv)
        favoriteTitleLabel
      }
      
      addSubview(stackView)
      stackView.pin
        .centerX()
        .width(56.rv)
        .top(offset: 14)
        .bottom(offset: 14)
        .activate()
    } else {
      backgroundColor = .clear
      iconImageView.image = UIImage(named: "favorities_bg.pdf")
      favoriteTitleLabel.isHidden = true
      
      addSubview(iconImageView)
      iconImageView.pin
        .all()
        .activate()
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

}
