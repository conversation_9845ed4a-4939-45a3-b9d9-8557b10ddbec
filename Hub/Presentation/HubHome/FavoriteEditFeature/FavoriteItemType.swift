//
//  FavoriteItemType.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON> won on 2/20/25.


import UIKit

public enum FavoriteItemType: String, CaseIterable {
  case audioRecording
  case impactSensitivity
  case autoParkingRecording
  case parkingModeDelay
  case lowpowerMode
  case speakerVolume
  case voiceAlert
  case eventAlert
  case speedUnit
  case displaySpeed
  
  var image: UIImage {
    // Convert enum case to snake_case format
    let snakeCase = self.rawValue
      .replacingOccurrences(of: "([A-Z])", with: "_$1", options: .regularExpression)
      .lowercased()
      .trimmingCharacters(in: CharacterSet(charactersIn: "_"))

    let imageName = "fav_\(snakeCase).pdf"
    return #imageLiteral(resourceName: imageName).withRenderingMode(.alwaysOriginal)
  }
  
  var imageName: String {
    let snakeCase = self.rawValue
      .replacingOccurrences(of: "([A-Z])", with: "_$1", options: .regularExpression)
      .lowercased()
      .trimmingCharacters(in: CharacterSet(charactersIn: "_"))

    return "fav_\(snakeCase).pdf"
  }
  
  var smIconName: String {
    
    let snakeCase = self.rawValue
      .replacingOccurrences(of: "([A-Z])", with: "_$1", options: .regularExpression)
      .lowercased()
      .trimmingCharacters(in: CharacterSet(charactersIn: "_"))

    return "fav_sm_\(snakeCase).pdf"
  }
  
  var title: String {
    switch self {
    case .audioRecording:
      L.sound_audio_rec.localized
    case .impactSensitivity:
      L.driving_impact_sensitivity.localized
    case .autoParkingRecording:
      L.parking_recording_system.localized
    case .parkingModeDelay:
      L.quick_parking_mode_delay.localized
    case .lowpowerMode:
      L.low_power_mode.localized
    case .speakerVolume:
      L.sound_speaker_volume.localized
    case .voiceAlert:
      L.voice_alert.localized
    case .eventAlert:
      L.sound_event_alert.localized
    case .speedUnit:
      L.system_speed_unit.localized
    case .displaySpeed:
      L.system_speed_mode.localized
    }
  }
  
  var valueItems: [String] {
    switch self {
    case.audioRecording:
      ConfigurationModel.audiorec
    case.impactSensitivity:
      ConfigurationModel.infsensorsenses
    case.autoParkingRecording:
      ConfigurationModel.parkmodes
    case.parkingModeDelay:
      ConfigurationModel.parkdelaymodes
    case.lowpowerMode:
      ConfigurationModel.sleepmodes
    case.speakerVolume:
      ConfigurationModel.speakervolumes
    case.voiceAlert:
      ConfigurationModel.audioGuide
    case.eventAlert:
      ConfigurationModel.alertEvent
    case.speedUnit:
      ConfigurationModel.speedunits
    case.displaySpeed:
      ConfigurationModel.speedmodes
    }
  }
  
  var savedValue: Int {
    guard let config = AppManager.shared.dashcamWifiConfig else { return 0 }
    switch self {
    case .audioRecording:
      return config.audiorec
    case .impactSensitivity:
      return config.infsensorsen
    case .autoParkingRecording:
      return config.parkmode
    case .parkingModeDelay:
      return config.parkDelay
    case .lowpowerMode:
      return config.sleepmode
    case .speakerVolume:
      return config.speakervolume
    case .voiceAlert:
      return config.audioguide
    case .eventAlert:
      return config.alertevent
    case .speedUnit:
      return config.speedunit
    case .displaySpeed:
      return config.speedmode
    }
  }
  
  var fastConfig: FastConfigurationModel {
    switch self {
    case .audioRecording:
      FastConfigurationModel(type: .voicerecord, value: 0)
    case .impactSensitivity:
      FastConfigurationModel(type: .infeventsense, value: 0)
    case .autoParkingRecording:
      FastConfigurationModel(type: .autoparkmode, value: 0)
    case .parkingModeDelay:
      FastConfigurationModel(type: .delayparkmode, value: 0)
    case .lowpowerMode:
      FastConfigurationModel(type: .lowpowermode, value: 0)
    case .speakerVolume:
      FastConfigurationModel(type: .speekervolume, value: 0)
    case .voiceAlert:
      FastConfigurationModel(type: .voiceguide, value: 0)
    case .eventAlert:
      FastConfigurationModel(type: .eventrecnotify, value: 0)
    case .speedUnit:
      FastConfigurationModel(type: .speedunit, value: 0)
    case .displaySpeed:
      FastConfigurationModel(type: .speedtextoverlay, value: 0)
    }
  }
}
