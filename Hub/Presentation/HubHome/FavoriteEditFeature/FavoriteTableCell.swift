//
//  FavoriteTableCell.swift
//  Hub
//
//  Created by ncn on 1/18/25.
//

import UIKit

protocol FavoriteTableCellDelegate: AnyObject {
  func favoriteTableCell(
    _ cell: FavoriteTableCell,
    didTapStarButton item: FavoriteFastSettingItem
  )
}

// MARK: - FavoriteTableCell
class FavoriteTableCell: UITableViewCell {
  weak var delegate: FavoriteTableCellDelegate?

  private let iconImageView: UIImageView = {
    let iv = UIImageView()
    iv.contentMode = .scaleAspectFit
    return iv
  }()

  private let titleLabel: UILabel = {
    let label = UILabel()
    label.font = .sub2
    label.textColor = .mainBlack
    label.sizeToFit()
    label.adjustsFontSizeToFitWidth = true
    label.minimumScaleFactor = 0.3
    return label
  }()

  let starButton: UIButton = {
    let button = UIButton(type: .custom)
    button.isEnabled = true
    button.setImage(#imageLiteral(resourceName: "fav_bookmark_normal.pdf"), for: .normal)
    button.setImage(#imageLiteral(resourceName: "fav_bookmark_select.pdf"), for: .selected)
    return button
  }()

  override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setupUI()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupUI() {
    backgroundColor = .white
    selectionStyle = .none
    contentView.addSubviews([iconImageView, titleLabel, starButton])
    
    Pin.activate([
      iconImageView.pin.size(28).centerY().start(offset: 15),
      titleLabel.pin.after(iconImageView, offset: 8).width(200).centerY(),
      starButton.pin.size(24).centerY().end(offset: -15)
    ])
  }

  func configure(with item: FavoriteFastSettingItem) {
    iconImageView.image = UIImage(named: item.smallIconName)
    titleLabel.text = item.getCurrentLocalTitle()
    starButton.isSelected = item.isSelected
    
    starButton.addAction(UIAction { [weak self] _ in
      guard let self else { return }
      delegate?.favoriteTableCell(self, didTapStarButton: item)
    }, for: .touchUpInside)
  }
}
