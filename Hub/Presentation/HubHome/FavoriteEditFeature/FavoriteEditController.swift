//
//  FavoriteEditController.swift
//  Hub
//
//  Created by ncn on 1/18/25.
//

import UIKit

class FavoriteEditController: UIViewController {

  private enum SectionType: Int, CaseIterable {
    case bookmark = 0
    case all = 1
    
    var rowHeight: CGFloat {
      switch self {
      case .bookmark:
        return 128.rv
      case .all:
        return 64.rv
      }
    }
  }

  // MARK: - Properties
  private var selectedFavorites: [FavoriteFastSettingItem] = []
  
  private var allItems: [FavoriteFastSettingItem] = [
    .init(itemType: .audioRecording),
    .init(itemType: .impactSensitivity),
    .init(itemType: .autoParkingRecording),
    .init(itemType: .parkingModeDelay),
    .init(itemType: .lowpowerMode),
    .init(itemType: .speakerVolume),
    .init(itemType: .voiceAlert),
    .init(itemType: .eventAlert),
    .init(itemType: .speedUnit),
    .init(itemType: .displaySpeed),
  ]

  // MARK: - UI Components

  private lazy var tableView: UITableView = {
    let tv = UITableView(frame: .zero, style: .insetGrouped)
    tv.backgroundColor = .background
    tv.delegate = self
    tv.dataSource = self
    tv.register(FavoriteTableCell.self, forCellReuseIdentifier: "FavoriteTableCell")
    tv.register(SelectedFavoriteTableCell.self, forCellReuseIdentifier: "SelectedFavoriteTableCell")
    tv.separatorStyle = .singleLine
    return tv
  }()

  // MARK: - Lifecycle
  override func viewDidLoad() {
    super.viewDidLoad()
    configureInitialData()
    setupUI()
  }

  // MARK: - Setup
  private func setupUI() {
    view.backgroundColor = .background
    self.title = L.quick_menu_title.localized
    let closeImage = #imageLiteral(resourceName: "close.pdf").withRenderingMode(.alwaysOriginal)
    self.navigationItem.rightBarButtonItem = UIBarButtonItem(
      image: closeImage,
      style: .plain,
      target: self,
      action: #selector( close )
    )

    view.addBody(tableView, safe: true)
  }

  private func configureInitialData() {
    selectedFavorites = UserDefaults.shared.favoriteFastSettingItems
    
    selectedFavorites.enumerated().forEach { saveIndex, saved in
      allItems.enumerated().forEach { index, item in
        if item == saved {
          allItems[index] = saved
        }
      }
    }
  }
  
  // MARK: - Actions
  @objc private func close() {
    UserDefaults.shared.favoriteFastSettingItems = selectedFavorites
    
    dismiss(animated: true)
  }
  
  deinit {
    aLogger.info("deinit \(Self.self)")
  }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension FavoriteEditController: UITableViewDelegate, UITableViewDataSource {
  func numberOfSections(in tableView: UITableView) -> Int {
    SectionType.allCases.count
  }
  
  func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
    guard let sectionType = SectionType(rawValue: section) else { return 0 }

    switch sectionType {
    case .bookmark:
      return 1
    case .all:
      return allItems.count
    }
  }

  func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    guard let sectionType = SectionType(rawValue: indexPath.section) else { return UITableViewCell() }
    let item = allItems[indexPath.row]
    
    switch sectionType {
    case .bookmark:
      let cell = tableView.dequeueReusableCell(withIdentifier: "SelectedFavoriteTableCell", for: indexPath) as! SelectedFavoriteTableCell
      cell.configure(with: selectedFavorites)
      cell.updateCounter(items: selectedFavorites)
      return cell
      
    case .all:
      let cell = tableView.dequeueReusableCell(withIdentifier: "FavoriteTableCell", for: indexPath) as! FavoriteTableCell
      cell.delegate = self
      cell.configure(with: item)
      
      // Remove all existing actions before adding new one
      cell.starButton.removeTarget(nil, action: nil, for: .allEvents)

      cell.starButton.addAction(UIAction { [weak self] _ in
        debugPrint("### indexpath: \(indexPath.row)")
        self?.tableView(tableView, didSelectRowAt: indexPath)
      }, for: .touchUpInside)
      
      return cell
    }
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    guard let sectionType = SectionType(rawValue: indexPath.section) else { return 0 }
    return sectionType.rowHeight
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    guard indexPath.section == SectionType.all.rawValue else {
      return
    }
    let allSelected = allItems.filter { $0.isSelected }
    var item = allItems[indexPath.row]
    if allSelected.count == 3, item.isSelected == false {
      return
    }
    
    // Toggle selection
    item.isSelected.toggle()
    allItems[indexPath.row] = item
    
    // Update selectedFavorites
    if item.isSelected {
      if selectedFavorites.count < 3 {
        selectedFavorites.append(item)
      }
    } else {
      selectedFavorites.remove(item)
    }
    
    // Update UI
    let boomarkIndexPath = IndexPath(row: 0, section: 0)
    tableView.reloadRows(at: [indexPath, boomarkIndexPath], with: .none)
  }
}

// MARK: - FavoriteTableCellDelegate
extension FavoriteEditController: FavoriteTableCellDelegate {
  func favoriteTableCell(
    _ cell: FavoriteTableCell,
    didTapStarButton item: FavoriteFastSettingItem
  ) {
    guard let indexPath = tableView.indexPath(for: cell) else { return }
    tableView(tableView, didSelectRowAt: indexPath)
  }
}
