//
//  FavoriteFastSettingItem.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON> won on 2/20/25.
//

import Foundation

public struct FavoriteFastSettingItem: Codable, Equatable {
  public var imageName: String
  public var smallIconName: String
  public var title: String
  public var isSelected: Bool = false
  public var fastConfig: FastConfigurationModel
  public var selectionValues: [String]
  public var savedConfigValue: Int
  
  public static func == (lhs: Self, rhs: Self) -> Bool {
    return lhs.title == rhs.title
  }
  
  public init(itemType: FavoriteItemType) {
    self.selectionValues = itemType.valueItems
    self.imageName = itemType.imageName
    self.smallIconName = itemType.smIconName
    self.title = itemType.title
    self.fastConfig = itemType.fastConfig
    self.savedConfigValue = itemType.savedValue
  }
  
  func getCurrentLocalTitle() -> String {
    let getConfigtype = fastConfig.type
    switch getConfigtype {
    case .voicerecord:
      return L.sound_audio_rec.localized
    case .inftimelapse:
      return L.driving_impact_sensitivity.localized
    case .infeventsense:
      return L.driving_impact_sensitivity.localized
    case .autoparkmode:
      return L.parking_recording_system.localized
    case .delayparkmode:
      return L.quick_parking_mode_delay.localized
    case .lowpowermode:
      return L.low_power_mode.localized
    case .speekervolume:
      return L.sound_speaker_volume.localized
    case .voiceguide:
      return L.voice_alert.localized
    case .eventrecnotify:
      return L.sound_event_alert.localized
    case .speedunit:
      return L.system_speed_unit.localized
    case .speedtextoverlay:
      return L.system_speed_mode.localized
    }
  }
}
