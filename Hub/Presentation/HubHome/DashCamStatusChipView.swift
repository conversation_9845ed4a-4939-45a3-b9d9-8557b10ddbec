//
//  DashCamStatusChipView.swift
//
//
//  Created by ncn on 10/31/24.
//

import UIKit

public class DashCamStatusChipView: UIView {
  public enum StatusType {
    case driving
    case parking
    case offline

    public var labelColor: UIColor {
      switch self {
      case .driving:
        return .mainGreen
      case .parking:
        return .mainBlue
      case .offline:
        return UIColor(hex: "#AFAFAF")
      }
    }
    
    public var displayText: String {
      switch self {
      case .driving:
        "\(L.just_drive.localized)"
      case .parking:
        "\(L.just_parking.localized)"
      case .offline:
        "Offline"
      }
    }

    public var bgColor: UIColor {
      switch self {
      case .driving:
        return .green10
      case .parking:
        return .blue10
      case .offline:
        return UIColor(hex: "#EDEDED")
      }
    }
  }

  lazy var statusLabel: UILabel = {
    let label = UILabel()
    label.font = .boldSystemFont(ofSize: 11)
    return label
  }()

  var type: StatusType {
    didSet {
      statusLabel.text = type.displayText
      statusLabel.textColor = type.labelColor
      backgroundColor = type.bgColor
    }
  }


  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init(type: StatusType = .driving) {
    self.type = type
    super.init(frame: .zero)

    statusLabel.text = type.displayText
    statusLabel.textColor = type.labelColor
    backgroundColor = .background
    roundCorners(.allCorners, radius: 5)

    addSubview(statusLabel)

    statusLabel.pin.center().activate()
  }
}
