//
//  HubLogger.swift
//  Hub
//
//  Created by ncn on 1/18/25.
//


import OSLog

// scene
let aLogger = Logger(subsystem: "hub", category: "app")
let hLogger = Logger(subsystem: "hub", category: "home")
let vLogger = Logger(subsystem: "hub", category: "vod")
let sLogger = Logger(subsystem: "hub", category: "setting")
let mLogger = Logger(subsystem: "hub", category: "myLibrary")
let lLogger = Logger(subsystem: "hub", category: "live")
let iLogger = Logger(subsystem: "hub", category: "history")

// core feature
let tLogger = Logger(subsystem: "hub", category: "test")

let nLogger = Logger(subsystem: "hub", category: "http")
let wsLogger = Logger(subsystem: "hub", category: "ws")
let fLogger = Logger(subsystem: "hub", category: "file")
let mapLogger = Logger(subsystem: "hub", category: "map")
let wLogger = Logger(subsystem: "hub", category: "wifi")
let pLogger = Logger(subsystem: "hub", category: "player")
let cLogger = Logger(subsystem: "hub", category: "focus")
