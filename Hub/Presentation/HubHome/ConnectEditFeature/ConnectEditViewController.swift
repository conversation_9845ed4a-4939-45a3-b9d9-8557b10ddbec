//
//  ConnectEditViewController.swift
//
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/28/24.
//

import UIKit
import RxSwift
import RxCocoa


// Task(Sheet): home > 최근연결리스트 순서 변경시 앱 크러시 발생

public final class ConnectEditViewController: UIViewController {
  private let disposedBag = DisposeBag()
  public let testButton = UIButton()
  
  private let tableView = UITableView()
  
  private let confirmButton = UIButton.customStyleButton(title: L.ok.localized)

  var viewModel: ConnectEditViewModel
  
  public init(viewModel: ConnectEditViewModel) {
    self.viewModel = viewModel
    super.init(nibName: nil, bundle: nil)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  public override func viewDidLoad() {
    super.viewDidLoad()

    self.title = L.dashcam_edit_title.localized
    let closeImage = #imageLiteral(resourceName: "close.pdf").withRenderingMode(.alwaysOriginal)
    self.navigationItem.rightBarButtonItem = UIBarButtonItem(
      image: closeImage,
      style: .plain,
      target: self,
      action: #selector( close )
    )

    setComponent()
    setAutoLayout()
    setTestBtn()
    bindViewModel(to: viewModel)
  }
  
  @objc
  func close() {
    self.dismiss(animated: true, completion: nil)
  }

  func bindViewModel(to viewModel: ConnectEditViewModel?) {
    guard let viewModel = viewModel else { return }
#if true
    let input = ConnectEditViewModel.Input(
      viewDidLoad: .just(()),
      itemMoveEvent: tableView.rx.itemMoved.asObservable(),
      itemDeleteEvent: tableView.rx.itemDeleted.asObservable(), 
      tapConfirmButton: confirmButton.rx.tap.asObservable(),
      tapTestButton: testButton.rx.tap.asObservable()
    )
#else
    let input = ConnectEditViewModel.Input(
      viewDidLoad: .just(()),
      itemMoveEvent: tableView.rx.itemMoved.asObservable(),
      itemDeleteEvent: tableView.rx.itemDeleted.asObservable(),
      tapConfirmButton: confirmButton.rx.tap.asObservable()
    )
#endif
    let output = viewModel.bind(input: input, disposedBag: disposedBag)

    output.popToPrevious
      .bind(onNext: { [weak self] in
        self?.navigationController?.dismiss(animated: true)
      })
      .disposed(by: disposedBag)
    
  }
  
  func setComponent() {
    view.backgroundColor = .background

    tableView.backgroundColor = .clear
    tableView.rowHeight = 65 + 8
    tableView.separatorStyle = .none
    tableView.sectionHeaderHeight = 20
    tableView.sectionFooterHeight = 0
    tableView.dragInteractionEnabled = true
//    tableView.dragDelegate = self
    tableView.dropDelegate = self
    tableView.dataSource = self
    tableView.delegate = self

    tableView.register(
      ConnectEditCell.self,
      forCellReuseIdentifier: ConnectEditCell.description()
    )

    tableView.register(
      ConnectEditSectionHeaderView.self,
      forHeaderFooterViewReuseIdentifier: ConnectEditSectionHeaderView.description()
    )

    view.addSubview(tableView)
    view.addSubview(confirmButton)
  }
  
  func setAutoLayout() {
    tableView.snp.makeConstraints {
      $0.top.equalToSuperview()
      $0.leading.trailing.equalToSuperview()
    }
    
    confirmButton.snp.makeConstraints {
      $0.top.equalTo(tableView.snp.bottom)
      $0.leading.trailing.equalToSuperview().inset(20)
      $0.bottom.equalTo(view.safeAreaLayoutGuide)
      $0.height.equalTo(51.rv)
    }
  }
  
  func setTestBtn() {
#if DEBUG
    testButton.setTitle("+", for: .normal)
    testButton.backgroundColor = .lightGray
    navigationItem.leftBarButtonItem = UIBarButtonItem(customView: testButton)
#endif
  }
}

extension ConnectEditViewController: UITableViewDataSource {
  public func numberOfSections(in tableView: UITableView) -> Int {
    return viewModel.connectItems.count > 3 ? 2 : 1
  }
  public func tableView(
    _ tableView: UITableView,
    numberOfRowsInSection section: Int
  ) -> Int {
    if section == 0 {
      return viewModel.connectItems.count > 3 ? 3 : viewModel.connectItems.count
    }

    return viewModel.connectItems.count - 3
  }

  public func tableView(
    _ tableView: UITableView,
    viewForHeaderInSection section: Int
  ) -> UIView? {
    guard let headerView = tableView.dequeueReusableHeaderFooterView(
      withIdentifier: ConnectEditSectionHeaderView.description()
    ) as? ConnectEditSectionHeaderView else {
      return nil
    }

    let title: String? = section == 0 ? L.edit_device_list_information.localized : nil
    headerView.configure(title: title)
    return headerView
  }

  public func tableView(
    _ tableView: UITableView,
    cellForRowAt indexPath: IndexPath
  ) -> UITableViewCell {
    guard let cell = tableView.dequeueReusableCell(
      withIdentifier: ConnectEditCell.description(),
      for: indexPath
    ) as? ConnectEditCell else {
      return UITableViewCell()
    }

    guard viewModel.connectItems.count > 0 else { return cell }

    let index = indexPath.section == 0 ? indexPath.row : indexPath.row + 3
   
    cell.configure(viewData: viewModel.connectItems[index])
      
    cell.titleLabel.textColor = indexPath.section == 0 ? .mainBlack : .grayText
    cell.macAddressLabel.textColor = indexPath.section == 0 ? .mainBlack : .grayText
    return cell
  }
}

extension ConnectEditViewController: UITableViewDelegate {
  public func tableView(
    _ tableView: UITableView,
    shouldIndentWhileEditingRowAt indexPath: IndexPath
  ) -> Bool { true }

  public func tableView(
    _ tableView: UITableView,
    editingStyleForRowAt indexPath: IndexPath
  ) -> UITableViewCell.EditingStyle { .delete }

  public func tableView(
    _ tableView: UITableView,
    commit editingStyle: UITableViewCell.EditingStyle,
    forRowAt indexPath: IndexPath
  ) {
    if editingStyle == .delete {
      let index = indexPath.section == 0 ? indexPath.row : indexPath.row + 3
      var items = viewModel.connectItems
      items.remove(at: index)
      viewModel.connectItems = items
      tableView.reloadData()
    }
  }

}


// MARK: -

extension ConnectEditViewController: UITableViewDropDelegate {
  public func tableView(
    _ tableView: UITableView,
    performDropWith coordinator: UITableViewDropCoordinator
  ) {
    guard let destinationIndexPath = coordinator.destinationIndexPath,
          let item = coordinator.items.first?.dragItem.localObject as? DashcamCardCellModel else {
      return
    }

    let sourceIndexPath = coordinator.items.first?.sourceIndexPath

    coordinator.drop(coordinator.items.first!.dragItem, toRowAt: destinationIndexPath)

    let fromIndex = sourceIndexPath?.section == 0 ? sourceIndexPath!.row : sourceIndexPath!.row + 3
    let toIndex = destinationIndexPath.section == 0 ? destinationIndexPath.row : destinationIndexPath.row + 3

    var items = viewModel.connectItems
    items.remove(at: fromIndex)
    items.insert(item, at: toIndex)
    viewModel.connectItems = items

    tableView.reloadData()
  }
}
