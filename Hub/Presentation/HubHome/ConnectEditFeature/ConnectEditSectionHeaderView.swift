//
//  ConnectEditSectionHeaderView.swift
//  Hub
//
//  Created by ncn on 2/10/25.
//

import UIKit

class ConnectEditSectionHeaderView: UITableViewHeaderFooterView {
  private let titleLabel = UILabel()
  private let separatorLine = UIView()

  override init(reuseIdentifier: String?) {
    super.init(reuseIdentifier: reuseIdentifier)
    setupViews()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupViews() {
    titleLabel.font = .pretendard(ofSize: 13)
    titleLabel.textColor = .subText
    titleLabel.adjustsFontSizeToFitWidth = true
    titleLabel.minimumScaleFactor = 0.5
    contentView.addSubview(titleLabel)

    separatorLine.backgroundColor = .line
    contentView.addSubview(separatorLine)

    titleLabel.pin.horizontally(offset: 15).centerY().height(16).activate() 
    separatorLine.pin.horizontally(offset: 15).top().height(1).activate()
  }

  func configure(title: String?) {
    if let title = title {
      titleLabel.text = title
      titleLabel.isHidden = false
      separatorLine.isHidden = true
    } else {
      titleLabel.isHidden = true
      separatorLine.isHidden = false
    }
  }
}
