//
//  ConnectEditCell.swift
//
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 8/28/24.
//

import UIKit

public final class ConnectEditCell: UITableViewCell {
  let containerView: UIView = {
    let view = UIView()
    view.backgroundColor = .white
    view.layer.cornerRadius = 10
    return view
  }()

  let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .pretendard(ofSize: 15, weight: .bold)
    label.adjustsFontSizeToFitWidth = true
    label.numberOfLines = 1
    return label
  }()

  let macAddressLabel: UILabel = {
    let label = UILabel()
    label.textColor = .grayText
    label.font = .pretendard(ofSize: 12)
    label.adjustsFontSizeToFitWidth = true
    label.numberOfLines = 1
    return label
  }()

  let dragImageView: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "3_hozontal_line.pdf")
    return view
  }()

  public override init(
    style: UITableViewCell.CellStyle, reuseIdentifier: String?
  ) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent() {
    self.backgroundColor = .background
    contentView.addSubview(containerView)
    containerView.addSubview(titleLabel)
    containerView.addSubview(macAddressLabel)
    containerView.addSubview(dragImageView)
  }

  func setAutoLayout() {
    containerView.pin.horizontally(offset: 15).vertically(offset: 8).activate()
    titleLabel.pin.top(offset: 12).start(offset: 12).activate()
    macAddressLabel.pin.bottom(offset: -12).start(offset: 12).activate()
    dragImageView.pin.centerY().size(24).end(offset: -12).activate()
  }

  func configure(viewData: DashcamCardCellViewData) {
    titleLabel.text = viewData.title
    macAddressLabel.text = viewData.editConnectionMacAddress
  }
}
