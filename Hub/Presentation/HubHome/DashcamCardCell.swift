//
//  DashcamCardCell.swift
//
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 9/2/24.
//

import UIKit
import Combine

public final class DashcamCardCell: UICollectionViewCell {
  var viewContainer: UIView = {
    let view = UIView()
    view.clipsToBounds = true
    view.backgroundColor = .clear
    return view
  }()

  private let containerView = UIView()
  private let dashcamIcon = UIImageView()
  private let dashcamModelLabel = UILabel()

  let dashcamIconTapSubject = PassthroughSubject<Void, Never>()
  var cancellables = Set<AnyCancellable>()

  var deleteButton: UIButton = {
    let button = UIButton()
    var config = UIButton.Configuration.plain()
    config.image = #imageLiteral(resourceName: "xmark.pdf")
    config.imagePlacement = .leading
    config.imagePadding = 4
    config.titleAlignment = .leading
    config.background.backgroundColor = .clear
    let titleAttributes: [NSAttributedString.Key: Any] = [
      .font: UIFont.body2,
      .foregroundColor: UIColor.subText
    ]
    config.attributedTitle = AttributedString(L.dialog_delete_title.localized, attributes: AttributeContainer(titleAttributes))
    config.contentInsets = NSDirectionalEdgeInsets(top: 5, leading: 7, bottom: 5, trailing: 7)
    button.configuration = config

    button.layer.borderWidth = 1
    button.layer.borderColor = UIColor.line.cgColor
    button.layer.cornerRadius = 6
    return button
  }()


  let infoContainerView = UIView()
  private let dashcamVolt = UILabel()
  private let dashcamFirmware = UILabel()
  private let dashcamState = DashCamStatusChipView(type: .driving)
  let dashcamConnectButton = UIButton.customStyleButton(title: L.home_disconnect_wifi.localized)

  var disconnectCoverView: UIView = {
    let view = UIView()
    view.backgroundColor = .grayButton.withAlphaComponent(0.55)
    view.isUserInteractionEnabled = false
    return view
  }()

  public override init(frame: CGRect) {
    super.init(frame: frame)
    contentView.backgroundColor = .clear
    contentView.addBody(viewContainer)
    setComponent()
    setAutoLayout()
    setupGesture()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public override func prepareForReuse() {
    super.prepareForReuse()

    // remove actions
    cancellables.removeAll()
    deleteButton.removeTarget(nil, action: nil, for: .allEvents)
    dashcamConnectButton.removeTarget(nil, action: nil, for: .allEvents)
}

  private func setupGesture() {
    hLogger.trace("setupGesture")
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapDashcamIcon))
    dashcamIcon.addGestureRecognizer(tapGesture)
  }

  @objc private func didTapDashcamIcon() {
    hLogger.trace("didTapDashcamIcon")
    dashcamIconTapSubject.send()
  }


  func setComponent() {
    containerView.roundCorners(.allCorners, radius: 10)
    containerView.clipsToBounds = true
    containerView.backgroundColor = .white
    viewContainer.addSubview(containerView)

    dashcamIcon.image = #imageLiteral(resourceName: "s1_4k_product.png")
    dashcamIcon.contentMode = .scaleAspectFit
    dashcamIcon.isUserInteractionEnabled = true
    viewContainer.addSubview(dashcamIcon)

    dashcamModelLabel.text = "S1-4K"
    dashcamModelLabel.font = .h1
    dashcamModelLabel.textColor = .mainBlack
    dashcamModelLabel.textAlignment = .left
    dashcamModelLabel.sizeToFit()
    containerView.addSubview(dashcamModelLabel)
    containerView.addSubview(dashcamState)

    // Info
    containerView.addSubview(infoContainerView)
    dashcamVolt.text = "Volt 00.0V"
    dashcamVolt.font = .sub1
    dashcamVolt.textColor = .subText
    infoContainerView.addSubview(dashcamVolt)

    dashcamFirmware.text = "FW 1.0.0"
    dashcamFirmware.font = .sub1
    dashcamFirmware.textColor = .subText
    infoContainerView.addSubview(dashcamFirmware)

    viewContainer.addSubview(disconnectCoverView)
    viewContainer.addSubview(deleteButton)
    viewContainer.addSubview(dashcamConnectButton)
  }

  func setAutoLayout() {
    dashcamIcon.snp.makeConstraints {
      $0.top.equalToSuperview().inset(0)
      $0.centerX.equalToSuperview()
      $0.height.equalTo(175.rv)
      $0.width.equalTo(309.rv)
    }

    containerView.snp.makeConstraints {
      $0.top.equalTo(dashcamIcon.snp.bottom)
      $0.leading.trailing.bottom.equalToSuperview().inset(0)
    }

    dashcamModelLabel.snp.makeConstraints {
      $0.top.equalToSuperview().offset(15)
      $0.leading.equalToSuperview().inset(15)
//      $0.width.equalTo(150)
    }

    dashcamState.snp.makeConstraints {
      $0.centerY.equalTo(dashcamModelLabel)
      $0.height.equalTo(20.rv)
      $0.width.equalTo(52.wrv)
      $0.leading.equalTo(dashcamModelLabel.snp.trailing).offset(3)
    }

    infoContainerView.snp.makeConstraints {
      $0.top.equalTo(dashcamModelLabel.snp.bottom).offset(12)
      $0.leading.trailing.equalToSuperview().inset(15)
      $0.height.equalTo(54.rv)
    }

    dashcamVolt.snp.makeConstraints {
      $0.top.equalToSuperview()
      $0.height.equalTo(18.rv)
      $0.leading.equalToSuperview()
    }

    dashcamFirmware.snp.makeConstraints {
      $0.top.equalTo(dashcamVolt.snp.bottom).offset(10.rv)
      $0.height.equalTo(18.rv)
      $0.leading.equalToSuperview()
    }

    disconnectCoverView.pin.all().activate()

    deleteButton.snp.makeConstraints {
      $0.centerY.equalTo(dashcamModelLabel)
      $0.trailing.equalToSuperview().inset(15)
      $0.height.equalTo(26.rv)
    }

    dashcamConnectButton.snp.makeConstraints {
      $0.leading.trailing.equalToSuperview().inset(15)
      $0.height.equalTo(49.rv)
      $0.top.equalTo(infoContainerView.snp.bottom).offset(15)
    }
  }

  func setCellModel(cellModel: DashcamCardCellModel) {
    dashcamVolt.attributedText = titleValueLabelString(prefix: "Volt", value: "\(cellModel.volt)")
    dashcamFirmware.attributedText = titleValueLabelString(prefix: "FW", value: "\(cellModel.firmwareVersion)")
    dashcamState.type = cellModel.state == .Driving ? .driving : .parking

    hLogger.info("DashcamCardCellModel state: \(cellModel.state.rawValue)")
    hLogger.info("DashcamCardCellModel isConnect: \(cellModel.isConnect)")
    hLogger.info("DashcamCardCellModel ssid: \(cellModel.ssid)")

    let modelName = cellModel.ssid.components(separatedBy: "_")[1]
    dashcamModelLabel.text = "\(modelName)_\(cellModel.macAddress)"
    
    if cellModel.isConnect {
      dashcamState.type = cellModel.state == .Driving ? .driving : .parking
      dashcamConnectButton.backgroundColor = .grayButton
      dashcamConnectButton.setTitleColor(.mainBlack, for: .normal)
      dashcamConnectButton.setTitle(L.home_disconnect_wifi.localized, for: .normal)
      disconnectCoverView.isHidden = true
    } else {
      dashcamState.type = .offline
      dashcamConnectButton.backgroundColor = .vueroidBlue
      dashcamConnectButton.setTitleColor(.white, for: .normal)
      dashcamConnectButton.setTitle(L.select_menu_description01.localized, for: .normal)
      disconnectCoverView.isHidden = false

      dashcamVolt.attributedText = titleValueLabelString(prefix: "Volt", value: " -")
      dashcamFirmware.attributedText = titleValueLabelString(prefix: "FW", value: " -")
    }
  }

  private func titleValueLabelString(prefix: String, value: String) -> NSAttributedString {
    let attributedString = NSMutableAttributedString(string: "\(prefix) ", attributes: [
      .foregroundColor: UIColor.subText,
      .font: UIFont.pretendard(ofSize: 15, weight: .bold) ?? .systemFont(ofSize: 15)
    ])

    attributedString.append(NSAttributedString(string: value, attributes: [
      .foregroundColor: UIColor.subText,
      .font: UIFont.pretendard(ofSize: 15) ?? .systemFont(ofSize: 15)
    ]))
    return attributedString
  }
}
