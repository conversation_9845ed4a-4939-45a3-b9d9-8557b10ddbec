//
//  DefaultHubHomeCoordinator.swift
//  Hub
//
//  Created by ncn on 11/26/24.
//

import UIKit

protocol HubHomeCoordinator: AnyObject {
  func navigate(to step: HubHomeSteps)
}

public protocol HubHomeCoordinatorFinishDelegate: AnyObject {
  func hubHomeCoordinatorDidFinish()
}
public enum HubHomeSteps: Step {
  case showHubHome
  case presentEditConnection
  case presentNotification
  case presentBleList
  case goToWifiSetting
  case presentEditFavorite
  case pushDeIdentifyScene
  case pushMapDetail
  case firmwareDownload(model: FirmwareModel)
  case hubHomeDidFinish
}

public enum HubHomeChildCoordinator {
  case bleList
  case popupDownload
}

final class DefaultHubHomeCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: HubHomeCoordinatorFinishDelegate?
  private var childCoordinators = [HubHomeChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  public func start() {
    navigate(to: .showHubHome)
  }
}

extension DefaultHubHomeCoordinator: HubHomeCoordinator {
  func navigate(to step: HubHomeSteps) {
    setNavigationBarAppearance()
    switch step {
    case .showHubHome:
      let vc = HubHomeController(coordinator: self)
      self.navigationController.isNavigationBarHidden = true
      self.navigationController.pushViewController(vc, animated: true)

    case .presentBleList:
      let bleCoordinator = DefaultBleListCoordinator(navigationController: navigationController)
      bleCoordinator.delegate = self
      childCoordinators[.bleList] = bleCoordinator
      bleCoordinator.start()
      break

    case .presentEditConnection:
      let items = UserDefaults.shared.dashcamCardCellModels
      let connectEditVM = ConnectEditViewModel(items: items)
      let connectEditVC = ConnectEditViewController(viewModel: connectEditVM)
      let nc = UINavigationController(rootViewController: connectEditVC)
      nc.modalPresentationStyle = .fullScreen
      if let hubHome = navigationController.viewControllers.last {
        hubHome.present(nc, animated: true)
      }
      break

    case .presentNotification:
      let items = UserDefaults.shared.notifications
      let listController = NotificationListViewController()
      let nc = UINavigationController(rootViewController: listController)
      nc.modalPresentationStyle = .fullScreen
      if let hubHome = navigationController.viewControllers.last {
        hubHome.present(nc, animated: true)
      }
      break

    case .presentEditFavorite:
      let nc = UINavigationController(rootViewController: FavoriteEditController())
      nc.modalPresentationStyle = .fullScreen
      if let hubHome = navigationController.viewControllers.last {
        hubHome.present(nc, animated: true)
      }
      break

    case .pushDeIdentifyScene:
      break
      
    case .pushMapDetail:
      cLogger.info(".pushMapDetail")
      let vc = MapDetailViewController()
      vc.weatherViewModel = WeatherViewModel()
      vc.hidesBottomBarWhenPushed = true
      self.navigationController.pushViewController(vc, animated: true)
      break
    
    case .firmwareDownload(model: let model):
      modalFirmwareDownload(model: model)
      break
      
    case .hubHomeDidFinish:
      break
    case .goToWifiSetting:
      // Navigate to system WiFi settings
      if let url = URL(string: UIApplication.openSettingsURLString) {
        UIApplication.shared.open(url, options: [:], completionHandler: nil)
      }
    }
  }
  
  func modalFirmwareDownload(model: FirmwareModel) {
    let downloadCoordinator = DefaultPopupDownloadCoordinator(navigationController: navigationController)
    downloadCoordinator.delegate = self
    childCoordinators[.popupDownload] = downloadCoordinator
    downloadCoordinator.start(with: .firmwareDownload(model: model))
  }
}

// MARK: FinishDelegate
extension DefaultHubHomeCoordinator: BleListCoordinatorFinishDelegate {
  func bleListCoordinatorDidFinish() {
    childCoordinators[.bleList] = nil
  }
}

extension DefaultHubHomeCoordinator: PopupDownloadCoordinatorFinishDelegate {
  func popupDownloadCoordinatorDidFinish() {
    childCoordinators[.popupDownload] = nil
  }
}
