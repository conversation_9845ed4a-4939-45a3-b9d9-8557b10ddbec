//
//  DashcamCardCellModel.swift
//
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 9/2/24.
//

import UIKit
import Foundation

public enum DashcamState: Int, Codable {
  case Driving = 0
  case Parking
  case Off
  case Sleep
  
  public var toText: String {
    switch self {
    case .Driving:
      "DRIVING"
    case .Parking:
      "PARKING"
    case .Off:
      "OFF"
    case .Sleep:
      "SLEEP"
    }
  }
}

public protocol DashcamCardCellViewData {
  var title: String { get }
  var editConnectionMacAddress: String { get }
}


public struct DashcamCardCellModel: Codable, Equatable {
  public var isConnect: Bool
  
  public let macAddress: String
  public let ssid: String
  public let password: String
  public let icon: String
  public let model: String
  public var volt: String
  public let firmwareVersion: String
  public let state: DashcamState
  
  public static func == (lhs: Self, rhs: Self) -> Bool {
    return lhs.macAddress == rhs.macAddress
  }
  
  public init(
    isConnect: Bool,
    macAddress: String,
    ssid: String,
    password: String,
    icon: String,
    model: String,
    volt: String,
    firmwareVersion: String,
    state: DashcamState
  ) {
    self.isConnect = isConnect
    self.macAddress = macAddress
    self.ssid = ssid
    self.password = password
    self.icon = icon
    self.model = model
    self.volt = volt
    self.firmwareVersion = firmwareVersion
    self.state = state
  }
  
  public mutating func toggleConnect() {
    self.isConnect.toggle()
  }
  
  public static func dummy(_ index: Int = 0) -> DashcamCardCellModel {
    
    // random 6 hex digit like 7fca43, 7fd023    
    let mac = (0..<3).map { _ in
      String(format: "%02x", arc4random_uniform(256))
    }.joined()
    
    hLogger.debug("dummy macAddress: \(mac)")
    let ssid = "VUEROID_S1_\(mac)_5G"
    let volt = String(format: "%.1f", Double.random(in: 13.8...14.3))
    return .init(
      isConnect: false,
      macAddress: mac,
      ssid: ssid,
      password: "wert2345",
      icon: "s1_4k_product",
      model: "S1-4K",
      volt: "\(volt)V",
      firmwareVersion: "0.59",
      state: DashcamState.Driving
    )
  }
}

extension DashcamCardCellModel : DashcamCardCellViewData {
  public var title: String {
    return "\(model)_\(macAddress)"
  }
  
    //"7fca43" -> "98-03-cf-7f-ca-43"
  public var editConnectionMacAddress: String {
    let mac = macAddress
    let formattedMac = mac.enumerated().map { index, char in
      return (index % 2 == 0 && index != 0) ? "-\(char)" : "\(char)"
    }.joined()
    return "98-03-cf-\(formattedMac)"
  }
}
