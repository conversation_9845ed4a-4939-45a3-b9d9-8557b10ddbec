//
//  StackViewController.swift
//
//
//  Created by ncn on 10/31/24.
//

import UIKit

public class StackViewController: UIViewController {
  private let scrollView = UIScrollView()
  private let stackView = UIStackView()

  public override func viewDidLoad() {
    super.viewDidLoad()
    view.addBody(scrollView, safe: true)
    scrollView.addBody(stackView)
    scrollView.showsVerticalScrollIndicator = false
    stackView.axis = .vertical
  }
}

extension StackViewController {
  func add(view: UIView, size: CGSize) {
    stackView.addArrangedSubview(view)
    view.pin.size(size).activate()
  }

  func add(_ child: UIViewController, size: CGSize) {
    addChild(child)
    child.view.isUserInteractionEnabled = true
    stackView.addArrangedSubview(child.view)
    child.didMove(toParent: self)

    child.view.translatesAutoresizingMaskIntoConstraints = false
    NSLayoutConstraint.activate([
      child.view.heightAnchor.constraint(equalToConstant: size.height),
      child.view.widthAnchor.constraint(equalToConstant:size.width)
    ])
  }

  func remove(_ child: UIViewController) {
    guard child.parent != nil else {
      return
    }

    child.willMove(toParent: nil)
    stackView.removeArrangedSubview(child.view)
    child.view.removeFromSuperview()
    child.removeFromParent()
  }
}
