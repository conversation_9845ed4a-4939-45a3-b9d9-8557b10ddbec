//
//  FavoriteSectionViewModel.swift
//  Hub
//
//  Created by ncn on 3/4/25.
//

import UIKit
import Combine

protocol FavoriteSectionViewModelProtocol: AnyObject {
  func bindSheetSelection(from sheetVC: SheetViewController)
  func sendFastConfiguration()
  func getFastConfiguration(item: FavoriteFastSettingItem)
  
  var fastSettingItem: FavoriteFastSettingItem? { set get }
  var viewState: CurrentValueSubject<FavoriteSectionViewModel.ViewState, Never> { get }
}

final class FavoriteSectionViewModel: FavoriteSectionViewModelProtocol {
  var fastSettingItem: FavoriteFastSettingItem? = nil
  
  private var cancellables = Set<AnyCancellable>()
  var viewState = CurrentValueSubject<ViewState, Never>(.empty)
  
  func bindSheetSelection(from sheetVC: SheetViewController) {
    sheetVC.selectedRow
      .sink { [weak self] selectedValue in
        self?.viewState.send(.selectValue(selectedValue))
        self?.viewState.send(.closeSheet)
      }
      .store(in: &cancellables)
  }

  func sendFastConfiguration() {
    guard let item = fastSettingItem else { return }
    hLogger.info("fastSettingItem: \(item.fastConfig.type.rawValue)")
    let header = HeaderModel(cmd: Command.setfastconfiguration)
    let send = SetFastConfiguration.Send(header: header, setfastconfiguration: item.fastConfig)

    Composers.homeUseCase.send(to: send) { [weak self] response, error in
      guard let self else { return }
      if let error = error {
        hLogger.error("Failed to set configuration: \(error)")
        viewState.send(.error(error.localizedDescription))
        return
      }

      if let _ = response {
        
        if item.fastConfig.type == .speedunit {
          Current.speedUnit = item.fastConfig.value == 0 ? .kph : .mph
        }
        
        hLogger.info("SetFastConfiguration success")
        viewState.send(.updatedSetting)
      }
    }
  }
  
  func getFastConfiguration(item: FavoriteFastSettingItem) {
    fastSettingItem = item
    hLogger.info("fastSettingItem: \(item.fastConfig.type.rawValue)")
    let header = HeaderModel(cmd: Command.getfastconfiguration)
    let send = GetFastConfiguration.Send(header: header, getfastconfiguration: item.fastConfig)

    Composers.homeUseCase.send(to: send) { [weak self] response, error in
      guard let self else { return }
      if let error = error {
        hLogger.error("Failed to set configuration: \(error)")
        viewState.send(.error(error.localizedDescription))
        return
      }
      
      if let getfastconfiguration = response?.getfastconfiguration,
         var fastSettingItem = self.fastSettingItem,
         let resultValue = getfastconfiguration[fastSettingItem.fastConfig.type.rawValue] {
        
        fastSettingItem.savedConfigValue = resultValue
        hLogger.info("GetFastConfiguration: \(fastSettingItem.fastConfig.type.rawValue), value: \(resultValue)")
        viewState.send(.getfastSetting(fastSettingItem, resultValue))
      }
    }
  }
}

extension FavoriteSectionViewModel {
  enum ViewState: Equatable {
//    case showSheet
    case selectValue(Int?)
    case updatedSetting
    case getfastSetting(FavoriteFastSettingItem, Int)
    case error(String)
    case closeSheet
    case empty

    static public func == (lhs: ViewState, rhs: ViewState) -> Bool {
      switch (lhs, rhs) {
      case (.closeSheet, .closeSheet):
        return true
      case (.error, .error):
        return true
      case (.empty, .empty):
        return true
      default:
        return false
      }
    }
  }
}
