//
//  AIPlateButton.swift
//  Hub
//
//  Created by ncn on 1/6/25.
//

import UIKit

class AIPlateButton: UIButton {

  override init(frame: CGRect) {
    super.init(frame: frame)
    initialize()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  var leftImage: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "ai_plate.pdf")
    return view
  }()

  var aiTitleLabel: UILabel = {
    let label = UILabel()
    label.text = L.plate_restoration_privacy_protection.localized
    label.font = .pretendard(ofSize: 15)
    label.textAlignment = .left
    label.numberOfLines = 2
    label.textColor = .subText
    label.sizeToFit()
    label.adjustsFontSizeToFitWidth = true
    label.minimumScaleFactor = 0.5
    return label
  }()

  var rightImage: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "day_next.pdf")
    return view
  }()

  func initialize() {
    backgroundColor = .white
    addSubviews([leftImage, aiTitleLabel, rightImage])
    leftImage.pin.size(38).start(offset: 15).centerY().activate()
    aiTitleLabel.pin.height(38).after(leftImage, offset: 15).centerY().activate()
    rightImage.pin.size(24).end(offset: -15).centerY().activate()
    aiTitleLabel.pin.before(rightImage, offset: -15).activate()
  }

  override func layoutSubviews() {
    super.layoutSubviews()
    layer.cornerRadius = 10
  }
}
