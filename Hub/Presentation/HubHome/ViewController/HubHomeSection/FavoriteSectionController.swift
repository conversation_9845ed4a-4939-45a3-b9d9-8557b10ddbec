//
//  FavoriteSectionController.swift
//
//
//  Created by ncn on 10/31/24.
//

import UIKit
import Combine

public class FavoriteSectionController: UIViewController {
  
  let containerView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()
  
  let titleLabel: UILabel = {
    let label = UILabel()
    label.text = L.text_favorites.localized
    label.textColor = .mainBlack
    label.textAlignment = .left
    label.font = .pretendard(ofSize: 17, weight: .bold)
    return label
  }()
  
  let editButton: UIButton = {
    let button = UIButton()
    button.setTitle(L.home_edit_text.localized, for: .normal)
    button.setTitleColor(.secondaryPrimary, for: .normal)
    button.titleLabel?.font = .pretendard(ofSize: 12, weight: .bold)
    button.titleLabel?.textAlignment = .right
    return button
  }()
  
  let buttonStackView: UIStackView = {
    let stackView = UIStackView()
    stackView.axis = .horizontal
    stackView.spacing = 10
    stackView.distribution = .fillEqually
    return stackView
  }()
  
  let shortcutStackEmptyView = UIView()
  let shortcutStackEmptyButton = UIButton()
  private let activationFeedBackGenerator = UIImpactFeedbackGenerator(style: .heavy)
  
  private lazy var sheetMenuViewController: SheetViewController = {
    let viewController = SheetViewController()
    let viewModel = SheetMenuViewModel()
    viewController.viewModel = viewModel
    return viewController
  }()

  private let viewModel: FavoriteSectionViewModelProtocol
  weak var coordinator: HubHomeCoordinator?
  
  private var disposeBag = Set<AnyCancellable>()
  
  init(viewModel: FavoriteSectionViewModelProtocol ,coordinator: HubHomeCoordinator?) {
    self.viewModel = viewModel
    self.coordinator = coordinator
    super.init(nibName: nil, bundle: nil)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  public override func viewDidLoad() {
    super.viewDidLoad()
    view.backgroundColor = .background
    view.addSubview(containerView)
    
    containerView.addSubviews([titleLabel, editButton, buttonStackView])
    
    editButton.addAction(UIAction { [weak self] _ in
      self?.didTapEditButton()
    } , for: .touchUpInside)
    
    Pin.activate([
      containerView.pin.horizontally(offset: 15).top(offset: 20.rv).bottom(),
      titleLabel.pin.top().start().width(80),
      editButton.pin.centerY(titleLabel).end().width(50),
      buttonStackView.pin.below(titleLabel, offset: 10.rv).horizontally().height(100.rv).bottom()
    ])
   
    subscribeToViewState()
  }

  public override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    
    let savedFastSetting = UserDefaults.shared.favoriteFastSettingItems
    buttonStackView.removeAllArrangedSubviews()
    // Add views for saved settings
    savedFastSetting.forEach { item in
      let favoriteView = FavoriteItemView(type: item)
      favoriteView.isUserInteractionEnabled = true
      let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapSettingItem(_:)))
      favoriteView.addGestureRecognizer(tapGesture)
      buttonStackView.addArrangedSubview(favoriteView)
    }
    
    // Fill remaining spots with empty items
    let remainingSpots = 3 - savedFastSetting.count
    for _ in 0..<remainingSpots {
      buttonStackView.addArrangedSubview(FavoriteItemView())
    }
  }
  
  private func subscribeToViewState() {
    viewModel
      .viewState
      .receive(on: defaultScheduler)
      .sink(receiveCompletion: { _ in }, receiveValue: { [weak self] state in
        self?.configureView(with: state)
      })
      .store(in: &disposeBag)
  }

  private func configureView(with state: FavoriteSectionViewModel.ViewState) {
    switch state {
    case let .selectValue(value):
      hLogger.info("sheet select value: \(value ?? 0)")
      viewModel.fastSettingItem?.fastConfig.value = value ?? 0
      viewModel.sendFastConfiguration()
      break
      
    case .updatedSetting:
      LKPopupView.popup.alert {[
        .title(L.dialog_setting_title.localized),
        .subTitle(L.dialog_setting_success.localized),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({ })
        ])
      ]}
      break
      
    case .getfastSetting(let item, let value):
      hLogger.info("get fast setting value: \(value)")

      // show SheetMenu
      let sheetModel = SheetMenuModel(title: item.getCurrentLocalTitle(), items: item.selectionValues)
      sheetMenuViewController.setItems(model: sheetModel)
      sheetMenuViewController.show()

      DispatchQueue.main.async {
        let path = IndexPath(row: item.savedConfigValue, section: 0)
        self.sheetMenuViewController.tableView.selectRow(
          at: path, animated: false, scrollPosition: .none
        )
      }

      break
      
      
    case let .error(msg):
      LKPopupView.popup.toast(hit: "\(msg)")
    case .closeSheet:
      sheetMenuViewController.hide()
    case .empty:
      viewModel.bindSheetSelection(from: sheetMenuViewController)
      break
    }
  }
}

extension FavoriteSectionController {
  func didTapEditButton() {
    hLogger.info("didTapEditButton")
    coordinator?.navigate(to: .presentEditFavorite)
  }

  @objc func didTapSettingItem(_ gesture: UITapGestureRecognizer) {
    guard let item = (gesture.view as? FavoriteItemView)?.type else {
      return
    }

    viewModel.fastSettingItem = item
    hLogger.info("didTapSettingItem : \(item.getCurrentLocalTitle()), type: \(item.fastConfig.type.rawValue)")
    activationFeedBackGenerator.impactOccurred()
    
    guard Current.isConnect else {
      LKPopupView.popup.alert {[
        .subTitle(L.blackbox_disconnect.localized),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({
            self.tabBarController?.selectedIndex = HubChildCoordinator.home.rawValue
          })
        ])
      ]}
      
      return
    }
    
    if let driveMode = AppManager.shared.initInfo?.driveMode,
       let state = DashcamState(rawValue: driveMode), state == .Parking &&
      AppManager.shared.dashcamWifiConfig?.sleepmode == 1  {
      LKPopupView.popup.alert {[
        .subTitle(L.s1_disable_setting_in_parking.localized),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({ })
        ])
      ]}
    } else {
      viewModel.getFastConfiguration(item: item)
    }
  }
}
