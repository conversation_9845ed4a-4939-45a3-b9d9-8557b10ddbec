//
//  NewHomeViewModel.swift
//
//
//

import UIKit
import Combine

protocol HubHomeConnectSectionViewModelProtocol: AnyObject {
  // MARK: - Input
  func viewWillAppear()

  // etc
  func connect(macAddress: String?)
  func disconnect()
  func verifyConnect()
  func updateConnectDeviceList()
  func updateConnectedVolatage()
  func requestFirmware()

  func updateViewData()
  // MARK: - Output

  var viewState: CurrentValueSubject<HubHomeConnectSectionViewModel.ViewState, Never> { get }

  // TODO(Hub): Replace Combine
  var viewDatas : [DashcamCardCellModel] { get }
}

final class HubHomeConnectSectionViewModel: HubHomeConnectSectionViewModelProtocol {
  private let useCase: HomeUseCase
  var retryCount = 5

  // MARK: - Public Api
  var viewState = CurrentValueSubject<ViewState, Never>(.empty)
  var viewDatas : [DashcamCardCellModel] = []

  init(useCase: HomeUseCase, retryCount: Int = 5) {
    self.useCase = useCase
    self.retryCount = retryCount
  }

  deinit {
    hLogger.info("deinit \(Self.self)")
  }

  func viewWillAppear() {
    hLogger.info("\(Self.self) viewWillAppear")
    requestFirmware()
    updateConnectedVolatage()
    updateViewData()
  }

  func updateViewData() {
    let allCards = UserDefaults.shared.dashcamCardCellModels
    self.viewDatas = Array(allCards.prefix(3))
  }

  func connect(macAddress: String?) {
    if Current.isConnect { return }

    viewState.send(.loading)
    useCase.connect { [weak self] response, error in
      guard let self else { return }

      if let error = error {
        hLogger.debug("Connect websocket error \(error.localizedDescription)")
        hLogger.debug("retryCount: \(self.retryCount)")

        self.handleConnectionError(macAddress: macAddress)
        return
      }
      self.handleConnectionResponse(response: response, macAddress: macAddress)
    }
  }

  private func handleConnectionError(macAddress: String?) {
    retryCount -= 1
    if retryCount < 0 {
      viewState.send(.error("Connection Fails"))
    } else {
      let delay = TimeInterval(1 + retryCount)
      DispatchQueue.main.asyncAfter(deadline: .now() + delay) { [weak self] in
        self?.connect(macAddress: macAddress)
      }
    }
  }

  private func handleConnectionResponse(response: [String: Any]?, macAddress: String?) {
    hLogger.debug("connect response: \(response?.debugDescription ?? "")")

    if let value = response?["connect"] as? String, value == "true" {
      hLogger.debug("macAddress: \(macAddress ?? "nil")")
#if USE_UPPERCASE_SSID
      Current.connectedMac = macAddress?.uppercased()
#else
      Current.connectedMac = macAddress
#endif

      viewState.send(Current.connectedMac == nil ? .firstConnecting : .connecting)
    } else {
      viewState.send(.error("Wifi Connection Fails"))
    }
  }

  func disconnect() {
    // disconnect Card Cell
    UserDefaults.shared.dashcamCardCellModels = UserDefaults.shared.dashcamCardCellModels.map { cellModel in
      var updatedModel = cellModel
      if cellModel.macAddress == Current.connectedMac {
        updatedModel.isConnect = false
      }
      return updatedModel
    }

    AppManager.shared.deviceInfo = nil

    updateViewData()
    Current.connectedMac = nil
    MainAsync {
      self.viewState.send(.updateDeviceList)
    }
  }

  func verifyConnect() {
    hLogger.debug("Current.connectedMac: \(Current.connectedMac ?? "nil")")

    let connect = Current.connectedMac ?? VerifyPasswordModel.ConnectValue.firstConnect.rawValue
    hLogger.info("verifyConnect connect: \(connect)")
    let header = HeaderModel(seq: 1, cmd: Command.verifypassword)
    let send = VerifyPasswordModel.Send(header: header, verifypassword: .init(connect: connect))
    useCase.send(to: send) { response, error in
      if let error = error {
        self.viewState.send(.error("\(error.localizedDescription)"))
        return
      }
      guard let result = response?.verifypassword.connect else { return }
      hLogger.info("verifyConnect result: \(result)")

      if result.contains("\(VerifyPasswordModel.ConnectValue.userCancel.rawValue)") ||
          result.contains("\(VerifyPasswordModel.ConnectValue.misMatch.rawValue)") {
        self.viewState.send(.userCancel)
      } else {
        if connect.contains("\(VerifyPasswordModel.ConnectValue.firstConnect.rawValue)") {
          Current.connectedMac = result.uppercased()
        }
        self.viewState.send(.connected(mac: result))
      }
    }
  }

  func updateConnectDeviceList() {
    let header = HeaderModel(cmd: Command.getInformation)
    let send = GetInformation.Send(header: header)

    self.useCase.sendGetInfo(to: send) { [weak self] response, error in
      guard let self else { return }

      if let error = error {
        let msg = "Failed to get device information :\(error.localizedDescription)"
        viewState.send(.error("\(msg)"))
        return
      }

      guard let getInfo = response?.getinformation else {
        viewState.send(.error("Invalid device information"))
        return
      }

      AppManager.shared.dashcam = response?.toDomain()

      let send = GetInitInfo.Send(header: HeaderModel(cmd: Command.getInitInfo))


      useCase.sendGetInitInfo(to: send)  { [weak self] response, error in
        guard let self, let initInfo = response?.getinitinfo else { return }
        if let error = error {
          let msg = "Failed to get initInfo: \(error.localizedDescription)"
          viewState.send(.error("\(msg)"))
          return
        }

        let header = HeaderModel(cmd: Command.getconfiguration)
        let configSend = GetConfiguration.Send(header: header)
        useCase.send(to: configSend) { [weak self] response, error in
          guard let self else { return }
          if let error = error {
            let msg = "Failed to getConfig: \(error.localizedDescription)"
            viewState.send(.error("\(msg)"))
            return
          }

          guard let verifyMac = Current.connectedMac else {
            self.viewState.send(.error("Current.connectedMac is nil"))
            return
          }

          guard let wifiSsid = Current.connectedWifiSsid else {
            self.viewState.send(.error("Current.wifiBandwidth is nil"))
            return
          }

          let password = Current.connectedWifiPassWord
          hLogger.info("ssid: \(wifiSsid), password: \(password)")
          let volt = initInfo.voltage ?? "xx.x"
          hLogger.info("volt: \(volt), response voltage: \(initInfo.voltage ?? "")")
          hLogger.info("initInfo driveMode: \(initInfo.driveMode ?? 0)")

#if USE_UPPERCASE_SSID
          let macaddress = verifyMac.uppercased()
#else
          let macaddress = verifyMac
#endif

          let cellModel = DashcamCardCellModel(
            isConnect: true,
            macAddress: "\(verifyMac.uppercased())",
            ssid: wifiSsid,
            password: password,
            icon: "s1_4k_product",
            model: "\(getInfo.model)",
            volt: "\(volt)",
            firmwareVersion: "\(getInfo.version)",
            state: DashcamState(rawValue: initInfo.driveMode ?? 0) ?? .Driving
          )

          var savedModels = UserDefaults.shared.dashcamCardCellModels

          if let existingIndex = savedModels.firstIndex(where: { $0.macAddress.uppercased() == cellModel.macAddress.uppercased() }) {
            if savedModels[existingIndex].isConnect != cellModel.isConnect ||
                savedModels[existingIndex].ssid != cellModel.ssid {
              if savedModels[existingIndex].ssid != cellModel.ssid {
                self.disconnect()
              }
              savedModels.remove(at: existingIndex)
              savedModels.insert(cellModel, at: 0)
              UserDefaults.shared.dashcamCardCellModels = savedModels
            }
          } else {
            // Add new device
            savedModels.insert(cellModel, at: 0)
            UserDefaults.shared.dashcamCardCellModels = savedModels
          }

          updateViewData()
          self.viewState.send(.updateDeviceList)
        }
      }
    }
  }

  func updateConnectedVolatage() {
    hLogger.info("updateConnectedVolatage isConnect: \(Current.isConnect)")
    guard Current.isConnect == true else { return }

    let send = GetInitInfo.Send(header: HeaderModel(cmd: Command.getInitInfo))
    useCase.sendGetInitInfo(to: send)  { [weak self] response, error in
      guard let self, let initInfo = response?.getinitinfo else { return }
      let volt = initInfo.voltage ?? "xx.x"
      hLogger.info("volt: \(volt), response voltage: \(initInfo.voltage ?? "")")

      // UserDefaults.shared.dashcamCardCellModels 중 isConnect true인 cellModel 의 volt를 update
      UserDefaults.shared.dashcamCardCellModels = UserDefaults.shared.dashcamCardCellModels.map { cellModel in
        var updatedModel = cellModel
        if cellModel.isConnect {
          updatedModel.volt = volt
        }
        return updatedModel
      }
      self.viewState.send(.updateConnectedDevice)
    }
  }

  func requestFirmware() {
    guard let modelName = AppManager.shared.deviceInfo?.model,
      let currentVersion = AppManager.shared.deviceInfo?.version,
      let tempPath = UrlList.tempPath()
    else {
      return
    }

    let isFirmwareFileExist = { (versionText: String) -> Bool in
      hLogger.debug("versionText: \(versionText)")
      return FileManager.loadFileAtFolder(path: tempPath).contains { url in
        let fileName = url.lastPathComponent
        hLogger.debug("temp fileName: \(fileName)")
        return fileName.contains("\(Current.modelName)") && fileName.contains("v\(versionText)")
      }
    }
    Current.deviceVersion = Version(currentVersion)
    let baseUrl = "\(Current.firmWareUrl)\(Current.firmWarePrefix)\(modelName)"
    hLogger.info("firmware: baseUrl: \(baseUrl)")
    guard let url = URL(string: baseUrl) else {
      return
    }

    var request = URLRequest(url: url)
    request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
    request.setValue("application/json", forHTTPHeaderField: "Accept")
    request.httpMethod = "GET"

    URLSession.shared.dataTask(with: request) { data, response, error in
      guard let data = data,
        let response = response as? HTTPURLResponse,
        error == nil
      else { return }

      guard (200...299) ~= response.statusCode else { return }

      if String(data: data, encoding: .utf8) != nil {
        data.parsing(type: FirmwareModel.self) {[weak self] data, error in
          guard let self else { return }
          if let value = data {
            Current.remoteFirmwareVersion = .init(value.ver)
            hLogger.info("remote firmwareVersion: \(Current.remoteFirmwareVersion.asString())")
            if isFirmwareFileExist(value.ver) {
              hLogger.warning("FirmwareFileExist - \(value.file)")
            } else {
              viewState.send(.firmwareUpdateAvailable(model: value))
            }
          }

          if let error = error {
            hLogger.error("parsing Error - \(error.localizedDescription)")
          }
        }
      } else {
        hLogger.warning("unable to parse response as string")
      }
    }.resume()
  }
}

extension HubHomeConnectSectionViewModel {
  enum ViewState: Equatable {
    case loading
    case firstConnecting
    case connecting
    case connected(mac: String)
    case userCancel
    case updateDeviceList
    case updateConnectedDevice
    case firmwareUpdateAvailable(model: FirmwareModel)
    case error(String)
    case empty

    static public func == (lhs: ViewState, rhs: ViewState) -> Bool {
      switch (lhs, rhs) {
      case (.loading, .loading):
        return true
      case (.connecting, .connecting):
        return true
      case (.firstConnecting, .firstConnecting):
        return true
      case (.connected, .connected):
        return true
      case (.updateDeviceList, .updateDeviceList):
        return true
      case (.error, .error):
        return true
      case (.empty, .empty):
        return true
      default:
        return false
      }
    }
  }
}
