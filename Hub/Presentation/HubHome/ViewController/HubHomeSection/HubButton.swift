//
//  HubButton.swift
//  Hub
//
//  Created by ncn on 12/17/24.
//



import UIKit

open class HubButton: UIButton {
  private struct Const {
    static let borderWidth: CGFloat = 1
    static let cornerRadius: CGFloat = 2
  }

  open override func tintColorDidChange() {
    super.tintColorDidChange()
    update()
  }

  open override var isHighlighted: Bool {
    didSet {
      if isHighlighted != oldValue {
        update()
      }
    }
  }

  open override var isEnabled: Bool {
    didSet {
      if isEnabled != oldValue {
        update()
      }
    }
  }

  private var animator = UIViewPropertyAnimator()
  private let activationFeedBackGenerator = UIImpactFeedbackGenerator(style: .light)

  public init() {
    super.init(frame: .zero)
    initialize()
  }

  public override init(frame: CGRect) {
    super.init(frame: frame)
    initialize()
  }

  public required init?(coder aDecoder: NSCoder) {
    super.init(coder: aDecoder)
    initialize()
  }

  open override func layoutSubviews() {
    super.layoutSubviews()
    layer.cornerRadius = Const.cornerRadius
  }

  func initialize() {
    update()
    NotificationCenter.default.addObserver(self, selector: #selector(didContentSizeCategoryDidChange),
                                           name: UIContentSizeCategory.didChangeNotification, object: nil)
    addTarget(self, action: #selector(didTouchDown), for: [.touchDown, .touchDragEnter])
    addTarget(self, action: #selector(didTouchUp), for: [.touchUpInside, .touchDragExit, .touchCancel])
  }

  private func update() {
  }

}

// MARK: - event
extension HubButton {
  @objc private func didContentSizeCategoryDidChange() {
    update()
  }

  @objc private func didTouchDown() {
    animator.stopAnimation(true)
//    backgroundColor = style.hightlightBackgroundColor
//    layer.borderColor = Colors.Button.borderHighlighted.cgColor
    activationFeedBackGenerator.impactOccurred()
  }

  @objc private func didTouchUp() {
    animator = UIViewPropertyAnimator(duration: 0.5, curve: .easeOut, animations: {
//      self.backgroundColor = self.style.backgroundColor
//      self.layer.borderColor = Colors.Button.border.cgColor
    })
    animator.startAnimation()
  }
}
