//
//  HubHomeViewController.swift
//
//
//

import UIKit
import Combine
import Network
import Foundation

// MARK: ConnectSectionController
class ConnectSectionController: UIViewController {
  private enum Const {
    static let itemSize = CGSize(width: CGRectGetWidth(UIScreen.main.bounds) * 0.85, height: 366.rv)
    static let itemSpacing = 30.0

    static var insetX: CGFloat {
      (UIScreen.main.bounds.width - Self.itemSize.width) / 2.0
    }

    static var collectionViewContentInset: UIEdgeInsets {
      UIEdgeInsets(top: 0, left: Self.insetX, bottom: 0, right: Self.insetX)
    }
  }

  private let collectionViewFlowLayout: UICollectionViewFlowLayout = {
    let layout = UICollectionViewFlowLayout()
    layout.scrollDirection = .horizontal
    layout.itemSize = Const.itemSize
    layout.minimumLineSpacing = Const.itemSpacing
    layout.minimumInteritemSpacing = 0
    return layout
  }()

  private lazy var cardCollectionView: UICollectionView = {
    let view = UICollectionView(frame: .zero, collectionViewLayout: self.collectionViewFlowLayout)
    view.isScrollEnabled = true
    view.showsHorizontalScrollIndicator = false
    view.showsVerticalScrollIndicator = true
    view.backgroundColor = .background
    view.clipsToBounds = true
    view.register(DashcamCardCell.self, forCellWithReuseIdentifier: DashcamCardCell.description())
    view.register(DashcamEmptyCardCell.self, forCellWithReuseIdentifier: DashcamEmptyCardCell.description())
    view.isPagingEnabled = false
    view.contentInsetAdjustmentBehavior = .never
    view.contentInset = Const.collectionViewContentInset
    view.decelerationRate = .fast
    view.translatesAutoresizingMaskIntoConstraints = false
    return view
  }()

  private lazy var pageControl: UIPageControl = {
    let pageControl = UIPageControl()
    pageControl.currentPageIndicatorTintColor = .vueroidBlue
    pageControl.pageIndicatorTintColor = .line
    pageControl.addTarget(self, action: #selector(pageControlTapped(_:)), for: .valueChanged)
    return pageControl
  }()

//  private var items : [DashcamCardCellModel] {
//    let allCards = UserDefaults.shared.dashcamCardCellModels
//    return Array(allCards.prefix(3))
//  }

  private var currentPage = 0

  private let activationFeedBackGenerator = UIImpactFeedbackGenerator(style: .heavy)
  var sceneDelegate: SceneDelegate? = {
    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
       let delegate = scene.delegate as? SceneDelegate {
      return delegate
    }
    return nil
  }()

  private var alertView: LKPopupView?
  private let viewModel: HubHomeConnectSectionViewModelProtocol
  weak var coordinator: HubHomeCoordinator?
  private var cancellables = Set<AnyCancellable>()

  init(viewModel: HubHomeConnectSectionViewModelProtocol, coordinator: HubHomeCoordinator?) {
    self.viewModel = viewModel
    super.init(nibName: nil, bundle: nil)
    self.coordinator = coordinator
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    view.backgroundColor = .background
    setupCollectionView()
    setupPageControl()

    // Add notification observer
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleForegroundTransition),
      name: .hubWillEnterForeground,
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleUpdateDeviceList),
      name: .updateDeviceList,
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(handleDisconnectedDeviceList),
      name: .disconnectedDevice,
      object: nil
    )
    subscribeToViewState()
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    viewModel.viewWillAppear()

    DispatchQueue.main.async {
      self.cardCollectionView.reloadData()
      self.updatePageControlFromScroll()
    }
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    applyInitialScale()
  }

  @objc private func handleForegroundTransition() {
    viewWillAppear(false)
  }

  @objc private func handleUpdateDeviceList() {
    hLogger.trace("handleUpdateDeviceList")
    viewModel.connect(macAddress: nil)
  }

  @objc private func handleDisconnectedDeviceList() {
    hLogger.trace("handleDisconnectedDeviceList")
    viewModel.disconnect()
  }

  deinit {
    NotificationCenter.default.removeObserver(self)
  }

  private func setupCollectionView() {
    view.addSubview(cardCollectionView)
    cardCollectionView.pin.horizontally().top(offset: 14.rv).height(Const.itemSize.height).activate()
    cardCollectionView.dataSource = self
    cardCollectionView.delegate = self
  }

  private func setupPageControl() {
    view.addSubview(pageControl)
    pageControl.pin.centerX().below(cardCollectionView, offset: 4).height(20).activate()

    // 최대 4개 페이지로 제한 (기기 3개 + 빈 카드 1개)
    let pageCount = min(viewModel.viewDatas.count + 1, 4)
    pageControl.numberOfPages = pageCount
    pageControl.currentPage = 0
  }

  @objc private func pageControlTapped(_ sender: UIPageControl) {
    let page = sender.currentPage

    let cellWidth = Const.itemSize.width + Const.itemSpacing
    let targetOffset = cellWidth * CGFloat(page) - cardCollectionView.contentInset.left

    cardCollectionView.setContentOffset(CGPoint(x: targetOffset, y: 0), animated: true)

    currentPage = page
  }

  private func applyInitialScale() {
    let visibleCells = cardCollectionView.visibleCells
    let centerX = cardCollectionView.contentOffset.x + cardCollectionView.frame.size.width / 2

    for cell in visibleCells {
      let cellCenterX = cell.center.x
      let distanceFromCenter = abs(cellCenterX - centerX)
      let maxDistance = cardCollectionView.frame.size.width / 2
      let scale = max(0.85, 1 - (distanceFromCenter / maxDistance) * 0.2)

      cell.transform = CGAffineTransform(scaleX: scale + 0.1, y: scale)
    }
  }

  private func updatePageControlFromScroll() {
    let cellWidth = Const.itemSize.width + Const.itemSpacing
    let scrolledOffsetX = cardCollectionView.contentOffset.x + cardCollectionView.contentInset.left
    let page = Int(round(scrolledOffsetX / cellWidth))

    // 최대 4개 페이지로 제한 (기기 3개 + 빈 카드 1개)
    let pageCount = min(viewModel.viewDatas.count + 1, 4)
    pageControl.numberOfPages = pageCount
    if page >= 0 && page < pageCount && pageControl.currentPage != page {
      pageControl.currentPage = page
      currentPage = page
    }
  }
}

// MARK: ViewState
extension ConnectSectionController {
  private func subscribeToViewState() {
    viewModel
      .viewState
      .receive(on: defaultScheduler)
      .sink(receiveCompletion: { _ in }, receiveValue: { [weak self] state in
        LKPopupView.popup.debugToast(hit: "viewState: \(state)")
        self?.configureView(with: state)
      })
      .store(in: &cancellables)
  }

  private func configureView(with state: HubHomeConnectSectionViewModel.ViewState) {
    switch state {
    case .loading:
      hLogger.info("state: loading")
      if Current.isConnect == false {
        LKPopupView.popup.loading(autoDismiss: false)
      }
    case .firstConnecting:
      hLogger.info("state: firstConnecting")
      LKPopupView.popup.hideLoading()

      alertView = LKPopupView.popup.alert {[
        .title(L.notification_wifi_text.localized),
        .subTitle(L.wifi_verify_popup_message.localized),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({})
        ])
      ]}

      viewModel.verifyConnect()
      break

    case .connecting:
      LKPopupView.popup.hideLoading()
      alertView?.dismissPopupView { isFinish in }
      viewModel.verifyConnect()
      break

    case let .connected(result):
      popup.dismissPopup()
      alertView?.dismissPopupView { isFinish in}
      hLogger.info("state: connected: ,result:\(result)")
      LKPopupView.popup.hideLoading()
      viewModel.updateConnectDeviceList()
      self.sceneDelegate?.scheduleKeepAliveTimerIfNeeded()
      break
      
    case .userCancel:
      popup.dismissPopup()
      alertView?.dismissPopupView { isFinish in }

      LKPopupView.popup.hideLoading()
      LKPopupView.popup.alert {[
        .subTitle("Authentication failed\n"),
        .showCancel(false),
        .confirmAction([
          .text(L.ok.localized),
          .bgColor(.vueroidBlue),
          .tapActionCallback({ })
        ])
      ]}
      hLogger.info("state: userCancel")
      break;
    case .updateConnectedDevice:
      viewModel.updateViewData()
      self.cardCollectionView.reloadData()
      break
      
    case .updateDeviceList:
      hLogger.info("state: updateDeviceList")
      if self.tabBarController?.selectedIndex != HubChildCoordinator.home.rawValue {
        LKPopupView.popup.hideLoading()
        self.tabBarController?.selectedIndex = HubChildCoordinator.home.rawValue
      }
      
      DispatchQueue.main.async {
        self.cardCollectionView.reloadData()
        self.applyInitialScale()
        self.updatePageControlFromScroll()
        if !UserDefaults.shared.dashcamCardCellModels.isEmpty {
          self.cardCollectionView.selectItem(
            at: IndexPath(item: 0, section: 0),
            animated: true,
            scrollPosition: .left
          )
        }
        
        let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
        PhotoUtils.getAlbum(title: "\(modelName)") { _ in }
        self.viewModel.requestFirmware()
      }
      break

    case .firmwareUpdateAvailable(let model):
      if Current.isUpdateAvailable {
        let desc = String(
          format: L.dialog_firmware_msg01.localized,
          Current.remoteFirmwareVersion.asString,
          Current.deviceVersion.asString
        )
        
        LKPopupView.popup.alert {[
          .title(L.dialog_firmware_title.localized),
          .subTitle(desc),
          .detail(L.home_firmware_check_subtitle.localized),
          .detailColor(.vueroidBlue),
          .showCancel(true),
          .confirmAction([
            .text(L.download.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              self.coordinator?.navigate(to: .firmwareDownload(model: model))
            })
          ])
        ]}
      }
      break
    case .error(let message):
      LKPopupView.popup.hideLoading()
      hLogger.error("error : \(message)")
      LKPopupView.popup.debugToast(hit: message)
      popup.dismissPopup()
      alertView?.dismissPopupView { isFinish in }
      viewModel.disconnect()
      break
    case .empty:
      if Current.connectingWifiHotspot != .connecting {
        LKPopupView.popup.hideLoading() 
      }
      break
    }
  }
}
// MARK: UICollectionViewDataSource
extension ConnectSectionController: UICollectionViewDataSource {
  public func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
    let maxDeviceCount = 3
    let deviceCount = min(self.viewModel.viewDatas.count, maxDeviceCount)
    hLogger.info("cardCollectionView count: \(deviceCount)")
    return deviceCount + 1  // 항상 +1(빈 카드용)
  }

  public func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
    // 최대 3개 기기 + 빈 카드 1개로 제한
    let dashcamCards = Array(UserDefaults.shared.dashcamCardCellModels.prefix(3))

    // 마지막 인덱스는 항상 빈 카드로 표시
    let maxDeviceCount = 3
    let deviceCount = min(dashcamCards.count, maxDeviceCount)
    if indexPath.item < deviceCount {
      guard let cell = collectionView.dequeueReusableCell(
        withReuseIdentifier: DashcamCardCell.description(),
        for: indexPath
      ) as? DashcamCardCell else {
        return UICollectionViewCell()
      }

      let data: DashcamCardCellModel = dashcamCards[indexPath.item]

      cell.setCellModel(cellModel: data)
      cell.dashcamConnectButton.addAction(UIAction { [weak self] _ in
        guard let self else { return }
         tapWifiConnect(cellData: data)
      }, for: .touchUpInside)

      cell.deleteButton.addAction(UIAction { [weak self] _ in
        guard let self else { return }
        deleteSavedDevice(cellData: data)
      }, for: .touchUpInside)
      
      cell.dashcamIconTapSubject
        .receive(on: RunLoop.main)
        .sink { [weak self] in
          guard let self else { return }
          if Current.isConnect {
            hLogger.info("dashcamIconTapSubject sink")
            activationFeedBackGenerator.impactOccurred()
            coordinator?.navigate(to: .pushMapDetail)
          } else  {
            LKPopupView.popup.toast(hit: "No drive data available")
          }
        }
        .store(in: &cell.cancellables)
      
      return cell
    } else {  //~ add cell
      guard let cell = collectionView.dequeueReusableCell(
        withReuseIdentifier: DashcamEmptyCardCell.description(),
        for: indexPath
      ) as? DashcamEmptyCardCell else {
        return UICollectionViewCell()
      }

      cell.findDeviceButton.addAction(UIAction { [weak self] _ in
         self?.tapFindDevice()
      }, for: .touchUpInside)
      return cell
    }
  }
}

// MARK: UICollectionViewDelegateFlowLayout
extension ConnectSectionController: UICollectionViewDelegateFlowLayout {
  func scrollViewWillEndDragging(
    _ scrollView: UIScrollView,
    withVelocity velocity: CGPoint,
    targetContentOffset: UnsafeMutablePointer<CGPoint>
  ) {
    let scrolledOffsetX = targetContentOffset.pointee.x + scrollView.contentInset.left
    let cellWidth = Const.itemSize.width + Const.itemSpacing
    let index = round(scrolledOffsetX / cellWidth)
    targetContentOffset.pointee = CGPoint(x: index * cellWidth - scrollView.contentInset.left, y: scrollView.contentInset.top)

    let targetPage = Int(index)
    if targetPage >= 0 && targetPage < viewModel.viewDatas.count {
      pageControl.currentPage = targetPage
      currentPage = targetPage
    }
  }

  func scrollViewDidScroll(_ scrollView: UIScrollView) {
    applyInitialScale()
  }

  func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
    updatePageControlFromScroll()
  }

  func scrollViewDidEndScrollingAnimation(_ scrollView: UIScrollView) {
    updatePageControlFromScroll()
  }
}

// MARK: Action
extension ConnectSectionController {

  func tapFindDevice() {
    activationFeedBackGenerator.impactOccurred()
    hLogger.info("tapFindDevice")
    checkLocalNetworkPermission()
  }

  private func checkLocalNetworkPermission() {
    // Create a browser that searches for Bonjour services
    let parameters = NWParameters()
    parameters.includePeerToPeer = true

    // Use the _http._tcp service type which is commonly used
    let browser = NWBrowser(for: .bonjour(type: "_http._tcp", domain: nil), using: parameters)

    // Set up a state update handler
    browser.stateUpdateHandler = { [weak self] state in
      guard let self = self else { return }

      switch state {
      case .ready, .setup:
        // Browser is ready, which means permission was granted
        DispatchQueue.main.async {
          browser.cancel()
          self.coordinator?.navigate(to: .presentBleList)
        }

      case .failed(let error):
        // Check if the error is related to permission denial
        if error.debugDescription.contains("denied") ||
           error.debugDescription.contains("permission") {
          DispatchQueue.main.async {
            browser.cancel()
            self.showLocalNetworkPermissionAlert()
          }
        } else {
          // Other errors might not be permission-related
          DispatchQueue.main.async {
            browser.cancel()
            self.coordinator?.navigate(to: .presentBleList)
          }
        }

      default:
        // For other states, we'll wait for the next update
        break
      }
    }

    // Start the browser to trigger the permission dialog
    browser.start(queue: .main)

    // Set a timeout to ensure we don't wait forever
//    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
//      browser.cancel()
//      // Assume granted if we didn't get a definitive answer
//      self?.coordinator?.navigate(to: .presentBleList)
//    }
  }

  // Show an alert explaining why Local Network permission is needed
  private func showLocalNetworkPermissionAlert() {
    LKPopupView.popup.alert {[
      .title("Local Network Permission Required"),
      .subTitle("To find and connect to your device, this app needs access to your local network. Please allow access when prompted."),
      .showCancel(true),
      .cancelAction([
        .text(L.cancel.localized),
        .bgColor(.gray),
        .tapActionCallback({ })
      ]),
      .confirmAction([
        .text("Settings"),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          if let url = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(url)
          }
        })
      ])
    ]}
  }

  private func disconnectWifi(completion: (() -> Void)? = nil) {
    guard Current.isConnect else {
      completion?()
      return
    }

    LKPopupView.popup.loading()
    Current.appKeepAliveTimerControl?.suspend()
    self.sceneDelegate?.sendKeepAliveRequest(timeout: 1)

    DispatchQueue.main.asyncAfter(deadline: .now() + 1.8) {
      LKPopupView.popup.hideLoading()
      LKPopupView.popup.debugToast(hit: "WIFI disconnected")
      DispatchQueue.main.async {
        completion?()
      }
    }
  }

  func deleteSavedDevice(cellData: DashcamCardCellModel) {
    self.popup.showPopup(
      title: L.warning.localized,
      desc: L.dialog_delete.localized,
      isCancel: true
    ) { [weak self] in
      guard let self = self else { return }

      self.disconnectWifi { [weak self] in
        guard let self = self else { return }

        hLogger.info("deleted : \(cellData.macAddress)")
        UserDefaults.shared.deleteConnectedDevice(macAddress: cellData.macAddress)
        hLogger.info("Current.dashcamCardCellModel count: \(UserDefaults.shared.dashcamCardCellModels.count)")

        viewModel.updateViewData()

        DispatchQueue.main.async {
          self.cardCollectionView.reloadData()
          self.applyInitialScale()
          self.updatePageControlFromScroll()
        }
      }
    }
  }


  func tapWifiConnect(cellData: DashcamCardCellModel) {
    activationFeedBackGenerator.impactOccurred()
    LKPopupView.popup.loading()

    if Current.isConnect {
      hLogger.info("disconnect Wifi")

      self.sceneDelegate?.sendKeepAliveRequest(timeout: 1)

      // after 1 secone , disconnect ui update
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.8) {
        LKPopupView.popup.hideLoading()
        LKPopupView.popup.debugToast(hit: "WIFI disconnected")
        Current.connectingWifiHotspot = .none
        self.viewModel.disconnect()
      }
    } else {
      hLogger.info("tapWifiConnect: ssid: \(cellData.ssid), password: \(cellData.password)")
      Current.connectedWifiSsid = cellData.ssid
      Current.modelName = cellData.ssid.components(separatedBy: "_")[1]
      let repo = Composers.wifiRepository
      let model = Wifi.ConnectModel(ssid: cellData.ssid, password: cellData.password, reconnectCount: 2)
      repo.connect(toWifi: model, reconnectCount: 1) { [weak self] isConnect, error in
        LKPopupView.popup.hideLoading()
        if isConnect {
          LKPopupView.popup.debugToast(hit: L.wifi_connected.localized)
          hLogger.info("isConnect: model: \(cellData.macAddress)")
          Current.connectingWifiHotspot = .connected
          self?.viewModel.connect(macAddress: cellData.macAddress)
        } else {
          Current.connectingWifiHotspot = .failed
          hLogger.error("wifi connect error :\(error.debugDescription)")
        }
      }
    }
  }
}
