//
//  HubHomeController.swift
//
//
//  Created by ncn on 10/31/24.
//

import UIKit
import CoreLocation
import CoreBluetooth

// Import our new permission manager
import Foundation


public class HubHomeController: StackViewController {
  private let locationManager = CLLocationManager()
  weak var coordinator: HubHomeCoordinator?
  private let activationFeedBackGenerator = UIImpactFeedbackGenerator(style: .heavy)
  
#if !targetEnvironment(simulator)
  var manager: CBCentralManager
#endif
  var tapCount = 0
  
  init(coordinator: HubHomeCoordinator) {
    self.coordinator = coordinator
#if !targetEnvironment(simulator)
    self.manager = CBCentralManager()
#endif
    super.init(nibName: nil, bundle: nil)
    
    manager.delegate = self
    locationManager.delegate = self
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  public override func viewDidLoad() {
    super.viewDidLoad()
    self.view.backgroundColor = .background
    
    let topBarView = UIView()
    setTopBarView(view: topBarView)
    let aiPlateSectionView = UIView()
    setAiPlateSection(view: aiPlateSectionView)
    
    //    let connectSectionController = ConnectSectionController(viewModel: viewModel, coordinator: coordinator)
    let connectSectionController = ConnectSectionController(
      viewModel: Composers.buildHubHomeConnectSectionViewModel(),
      coordinator: coordinator
    )
    
    let favoriteController = FavoriteSectionController(
      viewModel: Composers.buildFavoriteSectionViewModel(),
      coordinator: coordinator
    )
    let bannerController = BannerSectionController()
    
    add(view: topBarView, size: .init(width: UIScreen.width, height: 62.rv))
    add(connectSectionController, size: CGSize(width: UIScreen.width, height: 14.rv + 366.rv + 24.rv))
    add(view: aiPlateSectionView, size: .init(width: UIScreen.width, height: 20.rv + 68.rv))
    add(favoriteController, size: CGSize(width: UIScreen.width, height: 20.rv + 128.rv))
    add(bannerController, size: CGSize(width: UIScreen.width, height: 20.rv + 100.rv + 20.rv))
  }
  
  public override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    tabBarController?.toggleTabbar(isShow: true)
  }
}

// MARK: - View Setting look
extension HubHomeController {
  @objc func titleImageTapped() {
    tapCount += 1
    if tapCount >= 7 {
      tapCount = 0
      UserDefaults.shared.resetDailyFeatureUsage()
      LKPopupView.popup.toast(hit: "Reset Daily AI Feature Usage", position: .top)
    }
  }
  
  private func setTopBarView(view: UIView) {
    view.backgroundColor = .background
    
    let titleImage = #imageLiteral(resourceName: "navigationTitle.pdf").withRenderingMode(.alwaysOriginal)
    let titleImageView = UIImageView(image: titleImage)
    
    // titleImageView 의 tap을 7번 하면 resetDailyFeatureUsage() 를 호출한다
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(titleImageTapped))
    titleImageView.addGestureRecognizer(tapGesture)
    titleImageView.isUserInteractionEnabled = true
    
    view.addSubview(titleImageView)
    titleImageView.pin.height(25.rv).width(177.wrv).bottom(offset:-8.rv).start(offset:14).activate()
    
    let editConnectionButton = UIButton(type: .custom)
    let pushAlertButton = UIButton(type: .custom)
    
    let editButtonImage = #imageLiteral(resourceName: "edit_connection.pdf").withRenderingMode(.alwaysOriginal)
    let pushButtonImage = #imageLiteral(resourceName: "push_alert.pdf").withRenderingMode(.alwaysOriginal)
    
    editConnectionButton.setImage(editButtonImage, for: .normal)
    pushAlertButton.setImage(pushButtonImage, for: .normal)
    
    editConnectionButton.addAction(UIAction { [weak self] _ in
      self?.didTapEditConnection()
    }, for: .touchUpInside)
    
    pushAlertButton.addAction(UIAction { [weak self] _ in
      self?.didTapPushAlertList()
    }, for: .touchUpInside)
    
    view.addSubviews([editConnectionButton, pushAlertButton])
    pushAlertButton.pin.size(32).centerY(titleImageView).end(offset: -15).activate()
    editConnectionButton.pin.size(32).centerY(titleImageView).before(pushAlertButton, offset: -12).activate()
  }
  
  private func setAiPlateSection(view: UIView) {
    view.backgroundColor = .background
    let aiPlateButton = AIPlateButton()
    aiPlateButton.addAction(UIAction { [weak self] _ in
      self?.didTapAiPlateButton()
    }, for: .touchUpInside)
    
    view.addSubview(aiPlateButton)
    aiPlateButton.pin.horizontally(offset: 15).top(offset: 10.rv).height(74.rv).activate()
  }
  
  func didTapEditConnection() {
    hLogger.info("Edit Connection")
    coordinator?.navigate(to: .presentEditConnection)
  }
  
  func didTapPushAlertList() {
    coordinator?.navigate(to: .presentNotification)
  }
  
  func didTapAiPlateButton() {
    activationFeedBackGenerator.impactOccurred()
    AppManager.shared.mode = .file
    self.tabBarController?.selectedIndex = 3
    
    let descMsg = L.ai_plate_popup_text.localized
    
    LKPopupView.popup.alert {[
      .title(L.ai_plate_popup_title.localized),
      .subTitle(descMsg),
      .subTitleColor(.subText),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({ })
      ])
    ]}
  }
}


// MARK: CBCentralManagerDelegate
extension HubHomeController: CBCentralManagerDelegate {
  public func centralManagerDidUpdateState(_ central: CBCentralManager) {
    hLogger.info("ble state : \(central.state.rawValue)")
    switch central.state {
    case .poweredOn:
      self.checkLocationPermision()
      break
    case .poweredOff:
      hLogger.info("Bluetooth is Off.")
      LKPopupView.popup.toast(hit: "Bluetooth is Off.")
      break
    case .resetting:
      break
    case .unauthorized:
      PermissionManager.showSettingsAlert(for: .bluetooth)
      break
    case .unsupported:
      break
    case .unknown:
      break
    default:
      break
    }
  }
  
  private func checkLocationPermision() {
    let status = locationManager.authorizationStatus
    switch status {
    case .notDetermined, .restricted, .denied:
      // Use PermissionManager to show settings alert
      PermissionManager.showSettingsAlert(for: .location)
    case .authorizedAlways, .authorizedWhenInUse, .authorized:
      AppManager.shared.mode = .wifi
      break
    @unknown default:
      break
    }
  }
  
}

// MARK: CLLocationManagerDelegate
extension HubHomeController: CLLocationManagerDelegate {
  public func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
    hLogger.info("location manager did change authorization")
  }
}
