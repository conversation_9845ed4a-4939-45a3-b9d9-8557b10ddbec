//
//  HotspotViewModel.swift
//  Hub
//
//  Created by ncn on 2022/12/06.
//

import RxRelay
import RxSwift
import UIKit
import NetworkExtension

class HotspotViewModel: BaseViewModel {
  private let useCase: WifiUseCase
  private weak var coordinator: BleListCoordinator?

  struct Input {
    let connectedModel: Observable<Wifi.ConnectModel>
  }

  struct Output {
    let rxIsConnectWifi = PublishSubject<(Bool, Wifi.ConnectModel)>()
    let rxAlertMessage = PublishSubject<String>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxShowToast = PublishSubject<String>()
  }

  init(useCase: WifiUseCase, coordinator: BleListCoordinator) {
    self.useCase = useCase
    self.coordinator = coordinator
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.connectedModel
      .map({ ($0, output) })
      .do(onNext: { _ in output.rxIsIndicator.onNext(true) })
      .flatMapLatest(rxConnectWifi)
      .do(onNext: { _ in output.rxIsIndicator.onNext(false) })
      .subscribe(onNext: { [weak self] model, isConnect, error in
        guard let self else { return }
        if isConnect {
          output.rxIsConnectWifi.onNext((true, model))
          self.coordinator?.navigate(to: .bleListDidFinish)
        } else {
          Log.error(category: .Wifi, to: "\(error?.localizedDescription ?? "")")
          if let error = error,
            error.localizedDescription.contains("internal"),
            let _ = AppManager.shared.getSSID()
          {
            output.rxIsConnectWifi.onNext((true, model))
            self.coordinator?.navigate(to: .bleListDidFinish)
          } else {
//            output.rxShowToast.onNext(L.notification_wifi_time_out_txt.localized)
            LKPopupView.popup.alert {[
              .subTitle(L.connect_again_msg.localized),
              .showCancel(false),
              .confirmAction([
                .text(L.ok.localized),
                .bgColor(.vueroidBlue),
                .tapActionCallback({ })
              ])
            ]}
          }
        }
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension HotspotViewModel {
  func rxConnectWifi(_ param: (Wifi.ConnectModel, Output)) -> Observable<
    (Wifi.ConnectModel, Bool, Error?)
  > {
    var reconnectCount = 0
    if let nowSSID = AppManager.shared.getSSID(),
      param.0.ssid == nowSSID
    {
      return Observable.just((param.0, true, nil))
    }
    // 연결 시도 중단을 위한 플래그 추가
    var shouldStopReconnecting = false
    
    return Observable.create { [weak self] emitter in
      func requestWifiConnect() {
        // 취소 플래그 확인
        if shouldStopReconnecting {
          Log.info(category: .Wifi, to: "::@@ Connection attempts stopped by user")
          emitter.onNext((param.0, false, nil))
          emitter.onCompleted()
          return
        }
        
        reconnectCount += 1
        if reconnectCount > Current.wifiConnectRetryCount {
          Current.connectingWifiHotspot = .failed
          emitter.onNext((param.0, false, nil))
          emitter.onCompleted()
          return
        }

//        LKPopupView.popup.hideLoading()
        let message = String(format: L.wifi_try_connect.localized, "\(reconnectCount)")
        hLogger.info("## Trying to connect to WiFi: \(param.0.ssid) - Attempt \(reconnectCount)")
        param.1.rxShowToast.onNext(message)
        
        self?.useCase.connect(toWifi: param.0, reconnectCount: reconnectCount) { isConnect, error in
          hLogger.info("@@@ isConnect: \(isConnect) // error: \(error) // reconnectCount: \(reconnectCount)")
          
          if let error = error as NSError? {
            if error.domain == "NEHotspotConfigurationErrorDomain" &&
                (error.localizedDescription.contains("user's approval") ||
                 error.localizedDescription.contains("denied") ||
                 error.code == 7) { // userDenied 에러 코드
              
              Log.info(category: .Wifi, to: "User denied WiFi connection, stopping all reconnection attempts")
              shouldStopReconnecting = true
              emitter.onNext((param.0, false, error))
              emitter.onCompleted()
              return
            } else if error.domain == "WiFiConnectionUserCanceled" {
              Current.connectingWifiHotspot = .canceled
              shouldStopReconnecting = true
              emitter.onNext((param.0, false, error))
              emitter.onCompleted()
              return
            }
          }
          
          if isConnect {
            emitter.onNext((param.0, true, nil))
            emitter.onCompleted()
            return
          } else if let _ = error {
            if !shouldStopReconnecting {
              requestWifiConnect()
            } else {
              emitter.onNext((param.0, false, error))
              emitter.onCompleted()
            }
          } else {
            if !shouldStopReconnecting {
              requestWifiConnect()
            } else {
              emitter.onNext((param.0, false, nil))
              emitter.onCompleted()
            }
          }
        }
      }
      requestWifiConnect()
      return Disposables.create()
    }
  }
  
  func checkNetwork() {
    //manager.checkNetwork()
  }
}
