//
//  MainViewModel.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

class BleListViewModel: BaseViewModel {
  private let useCase: WifiUseCase
  private var coordinator: BleListCoordinator
  private var isConnected = false

  let connectedModel = PublishSubject<Wifi.ConnectModel>()
  let rxSelectedItem = BehaviorSubject<BleItemModel?>(value: nil)

  var listItem = BehaviorRelay<[BleCellSectionModel]>(value: [
    BleCellSectionModel(sectionType: .connected, items: []),
    BleCellSectionModel(sectionType: .scanned, items: []),
  ])
  struct Input {
    let homeButtonEvent: Observable<Void>  // for test

    let willAppearEvent: ControlEvent<Bool>
    let refreshEvent: ControlEvent<Void>
    let itemSelectedEvent: Observable<IndexPath>
    let connectButtonEvent: Observable<Void>
//    let backButtonEvent: Observable<Void>
  }

  struct Output {

  }

  init(useCase: WifiUseCase, coordinator: BleListCoordinator) {
    self.useCase = useCase
    self.coordinator = coordinator
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }
}

extension BleListViewModel {
  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.homeButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.pushHome()
      })
      .disposed(by: disposedBag)

    input.willAppearEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadConnected()
      })
      .disposed(by: disposedBag)

    input.refreshEvent
      .bind(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadConnected()
        self.appendBleItem(models: [])
      })
      .disposed(by: disposedBag)

    input.itemSelectedEvent
      .withLatestFrom(
        listItem,
        resultSelector: { indexPath, bleList in
          let selectedBle = bleList[indexPath.section].items[indexPath.row]
          hLogger.info("@@ itemSelectedEvent ssid-> \(selectedBle.wifiSSID ?? "")")
          return selectedBle
        }
      )
      .filter { $0.isDiscovered == true }
      .bind(to: rxSelectedItem)
      .disposed(by: disposedBag)

    input.connectButtonEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.pushWifi(selectedItem: nil)
      })
      .disposed(by: disposedBag)

//    input.backButtonEvent
//      .subscribe(onNext: { [weak self] _ in
//        guard let self = self else { return }
//        self.dismiss()
//      })
//      .disposed(by: disposedBag)

    return output
  }
}

// MARK: - Ble
extension BleListViewModel {
  func loadConnected() {
    DispatchQueue.main.async {
      self.isConnected = false
      var array = AppManager.shared.getConnectedDevice()
      if array.count < 1 {
        array = [BleItemModel(uuid: "", isDiscovered: false, isEmpty: true)]
      }
      var values = self.listItem.value
      let section = BleCellSectionModel(sectionType: .connected, items: array)
      values[0] = section
      self.listItem.accept(values)
    }
  }

  func pupConnectedItem(bleItem: BleItemModel) {
    let values = self.listItem.value
    if let obj = values.first {  // connected
      if var item = obj.items.filter({ $0.uuid == bleItem.uuid && $0.mac == bleItem.mac }).first {  // scaned
        item.date = Date()
        item.isDiscovered = true
        Current.connectedMac = item.mac
        hLogger.info("@@ Current.connectedMac -> \(Current.connectedMac ?? "")")
        AppManager.shared.appendConnectedDevice(obj: item)
      } else {
        if var item = values[1].items.filter({ $0.uuid == bleItem.uuid }).first {  // scaned
          item.date = Date()
          item.isDiscovered = true
          AppManager.shared.appendConnectedDevice(obj: item)
        }
      }
      loadConnected()
    }
  }

  func appendBleItem(models: [BleItemModel]) {
    let section = BleCellSectionModel(sectionType: .scanned, items: models)
    var values = listItem.value

    if values.count == 0 {
      values.append(section) /* first */
    } else if values.count == 1, let model = values.first {
      (model.sectionType == .scanned)
        ? (values[0].items = models) : values.append(section) /* just scaned */
    } else if values.count > 1 {
      values[1].items = models

      for model in models {
        if let key = model.mac,
          let index = self.isExistConnected(key: key)
        {
          values[0].items[index].isDiscovered = true
        }
      }
      Log.info(category: .Ble, to: values[0].items)
    }
    listItem.accept(values)
  }

  private func isExistConnected(key: String) -> Int? {
    let values = self.listItem.value
    if let obj = values.first {
      let items = obj.items
      return items.firstIndex(where: { $0.mac == key })
    }

    return nil
  }

  private func isExistConnected(uuid: String) -> Int? {
    let values = self.listItem.value
    if let obj = values.first {
      let items = obj.items
      return items.firstIndex(where: { $0.uuid == uuid })
    }

    return nil
  }
}

extension BleListViewModel {
  func pushWifi(selectedItem: BleItemModel?) {
    coordinator.navigate(to: .connectedWifiSelectWith(bleItem: selectedItem))
  }

  func dismiss() {
    coordinator.navigate(to: .bleListDidFinish)
  }

  func pushHome() {
    coordinator.navigate(to: .showHome)
  }
}
