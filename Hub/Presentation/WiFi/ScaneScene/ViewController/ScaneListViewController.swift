//
//  MainViewController.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import CoreLocation
import Network
import NetworkExtension
import RxCocoa
import RxDataSources
import RxSwift
import SystemConfiguration.CaptiveNetwork
import UIKit

class ScaneListViewController: UIViewController {
  let tableView: UITableView = UITableView(frame: .zero, style: .grouped)
  let refresh = UIRefreshControl()
  let containView = UIView()
  let connectButton = UIButton()
  let loadingHeaderView = BleListHeaderView(type: .loading)

  var bleListviewModel: BleListViewModel?
  var bleCentralViewModel: BleCentralViewModel?
  var hotspotViewModel: HotspotViewModel?
  var bleConnectedItem: BleItemModel?

  private var timer: Timer?
  private var screenTimer: Timer?
  private var isRegistNoti: Bool = false

  private var connectModel: Wifi.ConnectModel?

  var sceneDelegate: SceneDelegate? = {
    if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
      let delegate = scene.delegate as? SceneDelegate
    {
      return delegate
    }
    return nil
  }()

  let homeButton = NCButton()
  var disposedBag = DisposeBag()

  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    bleCentralViewModel?.stopScane()
    invalidateTimer()
    invalidateScreenTimer()
    LKPopupView.popup.hideLoading()
    #if QA || DEV
      Current.console.isVisible = false
    #endif
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    navigationItem.backButtonTitle = ""

    let closeImage = #imageLiteral(resourceName: "close.pdf").withRenderingMode(.alwaysOriginal)
    let close = UIBarButtonItem(
      image: closeImage, style: .plain, target: self, action: #selector(close))
    navigationItem.rightBarButtonItem = close

    #if DEVELOP || QA
      //    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      //      Current.console.isVisible = true
      //    }
    #endif
  }

  @objc
  func close() {
    self.dismiss(animated: true)
    self.bleListviewModel?.dismiss()
  }
  override func viewDidLoad() {
    super.viewDidLoad()
    beginScreenTimer()

    self.title = "Find Device"

    setComponent()
    setAutoLayout()
    setDataSoruce()

    bindViewModel(to: bleListviewModel)
    bindViewModel(to: bleCentralViewModel)
    bindViewModel(to: hotspotViewModel)
  }

  deinit {
    invalidateTimer()
    invalidateScreenTimer()
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func setComponent() {
    self.view.backgroundColor = .background
    self.view.addSubview(tableView)

    let attributes = [NSAttributedString.Key.foregroundColor: UIColor.lightGray]
    refresh.attributedTitle = NSAttributedString(string: "Pull to refresh", attributes: attributes)
    tableView.refreshControl = refresh
#if HUB
    setMenuButton()
#endif
    setTableView()
  }

  private func setMenuButton() {
    containView.backgroundColor = .background
    self.view.addSubview(containView)

    connectButton.titleLabel?.font = .pretendard(ofSize: 16, weight: .bold)
    connectButton.setBackgroundColor(.vueroidBlue, for: .normal)
    //    connectButton.setBackgroundColor(.lightGray, for: .highlighted)
    connectButton.setTitleColor(.white, for: .normal)
    connectButton.setTitle(L.direct_connection_txt.localized, for: .normal)
    connectButton.layer.cornerRadius = 10
    connectButton.clipsToBounds = true
    containView.addSubview(connectButton)
    //    #if DEBUG
    //      homeButton.setTitle("H", for: .normal)
    //      navigationView.addSubview(homeButton)
    //      homeButton.snp.makeConstraints { [weak self] make in
    //        guard let self = self else { return }
    //        make.right.equalTo(self.navigationView).offset(-20)
    //        make.centerY.equalTo(self.navigationView.snp.centerY)
    //        make.height.width.equalTo(40)
    //      }
    //    #endif
  }

  private func setTableView() {
    tableView.separatorStyle = .none
    tableView.backgroundColor = .background
    tableView.translatesAutoresizingMaskIntoConstraints = false
    tableView.rowHeight = UITableView.automaticDimension
    tableView.estimatedRowHeight = 80.rv
    tableView.allowsSelection = true
    tableView.register(cellType: PeripheralCell.self)
    tableView.register(cellType: BleEmptyCell.self)
    tableView.rx.setDelegate(self).disposed(by: self.disposedBag)
  }

  func setAutoLayout() {
    tableView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top)
      make.left.right.equalTo(self.view)
      make.bottom.equalTo(self.view.safeAreaLayoutGuide)
    }
#if HUB
    containView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.view)
      make.right.equalTo(self.view)
      make.top.equalTo(self.tableView.snp.bottom)
      make.bottom.equalTo(self.view)
    }

    connectButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.containView).offset(20.rv)
      make.left.equalTo(self.containView).offset(20)
      make.right.equalTo(self.containView).offset(-20)
      make.height.equalTo(54.rv)
    }
#endif
  }

  // MARK: - View Model
  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? BleListViewModel else { return }
    let liveEvent = homeButton.rx.tap.asObservable()
    let willAppearEvent = self.rx.viewWillAppear
    let refreshEvent = refresh.rx.controlEvent(.valueChanged)
    let selectEvent = tableView.rx.itemSelected.asDriver().throttle(.seconds(1), latest: false)
      .asObservable()
    let connectEvent = connectButton.rx.tap.asDriver().throttle(.seconds(1), latest: false)
      .asObservable()
    //    let backEvent = navigationView.leftButton.rx.tap.asDriver().throttle(.seconds(1), latest: false)
    //      .asObservable()

    let input = BleListViewModel.Input(
      homeButtonEvent: liveEvent,
      willAppearEvent: willAppearEvent,
      refreshEvent: refreshEvent,
      itemSelectedEvent: selectEvent,
      connectButtonEvent: connectEvent
        //      backButtonEvent: backEvent
    )

    // MARK: ble connect, connect wifi
    _ = model.bind(input: input, disposedBag: self.disposedBag)
    model.rxSelectedItem
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] item in
        guard let self = self, let item = item else { return }
        if let bleModel = self.bleCentralViewModel {
          self.loadingHeaderView.stop()
          let ok = UIAlertAction(
            title: L.confirm.localized, style: .default,
            handler: { action in
//              LKPopupView.popup.loading()
              hLogger.info("@@ rxSelectedItem  ssid-> \(item.wifiSSID ?? "")")
              self.onSelected(item: item, bleModel: bleModel)
            })

          let cancel = UIAlertAction(
            title: L.cancel.localized, style: .cancel,
            handler: { action in
              Log.info(category: .Ble, to: "cancel")
            })

          let message = String(format: L.bluetooth_device_access.localized, "\(item.name ?? item.uuid)")          
          let actions: [UIAlertAction] = [cancel, ok]
          self.popup.showMessageAlert(message: message, title: "Notification", actions: actions)
        }
      })
      .disposed(by: self.disposedBag)
  }

  private func onSelected(item: BleItemModel, bleModel: BleCentralViewModel) {
    if bleModel.failCount < 3 {
      //      let message = String(format: L.bluetooth_try_connect.localized, "\(bleModel.failCount + 1)")
      //      LKPopupView.popup.toast(hit: message, position: .top)
      bleModel.stopScane()
      #if true
      #if USE_UPPERCASE_SSID
      let ssid = item.wifiSSID?.uppercased()
      #else
      let ssid = item.wifiSSID?.lowercased()
      #endif
        hLogger.info("Connecting to \(ssid ?? "")")
        bleListviewModel?.pupConnectedItem(bleItem: item)
        bleCentralViewModel?.closeBle()
        let model = Wifi.ConnectModel(
          ssid: ssid ?? "",
          password: item.wifiPW ?? "wert2345"
        )
        bleListviewModel?.connectedModel.onNext(model)
      #else
        bleModel.connect(uuid: item.uuid) { [weak self] isConnect, isWritable, isSetNoti, error in
          guard let self = self else { return }
          Log.info(
            category: .Ble,
            to:
              "\(isConnect) // \(isWritable) // \(isSetNoti) // \(error?.localizedDescription ?? "") in"
          )
          LKPopupView.popup.hideLoading()
          if let e = error {
            let message = " \(item.name ?? item.uuid) fail to connect: \(e)"
            Log.error(category: .Ble, to: message)
            return
          }

          if isConnect == true {
            LKPopupView.popup.toast(hit: L.bluetooth_successful_connect.localized, position: .top)
          } else {
            Log.error(category: .Ble, to: "Fail to connect \(item.uuid)")
            bleModel.failCount += 1
            self.onSelected(item: item, bleModel: bleModel)
            return
          }

          // TODO: receive message 여러번 받는 경우 처리 필요
          if isWritable == true && isSetNoti == true {
            Log.info(category: .Ble, to: "Is available to send: \(item.uuid)")
            //self.viewModel?.pupConnectedItem(bleItem: item) /* connected ble */
            self.bleConnectedItem = item
            if let uuid = AppManager.shared.getUUID() {
              LKPopupView.popup.loading(hit: "Connecting WIFI")
              /* send uuid */
              Log.info(category: .Ble, to: "Send uuid: \(uuid)")
              var isCheck = false
              var isMode = false
              self.bleCentralViewModel?.send(uuid: uuid) { model in
                switch model.command {
                case .uuid, .ap, .sta, .unkonwn: break
                case .check, .button:
                  isCheck = true
                case .mode:
                  isMode = true
                }
                Log.info(
                  category: .Ble,
                  to: "Receive message: \(model), isCheck : \(isCheck), isMode : \(isMode)")
                DispatchQueue.main.async {
                  self.receive(model: model, isCheck: isCheck, isMode: isMode)
                }
              }
            } else {
              Log.warning(category: .Ble, to: "Fail to get uuid.")
            }
          } else {
            Log.info(
              category: .Ble, to: "else isWritable == \(isWritable) && isSetNoti == \(isSetNoti)")
          }
        }
      #endif
    } else {
      LKPopupView.popup.hideLoading()
      LKPopupView.popup.toast(
        hit: L.bluetooth_fail_connect.localized, icon: .toastFail, position: .top)
      return
    }
  }

  private func failToConnect() {
    /* Fail to connect ble */
    // MARK: 영어처리
    let alert = UIAlertController(
      title: L.notification_wifi_time_out_txt_title.localized,
      message: L.notification_wifi_time_out_txt.localized,
      preferredStyle: UIAlertController.Style.alert)

    let action = UIAlertAction(
      title: L.confirm.localized, style: UIAlertAction.Style.default
    ) { [weak self] action in
      self?.bleCentralViewModel?.closeBle()
      self?.bleListviewModel?.dismiss()
    }

    alert.addAction(action)
    self.present(alert, animated: true)
  }

  private func receive(model: BleReadModel, isCheck: Bool, isMode: Bool) {
    if let readValue = model as? BleCheckModel {
      Log.info(category: .Ble, to: "----->BleCheckModel")
      if readValue.isRegist != true, self.isRegistNoti == false {
        LKPopupView.popup.hideLoading()
        LKPopupView.popup.toast {
          [
            .hit(L.notification_ble_connection_dash_cam_set_btn.localized),
            .enableAutoDismiss(false),
            .autoDismissDuration(.seconds(value: 7)),
          ]
        }
        //        self.showMessageAlert(
        //          message: L.notification_ble_connection_dash_cam_set_btn.localized,
        //          title: L.notification_ble_connection_dash_cam_set_txt.localized
        //        )

        self.isRegistNoti = true
        self.beginTimer()
      }
      return
    }
    /* connect wifi */
    if let readValue = model as? BleApModel {
      Log.info(category: .Wifi, to: "----->BleApModel isCheck || isMode -- \(isCheck || isMode)")
      if let id = readValue.ssid,
        let pw = readValue.passowrd,
        isCheck || isMode
      {
        let param = Wifi.ConnectModel(ssid: id, password: pw, reconnectCount: 0)
        bleCentralViewModel?.closeBle()
        bleListviewModel?.connectedModel.onNext(param)
      } else {
        //        LKPopupView.popup.toast(hit: L.bluetooth_successful_connect.localized, position: .top)
      }
      return
    }
    /* Mode */
    if let mode = model as? BleModeModel {
      Log.info(category: .Ble, to: "----->BleModeModel")
      if mode.value == 0 {
        //        self.closeIndicator(runLocation: "receive - BleModeModel")
        LKPopupView.popup.hideLoading()
        //        self.dismissAlert { [weak self] in
        //          guard let self = self else { return }
        //        self.popup.showMessageAlert(message: L.bluetooth_successful_connect.localized, title: "")
        //        LKPopupView.popup.toast(hit: L.bluetooth_successful_connect.localized, icon: .toastSuccess, position: .top)
        //          self.showIndicator(runLocation: "receive - BleModeModel", timeout: -1)
        LKPopupView.popup.loading()
        //        }
      } else {
        //        self.dismissAlert()
        LKPopupView.popup.hideLoading()
      }
      return
    }
    /* Error */
    if let model = model as? BleErrorModel {
      Log.error(category: .Ble, to: "----->BleErrorModel: \(model.message)")
      LKPopupView.popup.toast(hit: model.message, position: .bottom)
      return
    }
  }

  private func beginScreenTimer() {
    invalidateScreenTimer()
    UIApplication.shared.isIdleTimerDisabled = true
    screenTimer = Timer.scheduledTimer(
      timeInterval: 300, target: self,
      selector: #selector(turnOffScreen(timer:)), userInfo: nil, repeats: false)
  }

  private func invalidateScreenTimer() {
    if screenTimer != nil {
      screenTimer?.invalidate()
      screenTimer = nil
    }
  }

  @objc private func turnOffScreen(timer: Timer) {
    Log.message(to: #function)
    UIApplication.shared.isIdleTimerDisabled = false
  }

  private func beginTimer() {
    invalidateTimer()
    timer = Timer.scheduledTimer(
      timeInterval: Current.bleScanTimeout, target: self,
      selector: #selector(onTick(timer:)), userInfo: nil, repeats: false
    )
  }

  private func invalidateTimer() {
    if timer != nil {
      timer?.invalidate()
      timer = nil
    }
  }

  @objc private func onTick(timer: Timer) {
    Log.message(to: "\(#function) -> hideControl()")
    //    self.dismissAlert()
    // !TODO: dismissAlert
    self.isRegistNoti = false
  }
}

extension ScaneListViewController: UITableViewDelegate {
  private func setDataSoruce() {
    let dataSource = RxTableViewSectionedReloadDataSource<BleCellSectionModel>(
      configureCell: { [weak self] (_, tv, indexPath, element) in
        guard let self = self else { return UITableViewCell() }
        if element.isEmpty == true {
          let cell = self.tableView.dequeueReusableCell(for: indexPath, cellType: BleEmptyCell.self)
          cell.selectionStyle = .none
          return cell
        } else {
          let cell = self.tableView.dequeueReusableCell(
            for: indexPath, cellType: PeripheralCell.self)
          cell.setData(data: element)
          cell.selectionStyle = .none
          return cell
        }
      },
      titleForHeaderInSection: { dataSource, sectionIndex in
        return ""  //dataSource[sectionIndex].self.sectionType.rawValue
      }
    )

    if let model = bleListviewModel {
      model.listItem
        .bind(to: tableView.rx.items(dataSource: dataSource))
        .disposed(by: self.disposedBag)
    }
  }

  func numberOfSections(in tableView: UITableView) -> Int {
    return 2
  }

  func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
    switch section {
    case 1:
      loadingHeaderView.label.text = L.searched_list.localized
      return loadingHeaderView
    default:
      let view = BleListHeaderView()
      view.label.text = L.recent_connected_list.localized
      return view
    }
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    switch indexPath.section {
    case 0:
      var height: CGFloat = 180.rv
      if let model = bleListviewModel {
        let values = model.listItem.value
        if let section = values.first,
          let item = section.items.first
        {
          height = item.isEmpty ? 180.rv : 86.rv
        }
      }
      return height
    case 1:
      return 76.rv
    default:
      return 0
    }
  }

  func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
    return 24.0
  }

  func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    beginScreenTimer()
  }
  func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
    beginScreenTimer()
  }

}

// MARK: - Wifi ViewModel

extension ScaneListViewController {
  func bindViewModel(to hotspotViewModel: HotspotViewModel?) {
    guard let viewModel = hotspotViewModel,
      let bleListViewModel = self.bleListviewModel
    else { return }

    let connectedModel = bleListViewModel.connectedModel.asObservable()

    let input = HotspotViewModel.Input(connectedModel: connectedModel)
    let output = viewModel.bind(input: input, disposedBag: self.disposedBag)
    Log.info(category: .Wifi, to: "getSSID to wifi - \(AppManager.shared.getSSID() ?? "") ")

    LKPopupView.popup.loading()
    output.rxIsConnectWifi
      .subscribe(onNext: { [weak self] (isConnect, model) in
        guard let self = self else { return }
        if self.bleConnectedItem != nil {
          bleListViewModel.pupConnectedItem(bleItem: self.bleConnectedItem!)
        }
        LKPopupView.popup.toast(hit: L.wifi_connected.localized, position: .top)
        self.dismiss(animated: true) {
          NotificationCenter.default.post(name: .updateDeviceList, object: nil)
        }
        bleListViewModel.dismiss()
      })
      .disposed(by: self.disposedBag)

    output.rxAlertMessage
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        let action = UIAlertAction(
          title: L.confirm.localized, style: .default,
          handler: { _ in
            let selectedItem = try? bleListViewModel.rxSelectedItem.value()
            bleListViewModel.pushWifi(selectedItem: selectedItem)
          })
        self.popup.showMessageAlert(message: msg, title: L.confirm.localized, actions: [action])
      })
      .disposed(by: self.disposedBag)

    output.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { value in
        value ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxShowToast
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { message in
        LKPopupView.popup.toast(hit: message, position: .bottom, durationTime: 15.0)
      })
      .disposed(by: self.disposedBag)
  }
}

// MARK: - BLE
extension ScaneListViewController {
  func bindViewModel(to bleViewModel: BleCentralViewModel?) {
    guard let viewModel = bleViewModel, let bleListViewModel = self.bleListviewModel else { return }

    let refreshEvent = refresh.rx.controlEvent(.valueChanged)
    let input = BleCentralViewModel.Input(
      wiillAppearEvent: self.rx.viewDidAppear,
      willDisappearEvent: self.rx.viewWillDisappear,
      refreshEvent: refreshEvent
    )

    let output = viewModel.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsStartScane
      .subscribe(onNext: { isStart in
        if isStart {
          Log.info(category: .Ble, to: "Ble begin scane.")
        }
      })
      .disposed(by: self.disposedBag)

    output.rxDiscovered
      .subscribe(onNext: { [weak self] discovered in
        guard let self = self else { return }
        LKPopupView.popup.hideLoading()
        DispatchQueue.main.async {
          bleListViewModel.appendBleItem(models: discovered)
          self.refresh.endRefreshing()
        }
      })
      .disposed(by: self.disposedBag)

    output.rxScaneError
      .subscribe(onNext: { error in
        LKPopupView.popup.hideLoading()
        Log.error(category: .Ble, to: "BLE error: \(error.localizedDescription)")
        LKPopupView.popup.toast(hit: error.localizedDescription, icon: .toastFail, position: .top)

      })
      .disposed(by: self.disposedBag)

    output.rxBeginLoading
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadingHeaderView.play()

      })
      .disposed(by: self.disposedBag)

    output.rxStopLoading
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadingHeaderView.stop()
      })
      .disposed(by: self.disposedBag)

  }
}
