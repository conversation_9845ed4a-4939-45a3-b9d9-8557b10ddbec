//
//  BleListCoordinator.swift
//  Hub
//
//  Created by ncn on 7/26/24.
//

import Foundation

protocol BleListCoordinator: AnyObject {
  func navigate(to step: BleListSteps)
}

public protocol BleListCoordinatorFinishDelegate: AnyObject {
  func bleListCoordinatorDidFinish()
}
public enum BleListSteps: Step {
  case bleListInit
  case showHome
  case connectedWifiSelectWith(bleItem: BleItemModel?)
  case bleListDidFinish
}

public enum BleListChildCoordinator {
  case home
  case wifiConnect
}
