//
//  DefaultMainCoordinator.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import UIKit

final class DefaultBleListCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  var presentController: UINavigationController?
  weak var delegate: BleListCoordinatorFinishDelegate?
  private var childCoordinators = [BleListChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }
  public func start() {
    navigate(to: .bleListInit)
  }
}

extension DefaultBleListCoordinator: BleListCoordinator {
  func navigate(to step: BleListSteps) {
    switch step {
    case .bleListInit:
      showBleList()
    case .showHome:
      pushWifiHome()
    case .connectedWifiSelectWith(bleItem: let item):
      pushWifiConnect(selectedItem: item)
    case .bleListDidFinish:
      navigationController.dismiss(animated: true)
      delegate?.bleListCoordinatorDidFinish()
    }
  }

  func showBleList() {
    let ble = BLECentral()
    let bleRepository = DefaultBleRepository(central: ble)
    let bleUseCase = DefaultBleUseCase(repository: bleRepository)
    let bleModel = BleCentralViewModel(services: [_ble_advertise_uuid], useCase: bleUseCase)
    
    let vc = ScaneListViewController()
    vc.bleListviewModel = Composers.buildBleListViewModel(coordinator: self)
    vc.bleCentralViewModel = bleModel
    vc.hotspotViewModel = Composers.buildHotSpotViewModel(coordinator: self)

    let nc = UINavigationController(rootViewController: vc)
    nc.navigationItem.backButtonTitle = ""
    nc.modalPresentationStyle = .overFullScreen
    
    if let hubHome = navigationController.viewControllers.last {
      presentController = nc
      hubHome.present(nc, animated: true)
    }
  }

  func pushWifiHome() {
    let homeCoordinator = DefaultHubHomeCoordinator(navigationController: navigationController)
    homeCoordinator.start()
  }

  func pushWifiConnect(selectedItem: BleItemModel?) {
    guard let presentController = presentController else { return }
    
    let wifiSelectCoordinator = DefaultWifiConnectCoordinator(navigationController: presentController)
    wifiSelectCoordinator.delegate = self
    childCoordinators[.wifiConnect] = wifiSelectCoordinator
    let nextStep = WifiConnectSteps.showWifiConnectWith(bleItem: selectedItem)
    wifiSelectCoordinator.start(with: nextStep)
  }
}

// MARK: FinishDelegate
extension DefaultBleListCoordinator: WifiConnectCoordinatorFinishDelegate {
  func wifiConnectCoordinatorDidFinish() {
    aLogger.info("wifiConnectCoordinatorDidFinish")
    childCoordinators[.wifiConnect] = nil
    navigationController.dismiss(animated: true)
  }
}
