//
//  BleViewModel.swift
//  Hub
//
//  Created by ncn on 2022/12/12.
//

import RxCocoa
import RxSwift
import UIKit

class WifiConnectViewModel: BaseViewModel {
  private let useCase: WifiUseCase
  private let coordinator: WifiConnectCoordinator
  private let selectedItem: BleItemModel?
  private var ssid: String?
  private var password: String?

  var listItem = BehaviorRelay<[BleCellSectionModel]>(value: [
    BleCellSectionModel(sectionType: .connected, items: [])
  ])

  struct Input {
    let viewWillAppearEvent: Observable<Bool>
    let ssidTextFieldEvent: Observable<String>
    let pwTextFieldEvent: Observable<String>
    let connectButtonEvent: Observable<Void>
    let settingButtonEvent: Observable<Void>
    let selectEvent: Observable<BleItemModel>
    let refreshEvent: Observable<Void>
  }

  struct Output {
    let rxIsButtonEnable = PublishSubject<Bool>()
    let rxIsConnectWifi = PublishSubject<Bool>()
    let rxAlertMessage = PublishSubject<String>()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxToastMessage = PublishSubject<String>()
    let rxEndRefresh = PublishSubject<Void>()
    let rxSelectedItem = PublishSubject<BleItemModel?>()
  }

  init(
    useCase: WifiUseCase,
    coordinator: WifiConnectCoordinator,
    selectedItem: BleItemModel? = nil
  ) {
    self.useCase = useCase
    self.coordinator = coordinator
    self.selectedItem = selectedItem
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.viewWillAppearEvent
      .do(onNext: { [weak self] _ in output.rxSelectedItem.onNext(self?.selectedItem) })
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadConnected()
      })
      .disposed(by: disposedBag)

    input.ssidTextFieldEvent
      .subscribe(onNext: { [weak self] ssid in
        guard let self = self else { return }
        self.ssid = ssid
        self.checkValidation(output: output)
      })
      .disposed(by: disposedBag)

    input.pwTextFieldEvent
      .subscribe(onNext: { [weak self] pw in
        guard let self = self else { return }
        self.password = pw
        self.checkValidation(output: output)
      })
      .disposed(by: disposedBag)

    input.connectButtonEvent
      .do(onNext: { output.rxIsIndicator.onNext(true) })
      .flatMapLatest(rxConnectWifi)
      .do(onNext: { _ in output.rxIsIndicator.onNext(false) })
      .subscribe(onNext: { [weak self] isConnect, error in
        guard let self = self else { return }
        if let error = error {
          LKPopupView.popup.hideLoading()
          output.rxToastMessage.onNext("\(L.manual_connect_error.localized)")
        } else if isConnect {
          output.rxToastMessage.onNext("\(L.home_wifi_connect.localized)")
          
          coordinator.navigate(to: .wifiConnectDidFinish)
        }
      })
      .disposed(by: disposedBag)

    input.settingButtonEvent
      .subscribe(onNext: { [weak self] event in
        guard let self = self else { return }
        Log.message(to: "????")
        self.pushSetting()
      })
      .disposed(by: disposedBag)

    input.selectEvent
      .bind(to: output.rxSelectedItem)
      .disposed(by: disposedBag)

    input.refreshEvent
      .do(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadConnected()
      })
      .bind(to: output.rxEndRefresh)
      .disposed(by: disposedBag)

    return output
  }

}

extension WifiConnectViewModel {
  private func checkValidation(output: Output) {
    guard let id = ssid else {
      output.rxIsButtonEnable.onNext(false)
      Log.message(to: "ssid is empty.")
      return
    }
    guard let pw = password else {
      output.rxIsButtonEnable.onNext(false)
      Log.message(to: "password is empty.")
      return
    }
    if id.count < 1 || pw.count < 1 {
      output.rxIsButtonEnable.onNext(false)
      Log.message(to: "password or ssid is empty.")
      return
    }

    output.rxIsButtonEnable.onNext(true)
  }

  func rxConnectWifi() -> Observable<(Bool, Error?)> {
    Log.info(
      category: .Wifi, to: "param:\(ssid ?? "") vs getSSID: \(AppManager.shared.getSSID() ?? "")")
    guard let id = ssid else {
      let error = NCError(title: "SSID", description: "와이파이 SSID를 입력하세요.", code: -1)
      return Observable.just((false, error))
    }
    guard let pw = password else {
      let error = NCError(title: "비밀번호", description: "와이파이 비밀번호를 입력하세요.", code: -1)
      return Observable.just((false, error))
    }

    if let nowSSID = AppManager.shared.getSSID(),
      ssid == nowSSID
    {
      return Observable.just((true, nil))
    }
    let param = Wifi.ConnectModel(ssid: id, password: pw)

    return Observable.create { [weak self] emitter in
      self?.useCase.connect(toWifi: param, reconnectCount: 0) { isConnect, error in
        if let error = error {
          emitter.onNext((isConnect, error))
          emitter.onCompleted()
        } else {
          emitter.onNext((isConnect, nil))
          emitter.onCompleted()
        }
      }
      return Disposables.create()
    }
  }

  // MARK: 이미 연결했던 BLE 리스트추가
  func loadConnected() {
    DispatchQueue.main.async {
      var array = AppManager.shared.getConnectedDevice()

      if array.count < 1 {
        array = [BleItemModel(uuid: "", isDiscovered: false, isEmpty: true)]
      }
      var values = self.listItem.value
      let section = BleCellSectionModel(sectionType: .connected, items: array)
      values[0] = section
      self.listItem.accept(values)
      Log.message(to: array)
    }
  }
}

extension WifiConnectViewModel {
  func pushSetting() {
    coordinator.navigate(to: .setting)
  }

  func dismiss() {
    coordinator.navigate(to: .wifiConnectDidFinish)
  }
}
