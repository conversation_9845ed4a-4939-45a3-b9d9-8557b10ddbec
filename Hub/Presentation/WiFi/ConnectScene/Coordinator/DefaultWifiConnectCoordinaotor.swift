//
//  DefaultWifiConnectCoordinator.swift
//  Hub
//
//  Created by ncn on 2022/12/12.
//

import UIKit

final class DefaultWifiConnectCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: WifiConnectCoordinatorFinishDelegate?
  var type: CoordinatorType = .wifi
  private var childCoordinators = [BleListChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  func start(with step: WifiConnectSteps) {
    navigate(to: step)
  }
}

extension DefaultWifiConnectCoordinator: WifiConnectCoordinator {
  func navigate(to step: WifiConnectSteps) {
    switch step {
    case .showWifiConnectWith(bleItem: let bleItem):
      showWifiConnectWith(with: bleItem)
    case .setting:
      pushSetting()
    case .home, .wifiConnectDidFinish:
      DispatchQueue.main.async { [weak self] in
        self?.navigationController.popViewController(animated: true) {
          NotificationCenter.default.post(name: .updateDeviceList, object: nil)
        }
        self?.delegate?.wifiConnectCoordinatorDidFinish()
      }
    }
  }
  
  func showWifiConnectWith(with bleItem: BleItemModel?) {
    let viewModel = Composers.buildWifiConnectViewModel(coordinator: self, bleItem: bleItem)
    let vc = WifiConnectViewController()
    vc.viewModel = viewModel
    vc.hidesBottomBarWhenPushed = true
    navigationController.pushViewController(vc, animated: true)
  }

  func pushSetting() {
    guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
      return
    }

    if UIApplication.shared.canOpenURL(settingsUrl) {
      UIApplication.shared.open(
        settingsUrl,
        completionHandler: { (success) in
          Log.info(to: "Settings opened: \(success)")
        })
    }
  }
}

