//
//  WifiConnectCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

protocol WifiConnectCoordinator: AnyObject {
  func navigate(to step: WifiConnectSteps)
}

public protocol WifiConnectCoordinatorFinishDelegate: AnyObject {
  func wifiConnectCoordinatorDidFinish()
}
public enum WifiConnectSteps: Step {
  case showWifiConnectWith(bleItem: BleItemModel?)
  case home
  case setting
  case wifiConnectDidFinish
}
