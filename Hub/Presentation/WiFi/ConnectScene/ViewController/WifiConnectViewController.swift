//
//  BleViewController.swift
//  Hub
//
//  Created by ncn on 2022/12/12.
//

import NetworkExtension
import RxCocoa
import RxDataSources
import RxSwift
import UIKit

class WifiConnectViewController: UIViewController {

  // 최근 접속리스트 추가
  let refresh = UIRefreshControl()
  let tableView = UITableView(frame: .zero, style: .grouped)

  let containView = UIView()
  let titleLabel = UILabel()
  let ssidView = InputView()
  let passwordView = InputView()
  let connectButton = UIButton.customStyleButton(title: L.direct_connection_txt.localized)
  let settingButton = UIButton()

  var viewModel: WifiConnectViewModel?
  var disposedBag = DisposeBag()

  override func viewDidLoad() {
    super.viewDidLoad()
    self.title = L.direct_connection_txt.localized
    
    setComponent()
    setAutoLayout()
    setDataSource()
    bindViewModel(to: viewModel)
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
  }

  func setComponent() {
    self.view.backgroundColor = .background
    self.view.addSubview(tableView)
    self.view.addSubview(containView)
    
    let attributes = [NSAttributedString.Key.foregroundColor: UIColor.lightGray]
    refresh.attributedTitle = NSAttributedString(
      string: "slide_refresh_title".localized, attributes: attributes)
    tableView.refreshControl = refresh
    tableView.separatorStyle = .none
    tableView.backgroundColor = .background
    tableView.translatesAutoresizingMaskIntoConstraints = false
    tableView.rowHeight = UITableView.automaticDimension
    tableView.estimatedRowHeight = 100
    tableView.allowsSelection = true
    tableView.register(cellType: PeripheralCell.self)
    tableView.register(cellType: BleEmptyCell.self)
    tableView.rx.setDelegate(self).disposed(by: self.disposedBag)

    titleLabel.text = L.direct_connection_txt.localized
    titleLabel.font = .boldSystemFont(ofSize: 18)
    titleLabel.textColor = .text
    containView.addSubview(titleLabel)

    ssidView.nameLabel.text = L.ssid_txt.localized
    ssidView.textField.attributedPlaceholder = NSAttributedString(
      string: L.direct_connection_edit_ssid.localized,
      attributes: [NSAttributedString.Key.foregroundColor: UIColor.mainBlack]
    )
    ssidView.textField.delegate = self
    containView.addSubview(ssidView)

    passwordView.nameLabel.text = L.password_txt.localized
    passwordView.textField.attributedPlaceholder = NSAttributedString(
      string: L.direct_connection_edit_password.localized,
      attributes: [NSAttributedString.Key.foregroundColor: UIColor.mainBlack]
    )
    passwordView.textField.delegate = self
    containView.addSubview(passwordView)
    containView.addSubview(connectButton)

    settingButton.setTitle(L.go_to_settings_txt.localized, for: .normal)
    settingButton.titleLabel?.font = .systemFont(ofSize: 16)
    settingButton.setTitleColor(.colorRGB(83, 182, 237), for: .normal)
    containView.addSubview(settingButton)
    settingButton.isHidden = true
    
    connectButton.backgroundColor = .gray82
    connectButton.isEnabled = false
  }

  func setAutoLayout() {
    tableView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.view).offset(20)
      make.right.equalTo(self.view).offset(-20)
      make.top.equalToSuperview()
      make.height.equalTo(self.view.height * 0.3)
    }

    containView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.view).offset(20)
      make.right.equalTo(self.view).offset(-20)
      make.top.equalTo(self.tableView.snp.bottom)
      make.height.equalTo(self.view.height * 0.6)
    }

    titleLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.containView)
      make.top.equalTo(self.containView)
      make.right.equalTo(self.containView)
    }

    ssidView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.containView)
      make.top.equalTo(self.containView).offset(52)
      make.right.equalTo(self.containView)
      make.height.equalTo(90)
    }

    passwordView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.ssidView.snp.bottom)
      make.left.equalTo(self.containView)
      make.right.equalTo(self.containView)
      make.height.equalTo(90)
    }

    connectButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.passwordView.snp.bottom).offset(30)
      make.left.right.equalTo(self.containView)
      make.height.equalTo(60)
    }

    settingButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self.containView)
      make.bottom.equalTo(self.containView).offset(-10)
      make.width.equalTo(130)
      make.height.equalTo(44)
    }
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let model = viewModel as? WifiConnectViewModel else { return }

    let viewWillAppearEvent = self.rx.viewWillAppear.asObservable()
    let ssidTextFieldEvent = ssidView.textField.rx.text.orEmpty.asObservable()
    let pwTextFieldEvent = passwordView.textField.rx.text.orEmpty.asObservable()
    let connectButtonEvent = connectButton.rx.tap.asDriver().throttle(.seconds(3), latest: false)
      .asObservable()
    let settingButtonEvent = settingButton.rx.tap.asDriver().throttle(.seconds(1), latest: false)
      .asObservable()
    let selectEvent = tableView.rx.modelSelected(BleItemModel.self).asDriver().throttle(.seconds(1))
      .asObservable()

    let refreshEvent = refresh.rx.controlEvent(.valueChanged).asObservable()

    let input = WifiConnectViewModel.Input(
      viewWillAppearEvent: viewWillAppearEvent,
      ssidTextFieldEvent: ssidTextFieldEvent,
      pwTextFieldEvent: pwTextFieldEvent,
      connectButtonEvent: connectButtonEvent,
      settingButtonEvent: settingButtonEvent,
      selectEvent: selectEvent,
      refreshEvent: refreshEvent
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsButtonEnable
      .subscribe(onNext: { [weak self] ret in
        guard let self = self else { return }

        self.connectButton.isEnabled = ret

        if ret {
          self.connectButton.backgroundColor = .vueroidBlue
        } else {
          self.connectButton.backgroundColor = .gray82
        }
      })
      .disposed(by: self.disposedBag)

    output.rxAlertMessage
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        self.popup.showMessageAlert(message: msg, title: L.confirm.localized)
        LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxToastMessage
      .subscribe(onNext: { msg in
        LKPopupView.popup.toast(hit: msg)
        LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { value in
        value ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxEndRefresh
      .bind(onNext: { [weak self] in
        guard let self = self else { return }
        self.refresh.endRefreshing()
      })
      .disposed(by: disposedBag)

    output.rxSelectedItem
      .compactMap { $0?.wifiSSID }
      .bind(onNext: { [weak self] ssid in
        Log.message(to: ssid)
        self?.ssidView.textField.text = ssid
        self?.ssidView.textField.sendActions(for: .valueChanged)
      })
      .disposed(by: disposedBag)

    output.rxSelectedItem
      .compactMap { $0?.wifiPW }
      .bind(onNext: { [weak self] pw in
        Log.message(to: pw)
        self?.passwordView.textField.text = pw
        self?.passwordView.textField.sendActions(for: .valueChanged)
      })
      .disposed(by: disposedBag)

  }
}

extension WifiConnectViewController: UITextFieldDelegate {
  func textField(
    _ textField: UITextField,
    shouldChangeCharactersIn range: NSRange,
    replacementString string: String
  ) -> Bool {
    if textField == ssidView.textField {
      return true
    }
    if string.isEmpty {
      return true
    }

    if let count = textField.text?.count, count <= 15 {
      if string.contains(where: { ($0 == "\"") || ($0 == "\\") || ($0 == "`") }) {
        LKPopupView.popup.toast(hit: L.dialog_password_error_msg02.localized, position: .bottom)
        return false
      } else {
        return true
      }
    } else {
      LKPopupView.popup.toast(hit: L.dialog_password_error_msg02.localized, icon: .toastFail)
      return false
    }
  }

}

extension WifiConnectViewController: UITableViewDelegate {

  private func setDataSource() {
    let dataSource = RxTableViewSectionedReloadDataSource<BleCellSectionModel>(
      configureCell: { [weak self] (_, tv, indexPath, element) in
        guard let self = self else { return UITableViewCell() }
        if element.isEmpty == true {
          let cell = self.tableView.dequeueReusableCell(for: indexPath, cellType: BleEmptyCell.self)
          cell.selectionStyle = .none
          return cell
        } else {
          let cell = self.tableView.dequeueReusableCell(
            for: indexPath, cellType: PeripheralCell.self)
          cell.setData(data: element)
          cell.selectionStyle = .none
          return cell
        }
      },
      titleForHeaderInSection: { dataSource, sectionIndex in
        return ""
      }
    )

    if let model = viewModel {
      model.listItem
        .bind(to: tableView.rx.items(dataSource: dataSource))
        .disposed(by: self.disposedBag)
    }
  }

  func numberOfSections(in tableView: UITableView) -> Int {
    return 1
  }

  func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
    let view = BleListHeaderView()
    view.label.text = L.recent_connected_list.localized
    return view
  }

  func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
    var height: CGFloat = 210
    if let model = viewModel {
      let values = model.listItem.value
      if let section = values.first,
        let item = section.items.first
      {
        height = item.isEmpty ? (210) : 100
      }
    }
    return height
  }

  func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
    return 44.0
  }

}
