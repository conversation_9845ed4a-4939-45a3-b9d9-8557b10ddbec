//
//  FileListViewController+WiFi.swift
//  Hub
//
//  Created by ncn on 2023/05/19.
//

import RxCocoa
import RxSwift
import UIKit

extension FileListViewController {
  func bindViewModel(to viewModel: FileListWifiViewModel?) {
    guard let wifiViewModel = viewModel,
      let shareViewModel = self.shareViewModel
    else { return }

    setWifiDashcamMenu()

    videoTypeMenuView.collectionView.selectItem(
      at: IndexPath(row: videoTypeSelectedIndex, section: 0),
      animated: false,
      scrollPosition: .right
    )

    let videoTypeSelectedEvent = videoTypeMenuView.selectFolderTypeWithDelegate.asObservable()
    let itemSelectedEvent = fileListView.zoomView.collectionView.rx.itemSelected.asObservable()

    let editButtonMenuEvent = editMenuView.rxSelectedType
      .map {
        [weak self] (storageType, actionType) -> (
          FileStorageType, FileActionType, [FileListCellModel], Bool
        ) in
        guard let self = self else { return (.dashcam, .download, [], false) }
        let zoomView = self.fileListView.zoomView
        if zoomView.isSelectMode {
          let selectedItemList = zoomView.selectedIndexPathList.compactMap {
            zoomView.viewModel.getItemAtIndex($0)
          }
          return (storageType, actionType, selectedItemList, true)
        } else {
          guard let selectedTooltip = zoomView.selectedTooltip else {
            return (.dashcam, .download, [], false)
          }
          return (storageType, actionType, [selectedTooltip], false)
        }
      }
      .asObservable()

    let input = FileListWifiViewModel.Input(
      didLoadEvent: Observable.just(()),
      willDisappearEvent: self.rx.viewWillDisappear,
      videoTypeSelectedEvent: videoTypeSelectedEvent,
      itemSelectedEvent: itemSelectedEvent,
      editMenuSelectEvent: editButtonMenuEvent,
      rxToolTipEvent: fileListView.zoomView.rxSelectedTooltip
    )

    let output = wifiViewModel.bind(input: input, disposedBag: self.disposedBag)
    output.rxFileStorageMode
      .subscribe(onNext: { [weak self] mode in
        switch mode {
        case .dashcam:
          self?.fileStorageSelectedIndex = 0
          self?.setWifiDashcamMenu()
        case .download:
          self?.fileStorageSelectedIndex = 1
          self?.setDownloadMenu()
        case .external:
          self?.fileStorageSelectedIndex = 2
          self?.setExternalMenu()
          self?.openDoucemntPicker()
        case .cloud, .autoUpload:
          break
        }
      })
      .disposed(by: disposedBag)

    output.rxSelectedIndexPath
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] index in
        guard let self = self else { return }
        if let vModel = self.shareViewModel {
          Current.playbackItemSelected = true
          let section = wifiViewModel.listItem.value[index.section]
          switch self.fileStorageSelectedIndex {
          case 1:
            let vodLocalModel = VodLocalModel(selectedIndex: index.row, items: section.items)
            vModel.pushLocal(model: vodLocalModel)
          default:
            let vodStreamModel = VodStreamModel(
              storageType: .WifiDashCam,
              selectedIndex: index.row,
              items: section.items,
              path: nil
            )
            vModel.pushVod(model: vodStreamModel)
          }
        }
      })
      .disposed(by: self.disposedBag)

    output.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { ret in
        ret ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()
      })
      .disposed(by: self.disposedBag)

    output.rxIsDismiss
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        viewModel?.dismiss()
      })
      .disposed(by: self.disposedBag)

    output.rxErrorMessage
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { msg in
        LKPopupView.popup.hideLoading()
        LKPopupView.popup.debugToast(hit: msg)
      })
      .disposed(by: self.disposedBag)

    output.rxAlertMessage
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        self.popup.showMessageAlert(message: msg, title: L.confirm.localized)
      })
      .disposed(by: self.disposedBag)

    output.rxDeletePopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] cellModel in
        let confirmAction: (() -> Void)? = {
          wifiViewModel.deleteFiles(models: cellModel, output: output)
        }
        self?.popup.showPopup(
          title: L.popup_delete_title.localized, desc: L.popup_delete_desc.localized,
          isCancel: true, confirmAction: confirmAction
        )
      })
      .disposed(by: disposedBag)

    output.rxDeleteLocalPopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] models in
        let confirmAction: (() -> Void)? = {
          wifiViewModel.deleteLocalFiles(models: models, output: output)
        }
        self?.popup.showPopup(
          title: L.popup_delete_title.localized, desc: L.popup_delete_desc.localized,
          isCancel: true, confirmAction: confirmAction)
      })
      .disposed(by: disposedBag)

    output.rxDeselectTooltip
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] in
        self?.fileListView.zoomView.selectedTooltip = nil
        self?.fileListView.zoomView.isSelectMode = false
        self?.editMenuView.checkButton.isSelected = false
      })
      .disposed(by: disposedBag)

    output.rxShare
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] shareUrls in
        guard let self = self else { return }
        let activityViewController = UIActivityViewController(
          activityItems: shareUrls, applicationActivities: nil)
        activityViewController.popoverPresentationController?.sourceView = self.view  // so that iPads won't crash
        activityViewController.excludedActivityTypes = [.message]
        self.present(activityViewController, animated: true)
      })
      .disposed(by: disposedBag)

    output.rxReleaseToast
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: msg)
      })
      .disposed(by: disposedBag)

    wifiViewModel.listItem
      .do(onNext: { _ in
        fLogger.info("isSelectMode: \(self.fileListView.zoomView.isSelectMode)")
        let isSelectMode = self.fileListView.zoomView.isSelectMode
        shareViewModel.rxIsCheck.onNext(isSelectMode)
      })
      .subscribe(onNext: { [weak self] array in
        guard let self = self else { return }

        DispatchQueue.main.async {
          self.fileListView.zoomView.list = array
        }
      })
      .disposed(by: self.disposedBag)

  }
}
