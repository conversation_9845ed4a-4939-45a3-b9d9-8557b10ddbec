//
//  FileListViewController.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import CoreServices
import RxCocoa
import RxSwift
import UIKit
import UniformTypeIdentifiers

class FileListViewController: UIViewController {
  lazy var scrollView: UIScrollView = {
    let view = UIScrollView()
    view.showsHorizontalScrollIndicator = false
    view.alwaysBounceHorizontal = false
    view.zoomScale = 1.0
    view.translatesAutoresizingMaskIntoConstraints = false
    view.backgroundColor = .background
    return view
  }()

  var videoTypeMenuView = StringCollectionMenuView(items: [
    L.file_viewer_drive.localized, L.file_viewer_event.localized, "Event(P)",
    L.file_viewer_park.localized, L.file_viewer_manual.localized, "Bookmark   "
  ])

  let editMenuView = ButtonMenuView(items: ["\(L.video_delete.localized)", "\(L.download.localized)"])

//  let checkButtonView = UIView()
  let checkButton = UIButton(type: .custom)

  let fileListView = FileListContainerView()
  
  let updownView = UpdownView()

  var fileStorageSelectedIndex = 0
  var videoTypeSelectedIndex = 0

  var shareViewModel: FileListViewModel?
  var wifiViewModel: FileListWifiViewModel?
  var localViewModel: FileListLocalViewModel?

  var disposedBag = DisposeBag()

  override func viewDidLoad() {
    super.viewDidLoad()
    navigationItem.backButtonTitle = ""
    setCheckButton()

    setComponent()
    setAutoLayout()
    bindViewModel(to: shareViewModel)

    if Current.mode == .wifi {
      bindViewModel(to: wifiViewModel)
    } else {
      bindViewModel(to: localViewModel)
    }
  }

  func setCheckButton() {
    checkButton.setImage(#imageLiteral(resourceName: "icon_check_default.pdf"), for: .normal)
    checkButton.setImage(#imageLiteral(resourceName: "icon_check_active.pdf"), for: .selected)
    checkButton.frame = CGRect(x: 0, y: 0, width: 24, height: 24)
    checkButton.isSelected = false
    let rightButtonItem = UIBarButtonItem(customView: checkButton)
    navigationItem.setRightBarButton(rightButtonItem, animated: false)
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
  }

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    AppUtility.lockOrientation(.portrait)
    tabBarController?.toggleTabbar(isShow: true)
    
    mLogger.info("🔃 FileListViewController willAppearEvent ")
    if let index = self.tabBarController?.selectedIndex {
      switch index {
      case 2:
        self.navigationItem.title = L.fileviewer_title.localized
        Current.playbackItemSelected = false
      case 3:
        self.navigationItem.title = L.my_library_title.localized
      default:
        break
      }
    }
  }

  deinit {
    aLogger.info("FileListViewController deinit")
  }
    
  func removeAllSelected() {
    let zoomView = self.fileListView.zoomView
    zoomView.selectedIndexPathList.removeAll()
    
    DispatchQueue.main.async {
      zoomView.collectionView.reloadData()
    }
  }

  func setComponent() {
    self.view.backgroundColor = .white
    self.view.addSubview(self.scrollView)
//    if AppManager.shared.mode != .file {
//      self.scrollView.addSubview(fileStorageMenuView)
//    }

//    self.scrollView.addSubview(storagePercentView)
    self.scrollView.addSubview(videoTypeMenuView)
    self.scrollView.addSubview(editMenuView)
    editMenuView.isHidden = true

    self.scrollView.addSubview(fileListView)

    self.fileListView.addSubview(updownView)
    updownView.isHidden = true
  }

  func setAutoLayout() {
    self.scrollView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalToSuperview()
      make.left.right.equalTo(self.view)
      make.bottom.equalTo(self.view.safeAreaLayoutGuide)
    }

    if Current.mode == .wifi {
      let startOffset = 10
      videoTypeMenuView.snp.updateConstraints { make in
        make.left.equalTo(self.view).offset(startOffset)
        make.right.equalTo(self.view).offset(0)
        make.top.equalToSuperview()
        make.height.equalTo(60)
      }
    } else {
      videoTypeMenuView.removeFromSuperview()
      scrollView.addSubview(videoTypeMenuView)
      videoTypeMenuView.pin.top().centerX().width(235).height(60).activate()
    }
    
    editMenuView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.equalTo(self.view)
      make.top.equalToSuperview()
      make.height.equalTo(60)
    }

    fileListView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.equalTo(self.view)
      make.top.equalTo(self.videoTypeMenuView.snp.bottom).offset(8)
      make.bottom.equalTo(self.view.safeAreaLayoutGuide)
    }

    updownView.snp.makeConstraints { make in
      make.right.bottom.equalToSuperview().inset(20)
    }
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let shareViewModel = viewModel as? FileListViewModel else { return }
    let checkButtonEvent = checkButton.rx.tapWithIsSelected.asObservable()
    let moveToTopEvent = updownView.upButton.rx.tap.asObservable()
    let moveToBotEvent = updownView.downButton.rx.tap.asObservable()
    let scrollStart = fileListView.zoomView.collectionView.rx.willBeginDragging.asObservable()
    let scrollEnd = fileListView.zoomView.collectionView.rx.didEndDecelerating.asObservable()
    
    let input = FileListViewModel.Input(
      checkButtonEvent: checkButtonEvent,
      moveToTopEvent: moveToTopEvent,
      moveToBotEvent: moveToBotEvent,
      scrollStart: scrollStart,
      scrollEnd: scrollEnd
    )

    let output = shareViewModel.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsBack
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        guard let self else { return }
        self.shareViewModel?.dismiss()
      })
      .disposed(by: self.disposedBag)

    shareViewModel.rxExternalFiles
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] list in
        guard let self = self else { return }
        if let obj = self.wifiViewModel {
          obj.listItem.accept(list)
        }
        if let obj = self.localViewModel {
          obj.listItem.accept(list)
        }
      })
      .disposed(by: self.disposedBag)

    shareViewModel.rxIsIndicator
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { ret in
        ret ? LKPopupView.popup.loading() : LKPopupView.popup.hideLoading()

      })
      .disposed(by: self.disposedBag)

    let isCheck = shareViewModel.rxIsCheck
      .rxDebug("isCheck")
      .observe(on: MainScheduler.asyncInstance)
      .share()

//    isCheck
//      .bind(to: self.editMenuView.checkButton.rx.isSelected)
//      .disposed(by: disposedBag)

    isCheck
      .rxDebug("isCheck")
      .bind(onNext: { [weak self] bool in
        guard let self else { return }
        editMenuView.isHidden = !bool
        videoTypeMenuView.isHidden = bool
        checkButton.isSelected = bool
        fileListView.zoomView.removeToolTipView()
        fileListView.zoomView.isSelectMode = bool
      })
      .disposed(by: disposedBag)

    output.rxUpdown
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] updown in
        guard let self = self else { return }
        let collectionView = self.fileListView.zoomView.collectionView
        guard collectionView.numberOfSections != 0 else { return }
        switch updown {
        case .Up:
          collectionView.setContentOffset(.zero, animated: true)
        case .Down:
          let lastSection = collectionView.numberOfSections - 1
          let lastRow = collectionView.numberOfItems(inSection: lastSection) - 1
          let lastIndexPath = IndexPath(row: lastRow, section: lastSection)
          collectionView.scrollToItem(at: lastIndexPath, at: .bottom, animated: true)
        }
      })
      .disposed(by: disposedBag)

    output.rxUpdownIsHidden
      .bind(to: updownView.rx.isHidden)
      .disposed(by: disposedBag)

  }
}

extension FileListViewController {
  internal func setWifiDashcamMenu() {
    // 삭제, 다운로드
    videoTypeMenuView.isHidden = false
    videoTypeMenuView.stringItems = [
      L.file_viewer_drive.localized,
      L.file_viewer_event.localized,
      "Event(P)",
      L.file_viewer_park.localized,
      L.file_viewer_manual.localized,
      "Bookmark"
    ]

    videoTypeMenuView.snp.updateConstraints { make in
      make.height.equalTo(60)
    }

    storagePercentViewState(isHidden: true)

    let download = MenuIconModel(
      fileActionType: .download,
      fileStorageType: .dashcam,
      normal: UIImage(named: "icon_download_select")!,
      highlight: UIImage(named: "icon_download_select")!,
      title: "   \(L.download.localized)"
    )

    let delete = MenuIconModel(
      fileActionType: .delete,
      fileStorageType: .dashcam,
      normal: UIImage(named: "icon_delete_select")!,
      highlight: UIImage(named: "icon_delete_select")!,
      title: "   \(L.dialog_delete_title.localized)"
    )

    editMenuView.items = [L.dialog_delete_title.localized , L.download.localized]
    editMenuView.iconTitles = [delete, download]

    let selectIndexPath = IndexPath(row: videoTypeSelectedIndex, section: 0)
    videoTypeMenuView.collectionView.selectItem(
      at: selectIndexPath, animated: false, scrollPosition: .right
    )

    videoTypeMenuView.collectionView(
      videoTypeMenuView.collectionView, didSelectItemAt: selectIndexPath
    )
  }

  internal func setDownloadMenu() {
    // 공유, 삭제
    videoTypeMenuView.isHidden = false
    videoTypeMenuView.stringItems = [
      "All", "Video", "Image"
    ]
    videoTypeMenuView.snp.updateConstraints { make in
      make.height.equalTo(60)
    }

    storagePercentViewState(isHidden: true)

    let share = MenuIconModel(
      fileActionType: .share,
      fileStorageType: .download,
      normal: #imageLiteral(resourceName: "share.pdf"),
      highlight: #imageLiteral(resourceName: "share.pdf"),
      title: "   \(L.dialog_share_title.localized)"
    )

    let delete = MenuIconModel(
      fileActionType: .delete,
      fileStorageType: .download,
      normal: UIImage(named: "icon_delete_select")!,
      highlight: UIImage(named: "icon_delete_select")!,
      title: "   \(L.video_delete.localized)"
    )
    editMenuView.iconTitles = [delete, share]

    let selectIndexPath = IndexPath(row: videoTypeSelectedIndex, section: 0)
    videoTypeMenuView.collectionView.selectItem(
      at: selectIndexPath, animated: false, scrollPosition: .right)
    videoTypeMenuView.collectionView(
      videoTypeMenuView.collectionView, didSelectItemAt: selectIndexPath)
  }

  internal func setExternalMenu() {
    // 다운로드, 삭제
    videoTypeMenuView.isHidden = true
    videoTypeMenuView.stringItems = []
    videoTypeMenuView.snp.updateConstraints { make in
      make.height.equalTo(0)
    }

    storagePercentViewState(isHidden: true)

    let download = MenuIconModel(
      fileActionType: .download,
      fileStorageType: .external,
      normal: UIImage(named: "icon_dw_b")!,
      highlight: UIImage(named: "icon_dw_w")!,
      title: L.download.localized
    )

    let delete = MenuIconModel(
      fileActionType: .delete,
      fileStorageType: .external,
      normal: UIImage(named: "icon_del_b")!,
      highlight: UIImage(named: "icon_del_w")!,
      title: L.dialog_delete_title.localized
    )
    editMenuView.iconTitles = [download, delete]
  }

  func storagePercentViewState(isHidden: Bool) {
//    self.storagePercentView.isHidden = isHidden
//    storagePercentView.snp.updateConstraints { [weak self] make in
//      make.height.equalTo(isHidden ? 0 : 46)
//    }
  }
}

extension FileListViewController {
  func openDoucemntPicker() {
    var controller: UIDocumentPickerViewController!

    if #available(iOS 14.0, *) {
      #if false
        var types: [UTType] = [
          .folder, .jpeg,
          .png,
          .mp3,
          .quickTimeMovie,
          .mpeg,
          .mpeg2Video,
          .mpeg2TransportStream,
          .mpeg4Movie,
          .mpeg4Audio,
          .appleProtectedMPEG4Audio,
          .avi,
          .aiff,
          .wav,
          .midi,
          .tiff,
          .gif, .icns,
        ]
        if let atype: UTType = UTType("com.apple.quicktime-image") {
          types.append(atype)
        }
      #else
        let types: [UTType] = [.folder]
      #endif
      controller = UIDocumentPickerViewController(forOpeningContentTypes: types)
    } else {
      let supportedTypes: [String] = [kUTTypeFolder as String]
      controller = UIDocumentPickerViewController(documentTypes: supportedTypes, in: .import)
    }

    if let model = shareViewModel {
      controller.delegate = model
    }
    controller.allowsMultipleSelection = false
    controller.modalPresentationStyle = .formSheet
    self.present(controller, animated: true, completion: nil)
  }
}

extension FileListViewController: DismissDelegate {
  func dismissWithData(data: Any?) {
    guard let cellModel = data as? FileListCellModel else { return }
    if let wifiViewModel = wifiViewModel {
      wifiViewModel.deleteCell(cellModel: cellModel)
    }
    if let localViewModel = localViewModel {
      localViewModel.deleteCell(cellModel: cellModel)
    }
  }
}
