//
//  FileListViewController+Local.swift
//  Hub
//
//  Created by ncn on 2023/05/23.
//

import RxSwift
import UIKit

extension FileListViewController {
  func bindViewModel(to viewModel: FileListLocalViewModel?) {
    guard let model = viewModel,
      let shareViewModel = shareViewModel
    else { return }

    model.menuItem = setLocalMenuIcon()
    setDownloadMenu()

//    fileStorageMenuView.collectionView.selectItem(
//      at: IndexPath(row: fileStorageSelectedIndex, section: 0),
//      animated: false,
//      scrollPosition: .right
//    )

    videoTypeMenuView.collectionView.selectItem(
      at: IndexPath(row: videoTypeSelectedIndex, section: 0),
      animated: false,
      scrollPosition: .right
    )

//    let fileStorageSelectedEvent = fileStorageMenuView.collectionView.rx.itemSelected.asObservable()
    let videoTypeSelectedEvent = videoTypeMenuView.selectIndexPathWithDelegate.asObservable()
    let itemSelectedEvent = fileListView.zoomView.collectionView.rx.itemSelected.asObservable()
    let editButtonMenuEvent = editMenuView.rxSelectedType
      .map {
        [weak self] (storageType, actionType) -> (
          FileStorageType, FileActionType, [FileListCellModel], Bool
        ) in
        guard let self = self else { return (.dashcam, .download, [], false) }
        let zoomView = self.fileListView.zoomView
        if zoomView.isSelectMode {
          let selectedItemList = zoomView.selectedIndexPathList.compactMap {
            zoomView.viewModel.getItemAtIndex($0)
          }
          return (storageType, actionType, selectedItemList, true)
        } else {
          guard let selectedTooltip = zoomView.selectedTooltip else {
            return (.dashcam, .download, [], false)
          }
          return (storageType, actionType, [selectedTooltip], false)
        }
      }
      .asObservable()

    let input = FileListLocalViewModel.Input(
      didLoadEvent: Observable.just(()),
      willAppearEvent: self.rx.viewWillAppear.asObservable(),
//      fileStorageSelectedEvent: fileStorageSelectedEvent,
      videoTypeSelectedEvent: videoTypeSelectedEvent,
      itemSelectedEvent: itemSelectedEvent,
      editMenuSelectEvent: editButtonMenuEvent,
      rxToolTipEvent: fileListView.zoomView.rxSelectedTooltip
    )

    let output = model.bind(input: input, disposedBag: self.disposedBag)

    output.rxIsDownloadMode
      .subscribe(onNext: { [weak self] value in
        guard let self = self else { return }
        self.fileStorageSelectedIndex = 0
        self.setDownloadMenu()
      })
      .disposed(by: self.disposedBag)

    output.rxIsExternalMode
      .subscribe(onNext: { [weak self] value in
        guard let self = self else { return }
        self.fileStorageSelectedIndex = 1
        self.setExternalMenu()
        self.openDoucemntPicker()
      })
      .disposed(by: self.disposedBag)

    output.rxSelectedVideoTypeIndexPath
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        self.videoTypeSelectedIndex = indexPath.row
      })
      .disposed(by: disposedBag)

    output.rxSelectedIndexPath
      .subscribe(onNext: { [weak self] index in
        guard let self = self else { return }
        if let vModel = self.shareViewModel {
          let section = model.listItem.value[index.section]
          let obj = VodLocalModel(selectedIndex: index.row, items: section.items)
          Log.message(to: obj.items.first ?? "")
          vModel.pushLocal(model: obj)
        }
      })
      .disposed(by: self.disposedBag)

    output.rxDeleteLocalPopup
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] cellModels in
        let confirmAction: (() -> Void)? = {
          self?.removeAllSelected()
          model.deleteLocalFiles(models: cellModels, output: output)
        }
        self?.popup.showPopup(
          title: L.popup_delete_title.localized, desc: L.dialog_delete_msg.localized,
          isCancel: true, confirmAction: confirmAction)
      })
      .disposed(by: disposedBag)

    output.rxShare
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] shareUrls in
        guard let self = self else { return }
        let activityViewController = UIActivityViewController(
          activityItems: shareUrls, applicationActivities: nil)
        activityViewController.popoverPresentationController?.sourceView = self.view  // so that iPads won't crash
        activityViewController.excludedActivityTypes = [.message]
        self.present(activityViewController, animated: true)
      })
      .disposed(by: disposedBag)

    output.rxReleaseToast
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { msg in
        LKPopupView.popup.toast(hit: msg)
      })
      .disposed(by: disposedBag)
    
    output.rxAlertMessage
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] msg in
        guard let self = self else { return }
        self.popup.showMessageAlert(message: msg, title: L.confirm.localized)
      })
      .disposed(by: self.disposedBag)

    model.listItem
      .do(onNext: { _ in
        fLogger.debug("isSelectMode: \(self.fileListView.zoomView.isSelectMode)")
        let isSelectMode = self.fileListView.zoomView.isSelectMode
        shareViewModel.rxIsCheck.onNext(isSelectMode)
      })
      .subscribe(onNext: { [weak self] array in
        guard let self = self else { return }

        DispatchQueue.main.async {
          self.fileListView.zoomView.list = array
        }

      })
      .disposed(by: self.disposedBag)
  }

  @discardableResult
  private func setLocalMenuIcon() -> [MenuIconModel] {
    let download = MenuIconModel(
      fileActionType: nil,
      fileStorageType: .download,
      normal: UIImage(named: "icon_download_g")!,
      highlight: UIImage(named: "icon_download_w")!,
      title: L.download.localized
    )
    let external = MenuIconModel(
      fileActionType: nil,
      fileStorageType: .external,
      normal: UIImage(named: "icon_external_g")!,
      highlight: UIImage(named: "icon_external_w")!,
      title: "External"
    )

    let icons = [download, external]

//    fileStorageMenuView.menuIcons = icons

    return icons
  }
}
