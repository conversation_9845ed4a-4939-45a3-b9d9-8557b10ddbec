//
//  FileListLocalViewModel.swift
//  Hub
//
//  Created by ncn on 2023/05/23.
//

import RxCocoa
import RxSwift
import UIKit


class FileListLocalViewModel: BaseViewModel {
  weak var coordinator: FileListCoordinator?

  var menuItem: [MenuIconModel] = []
  var menuType: FileStorageType = .download
  var fileStorageIdx: Int = 0
  var videoTypeIdx: Int = 0
  var listItem = BehaviorRelay<[FileListSectionModel]>(value: [])
  
  struct Input {
    let didLoadEvent: Observable<Void>
    let willAppearEvent: Observable<Bool>
//    let fileStorageSelectedEvent: Observable<IndexPath>
    let videoTypeSelectedEvent: Observable<IndexPath>
    let itemSelectedEvent: Observable<IndexPath>
    let editMenuSelectEvent:
      Observable<(FileStorageType, FileActionType, [FileListCellModel], Bool)>
    let rxToolTipEvent: Observable<FileListCellModel>
  }

  struct Output {
    let rxIsDownloadMode = PublishSubject<Bool>()
    let rxIsExternalMode = PublishSubject<Bool>()
    let rxSelectedVideoTypeIndexPath = PublishSubject<IndexPath>()
    let rxSelectedIndexPath = PublishSubject<IndexPath>()
    let rxDeleteLocalPopup = PublishSubject<[FileListCellModel]>()
    let rxShare = PublishSubject<[URL]>()
    let rxIsIndicator = PublishSubject<Bool>()

    let rxReleaseToast = PublishSubject<String>()
    let rxAlertMessage = PublishSubject<String>()
  }

  init(coordinator: FileListCoordinator) {
    self.coordinator = coordinator
  }

  deinit {
    aLogger.info("deinit \(Self.self)")
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.didLoadEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self = self else { return }
        self.loadFile(from: .video, output: output)
      })
      .disposed(by: disposedBag)

    input.willAppearEvent
      .subscribe(onNext: { [weak self] _ in
        guard let self else { return }
        didSelectMenu(indexPath: .init(row: 0, section: 0), output: output)
        mLogger.info("🔃 LocalViewModel willAppearEvent videoTypeIndex: \(videoTypeIdx)")
        switch fileStorageIdx {
        case 0:
          if videoTypeIdx == 0 {
            loadFile(from: .all, output: output)
          } else if self.videoTypeIdx == 1 {
            loadFile(from: .video, output: output)
          } else if self.videoTypeIdx == 2 {
            loadFile(from: .snapShot, output: output)
          }
        case 1: break  // 외부저장소 열었을 떄
        default:
          loadFile(from: .all, output: output)
        }
      })
      .disposed(by: disposedBag)

    input.videoTypeSelectedEvent
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        self.videoTypeIdx = indexPath.row
        mLogger.info("videoTypeIdx: \(self.videoTypeIdx)")
        if indexPath.row == 0 {
          self.loadFile(from: .all, output: output)
        } else if indexPath.row == 1 {
          self.loadFile(from: .video, output: output)
        } else if indexPath.row == 2 {
          self.loadFile(from: .snapShot, output: output)
        }
        
        output.rxSelectedVideoTypeIndexPath.onNext(indexPath)
      })
      .disposed(by: disposedBag)

    input.itemSelectedEvent
      .flatMapLatest(getCellModelFromIndex)
      .subscribe(onNext: { item in
        Log.message(to: item)
      })
      .disposed(by: disposedBag)

    input.editMenuSelectEvent
      .subscribe(onNext: { [weak self] (storageType, actionType, models, isSelect) in
        guard let self = self else { return }
        Log.message(to: "storageType: \(storageType), actionType: \(actionType), model: \(models)")
        if models.isEmpty {
          output.rxReleaseToast.onNext(L.filelist_none_select.localized)
        } else {
          let data = (storage: storageType, type: self.videoTypeIdx, menu: actionType)
          self.editMenuRouting(data: data, models: models, output: output)
        }
      })
      .disposed(by: disposedBag)

    input.rxToolTipEvent
      .subscribe(onNext: { value in
        if let idx = value.indexPath {
          output.rxSelectedIndexPath.onNext(idx)
        }
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension FileListLocalViewModel {

  func editMenuRouting(
    data: (storage: FileStorageType, type: Int, menu: FileActionType), models: [FileListCellModel],
    output: Output
  ) {
    let urls = models.compactMap { $0.filePath }
    switch data.storage {
    case .download:  // 다운로드
      switch data.type {
      case 0:  // VOD
        switch data.menu {
        case .share:  // 공유
          if models.count == 1 {
            output.rxShare.onNext(urls)
          } else {
            output.rxReleaseToast.onNext(L.shrae_warning_msg_01.localized)
          }
        case .delete:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        default:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        }
      default:  // SnapShot
        switch data.menu {
        case .share:  // 공유
          if models.count == 1 {
            output.rxShare.onNext(urls)
          } else {
            output.rxReleaseToast.onNext(L.shrae_warning_msg_01.localized)
          }
        case .delete:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        default:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        }
      }
    case .external:  // 외부
      switch data.menu {
      case .share:  // 공유
        let urls = models.compactMap { $0.filePath }
        output.rxShare.onNext(urls)
      case .delete:  // 삭제
        output.rxDeleteLocalPopup.onNext(models)
      default:  // 삭제
        output.rxDeleteLocalPopup.onNext(models)
      }
    default:
      break
    }
  }

  // zoomView 의 indexPath를 통해 Item가져오기
  func getCellModelFromIndex(_ indexPath: IndexPath) -> Observable<FileListSectionModel.Item> {
    let section = indexPath.section
    let row = indexPath.row
    let currentData = listItem.value

    // 현재 섹션의 데이터를 가져옵니다.
    guard section < currentData.count else { return Observable.empty() }

    let sectionData = currentData[section]
    // 현재 섹션의 아이템 개수를 확인하고 해당 아이템을 반환합니다.
    if row < sectionData.items.count {
      let cellModel = sectionData.items[row]
      return Observable.just(cellModel)
    } else {
      // 유효한 행 인덱스가 아닌 경우 빈 Observable을 반환합니다.
      return Observable.empty()
    }
  }

  func loadFile(from type: FileFolderType, output: Output) {
    listItem.accept([])
    var dict: [String: [FileListCellModel]]

    switch type {
    case .video:
      let vodArray = FileManager.loadFileAtFolder(path: UrlList.downloadedPath()!).filter({
        $0.lastPathComponent.contains(".mp4")
      })
      mLogger.info("vodArray count: \(vodArray.count)")
      dict = NCUtil.videoFileSectionDict(vodArray: vodArray)
    case .snapShot:
      let array = FileManager.loadFileAtFolder(path: UrlList.screenshotPath()!)
      dict = NCUtil.snapShotFileSectionDict(array: array)
    case .all:
      // Video files
      let vodArray = FileManager.loadFileAtFolder(path: UrlList.downloadedPath()!).filter({
          $0.lastPathComponent.contains(".mp4")
      })
      let videoDict = NCUtil.videoFileSectionDict(vodArray: vodArray)
      
      // Screenshot files
      let snapArray = FileManager.loadFileAtFolder(path: UrlList.screenshotPath()!)
      let snapDict = NCUtil.snapShotFileSectionDict(array: snapArray)
      
      // Merge both dictionaries
      dict = videoDict.merging(snapDict) { (video, snap) in
          video + snap
      }
    default:
      return
    }

    let sortedDict = dict.sorted(by: { $0.key > $1.key })
    var item: [FileListSectionModel] = []
    for (k, v) in sortedDict {
      let sortedItems = v.sorted(by: {
        $0.fileName.replacingOccurrences(of: ".", with: "")
          > $1.fileName.replacingOccurrences(of: ".", with: "")
      })
      item.append(FileListSectionModel(items: sortedItems, header: k))
    }

    mLogger.debug("🔃 loadFile item count: \(item.count)")
    listItem.accept(item)
  }

  func deleteLocalFiles(models: [FileListCellModel], output: Output) {
    output.rxIsIndicator.onNext(true)
    for model in models {
      guard let path = model.filePath else { continue }
      FileManager.remove(file: path)
    }

    if videoTypeIdx == 0 {
      loadFile(from: .all, output: output)
    } else if self.videoTypeIdx == 1 {
      loadFile(from: .video, output: output)
    } else if self.videoTypeIdx == 2 {
      loadFile(from: .snapShot, output: output)
    }
    output.rxIsIndicator.onNext(false)
    output.rxReleaseToast.onNext("dialog_delete_success".localized)
  }
}

extension FileListLocalViewModel {
  func didSelectMenu(indexPath: IndexPath, output: Output) {
    let menu = menuItem[indexPath.row]
    if menuType == menu.fileStorageType { return }
    listItem.accept([])
    menuType = menu.fileStorageType

    self.fileStorageIdx = indexPath.row
    switch menu.fileStorageType {
    case .download:
      output.rxIsDownloadMode.onNext(true)
      break
    case .external:
      output.rxIsExternalMode.onNext(true)
      break
    default:
      break
    }
  }

  func deleteCell(cellModel: FileListCellModel) {
    var reloadList = self.listItem.value
    self.listItem.accept([])
    sectionLoop: for (sectionIdx, section) in reloadList.enumerated() {
      cellLoop: for (cellIdx, cell) in section.items.enumerated() {
        if cellModel == cell {
          reloadList[sectionIdx].items.remove(at: cellIdx)
          break cellLoop
        }
      }
      if reloadList[sectionIdx].items.isEmpty {
        reloadList.remove(at: sectionIdx)
        break sectionLoop
      }
    }
    self.listItem.accept(reloadList)
  }
}
