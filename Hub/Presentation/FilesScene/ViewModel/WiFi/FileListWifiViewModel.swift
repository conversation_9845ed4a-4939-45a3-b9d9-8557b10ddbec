//
//  FileListCommandViewModel.swift
//  Hub
//
//  Created by ncn on 2023/05/19.
//

import Foundation
import RxCocoa
import RxSwift
import UIKit

class FileListWifiViewModel: BaseViewModel {
  weak var coordinator: FileListCoordinator?
  let useCase: FileCommandUseCase

  private var fileStorageType: FileStorageType = .dashcam
  private var fileFolderType: FileFolderType = .Drive
  var listItem = BehaviorRelay<[FileListSectionModel]>(value: [])
//  var isItemSelected = false
  
  struct Input {
    let didLoadEvent: Observable<Void>
    let willDisappearEvent: ControlEvent<Bool>
    let videoTypeSelectedEvent: Observable<FileFolderType>
    let itemSelectedEvent: Observable<IndexPath>
    let editMenuSelectEvent:
      Observable<(FileStorageType, FileActionType, [FileListCellModel], Bool)>
    let rxToolTipEvent: Observable<FileListCellModel>
  }

  struct Output {
    // 상단 메뉴
    let rxFileStorageMode = BehaviorSubject<FileStorageType>(value: .dashcam)

    let rxIsIndicator = PublishSubject<Bool>()
    let rxIsDismiss = PublishSubject<Bool>()
    let rxErrorMessage = PublishSubject<String>()
    let rxAlertMessage = PublishSubject<String>()
    let rxReleaseToast = PublishSubject<String>()

    let rxSelectedIndexPath = PublishSubject<IndexPath>()
    let rxSelectedVideoTypeIndexPath = PublishSubject<IndexPath>()

    let rxDownloadPopup = PublishSubject<FileListCellModel>()
    let rxDeletePopup = PublishSubject<[FileListCellModel]>()
    let rxDeleteLocalPopup = PublishSubject<[FileListCellModel]>()
    let rxDeselectTooltip = PublishSubject<Void>()
    let rxShare = PublishSubject<[URL]>()

  }

  init(coordinator: FileListCoordinator, useCase: FileCommandUseCase) {
    self.useCase = useCase
    self.coordinator = coordinator
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    self.fileFolderType = .Drive

    input.willDisappearEvent
      .observe(on: MainScheduler.asyncInstance)
      .subscribe(onNext: { [weak self] _ in
        guard let self, Current.playbackItemSelected == false else { return }
        Current.playbackItemSelected = false
        self.requestStartRecord(output: output)
      })
      .disposed(by: disposedBag)

    let videoTypeSelectedEvent = input.videoTypeSelectedEvent.share()
    videoTypeSelectedEvent
      .withLatestFrom(output.rxFileStorageMode) { fileFolderType, fileStorageType in
        return (fileFolderType, fileStorageType)
      }
      .do(onNext: { [weak self] (fileFolderType, _) in
        guard let self = self else { return }
        self.fileFolderType = fileFolderType
      })
      .delay(.seconds(1), scheduler: MainScheduler.instance)
      .flatMapLatest {
        [weak self] (fileFolderType, fileStorageType) -> Observable<
          (FileFolderType, FileStorageType)
        > in
        guard let self = self else { return .empty() }
        return self.recStopObservable(true)
          .map { _ in (fileFolderType, fileStorageType) }
      }
      .subscribe(onNext: { [weak self] (fileFolderType, fileStorageType) in
        guard let self = self else { return }
        switch fileStorageType {
        case .dashcam:
          self.requestFileList(folderType: fileFolderType, output: output)
        case .download:
          self.loadFile(from: fileFolderType, output: output)
          if fileFolderType == .all {
            self.loadFile(from: .all, output: output)
          }
        case .external:
          self.requestFileList(folderType: fileFolderType, output: output)
        case .cloud, .autoUpload:
          break
        }
      })
      .disposed(by: disposedBag)

    input.itemSelectedEvent
      .flatMap(getCellModelFromIndex)
      .subscribe(onNext: { item in
        //output.rxSelectedIndexPath.onNext(indexPath)
        Log.message(to: item)

      })
      .disposed(by: disposedBag)

    input.editMenuSelectEvent
      .subscribe(onNext: { [weak self] (storageType, actionType, models, isSelect) in
        guard let self = self else { return }
        if models.isEmpty {
          output.rxReleaseToast.onNext(L.filelist_none_select.localized)
        } else {
          let data = (storage: storageType, folder: self.fileFolderType, menu: actionType)
          self.editMenuRouting(data: data, models: models, isSelect: isSelect, output: output)
        }
      })
      .disposed(by: disposedBag)

    input.rxToolTipEvent
      .compactMap { $0.indexPath }
      .bind(to: output.rxSelectedIndexPath)
      .disposed(by: disposedBag)

    return output
  }
}

extension FileListWifiViewModel {
  func editMenuRouting(
    data: (storage: FileStorageType, folder: FileFolderType, menu: FileActionType),
    models: [FileListCellModel], isSelect: Bool,
    output: Output
  ) {
    //        let models = models.compactMap { $0 }
    switch data.storage {
    case .dashcam:  // 대시캠 --------------------
      // 2단계 드라이브, 이벤트, 파크, 메뉴얼 모두 같은 동작임
      switch data.menu {
      case .download:  // 다운로드
        coordinator?.navigate(to: .download(models: models, type: data.storage))
      case .delete:  // 삭제
        let hasLockFile = models.contains { $0.fileName.contains("_L.") }
        if hasLockFile {
          LKPopupView.popup.toast(hit: L.select_protect_file_txt.localized)
        } else {
          output.rxDeletePopup.onNext(models)
        }
      default:  // 삭제
        output.rxDeletePopup.onNext(models)
      }
    case .download:  // 다운로드 --------------------
      switch data.folder {
      case .video:  // VOD +++++++++++++++
        switch data.menu {
        case .share:  // 공유
          if models.count == 1 {
            coordinator?.navigate(to: .vodEdit(model: models[0]))
          } else {
            output.rxReleaseToast.onNext(L.shrae_warning_msg_01.localized)
          }
        case .delete:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        default:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        }
      case .snapShot:  // SnapShot +++++++++++++++
        switch data.menu {
        case .share:  // 공유
          let urlList = models.compactMap { $0.filePath }
          if models.count == 1 {
            output.rxShare.onNext(urlList)
          } else {
            output.rxReleaseToast.onNext(L.shrae_warning_msg_01.localized)
          }
        case .delete:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        default:  // 삭제
          output.rxDeleteLocalPopup.onNext(models)
        }
      default:  // SnapShot +++++++++++++++
        break
      }
    case .external:  // OTG --------------------
      // 2단계 메뉴가 없음
      switch data.menu {
      case .download:
        let urlList = models.compactMap { $0.filePath }
        output.rxShare.onNext(urlList)
      case .delete:  // 삭제
        output.rxDeleteLocalPopup.onNext(models)
      default:  // 삭제
        output.rxDeleteLocalPopup.onNext(models)
      }
    default:  // 외부 --------------------
      break
    }
  }

  // zoomView 의 indexPath를 통해 Item가져오기
  func getCellModelFromIndex(_ indexPath: IndexPath) -> Observable<FileListSectionModel.Item> {
    let section = indexPath.section
    let row = indexPath.row
    let currentData = listItem.value

    // 현재 섹션의 데이터를 가져옵니다.
    guard section < currentData.count else { return Observable.empty() }

    let sectionData = currentData[section]
    // 현재 섹션의 아이템 개수를 확인하고 해당 아이템을 반환합니다.
    if row < sectionData.items.count {
      let cellModel = sectionData.items[row]
      return Observable.just(cellModel)
    } else {
      // 유효한 행 인덱스가 아닌 경우 빈 Observable을 반환합니다.
      return Observable.empty()
    }
  }

  func requestStopRecord(input: Input, output: Output) {
    output.rxIsIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstop", param: 1)
    let send = SetCommand.Send(header: header, setcommand: command)
    useCase.send(to: send) { [weak self] response, error in
      guard let self else { return }
      switch self.fileStorageType {
      case .download:
        self.loadFile(from: fileFolderType, output: output)
      case .dashcam:
        self.requestFileList(folderType: self.fileFolderType, output: output)
      default: break
      }
    }
  }

  func recStopObservable(_ bool: Bool) -> Observable<Bool> {
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstop", param: 1)
    let send = SetCommand.Send(header: header, setcommand: command)
    return Observable.create { [weak self] emitter in
      self?.useCase.send(to: send) { response, error in
        Log.message(to: "\(String(describing: response)) error: \(String(describing: error))")
        if error != nil {
          emitter.onNext(false)
        } else {
          emitter.onNext(true)
        }
      }
      return Disposables.create()
    }
  }

  func requestStartRecord(output: Output) {
    output.rxIsIndicator.onNext(true)
    let header = HeaderModel(cmd: Command.setcommand)
    let command = SetCommandModel(command: "recstart", param: 1)
    let send = SetCommand.Send(header: header, setcommand: command)
    useCase.send(to: send) { response, error in
      Log.message(to: "\(String(describing: response)) error: \(String(describing: error))")
      output.rxIsDismiss.onNext(true)
      output.rxIsIndicator.onNext(false)
    }
  }

  func requestFileList(folderType: FileFolderType, output: Output) {
    self.listItem.accept([])

    output.rxIsIndicator.onNext(true)
    hLogger.info("folder type: \(folderType.rawValue)")
    let header = HeaderModel(cmd: Command.getfilelist)
    let command = GetFileListModel(type: folderType.typeParamValue, thumb: 1, url: 1)
    let send = GetFileList.Send(header: header, getfilelist: command)

    useCase.send(to: send) { [weak self] response, error in
      output.rxIsIndicator.onNext(false)
      guard let self = self else { return }

      output.rxErrorMessage.onNext(error.debugDescription)
      response?.getfilelist?.toDomain { obj in
        let array = obj.items
        let grouped = array.sliced(by: [.year, .month, .day], for: \.date)
        let sorted = grouped.sorted(by: { $0.key > $1.key })
        var models: [FileListSectionModel] = []

        for (key, value) in sorted {
          let sortedItems = value.sorted(by: {
            $0.fileName.replacingOccurrences(of: ".", with: "")
              > $1.fileName.replacingOccurrences(of: ".", with: "")
          })

          let model = FileListSectionModel(
            items: sortedItems,
            header: key.toString(format: "yyyy / MM / dd")
          )

          models.append(model)
        }
        output.rxDeselectTooltip.onNext(())
        self.listItem.accept(models)
      }

      if let customError = error as? NCError {
        output.rxAlertMessage.onNext(customError.failureReason)
      } else if let e = error {
        output.rxAlertMessage.onNext(e.localizedDescription)
      }

    }
  }

  func deleteFiles(models: [FileListCellModel], output: Output) {
    guard let model = models.first else {
      Log.message(to: "delete end")
      output.rxIsIndicator.onNext(false)
      //            if let error = deleteError {
      //                output.rxAletMessage.onNext(error.localizedDescription)
      //                output.rxIsDismiss.onNext(true)
      //            }
      self.requestFileList(folderType: self.fileFolderType, output: output)
      return
    }
    var models = models
    output.rxIsIndicator.onNext(true)

    let header = HeaderModel(cmd: Command.deletefile)
    let command = DeleteFileModel.Send.DeleteFile(
      path: model.path ?? "/mnt/mmc/INF", name: model.fileName)
    let send = DeleteFileModel.Send(header: header, deletefile: command)
    Log.message(to: "delete \(model.fileName)")
    models.removeFirst()
    self.useCase.send(to: send) { response, error in
      Log.message(to: "delete \(model.fileName) <-- \(models)")
      self.deleteFiles(models: models, output: output)
    }

  }

  func deleteLocalFiles(models: [FileListCellModel], output: Output) {
    output.rxIsIndicator.onNext(true)

    for model in models {
      guard let path = model.filePath else { continue }
      FileManager.remove(file: path)
    }
    self.loadFile(from: fileFolderType, output: output)
    output.rxIsIndicator.onNext(false)
  }

  func loadFile(from type: FileFolderType, output: Output) {
    self.listItem.accept([])
    let dict: [String: [FileListCellModel]]
    switch type {
    case .video:
      let vodArray = FileManager.loadFileAtFolder(path: UrlList.downloadedPath()!).filter({
        $0.lastPathComponent.contains(".mp4")
      })
      dict = NCUtil.videoFileSectionDict(vodArray: vodArray)
    case .snapShot:
      let array = FileManager.loadFileAtFolder(path: UrlList.screenshotPath()!)
      dict = NCUtil.snapShotFileSectionDict(array: array)
    default:
      return
    }

    let sortedDict = dict.sorted(by: { $0.key > $1.key })
    var item: [FileListSectionModel] = []
    for (k, v) in sortedDict {
      let sortedItems = v.sorted(by: {
        $0.fileName.replacingOccurrences(of: ".", with: "")
          > $1.fileName.replacingOccurrences(of: ".", with: "")
      })
      item.append(FileListSectionModel(items: sortedItems, header: k))
    }

    listItem.accept(item)
    output.rxIsIndicator.onNext(false)
  }
}

extension FileListWifiViewModel {
  func rxDidSelectMenu(fileStorageType: FileStorageType) -> Observable<FileStorageType> {
    self.listItem.accept([])
    self.fileStorageType = fileStorageType
    return Observable.just(fileStorageType)
  }

  func deleteCell(cellModel: FileListCellModel) {
    var reloadList = self.listItem.value
    self.listItem.accept([])
    sectionLoop: for (sectionIdx, section) in reloadList.enumerated() {
      cellLoop: for (cellIdx, cell) in section.items.enumerated() {
        if cellModel == cell {
          reloadList[sectionIdx].items.remove(at: cellIdx)
          break cellLoop
        }
      }
      if reloadList[sectionIdx].items.isEmpty {
        reloadList.remove(at: sectionIdx)
        break sectionLoop
      }
    }
    self.listItem.accept(reloadList)
  }
}

// MARK: - send Socket Message with Rx
extension FileListWifiViewModel {
  func rxRequestFileList(type: Int) -> Observable<Result<ResponseFileListModel?, Error>> {
    let header = HeaderModel(cmd: Command.getfilelist)
    let command = GetFileListModel(type: type, thumb: 1, url: 1)
    let send = GetFileList.Send(header: header, getfilelist: command)
    return Observable.create { [weak self] emitter in
      self?.useCase.send(to: send) { response, error in
        if let error = error {
          emitter.onNext(.failure(error))
        } else {
          emitter.onNext(.success(response?.getfilelist))
        }
        emitter.onCompleted()
      }
      return Disposables.create()
    }
  }

}

extension FileListWifiViewModel {
  func dismiss() {
    coordinator?.navigate(to: .fileListDidFinish)
  }
}
