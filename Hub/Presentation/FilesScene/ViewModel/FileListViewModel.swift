//
//  FileListViewModel.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import RxCocoa
import RxSwift
import UIKit

enum FileFolderType: String {
  case Drive = "Drive"
  case Event = "Event"
  case Park = "Park"
  case Manual = "Manual"
  case ParkEvent = "Event(P)"
  case Bookmark = "Bookmark"
  case all = "All"
  case video = "Video"
  case snapShot = "Image"
  
  var typeParamValue: Int {
    switch self {
    case .Drive:
      0
    case .Event:
      1
    case .Park:
      2
    case .Manual:
      3
    case .ParkEvent:
      4
    case .Bookmark:
      5
    case .video, .snapShot, .all:
      0
    }
  }
}

enum UpDown {
  case Up
  case Down
}

class FileListViewModel: NSObject, BaseViewModel {
  weak var coordinator: FileListCoordinator?
  let rxExternalFiles = PublishSubject<[FileListSectionModel]>()
  let rxIsIndicator = PublishSubject<Bool>()
  let rxIsCheck = PublishSubject<Bool>()

  weak var dismissDelegate: DismissDelegate?

  struct Input {
//    let backButtonEvent: Observable<Void>
//    let storateSeletedEvent: Observable<IndexPath>
    let checkButtonEvent: Observable<Bool>
    let moveToTopEvent: Observable<Void>
    let moveToBotEvent: Observable<Void>
    let scrollStart: Observable<Void>
    let scrollEnd: Observable<Void>
  }

  struct Output {
    let rxIsBack: PublishRelay<Bool> = PublishRelay()
    let rxIsIndicator = PublishSubject<Bool>()
    let rxUpdown = PublishRelay<UpDown>()
    let rxUpdownIsHidden = PublishRelay<Bool>()
  }

  init(coordinator: FileListCoordinator) {
    self.coordinator = coordinator
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

//    input.backButtonEvent
//      .scan(false) { (lastState, newValue) in
//        !lastState
//      }
//      .bind(to: output.rxIsBack)
//      .disposed(by: disposedBag)

    
//    input.storateSeletedEvent
//      .subscribe(onNext: { indexPath in
//      })
//      .disposed(by: disposedBag)

    input.checkButtonEvent
      .rxDebug("")
      .map { !$0 }
      .bind(to: rxIsCheck)
      .disposed(by: disposedBag)

    input.moveToTopEvent
      .map { UpDown.Up }
      .bind(to: output.rxUpdown)
      .disposed(by: disposedBag)

    input.moveToBotEvent
      .map { UpDown.Down }
      .bind(to: output.rxUpdown)
      .disposed(by: disposedBag)

    input.scrollStart
      .map { false }
      .bind(to: output.rxUpdownIsHidden)
      .disposed(by: disposedBag)

    input.scrollEnd
      .map { true }
      .bind(to: output.rxUpdownIsHidden)
      .disposed(by: disposedBag)

    return output
  }
}

extension FileListViewModel: UIDocumentPickerDelegate {
  func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL])
  {
    rxIsIndicator.onNext(true)
    guard let targetUrl = urls.first else {
      Log.message(to: "Invalid urls.")
      rxIsIndicator.onNext(false)
      return
    }

    let shouldStopAccessing = targetUrl.startAccessingSecurityScopedResource()
    defer {
      if shouldStopAccessing {
        Log.message(to: "Stop accesing security.")
        targetUrl.stopAccessingSecurityScopedResource()
      }
      rxIsIndicator.onNext(false)
    }

    /* File read from file app. */
    let localFileManager = FileManager()
    let resourceKeys = Set<URLResourceKey>([.nameKey, .isDirectoryKey])
    let directoryEnumerator = localFileManager.enumerator(
      at: targetUrl,
      includingPropertiesForKeys: Array(resourceKeys),
      options: .skipsHiddenFiles
    )!

    var fileURLs: [URL] = []
    for case let fileURL as URL in directoryEnumerator {
      guard let resourceValues = try? fileURL.resourceValues(forKeys: resourceKeys),
        let isDirectory = resourceValues.isDirectory,
        let name = resourceValues.name
      else {
        continue
      }

      if isDirectory {
        if name == "_extras" {
          directoryEnumerator.skipDescendants()
        }
      } else {
        if let decoded = fileURL.description.removingPercentEncoding {
          Log.message(to: "At file app: " + decoded)
        } else {
          Log.message(to: "At file app: " + fileURL.description)
        }

        #if false
          // file copy
          if let ret = fileURL.copyItemWithUrl() {
            Log.message(to: "\(ret)")
            fileURLs.append(ret)  // copy url
          } else {
            Log.message(to: "Did't copy file:" + fileURL.description)
          }
        #else
          //fileURLs.append(fileURL)
          if let ret = onBookmarks(url: fileURL) {
            fileURLs.append(ret)
          }
        #endif
      }
    }

    let models = toDomain(urls: fileURLs)
    rxExternalFiles.onNext(models)
  }

  private func onBookmarks(url: URL) -> URL? {
    do {
      let bookmarkData: Foundation.Data = try url.bookmarkData(options: .minimalBookmark)
      let data = bookmarkData
      var stale = false
      if let url = try? URL(resolvingBookmarkData: data, bookmarkDataIsStale: &stale),
        stale == false
      {
        return url
      }
    } catch {
      Log.error(to: error.localizedDescription)
    }
    return nil
  }

  private func toDomain(urls: [URL]) -> [FileListSectionModel] {
    var list: [FileListCellModel] = []
    for path in urls {
      let obj = FileListCellModel(
        type: .file,
        fileName: path.lastPathComponent,
        filePath: path
      )
      list.append(obj)
    }

    let grouped = list.sliced(by: [.year, .month, .day], for: \.date)
    let sorted = grouped.sorted(by: { $0.0 < $1.0 })
    var models: [FileListSectionModel] = []

    for obj in sorted {
      let model = FileListSectionModel(
        items: obj.value, header: obj.key.toString(format: "yyyy / MM / dd"))
      models.append(model)
    }

    return models
  }
}

extension FileListViewModel {
  func dismiss() {
    coordinator?.navigate(to: .fileListDidFinish)
  }

  func pushVod(indexPath: IndexPath) {
  }

  func pushVod(model: VodStreamModel) {
    let fileExtension = model.items[model.selectedIndex].fileName.fileExtension.lowercased()
    let type = fileExtension.contains("jpg") || fileExtension.contains("png") ? PlayerViewType.bookmark : PlayerViewType.vod
    fLogger.info("fileExtension: \(fileExtension), type: \(type.rawValue)")
    coordinator?.navigate(to: .selectVod(model: model, type: type))
  }

  func pushLocal(model: VodLocalModel) {
    guard let filePath = model.items[model.selectedIndex].filePath else { return }
    mLogger.debug("local select filePath: \(filePath.lastPathComponent)")
    let fileExtension = filePath.lastPathComponent.fileExtension.lowercased()
    if (fileExtension == "mp4") || (fileExtension == "avi") || (fileExtension == "mov") {
      coordinator?.navigate(to: .selectLocalVod(model: model, type: .vod))
    } else if (fileExtension == "png") || (fileExtension == "jpg") || (fileExtension == "jpeg") {
      coordinator?.navigate(to: .selectLocalVod(model: model, type: .snapShot))
    }
  }

  func pushEdit(model: FileListCellModel) {
    coordinator?.navigate(to: .vodEdit(model: model))
  }
}
