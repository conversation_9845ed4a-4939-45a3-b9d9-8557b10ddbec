//
//  FileListCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import Foundation

protocol FileListCoordinator: AnyObject {
  func navigate(to step: FileListSteps)
}

public protocol FileListCoordinatorFinishDelegate: AnyObject {
  func fileListCoordinatorDidFinish()
}

public enum FileListSteps: Step {
  case showFileList
  case selectVod(model: VodStreamModel, type: PlayerViewType)
  case selectLocalVod(model: VodLocalModel, type: PlayerViewType)
  case download(models: [FileListCellModel], type: FileStorageType)
  case share(models: [FileListCellModel], type: FileStorageType)
  case vodEdit(model: FileListCellModel)
  case fileListDidFinish
}

public enum FileListChildCoordinator {
  case vod
  case download
  case edit
}
