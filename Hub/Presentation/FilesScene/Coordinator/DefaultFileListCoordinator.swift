//
//  DefaultFileListCoordinator.swift
//  Hub
//
//  Created by ncn on 2023/01/20.
//

import UIKit

final class DefaultFileListCoordinator: NavigationCoordinator {
  public let  navigationController: UINavigationController
  weak var delegate: FileListCoordinatorFinishDelegate?
  weak var dismissDelegate: DismissDelegate?

  var childCoordinators = [FileListChildCoordinator: Coordinator]()

  required init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func start() {
    navigate(to: .showFileList)
  }
}

extension DefaultFileListCoordinator {
  func make() -> FileListViewController {
    let vc = FileListViewController()
    vc.shareViewModel = FileListViewModel(coordinator: self)

    switch AppManager.shared.mode {
    case .wifi:
      configWifiModel(viewController: vc)
      break
    case .file:
      configLocalModel(viewController: vc)
      break
    }
    dismissDelegate = vc
    return vc
  }

  private func configWifiModel(viewController: FileListViewController) {
    let commandViewModel = FileListWifiViewModel(coordinator: self, useCase: Composers.fileCommandUseCase)
    commandViewModel.coordinator = self

    viewController.wifiViewModel = commandViewModel
  }

  private func configLocalModel(viewController: FileListViewController) {
    let localViewModel = FileListLocalViewModel(coordinator: self)
    localViewModel.coordinator = self
    viewController.localViewModel = localViewModel
  }
}

extension DefaultFileListCoordinator: FileListCoordinator {
  func navigate(to step: FileListSteps) {
    switch step {
    case .showFileList:
      let vc = make()
      navigationController.pushViewController(vc, animated: true)
    case let .selectVod(model: model, type: type):
      pushVod(model: model, vodType: type)
    case let .selectLocalVod(model: model, type: type):
      pushVod(model: model, vodType: type)
    case let .download(models: models, type: type):
      pushDownload(models: models, type: type)
    case let .share(models: models, type: type):
      pushShare(models: models, type: type)
    case let .vodEdit(model: model):
      pushEdit(model: model)
    case .fileListDidFinish:
      DispatchQueue.main.async { [weak self] in
        self?.navigationController.popViewController(animated: true)
        self?.delegate?.fileListCoordinatorDidFinish()
      }
    }
  }

  func pushVod(model: VodStreamModel, vodType: PlayerViewType) {
    let vodCoordinator = DefaultVodCoordinator(navigationController: navigationController)
    vodCoordinator.delegate = self
    childCoordinators[.vod] = vodCoordinator
    vodCoordinator.start(with: .showStreamVod(model: model, type: vodType, delegate: dismissDelegate))
  }

  func pushVod(model: VodLocalModel, vodType: PlayerViewType) {
    let vodCoordinator = DefaultVodCoordinator(navigationController: navigationController)
    vodCoordinator.delegate = self
    childCoordinators[.vod] = vodCoordinator
    vodCoordinator.start(with: .showLocalVod(model: model, type: vodType, delegate: dismissDelegate))
  }

  func pushEdit(model: FileListCellModel) {
    let editCoordinator = DefaultFileEditCoordinator(navigationController: navigationController)
    editCoordinator.delegate = self
    childCoordinators[.edit] = editCoordinator
    editCoordinator.start(with: .showFileEdit(model: model, editType: .trim))
  }

  func pushDownload(models: [FileListCellModel], type: FileStorageType) {
    let downloadCoordinator = DefaultPopupDownloadCoordinator(navigationController: navigationController)
    downloadCoordinator.delegate = self
    childCoordinators[.download] = downloadCoordinator
    downloadCoordinator.start(with: .showPopupDownload(model: models, editType: .trim, type: type, isToEdit: false))
  }

  func pushShare(models: [FileListCellModel], type: FileStorageType) {
    let downloadCoordinator = DefaultPopupDownloadCoordinator(navigationController: navigationController)
    downloadCoordinator.delegate = self
    childCoordinators[.download] = downloadCoordinator
    downloadCoordinator.start(with: .showPopupDownload(model: models, editType: .trim, type: type, isToEdit: true))
  }
}


// MARK: FinishDelegate
extension DefaultFileListCoordinator: VodCoordinatorFinishDelegate {
  func vodCoordinatorDidFinish() {
    childCoordinators[.vod] = nil
  }
}

extension DefaultFileListCoordinator: FileEditCoordinatorFinishDelegate {
  func fileEditCoordinatorDidFinish() {
    childCoordinators[.edit] = nil
  }
}

extension DefaultFileListCoordinator: PopupDownloadCoordinatorFinishDelegate {
  func popupDownloadCoordinatorDidFinish() {
    childCoordinators[.download] = nil
  }
}
