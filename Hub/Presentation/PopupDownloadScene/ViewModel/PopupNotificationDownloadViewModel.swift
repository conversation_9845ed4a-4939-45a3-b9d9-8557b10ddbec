//
//  PopupNotificationDownloadViewModel.swift
//  Hub
//
//  Created by ncn on 6/24/25.
//


import Foundation
import RxCocoa
import RxSwift

final class PopupNotificationDownloadViewModel: BaseViewModel {
  let aiUseCase: AIUseCase
  let saveFileName: String
  let model: NotificationModel

  init(useCase: AIUseCase, model: NotificationModel, saveFileName: String) {
    self.aiUseCase = useCase
    self.model = model
    self.saveFileName = saveFileName
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxDownloadPercent = PublishRelay<Double>()
    let rxSaveEnd = PublishRelay<(Bool, NotificationModel?)>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    let downloadStart = input.viewDidLoad
      .compactMap { [weak self] in self?.model }
      .flatMapLatest(downloadFile(model:))  // 파일 다운로드
      .share()

    downloadStart
      .compactMap { percent, data in data }
      .flatMapLatest(saveNotificationFile(data:))
      .compactMap { [weak self] bool in
        return (true, self?.model)
      }
      .bind(to: output.rxSaveEnd)
      .disposed(by: disposedBag)
    //
    downloadStart  // 파일 다운로드 중 프로그래스바 올림
      .map { percent, data in percent }
      .bind(to: output.rxDownloadPercent)
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupNotificationDownloadViewModel {

  func downloadFile(model: NotificationModel) -> Observable<(Double, Data?)> {
    return Observable.create { [weak self] emitter in
      self?.aiUseCase.download(
        url: model.fileUrl,
        download: { percent in
          emitter.onNext((percent, nil))
        },
        completion: { result in
          switch result {
          case .success(let data):
            emitter.onNext((1.0, data))
            emitter.onCompleted()
          case .failure(let error):
            Log.error(to: error.localizedDescription)
            emitter.onNext((0.0, nil))
            emitter.onCompleted()
          }
        }
      )
      return Disposables.create()
    }
  }
  
  func saveNotificationFile(data: Data?) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let downloadPath = UrlList.downloadedPath(),
          let screenshotPath = UrlList.screenshotPath() else {
      return Observable.just(nil)
    }

    let path = saveFileName.contains(".jpg") ? screenshotPath : downloadPath
    aLogger.info("Save file at path: \(path.absoluteString), saveFileName: \(self.saveFileName)")
    FileManager.writeToFile(data: data, atPath: path, name: saveFileName)
    
    guard let fullPath = FileManager.loadFileAtFolder(path: path).first else {
      return Observable.just(nil)
    }
    aLogger.info("Save full path: \(fullPath.path)")
    return Observable.just(fullPath)
  }

}
