//
//  PopupDownloadViewModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 2023/09/13.
//

import Foundation
import RxCocoa
import RxSwift

final class PopupWifiDownloadViewModel: BaseViewModel {
  let useCase: PopupDownloadUseCase
  let fileModel: [FileListCellModel]
  let isToEdit: Bool
  weak var coordinator: PopupDownloadCoordinator?

  init(useCase: PopupDownloadUseCase, fileModel: [FileListCellModel], isToEdit: Bo<PERSON>, coordinator: PopupDownloadCoordinator?) {
    self.useCase = useCase
    self.fileModel = fileModel
    self.isToEdit = isToEdit
    self.coordinator = coordinator
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxDownloadStart = PublishRelay<(String,Int, Int)>()
    let rxDownloadPercent = PublishRelay<Double>()
    let rxSaveEnd = PublishRelay<(Bool, FileListCellModel?)>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    // 한개만 다운 받을 경우
    let downloadStart = input.viewDidLoad
      .compactMap { [weak self] in self?.fileModel }
      .filter { $0.count == 1 }
      .compactMap { $0.first }
      .flatMapLatest(downloadFile(fileModel:))  // 파일 다운로드
      .share()
    
    downloadStart
      .filter { [weak self] _, _, _ in self?.isToEdit == true }
      .compactMap { percent, data, isSuccess in data }
      .flatMapLatest(saveTempVodFile(data:))  // avi파일로 내부저장소에 저장
//      .flatMapLatest(encodeVideo(aviFullPath:))  // mp4파일로 변환
      .compactMap { [weak self] bool in
        guard let self else { return (false, nil) }
        return (self.isToEdit, self.fileModel.first)
      }
      .bind(to: output.rxSaveEnd)
      .disposed(by: disposedBag)

    downloadStart
      .filter { [weak self] _, _, _ in self?.isToEdit == false }
      .compactMap { percent, data, isSuccess in isSuccess }
      .compactMap { [weak self] bool in
        guard let self else { return (false, nil) }
        return (self.isToEdit, self.fileModel.first)
      }
      .bind(to: output.rxSaveEnd)
      .disposed(by: disposedBag)

    downloadStart  // 파일 다운로드 중 프로그래스바 올림
      .map { percent, data, isSuccess in percent }
      .bind(to: output.rxDownloadPercent)
      .disposed(by: disposedBag)

    // 여러개 다운 받을 경우
    input.viewDidLoad
      .compactMap { [weak self] in self?.fileModel }
      .filter { $0.count != 1 }
      .bind(onNext: { [weak self] models in
        self?.downloadFiles(fileModels: models, output: output)
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupWifiDownloadViewModel {

  func downloadFiles(fileModels: [FileListCellModel], output: Output) {
    var models = fileModels
    guard let fileModel = models.first,
      let downloadPath = UrlList.downloadedPath(),
          let screenshotPath = UrlList.screenshotPath()
    else {
      // 파일 모두 다운로드 완료
      fLogger.info("All files downloaded")
      output.rxSaveEnd.accept((false, nil))
      return
    }
    
    let current = self.fileModel.count - models.count + 1
    fLogger.info("Downloading file: \(fileModel.fileName), current: \(current), count: \(self.fileModel.count)")
    output.rxDownloadStart.accept(("\(fileModel.fileName)", current, self.fileModel.count))
    let header = HeaderModel(cmd: "filedownload")
    let body = WifiFileDownloadModel.Request.FileDownload(
      path: fileModel.path ?? "", name: fileModel.fileName)
    let command = WifiFileDownloadModel.Request(header: header, filedownload: body)
    models.removeFirst()
    useCase.request(
      command: command,
      download: { percent in
        output.rxDownloadPercent.accept(percent)
      },
      completion: { [weak self] result in
        guard let self else { return }
        switch result {
        case .success(let data):
          fLogger.info("downloading success: \(fileModel.fileName) < - \(models)")
          let isBookmarkFile = fileModel.fileName.contains(".jpg")
          
          if isBookmarkFile {
            FileManager.writeToFile(data: data, atPath: screenshotPath, name: fileModel.fileName)
          } else {
            FileManager.writeToFile(data: data, atPath: downloadPath, name: fileModel.fileName)
          }
          downloadFiles(fileModels: models, output: output)
          if current == self.fileModel.count {
            output.rxSaveEnd.accept((true, nil))
          }

        case .failure(let error):
          fLogger.error("downloading failed: \(fileModel.fileName), \(error.localizedDescription)")
          output.rxSaveEnd.accept((false, nil))
          return
        }
      })
  }

  func downloadFile(fileModel: FileListCellModel) -> Observable<(Double, Data?, Bool?)> {
    guard let downloadPath = UrlList.downloadedPath(),
          let screenshotPath = UrlList.screenshotPath() else { return .empty() }
    let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
    let header = HeaderModel(cmd: "filedownload")
    let body = WifiFileDownloadModel.Request.FileDownload(
      path: fileModel.path ?? "", name: fileModel.fileName)
    let command = WifiFileDownloadModel.Request(header: header, filedownload: body)
    return Observable.create { [weak self] emitter in
      self?.useCase.request(
        command: command,
        download: { percent in
          emitter.onNext((percent, nil, nil))
        },
        completion: { result in
          switch result {
          case .success(let data):
            if self?.isToEdit == true {
              emitter.onNext((1.0, data, true))
              return
            }

            let isBookmarkFile = fileModel.fileName.contains(".jpg")
            let writePath: URL = isBookmarkFile ? screenshotPath : downloadPath
            let newFilename = FileManager.default.getUniqueName(fileName: fileModel.fileName , path: writePath)
            fLogger.info("##@@@ fileModel.fileName: \(fileModel.fileName) filename: \(newFilename)")

            FileManager.writeToFile(data: data, atPath: writePath, name: newFilename, isSaveToPhoto: false)
            let mp4Path = downloadPath.appendingPathComponent(newFilename)
            PhotoUtils.saveVideo(
              url: mp4Path,
              toAlbum: modelName,
              completion: { isSuccess, error in
                if !isSuccess || error != nil {
                  emitter.onNext((1.0, nil, false))
                } else {
                  emitter.onNext((1.0, nil, true))
                }
                emitter.onCompleted()
              }
            )
          case .failure(let error):
            Log.error(to: error.localizedDescription)
            emitter.onNext((0.0, nil, false))
            emitter.onCompleted()
          }
        })
      return Disposables.create()
    }
  }

  func saveTempVodFile(data: Data?) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let path = UrlList.vodTemp() else {
      fLogger.info("Fail to get path.")
      return Observable.just(nil)
    }
    if FileManager.loadFileAtFolder(path: path) != [] {
      FileManager.remove(folder: path)
    }
    let name = "tempVOD.mp4"
    FileManager.writeToFile(data: data, atPath: path, name: name, isSaveToPhoto: false)
    guard let mp4FullPath = FileManager.loadFileAtFolder(path: path).first else {
      return Observable.just(nil)
    }
    return Observable.just(mp4FullPath)
  }

  func encodeVideo(aviFullPath: URL?) -> Observable<Bool> {
    // avi와 같은곳에 생성
    guard let aviFullPath = aviFullPath else { return Observable.just(false) }
    let channelCount = fileModel.first?.channelbits.channelBitMaskingCount ?? 1
    // avi -> MP4 or MOV파일로 변환
    return Observable.create { emitter in
      var isAllComplete: [String: Bool] = [:]
      for channel in 0..<channelCount {
        let state = FFMpegUtils.encodeToTempMP4(aviFullPath: aviFullPath, channel: channel)
        switch state {
        case .completed:
          fLogger.info("completed")
          isAllComplete.updateValue(true, forKey: "ch_\(channel)_end")
        default:
          fLogger.info("fail")
          isAllComplete.updateValue(false, forKey: "ch_\(channel)_end")
        }
      }
      Log.message(
        to: "isAllComplete.values.allSatisfy \(isAllComplete.values.allSatisfy({ $0 == true }))")
      if isAllComplete.values.allSatisfy({ $0 == true }) {
        FileManager.remove(file: aviFullPath)
        emitter.onNext(true)
      } else {
        emitter.onNext(false)
      }
      emitter.onCompleted()
      return Disposables.create()
    }
  }
}

// MARK: - navigation
extension PopupWifiDownloadViewModel {
  func dismiss() {
    if let coordinator = coordinator {
      coordinator.navigate(to: .popupDownloadDidFinish)
    }
  }

  func pushEdit(fileModel: FileListCellModel, editType: FileEditType) {
    coordinator?.navigate(to: .edit(fileModel: fileModel, editType: editType))
  }
}
