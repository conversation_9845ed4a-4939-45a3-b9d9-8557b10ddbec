//
//  PopupFirmwareDownloadViewModel.swift
//  Hub
//
//  Created by ncn on 4/4/24.
//

import Foundation
import RxCocoa
import RxSwift

final class PopupFirmwareDownloadViewModel: BaseViewModel {
  let useCase: PopupDownloadUseCase
  let model: FirmwareModel

  init(useCase: PopupDownloadUseCase, model: FirmwareModel) {
    self.useCase = useCase
    self.model = model
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxIsNewFirmware = PublishRelay<Bool>()
    let rxDownloadPercent = PublishRelay<Double>()
    let rxSaveEnd = PublishRelay<(Bool, FirmwareModel?)>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    let downloadStart = input.viewDidLoad
      .compactMap { [weak self] in self?.model }
      .filter { $0.version > Current.deviceVersion }
      .flatMapLatest(downloadFile(model:))  // 파일 다운로드
      .share()

    downloadStart
      .compactMap { percent, data in data }
      .flatMapLatest(saveTempFirmwareFile(data:))
      .compactMap { [weak self] bool in
        return (true, self?.model)
      }
      .bind(to: output.rxSaveEnd)
      .disposed(by: disposedBag)
    //
    downloadStart  // 파일 다운로드 중 프로그래스바 올림
      .map { percent, data in percent }
      .bind(to: output.rxDownloadPercent)
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupFirmwareDownloadViewModel {

  func downloadFile(model: FirmwareModel) -> Observable<(Double, Data?)> {
    return Observable.create { [weak self] emitter in
      self?.useCase.request(
        firmwareDownloadUrl: model.url,
        download: { percent in
          emitter.onNext((percent, nil))
        },
        completion: { result in
          switch result {
          case .success(let data):
            emitter.onNext((1.0, data))
            emitter.onCompleted()
          case .failure(let error):
            Log.error(to: error.localizedDescription)
            emitter.onNext((0.0, nil))
            emitter.onCompleted()
          }
        }
      )
      return Disposables.create()
    }
  }

  func saveTempFirmwareFile(data: Data?) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let path = UrlList.tempPath() else {
      Log.message(to: "Fail to get path.")
      return Observable.just(nil)
    }

    if FileManager.loadFileAtFolder(path: path) != [] {
      FileManager.remove(folder: path)
    }

    FileManager.writeToFile(data: data, atPath: path, name: model.file, isSaveToPhoto: false)
    guard let fullPath = FileManager.loadFileAtFolder(path: path).first else {
      return Observable.just(nil)
    }
    return Observable.just(fullPath)
  }
}
