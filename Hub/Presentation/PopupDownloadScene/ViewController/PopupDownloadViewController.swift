//
//  PopupDownloadViewController.swift
//  Hub
//
//  Created by leej<PERSON><PERSON><PERSON> on 2023/09/13.
//

import Alamofire
import RxCocoa
import RxSwift
import SnapKit
import UIKit

final class PopupWifiDownloadViewController: UIViewController {
  let containerView = UIView()
  let titleLabel = UILabel()
  let descriptionLabel = UILabel()
  let downloadCountLabel = UILabel()
  var fileName: String = ""

  let progressView = ProgressView()

  let buttonStack = UIStackView()
  let cancelBtn = UIButton()

  weak var coordinator: PopupDownloadCoordinator?
  var wifiViewModel: PopupWifiDownloadViewModel?
  var firmwareDownloadViewModel: PopupFirmwareDownloadViewModel?
  var notificationDownlaodViewModel: PopupNotificationDownloadViewModel?
  var editType: FileEditType?

  var disposedBag = DisposeBag()

  init(
    coordinator: PopupDownloadCoordinator?, editType: FileEditType, fileName: String = "",
    totalCount: Int
  ) {
    super.init(nibName: nil, bundle: nil)
    self.modalTransitionStyle = .crossDissolve
    self.modalPresentationStyle = .overCurrentContext
    self.titleLabel.text = L.dialog_save_ing.localized
    self.descriptionLabel.text = "Downloading the file\n\(fileName)"
    if totalCount == -1 {
      self.downloadCountLabel.isHidden = true
    } else {
      self.downloadCountLabel.isHidden = false
      self.downloadCountLabel.text = "\(1)/\(totalCount)"
    }
    self.fileName = fileName
    self.coordinator = coordinator
    self.editType = editType
  }

  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  override func viewDidDisappear(_ animated: Bool) {
    super.viewDidDisappear(animated)
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setComponent()
    setAutoLayout()
    bindViewModel(to: wifiViewModel)
    bindViewModel(to: firmwareDownloadViewModel)
    bindViewModel(to: notificationDownlaodViewModel)
  }

  @objc private func addCancelAction() {
    AF.session.getAllTasks { task in
      task.forEach { $0.cancel() }
    }
    if notificationDownlaodViewModel != nil{
      self.dismiss(animated: true)
    }
    
    wifiViewModel?.dismiss()
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let viewModel = viewModel as? PopupWifiDownloadViewModel else { return }
    let input = PopupWifiDownloadViewModel.Input(viewDidLoad: Observable.just(()))
    let output = viewModel.bind(input: input, disposedBag: disposedBag)

    output.rxDownloadStart
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] (fileName, current, total) in
        guard let self else { return }
        descriptionLabel.text = "Downloading the file\n\(fileName)"
        downloadCountLabel.text = "\(current)/\(total)"
      })
      .disposed(by: disposedBag)

    
    output.rxDownloadPercent
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] percent in
        self?.progressView.updateProgress(percent: percent)
      })
      .disposed(by: disposedBag)

    output.rxSaveEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isEdit, fileModel in
        guard let self else { return }
        if isEdit {
          guard let fileModel = fileModel else { return }
          viewModel.dismiss()
          self.coordinator?.navigate(to: .edit(fileModel: fileModel, editType: self.editType ?? .trim))
        } else {
          viewModel.dismiss()
          showSaveSuccessPopup()
        }
      })
      .disposed(by: disposedBag)
  }

  func showSaveSuccessPopup() {
    LKPopupView.popup.alert {[
      .title(L.dialog_save_title.localized),
      .subTitle(L.dialog_save_success.localized),
      .showCancel(false),
      .confirmAction([
        .text(L.ok.localized),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          self.dismiss(animated: true)
          NotificationCenter.default.post(name: .reloadLocalFileList, object: nil)
        })
      ]),
      .textAlignment(.left)
    ]}
  }
  
  func bindViewModel(to viewModel: PopupFirmwareDownloadViewModel?) {
    guard let viewModel = viewModel else { return }
    let input = PopupFirmwareDownloadViewModel.Input(viewDidLoad: Observable.just(()))
    let output = viewModel.bind(input: input, disposedBag: disposedBag)

    output.rxDownloadPercent
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] percent in
        self?.progressView.updateProgress(percent: percent)
      })
      .disposed(by: disposedBag)

    output.rxSaveEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] (isSave, _) in
        guard let self else { return }
        if isSave {
          self.dismiss(animated: true) { [weak self] in
            self?.showSaveSuccessPopup()
          }
        } else {
          self.dismiss(animated: true)
        }
      })
      .disposed(by: disposedBag)
  }

  func bindViewModel(to viewModel: PopupNotificationDownloadViewModel?) {
    guard let viewModel = viewModel else { return }
    let input = PopupNotificationDownloadViewModel.Input(viewDidLoad: Observable.just(()))
    let output = viewModel.bind(input: input, disposedBag: disposedBag)

    output.rxDownloadPercent
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] percent in
        self?.progressView.updateProgress(percent: percent)
      })
      .disposed(by: disposedBag)

    output.rxSaveEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] (isSave, _) in
        guard let self = self else { return }
        if isSave {
          if let deleteName = self.notificationDownlaodViewModel?.model.fileName {
            aLogger.debug("deleteName: \(deleteName)")
            UserDefaults.shared.notifications.removeAll { $0.fileName == deleteName }
          }
          MainAsync(after: 0.5) {
            self.dismiss(animated: true) { [weak self] in
              self?.showSaveSuccessPopup()
            }
          }
        } else {
          self.dismiss(animated: true)
        }
      })
      .disposed(by: disposedBag)
  }

  func setComponent() {
    self.view.backgroundColor = .black.withAlphaComponent(0.7)

    containerView.roundCorners(.allCorners, radius: 15)
    containerView.backgroundColor = .white
    containerView.layer.shadowColor = UIColor.black.cgColor
    containerView.layer.shadowOpacity = 0.2
    containerView.layer.shadowOffset = CGSize(width: 0, height: 2)

    self.view.addSubview(containerView)

    titleLabel.font = .h1
    titleLabel.textColor = .vueroidBlue
    titleLabel.numberOfLines = 0
    containerView.addSubview(titleLabel)

    descriptionLabel.font = .sub3
    descriptionLabel.textColor = .text
    descriptionLabel.numberOfLines = 0
    descriptionLabel.textAlignment = .center
    containerView.addSubview(descriptionLabel)

    downloadCountLabel.font = .body1
    downloadCountLabel.textColor = .text
    downloadCountLabel.numberOfLines = 0
    containerView.addSubview(downloadCountLabel)

    progressView.roundCorners(.allCorners, radius: 7.5)
    containerView.addSubview(progressView)

    buttonStack.distribution = .fillEqually
    buttonStack.spacing = 10
    buttonStack.axis = .horizontal
    containerView.addSubview(buttonStack)

    cancelBtn.setTitle(L.cancel.localized, for: .normal)
    cancelBtn.setTitleColor(.white, for: .normal)
    cancelBtn.setBackgroundColor(.vueroidBlue, for: .normal)
    cancelBtn.roundCorners(.allCorners, radius: 5)
    cancelBtn.addTarget(self, action: #selector(addCancelAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(cancelBtn)

  }

  func setAutoLayout() {
    containerView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      if traitCollection.verticalSizeClass == .compact {
        // Landscape mode
        make.leading.trailing.equalToSuperview().inset(250)
      } else {
        // Portrait mode
        make.leading.trailing.equalToSuperview().inset(20)
      }
    }
    titleLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalToSuperview().inset(40)
      make.height.greaterThanOrEqualTo(30)
    }
    descriptionLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(titleLabel.snp.bottom).offset(22)
      make.height.greaterThanOrEqualTo(18)
    }
    
    downloadCountLabel.pin.height(15).width(25).below(descriptionLabel, offset: 5).end(descriptionLabel).activate()
    
    progressView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(descriptionLabel.snp.bottom).offset(20)
      make.height.equalTo(15)
    }

    buttonStack.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(progressView.snp.bottom).offset(30)
      make.bottom.equalToSuperview().inset(20)
      make.height.equalTo(42.rv)
    }
  }
}
