//
//  DefaultPopupDownloadCoordinator.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/09/14.
//

import UIKit

final class DefaultPopupDownloadCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: PopupDownloadCoordinatorFinishDelegate?
  var childCoordinators = [PopupDownloadChildCoordinator: Coordinator]()
  var viewController: PopupWifiDownloadViewController?

  init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  func start(with step: PopupDownloadSteps) {
    navigate(to: step)
  }
}

extension DefaultPopupDownloadCoordinator: PopupDownloadCoordinator {
  // 남은 용량(MB) 반환
  private func getAvailableDiskSpaceInMB() -> Double {
      if let attributes = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory()),
         let freeSize = attributes[.systemFreeSize] as? NSNumber {
          return freeSize.doubleValue / (1024 * 1024)
      }
      return 0
  }

  func navigate(to step: PopupDownloadSteps) {
    switch step {
    case let .showPopupDownload(model: model, editType: editType, type: type, isToEdit: isToEdit):
      // phone 의 남은 용량을 확인
      let availableMB = getAvailableDiskSpaceInMB()
      fLogger.debug("availableMB: \(availableMB), requiredMB: \(Current.requiredMB)")
      if availableMB < Current.requiredMB {
        LKPopupView.popup.alert {[
          .title(L.warning.localized),
          .subTitle(L.error_diskfull_msg.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
            })
          ])
        ]}
      } else  {
        fLogger.info("download start")
        popupDownload(models: model, editType: editType, type: type, isToEdit: isToEdit)
      }

    case .firmwareDownload(model: let model):
      firmwareDownload(model: model)
    case let .edit(fileModel: fileModel, editType: editType):
      viewController?.dismiss(animated: true)
      pushEdit(fileModel: fileModel, editType: editType)
    case .popupDownloadDidFinish:
      DispatchQueue.main.async {
        fLogger.info("download finish")
        self.viewController?.dismiss(animated: true)
        self.delegate?.popupDownloadCoordinatorDidFinish()
      }
    }
  }

  func popupDownload(models: [FileListCellModel], editType: FileEditType, type: FileStorageType, isToEdit: Bool) {
    var vc: PopupWifiDownloadViewController?
    switch type {
    case .dashcam:

      let vm = PopupWifiDownloadViewModel(
        useCase: Composers.popupDownloadUseCase,
        fileModel: models,
        isToEdit: isToEdit,
        coordinator: self
      )

      guard let model = models.first,
         let downloadPath = UrlList.downloadedPath() else {
        return
      }

      let filename = FileManager.default.getUniqueName(fileName: model.fileName , path: downloadPath)
      fLogger.info("##@@@ count: \(models.count), model.fileName: \(model.fileName) filename: \(filename)")

      vc = PopupWifiDownloadViewController(
        coordinator: self,
        editType: editType,
        fileName: filename,
        totalCount: models.count
      )
      viewController = vc
      vc?.wifiViewModel = vm

    case .external, .autoUpload, .cloud: break

    default:
      break
    }
    guard let vc = vc else { return }
    present(viewController: vc)
  }

  func firmwareDownload(model: FirmwareModel) {

    // firmware download popup
    let vm = PopupFirmwareDownloadViewModel(useCase: Composers.popupDownloadUseCase, model: model)
    let vc = PopupWifiDownloadViewController(
      coordinator: self,
      editType: .trim,
      fileName: model.file,
      totalCount: -1
    )
    vc.firmwareDownloadViewModel = vm
    vc.wifiViewModel = nil
    present(viewController: vc)
  }

  func pushEdit(fileModel: FileListCellModel, editType: FileEditType) {
    let editCoordinator = DefaultFileEditCoordinator(navigationController: navigationController)
    childCoordinators[.edit] = editCoordinator
    editCoordinator.delegate = self
    editCoordinator.start(with: .showFileEdit(model: fileModel, editType: editType))
  }
  
  private func present(viewController: UIViewController) {
    if let rootViewController = UIApplication.shared.keyWindow?.rootViewController {
      rootViewController.present(viewController, animated: true)
    } else {
      navigationController.present(viewController, animated: true)
    }
  }
}

// MARK: Finish Delegate
extension DefaultPopupDownloadCoordinator: FileEditCoordinatorFinishDelegate {
  func fileEditCoordinatorDidFinish() {
    childCoordinators[.edit] = nil
  }
}
