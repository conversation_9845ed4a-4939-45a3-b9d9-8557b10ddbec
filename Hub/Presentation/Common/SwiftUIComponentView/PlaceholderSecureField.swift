//
//  PlaceholderSecureField.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/3/24.
//

import SwiftUI

struct PlaceholderSecureField: View {
  @Binding var text: String
  let placeholder: String
  let placeholderColor: Color
  let maxLength: Int
  let onCommit: (() -> Void)?

  init(
    text: Binding<String>, 
    placeholder: String,
    placeholderColor: Color,
    maxLength: Int,
    onCommit: (() -> Void)? = nil
  ) {
    self._text = text
    self.placeholder = placeholder
    self.placeholderColor = placeholderColor
    self.onCommit = onCommit
    self.maxLength = maxLength
  }

  var body: some View {
    SecureField(
      "", text: $text,
      onCommit: {
        onCommit?()
      }
    )
    .maxLength(text: $text, maxLength)
    .font(.MontserratRegular(ofSize: 12))
    .foregroundColor(Color(.white))
    .background(
      HStack {
        if text.isEmpty {
          Text(placeholder)
            .font(.MontserratRegular(ofSize: 12))
            .foregroundColor(placeholderColor)
          Spacer()
        }
      }
    )
  }
}

#Preview {
  PlaceholderSecureField(text: .constant("test"), placeholder: "test", placeholderColor: .red, maxLength: 10)
}
