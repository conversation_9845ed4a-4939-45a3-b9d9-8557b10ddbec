//
//  TextFieldCellWithTitle.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/12/23.
//

import SwiftUI

struct TextFieldCellWithTitleView: View {
  var title: String = "title"
  @State var placeholder: String = "placeholder"
  @State var textfieldType: TextfieldType = .Text
  @Binding var textValue: String
  let maxLength: Int

  var body: some View {
    VStack(spacing: 0) {
      Text("\(title)")
        .font(.MontserratMedium(ofSize: 16))
        .foregroundColor(.white)
        .frame(maxWidth: .infinity, alignment: .leading)
      TextFieldCellView(
        placeholder: placeholder, textfieldType: textfieldType, textValue: $textValue, maxLength: maxLength)
    }

  }
}

#Preview {
  TextFieldCellWithTitleView(textValue: .constant("test"), maxLength: 10)
}
