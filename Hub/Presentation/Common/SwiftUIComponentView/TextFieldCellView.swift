//
//  TextFieldCellView.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/12/23.
//

import SwiftUI

enum TextfieldType {
  case Text
  case Password
}
struct TextFieldCellView: View {
  @State var placeholder: String = "placeholder"
  @State var textfieldType: TextfieldType = .Text
  @State private var isShow = false
  @Binding var textValue: String
  var maxLength: Int
  
  var body: some View {
    VStack(alignment: .center, spacing: 0) {
      switch textfieldType {
      case .Password:
        ZStack(alignment: .trailing) {
          // 4. isSecure를 토글하는
          if isShow {
            PlaceholderTextField(
              text: _textValue,
              placeholder: placeholder,
              placeholderColor: .white.opacity(0.7),
              maxLength: maxLength
            )
            .opacity(isShow ? 1 : 0)
          } else {
            PlaceholderSecureField(
              text: _textValue,
              placeholder: placeholder,
              placeholderColor: .white.opacity(0.7),
              maxLength: maxLength
            )
            .opacity(isShow ? 0 : 1)
          }
          HStack {
            Image(systemName: isShow ? "eye.slash" : "eye")
              .resizable()
              .aspectRatio(contentMode: .fit)
              .foregroundColor(.gray)
              .frame(width: 32, height: 32)
              .onTapGesture(perform: {
                isShow = !isShow
              })
            if textValue != "" {
              Circle()
                .overlay(
                  Image("icon_exit")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                )
                .foregroundColor(.gray)
                .frame(width: 32, height: 32)
                .onTapGesture {
                  textValue = ""
                }
            }
          }
        }
        .frame(height: 54)
      case .Text:
        ZStack(alignment: .trailing) {
          PlaceholderTextField(
            text: _textValue,
            placeholder: placeholder,
            placeholderColor: .white.opacity(0.7),
            maxLength: maxLength
          )

          if textValue != "" {
            Circle()
              .overlay(
                Image("icon_exit")
                  .resizable()
                  .aspectRatio(contentMode: .fit)
              )
              .foregroundColor(.gray)
              .frame(width: 32, height: 32)
              .onTapGesture {
                textValue = ""
              }
          }
        }
        .frame(height: 54)
      }

      Rectangle()
        .fill(Color(UIColor.customSteel))
        .frame(height: 1)
        .clipShape(.rect(cornerRadius: 0.5))
    }
  }
}

#Preview {
  TextFieldCellView(
    textfieldType: .Password,
    textValue: .constant("aaaa"),
    maxLength: 10
  )
}
