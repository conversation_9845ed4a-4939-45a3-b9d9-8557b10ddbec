//
//  PlaceholderTextField.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/3/24.
//

import SwiftUI

struct PlaceholderTextField: View {
  @Binding var text: String
  let placeholder: String
  let placeholderColor: Color
  let maxLength: Int
  let onCommit: (() -> Void)?

  init(
    text: Binding<String>,
    placeholder: String,
    placeholderColor: Color,
    maxLength: Int,
    onCommit: (() -> Void)? = nil
  ) {
    self._text = text
    self.placeholder = placeholder
    self.placeholderColor = placeholderColor
    self.maxLength = maxLength
    self.onCommit = onCommit
  }

  var body: some View {
    TextField(
      "", text: $text,
      onCommit: {
        onCommit?()
      }
    )
    .maxLength(text: $text, maxLength)
    .font(.MontserratRegular(ofSize: 12))
    .foregroundColor(Color(.white))
    .background(
      HStack {
        if text.isEmpty {
          Text(placeholder)
            .font(.MontserratRegular(ofSize: 12))
            .foregroundColor(placeholderColor)
          Spacer()
        }
      }
    )
  }
}

#Preview {
  PlaceholderTextField(
    text: .constant("test"), placeholder: "test", placeholderColor: .red, maxLength: 10)
}
