//
//  SwitchCellWithTitleView.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/12/23.
//

import SwiftUI

struct SwitchCellWithTitleView: View {
  @Binding var title: String
  @Binding var switchIsOn: Bool

  var body: some View {
    VStack(alignment: .center, spacing: 20) {
      HStack(alignment: .center) {
        Text("\(title)")
          .font(.MontserratBold(ofSize: 18))
          .foregroundColor(.white)
        Spacer()
      }
      SwitchCellView(switchValue: $switchIsOn)
    }
    .background(Color(.clear))
  }
}

#Preview {
  SwitchCellWithTitleView(
    title: .constant("test Title"),
    switchIsOn: .constant(true))
}
