//
//  RoundButtonVIew.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/20/23.
//

import SwiftUI
import UIKit

struct RoundButtonView: View {
  @State var buttonTitle: String = "test"

  var body: some View {
    ZStack {
      Capsule()
        .fill(Color(.customDarkSkyBlue))
      Text(buttonTitle)
        .foregroundColor(Color(.white))
        .font(.MontserratBold(ofSize: 19))
    }
  }
}

struct RoundButtonView_Previews: PreviewProvider {
  static var previews: some View {
    RoundButtonView()
      .previewLayout(.fixed(width: 400, height: 60))
  }
}
