//
//  BottomSheet.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/13/24.
//

import SwiftUI

struct ListBottomSheet: View {

  @Binding var isShowing: Bool
  var title: String = ""
  var listItem: [String] = [""]
  var action: ((Int) -> Void) = { _ in }

  var body: some View {
    GeometryReader { geometry in
      ZStack(alignment: .bottom) {
        if isShowing {
          Color.black
            .opacity(0.3)
            .ignoresSafeArea()
            .onTapGesture {
              isShowing.toggle()
            }
          VStack(spacing: 0) {
            Text(title)
              .font(.MontserratBold(ofSize: 18))
              .foregroundColor(.white)
              .frame(maxWidth: .infinity, minHeight: 52, alignment: .leading)
              .padding(.horizontal, 20)
            Rectangle()
              .foregroundColor(.init(.customSteel))
              .frame(height: 1)
            List {
              ForEach(listItem.indices, id: \.self) { idx in
                Text(listItem[idx])
                  .font(.MontserratRegular(ofSize: 16))
                  .foregroundColor(.white)
                  .frame(height: 52, alignment: .leading)
                  .listRowInsets(EdgeInsets())
                  .listRowBackground(Color.init(UIColor(hexCode: "2f3034", alpha: 1)))
                  .onTapGesture {
                    isShowing.toggle()
                    action(idx)
                  }
              }
            }
            .listStyle(.plain)
            .padding(.horizontal, 20)
          }
          .cornerRadius(20, corners: [.topLeft, .topRight])
          .background(Color(UIColor(hexCode: "2f3034", alpha: 1)))
          .frame(
            height: (CGFloat(listItem.count + 1) * 52 + geometry.safeAreaInsets.bottom)
              > (geometry.size.height / 2)
              ? (geometry.size.height / 2)
              : (CGFloat(listItem.count + 1) * 52 + geometry.safeAreaInsets.bottom),
            alignment: .bottom
          )
          .transition(.move(edge: .bottom))
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
      .ignoresSafeArea()
      .animation(.easeInOut, value: isShowing)
    }
  }
}

struct ListBottomSheet_Previews: PreviewProvider {
  static var previews: some View {
    ListBottomSheet(
      isShowing: .constant(true),
      title: "auth",
      listItem: ["1", "1", "1", "1", "1"]
    )
  }
}
