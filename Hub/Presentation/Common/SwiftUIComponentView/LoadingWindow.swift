//
//  LoadingWindow.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 1/5/24.
//

import SwiftUI
import UIKit

public class LoadingWindow: UIWindow {
  public static let shared = LoadingWindow(frame: UIScreen.main.bounds)

  override init(frame: CGRect) {
    super.init(frame: frame)
    let loadingVC = UIHostingController(rootView: LoadingView())
    //    loadingVC.view.backgroundColor = .clear
    self.rootViewController = loadingVC
    self.isHidden = true
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func show() {
    self.isHidden = false
  }

  func hide() {
    self.isHidden = true
  }

  func toggle() {
    if self.isHidden {
      show()
    } else {
      hide()
    }
  }
}
