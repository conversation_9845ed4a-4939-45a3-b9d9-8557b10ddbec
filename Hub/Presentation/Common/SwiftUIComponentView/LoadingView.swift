//
//  LoadingView.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 1/5/24.
//

import SwiftUI

//public struct LoadingView: View {
//  public init() { }
//
//  public var body: some View {
//    VStack(spacing: 10) {
//      SwiftUI.ProgressView()
//      Text("Loading")
//    }
//    .frame(maxWidth: .infinity, maxHeight: .infinity)
//    .background(Color(UIColor.black).opacity(0.5))
//  }
//}
public struct LoadingView: View {
  public var body: some View {
    ZStack {
      Color(UIColor.black.withAlphaComponent(0.5))
        .ignoresSafeArea(edges: [.all])
      SwiftUI.ProgressView()
        .foregroundColor(.white)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
  }
}
#Preview {
  LoadingView()
}
