//
//  HotspotTestView.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/12/23.
//

import SwiftUI

struct SwitchCellView: View {
  @Binding var switchValue: Bool
  var body: some View {
    HStack(alignment: .center) {
      Spacer()
        .frame(width: 20)
      Toggle(L.wifi_auto_on_text.localized, isOn: $switchValue)
        .font(.MontserratBold(ofSize: 12))
        .foregroundColor(Color(UIColor.customSteel))
      Spacer()
        .frame(width: 20)
    }
    .frame(height: 50)
    .background(Color(UIColor.background))
    .clipShape(.rect(cornerRadius: 10))
  }
}

#Preview {
  SwitchCellView(switchValue: .constant(true))
}
