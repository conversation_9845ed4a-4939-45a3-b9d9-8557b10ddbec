//
//  LoginInputView.swift
//  Hub
//
//  Created by ncn on 2023/02/10.
//

import UIKit

class InputView: BaseView {
  let nameLabel = UILabel()
  let textField = UITextField()
  let lineView = UIView()

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()

  }

  override func setComponent() {
    self.backgroundColor = .clear

    nameLabel.font = .systemFont(ofSize: 16)
    nameLabel.textColor = .text
    self.addSubview(nameLabel)

    textField.font = .systemFont(ofSize: 16)
    textField.textColor = .text
    self.addSubview(textField)

    lineView.backgroundColor = .customGray
    self.addSubview(lineView)
  }

  override func setAutoLayout() {
    nameLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.left)
      make.top.equalTo(self.top)
    }

    textField.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.snp.left).offset(20)
      make.top.equalTo(self.snp.top).offset(50)
      make.right.equalTo(self.snp.right).offset(-20)
    }

    lineView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.equalTo(self)
      make.height.equalTo(1)
      make.bottom.equalTo(self)
    }
  }

}
