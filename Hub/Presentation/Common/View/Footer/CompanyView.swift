//
//  CompanyView.swift
//  Hub
//
//  Created by ncn on 2023/05/31.
//

import UIKit

class CompanyView: BaseView {

  let stackView = UIStackView()

  let termsButton = UIButton()
  let line01 = UIView()
  let privacyButton = UIButton()
  let line02 = UIView()
  let libraryButton = UIButton()

  let homepageButton = UIButton()

  let descriptionLabel = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    stackView.axis = .horizontal
    stackView.distribution = .equalSpacing
    stackView.alignment = .center
    stackView.spacing = 10
    self.addSubview(stackView)

    termsButton.setTitle(L.footer_term_of_use_text.localized, for: .normal)
    termsButton.titleLabel?.font = .MontserratSemiBold(ofSize: 10)
    termsButton.setTitleColor(.customGray, for: .normal)
    stackView.addArrangedSubview(termsButton)

    line01.backgroundColor = .customGray
    stackView.addArrangedSubview(line01)

    privacyButton.setTitle(L.footer_private_poltcy_text.localized, for: .normal)
    privacyButton.titleLabel?.font = .MontserratSemiBold(ofSize: 10)
    privacyButton.setTitleColor(.customGray, for: .normal)
    stackView.addArrangedSubview(privacyButton)

    line02.backgroundColor = .customGray
    stackView.addArrangedSubview(line02)

    libraryButton.setTitle(L.footer_open_source_lib_text.localized, for: .normal)
    libraryButton.titleLabel?.font = .MontserratSemiBold(ofSize: 10)
    libraryButton.setTitleColor(.customGray, for: .normal)
    stackView.addArrangedSubview(libraryButton)

    homepageButton.setTitle("NC&Co.Ltd", for: .normal)
    homepageButton.titleLabel?.font = .MontserratSemiBold(ofSize: 10)
    homepageButton.setTitleColor(.customGray, for: .normal)
    self.addSubview(homepageButton)

    let text =
      "ⓒNC& All Rights Reserved.\n"
    //      + "경기도 성남시 분당구 판교로 323 벤처포럼빌딩\n"
    //      + "TEL (+82) 02-3460-4700  |  FAX 02-3460-4799  |   관리자메일"
    descriptionLabel.text = text
    descriptionLabel.font = .MontserratSemiBold(ofSize: 11)
    descriptionLabel.textColor = .customGray
    descriptionLabel.numberOfLines = 0
    self.addSubview(descriptionLabel)
  }

  override func setAutoLayout() {
    line01.snp.makeConstraints { make in
      make.width.equalTo(1)
      make.height.equalTo(13)
    }

    line02.snp.makeConstraints { make in
      make.width.equalTo(1)
      make.height.equalTo(13)
    }

    stackView.snp.makeConstraints { make in
      make.leading.equalToSuperview().inset(20)
      make.top.equalToSuperview().inset(11)
      make.height.equalTo(23)
    }

    homepageButton.snp.makeConstraints { make in
      make.leading.equalToSuperview().inset(20)
      make.top.equalTo(stackView.snp.bottom).inset(5)
      make.height.equalTo(23)
    }

    descriptionLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(homepageButton.snp.bottom).inset(5)
      make.left.equalTo(self).offset(20)
      make.right.equalTo(self).offset(-20)
      make.bottom.equalTo(self).offset(-25)
    }

    termsButton.snp.makeConstraints { make in
      make.height.equalTo(23)
    }

    privacyButton.snp.makeConstraints { make in
      make.height.equalTo(23)
    }

    libraryButton.snp.makeConstraints { make in
      make.height.equalTo(23)
    }
  }
}
