//
//  RangeSlider.swift
//  momento
//
//  Created by cheche on 2018. 10. 6..
//  Copyright © 2018년 momento. All rights reserved.
//

import QuartzCore
import UIKit

class RangeSliderTrackLayer: CALayer {
  weak var rangeSlider: RangeSlider?

  override func draw(in ctx: CGContext) {
    guard let slider = rangeSlider else {
      return
    }

    // Clip
    let cornerRadius = bounds.height * slider.curvaceousness / 2.0
    let path = UIBezierPath(roundedRect: bounds, cornerRadius: cornerRadius)
    ctx.addPath(path.cgPath)

    // Fill the track
    ctx.setFillColor(slider.trackTintColor.cgColor)
    ctx.addPath(path.cgPath)
    ctx.fillPath()

    // Fill the highlighted range
    ctx.setFillColor(slider.trackHighlightTintColor.cgColor)
    let lowerValuePosition = CGFloat(slider.positionForValue(slider.lowerValue))
    let upperValuePosition = CGFloat(slider.positionForValue(slider.upperValue))
    let rect = CGRect(
      x: lowerValuePosition, y: 0.0, width: upperValuePosition - lowerValuePosition,
      height: bounds.height)
    ctx.fill(rect)
  }
}

class RangeSliderThumbLayer: CALayer {
  var highlighted: Bool = false {
    didSet {
      setNeedsDisplay()
    }
  }
  weak var rangeSlider: RangeSlider?

  var strokeColor: UIColor = UIColor.mainYellow {
    didSet {
      setNeedsDisplay()
    }
  }
  var lineWidth: CGFloat = 1.0 {
    didSet {
      setNeedsDisplay()
    }
  }

  override func draw(in ctx: CGContext) {
    guard let slider = rangeSlider else {
      return
    }

    let thumbFrame = bounds.insetBy(dx: 2.0, dy: 2.0)
    let cornerRadius = thumbFrame.height * slider.curvaceousness / 2.0
    let thumbPath = UIBezierPath(roundedRect: thumbFrame, cornerRadius: cornerRadius)

    // Fill
    ctx.setFillColor(slider.thumbTintColor.cgColor)
    ctx.addPath(thumbPath.cgPath)
    ctx.fillPath()

    // Outline
    ctx.setStrokeColor(strokeColor.cgColor)
    ctx.setLineWidth(lineWidth)
    ctx.addPath(thumbPath.cgPath)
    ctx.strokePath()

    if highlighted {
      ctx.setFillColor(UIColor(white: 0.0, alpha: 0.1).cgColor)
      ctx.addPath(thumbPath.cgPath)
      ctx.fillPath()
    }
  }

}

@IBDesignable
class RangeSlider: UIControl {
  @IBInspectable var lowerLayerSelected = Bool()

  @IBInspectable var minimumValue: Double = 0.0 {
    willSet(newValue) {
      assert(newValue < maximumValue, "RangeSlider: minimumValue should be lower than maximumValue")
    }
    didSet {
      updateLayerFrames()
    }
  }

  @IBInspectable var maximumValue: Double = 100 {
    willSet(newValue) {
      assert(
        newValue > minimumValue, "RangeSlider: maximumValue should be greater than minimumValue")
    }
    didSet {
      updateLayerFrames()
    }
  }

  @IBInspectable var lowerValue: Double = 0.0 {
    didSet {
      updateLayerFrames()
    }
  }

  @IBInspectable var upperValue: Double = 100 {
    didSet {
      updateLayerFrames()
    }
  }

  var gapBetweenThumbs: Double = 0.0 {
    didSet {
      updateLayerFrames()
    }
  }

  @IBInspectable var trackTintColor: UIColor = UIColor.clear {
    didSet {
      trackLayer.setNeedsDisplay()
    }
  }

  @IBInspectable var trackHighlightTintColor: UIColor = UIColor.clear {
    didSet {
      trackLayer.setNeedsDisplay()
    }
  }

  @IBInspectable var thumbTintColor: UIColor = UIColor.defaultColor {
    didSet {
      lowerThumbLayer.setNeedsDisplay()
      upperThumbLayer.setNeedsDisplay()
    }
  }

  @IBInspectable var thumbBorderColor: UIColor = UIColor.gray {
    didSet {
      lowerThumbLayer.strokeColor = thumbBorderColor
      upperThumbLayer.strokeColor = thumbBorderColor
    }
  }

  @IBInspectable var thumbBorderWidth: CGFloat = 0.5 {
    didSet {
      lowerThumbLayer.lineWidth = thumbBorderWidth
      upperThumbLayer.lineWidth = thumbBorderWidth
    }
  }

  @IBInspectable var curvaceousness: CGFloat = 1.0 {
    didSet {
      if curvaceousness < 0.0 {
        curvaceousness = 0.0
      }

      if curvaceousness > 1.0 {
        curvaceousness = 1.0
      }

      trackLayer.setNeedsDisplay()
      lowerThumbLayer.setNeedsDisplay()
      upperThumbLayer.setNeedsDisplay()
    }
  }

  fileprivate var previouslocation = CGPoint()

  fileprivate let trackLayer = RangeSliderTrackLayer()
  fileprivate let lowerThumbLayer = RangeSliderThumbLayer()
  fileprivate let upperThumbLayer = RangeSliderThumbLayer()

  fileprivate var thumbWidth: CGFloat = 18
  fileprivate var thumbHeight: CGFloat = 52

  override var frame: CGRect {
    didSet {
      updateLayerFrames()
    }
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    initializeLayers()
  }

  required init?(coder: NSCoder) {
    super.init(coder: coder)
    initializeLayers()
  }

  override func layoutSublayers(of: CALayer) {
    super.layoutSublayers(of: layer)
    updateLayerFrames()
  }

  fileprivate func initializeLayers() {
    layer.backgroundColor = UIColor.clear.cgColor

    trackLayer.rangeSlider = self
    trackLayer.contentsScale = UIScreen.main.scale
    layer.addSublayer(trackLayer)

    lowerThumbLayer.rangeSlider = self
    lowerThumbLayer.strokeColor = .mainYellow
    lowerThumbLayer.contentsScale = UIScreen.main.scale
    layer.addSublayer(lowerThumbLayer)

    upperThumbLayer.rangeSlider = self
    upperThumbLayer.strokeColor = .mainYellow
    upperThumbLayer.contentsScale = UIScreen.main.scale
    layer.addSublayer(upperThumbLayer)
  }

  func updateLayerFrames() {
    CATransaction.begin()
    CATransaction.setDisableActions(true)

    trackLayer.frame = bounds.insetBy(dx: 0.0, dy: bounds.height / 3)
    trackLayer.setNeedsDisplay()

    let lowerThumbCenter = CGFloat(positionForValue(lowerValue))
    lowerThumbLayer.frame = CGRect(
      x: lowerThumbCenter - thumbWidth / 2.0, y: -6.0,
      width: thumbWidth, height: thumbHeight)
    lowerThumbLayer.setNeedsDisplay()

    let upperThumbCenter = CGFloat(positionForValue(upperValue))
    upperThumbLayer.frame = CGRect(
      x: upperThumbCenter - thumbWidth / 2.0, y: -6.0,
      width: thumbWidth, height: thumbHeight)
    upperThumbLayer.setNeedsDisplay()

    CATransaction.commit()
  }

  func positionForValue(_ value: Double) -> Double {
    return Double(bounds.width - thumbWidth) * (value - minimumValue)
      / (maximumValue - minimumValue) + Double(thumbWidth / 2.0)
  }

  func boundValue(_ value: Double, toLowerValue lowerValue: Double, upperValue: Double) -> Double {
    return min(max(value, lowerValue), upperValue)
  }

  // MARK: - Touches

  override func beginTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
    previouslocation = touch.location(in: self)

    if lowerThumbLayer.frame.contains(previouslocation) {
      lowerThumbLayer.highlighted = true
      lowerLayerSelected = lowerThumbLayer.highlighted

    } else if upperThumbLayer.frame.contains(previouslocation) {
      upperThumbLayer.highlighted = true
      lowerLayerSelected = lowerThumbLayer.highlighted

    }
    return lowerThumbLayer.highlighted || upperThumbLayer.highlighted
  }

  override func continueTracking(_ touch: UITouch, with event: UIEvent?) -> Bool {
    let location = touch.location(in: self)
    let deltaLocation = Double(location.x - previouslocation.x)
    let deltaValue =
      (maximumValue - minimumValue) * deltaLocation / Double(bounds.width - bounds.height)

    previouslocation = location
    if lowerThumbLayer.highlighted {
      lowerValue = boundValue(
        lowerValue + deltaValue,
        toLowerValue: minimumValue,
        upperValue: upperValue - 7.0)
    } else if upperThumbLayer.highlighted {
      upperValue = boundValue(
        upperValue + deltaValue,
        toLowerValue: lowerValue + 7.0,
        upperValue: maximumValue)
    }

    sendActions(for: .valueChanged)

    return true
  }

  override func endTracking(_ touch: UITouch?, with event: UIEvent?) {
    lowerThumbLayer.highlighted = false
    upperThumbLayer.highlighted = false
  }
}
