//
//  SheetViewModel.swift
//  Hub
//
//  Created by ncn on 2023/04/24.
//

import RxCocoa
import RxRelay
import RxSwift
import UIKit

struct SheetMenuModel {
  let title: String
  let subTitle: String?
  let items: [String]
  var subItems: [String]? = nil
  var switchStates: [Bool]?

  init(title: String, items: [String], subItems: [String]? = nil, switchStates: [Bool]? = nil) {
    self.title = title
    self.subTitle = nil
    self.items = items
    self.subItems = subItems
    self.switchStates = switchStates
  }
}

class SheetMenuViewModel: BaseViewModel {
  var listItem = BehaviorRelay<[String]>(value: [])
  var subListItem = BehaviorRelay<[String]>(value: [])
  var switchStates = BehaviorRelay<[Bool]>(value: [])

  struct Input {
  }

  struct Output {
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    return output
  }
}

extension SheetMenuViewModel {
  func setListItem(items: [String], subItems: [String]? = nil, switchStates: [Bool]? = nil) {
    self.listItem.accept(items)
    self.subListItem.accept(subItems ?? [])
    if let states = switchStates {
      self.switchStates.accept(states)
    }
  }
}
