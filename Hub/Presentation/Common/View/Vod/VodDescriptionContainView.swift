//
//  VodDescriptionContainView.swift
//  Hub
//
//  Created by ncn on 2023/06/01.
//

import UIKit
import SnapKit

class VodDescriptionContainView: UIView {
  let descriptionView = VodDescriptionView()
  let editMenuView = ButtonMenuView(items: [""])

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent() {
    self.addSubview(descriptionView)

    let download = MenuIconModel(
      fileActionType: nil,
      fileStorageType: .download,
      normal: UIImage(named: "icon_dw_b")!,
      highlight: UIImage(named: "icon_dw_w")!,
      title: L.download.localized
    )

    let upload = MenuIconModel(
      fileActionType: nil,
      fileStorageType: .external,
      normal: UIImage(named: "icon_up_b")!,
      highlight: UIImage(named: "icon_up_w")!,
      title: "Upload"
    )

    let delete = MenuIconModel(
      fileActionType: nil,
      fileStorageType: .dashcam,
      normal: UIImage(named: "icon_del_b")!,
      highlight: UIImage(named: "icon_del_w")!,
      title: L.dialog_delete_title.localized
    )

    editMenuView.iconTitles = [download, upload, delete]
    self.addSubview(editMenuView)
  }

  func setAutoLayout() {
    descriptionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.top.right.equalTo(self)
      make.height.equalTo(70)
    }

    editMenuView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.equalTo(self)
      make.height.equalTo(40)
      make.bottom.equalTo(self).offset(-10)
    }
  }

}
