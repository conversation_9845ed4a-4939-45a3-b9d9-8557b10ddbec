//
//  VodDescriptionView.swift
//  Hub
//
//  Created by ncn on 2023/06/01.
//

import UIKit
import SnapKit

class VodDescriptionView: UIView {
  let nameLabel = UILabel()
  let dateLabel = UILabel()
  let timeIcon = UIImageView(image: UIImage(named: "icon_time"))
  let speakerIcon = UIImageView(image: UIImage(named: "icon_speaker"))
  let durationLabel = UILabel()
  let badgeStack = UIStackView()
  //  let channalLabel = PaddingLabel(padding: .init(top: 0, left: 4, bottom: 0, right: 4))
  let audioLabel = PaddingLabel(padding: .init(top: 0, left: 4, bottom: 0, right: 4))
  let recordLabel = PaddingLabel(padding: .init(top: 0, left: 4, bottom: 0, right: 4))

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }
  
  func setComponent() {
    nameLabel.textColor = .text
    nameLabel.font = .MontserratSemiBold(ofSize: 13)
    self.addSubview(nameLabel)

    dateLabel.textColor = .customGray
    dateLabel.font = .MontserratRegular(ofSize: 10)
    self.addSubview(dateLabel)

    timeIcon.tintColor = .customSteel
    self.addSubview(timeIcon)

    durationLabel.font = .MontserratRegular(ofSize: 10)
    durationLabel.textColor = .customGray
    self.addSubview(durationLabel)

    badgeStack.axis = .horizontal
    badgeStack.distribution = .equalSpacing
    badgeStack.alignment = .fill
    badgeStack.spacing = 4
    self.addSubview(badgeStack)

    audioLabel.font = .MontserratMedium(ofSize: 10)
    audioLabel.textColor = .text
    audioLabel.backgroundColor = .defaultColor
    audioLabel.layer.cornerRadius = 6
    audioLabel.layer.masksToBounds = true
    audioLabel.textAlignment = .right
    audioLabel.isHidden = true
    badgeStack.addArrangedSubview(audioLabel)
    audioLabel.addSubview(speakerIcon)

    recordLabel.textColor = .text
    recordLabel.backgroundColor = .customOrange
    recordLabel.font = .MontserratMedium(ofSize: 10)
    recordLabel.layer.cornerRadius = 6
    recordLabel.layer.masksToBounds = true
    badgeStack.addArrangedSubview(recordLabel)

    nameLabel.text = "20220617_103709_INF_1.mp4"
    dateLabel.text = "2022/06/07 10:37:09"
    durationLabel.text = "13:00"
    audioLabel.text = L.setting_detail_on_value.localized
    recordLabel.text = "상시녹화"
  }

  func setAutoLayout() {
    nameLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(20)
      make.top.equalTo(self).offset(10)
    }

    dateLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(20)
      make.top.equalTo(self.nameLabel.snp.bottom).offset(5)
    }

    timeIcon.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.dateLabel.snp.right).offset(10)
      make.centerY.equalTo(self.dateLabel.snp.centerY)
      make.width.height.equalTo(10)
    }

    durationLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.timeIcon.snp.right).offset(3)
      make.centerY.equalTo(timeIcon.snp.centerY)
    }

    badgeStack.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalToSuperview().inset(20)
      make.top.equalTo(self.dateLabel.snp.bottom).offset(6)
      make.height.equalTo(13)
    }

    speakerIcon.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.audioLabel.snp.left)
      make.centerY.equalTo(self.audioLabel.snp.centerY)
    }
  }
}

extension VodDescriptionView {
  func setData(model: FileListCellModel) {
    if model.type == .screenshot {
      badgeStack.isHidden = true
      durationLabel.isHidden = true
      timeIcon.isHidden = true

      badgeStack.snp.updateConstraints { make in
        make.top.equalTo(self.dateLabel.snp.bottom)
        make.height.equalTo(0)
      }
    }

    nameLabel.text = model.fileName
    let recDateString = model.fileName.components(separatedBy: "_")[safe: 0..<2].joined()
      .replacingOccurrences(of: ".", with: "")
    dateLabel.text = recDateString.convertDayString(
      from: "yyyyMMddHHmmss", to: "yyyy/MM/dd HH:mm:ss ")
    recordLabel.text = VodRecordType(rawValue: model.vodType)?.string

    badgeStack.removeAllSubviews()
    let channelCount = model.channelbits.channelBitMaskingCount
    (0..<channelCount).forEach { [weak self] idx in
      let channelLabel = PaddingLabel(padding: .init(top: 0, left: 4, bottom: 0, right: 4))
      channelLabel.font = .MontserratMedium(ofSize: 8)
      channelLabel.backgroundColor = .customGray
      channelLabel.layer.cornerRadius = 4
      channelLabel.layer.masksToBounds = true
      channelLabel.text = "\(idx + 1)ch"
      self?.badgeStack.addArrangedSubview(channelLabel)
    }
    badgeStack.addArrangedSubview(audioLabel)
    badgeStack.addArrangedSubview(recordLabel)
    let time = Int(model.videoTime ?? "0") ?? 0
    durationLabel.text = time.getTimeString()
  }

  func setDuration(time: Int) {
    durationLabel.text = time.getTimeString()
  }
}
