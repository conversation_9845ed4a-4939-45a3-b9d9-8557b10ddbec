//
//  MapTitleView.swift
//  Hub
//
//  Created by ncn on 2023/02/13.
//

import UIKit

enum ContentTitleViewType {
  case driveInfo
  case gSensor
}

class ContentTitleView: BaseView {
  lazy var lineView: UIView = {
    var view = UIView()
    view.backgroundColor = .linePressed
    return view
  }()

  lazy var gSensorTitleButton: UIButton = {
    var button = UIButton()
    button.setTitle("G-Sensor", for: .normal)
    button.setTitleColor(.iconDark, for: .normal)
    button.titleLabel?.font = .sub1
    button.titleLabel?.adjustsFontSizeToFitWidth = true
    button.sizeToFit()
    return button
  }()

  lazy var drivingTitleButton: UIButton = {
    var button = UIButton()
    button.setTitle(L.s1_drive_info_title.localized, for: .normal)
    button.setTitleColor(.mainBlue, for: .normal)
    button.titleLabel?.font = .sub1
    button.titleLabel?.adjustsFontSizeToFitWidth = true
    button.sizeToFit()
    return button
  }()

  var type: ContentTitleViewType = .gSensor {
    didSet {
      switch type {
      case .driveInfo:
        drivingTitleButton.setTitleColor(.mainBlue, for: .normal)
        gSensorTitleButton.setTitleColor(.iconDark, for: .normal)
      case .gSensor:
        drivingTitleButton.setTitleColor(.iconDark, for: .normal)
        gSensorTitleButton.setTitleColor(.mainBlue, for: .normal)
      }
    }
  }
  
  public init(type: ContentTitleViewType = .gSensor) {
    self.type = type
    super.init(frame: .zero)
    self.backgroundColor = .white
    
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    addSubviews([drivingTitleButton, lineView, gSensorTitleButton])
  }

  override func setAutoLayout() {
    Pin.activate([
      drivingTitleButton.pin.centerY().start(offset: 8),
      lineView.pin.after(drivingTitleButton, offset: 8).centerY().width(1).height(12),
      gSensorTitleButton.pin.centerY().after(lineView, offset: 8)
    ])
  }
}
