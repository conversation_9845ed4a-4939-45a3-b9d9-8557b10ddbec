//
//  CollectionCell.swift
//  Prepare
//
//  Created by ncn on 2023/02/01.
//

import UIKit
import Kingfisher

class FileListCell: UICollectionViewCell, Reusable {
  let imageView = UIImageView()
  let mediaTypeView = MediaTagView(type: .image)

  let selectIconButton = UIButton()

  var markStackView: UIStackView = {
    let view = UIStackView()
    view.axis = .horizontal
    view.spacing = 2
    return view
  }()

  let timeLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 10, weight: .medium)
    label.textColor = .mainBlack
    label.text = "AM 00:60"
    label.textAlignment = .right
    label.sizeToFit()
    return label
  }()

  lazy var lockImageView: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "icon_lock.pdf")
    return view
  }()
  
  lazy var lockBackgroundView: UIView = {
    let view = UIView()
    view.backgroundColor = .black.withAlphaComponent(0.5)
    return view
  }()
  
  lazy var indicatorView: UIActivityIndicatorView = {
    let indicator = UIActivityIndicatorView(style: .medium)
    indicator.tag = 777
    indicator.color = .darkGray
    self.addSubview(indicator)
    indicator.snp.makeConstraints { [weak self] v in
      guard let self = self else { return }
      v.center.equalTo(self)
    }
    return indicator
  }()

  override init(frame: CGRect) {
    super.init(frame: frame)

    setComponent()
    setAutoLayout()
    setupPlaceholder()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func layoutSubviews() {
    super.layoutSubviews()
    imageView.layer.cornerRadius = 2.0
  }

  override func prepareForReuse() {
    super.prepareForReuse()
    markStackView.removeAllArrangedSubviews()
    imageView.removeAllSubviews()
  }
  
  private func setComponent() {
    self.backgroundColor = .clear
    contentView.backgroundColor = .clear
    imageView.backgroundColor = .disable
    self.contentView.addSubviews([imageView, mediaTypeView, markStackView, timeLabel])

    selectIconButton.setImage(#imageLiteral(resourceName: "icon_checkbox_default.pdf"), for: .normal)
    selectIconButton.setImage(#imageLiteral(resourceName: "icon_checkbox_active.pdf"), for: .selected)

    selectIconButton.isHidden = true
    self.contentView.addSubview(selectIconButton)
  }
  
  private func setAutoLayout() {
    Pin.activate([
      markStackView.pin.start().bottom().height(15),
      timeLabel.pin.end(offset: -1).centerY(markStackView).width(48),
      selectIconButton.pin.top().start().size(18),
      imageView.pin.top().width(self).above(markStackView, offset: -4),
      mediaTypeView.pin.bottom(imageView).start().height(15),
    ])
  }

  private func setupPlaceholder() {
    // 검은색 플레이스홀더 이미지 설정
    imageView.backgroundColor = .black
    imageView.contentMode = .scaleAspectFill
    imageView.clipsToBounds = true
  }

  func showIndicator(
    timeout: Int = _indicator_timeout,
    completion: @escaping (_ isClose: Bool) -> Void = { _ in }
  ) {
    DispatchQueue.main.async {
      self.indicatorView.isHidden = false
      self.bringSubviewToFront(self.indicatorView)
      self.indicatorView.startAnimating()
    }
  }

  func closeIndicator() {
    DispatchQueue.main.async {
      self.indicatorView.isHidden = true
      self.indicatorView.stopAnimating()
    }
  }
}

extension FileListCell {
  func setData(model: FileListCellModel) {
    imageView.image = nil
    // 이미지가 로드되기 전에 검은색 배경 유지
    imageView.backgroundColor = .black
    
    if model.type == .dummy {
      self.backgroundColor = .clear
      imageView.isHidden = true
    } else {
      self.backgroundColor = .clear
      imageView.isHidden = false

      fLogger.debug("channelbits: \(model.channelbits), videoTime: \(model.videoTime ?? "00"), vodType: \(model.vodType)")

      if model.fileName.contains("_L.") && AppManager.shared.mode == .wifi {
        imageView.addSubviews([lockBackgroundView, lockImageView])
        lockBackgroundView.pin.all().activate()
        lockImageView.pin.size(24).center().activate()
      }
      
      let frontMarkView = ChannelMarkView(style: .front)
      let rearMarkView = ChannelMarkView(style: .rear)
      let internalMarkView = ChannelMarkView(style: .internel)

      // config channel mark View
      if AppManager.shared.mode == .file {
        vLogger.debug("fileName: \(model.fileName)")
        let trimMarkView = ChannelMarkView(style: .trim)
        let afterMarkView = ChannelMarkView(style: .after)
        let beforeMarkView = ChannelMarkView(style: .before)
        let privateMarkView = ChannelMarkView(style: .private)
        let licenseMarkView = ChannelMarkView(style: .license)
        let originalMarkView = ChannelMarkView(style: .original)

        if model.fileName.contains("_F") {
          markStackView.addArrangedSubview(frontMarkView)
          if model.fileName.contains("_FT") {
            markStackView.addArrangedSubview(trimMarkView)
          } else if model.fileName.contains("_FP") {
            markStackView.addArrangedSubview(privateMarkView)
          } else if model.fileName.contains("_FL") {
            markStackView.addArrangedSubview(licenseMarkView)
          }
        } else if model.fileName.contains("_R") {
          markStackView.addArrangedSubview(rearMarkView)
          if model.fileName.contains("_RT") {
            markStackView.addArrangedSubview(trimMarkView)
          } else if model.fileName.contains("_RP") {
            markStackView.addArrangedSubview(privateMarkView)
          } else if model.fileName.contains("_RL") {
            markStackView.addArrangedSubview(licenseMarkView)
          }
        } else if model.fileName.contains("_I") {
          markStackView.addArrangedSubview(internalMarkView)
          if model.fileName.contains("_IT") {
            markStackView.addArrangedSubview(trimMarkView)
          } else if model.fileName.contains("_IP") {
            markStackView.addArrangedSubview(privateMarkView)
          } else if model.fileName.contains("_IL") {
            markStackView.addArrangedSubview(licenseMarkView)
          }
        }
        
        if model.fileName.contains("B") {
          markStackView.addArrangedSubview(beforeMarkView)
        } else if model.fileName.contains("A") {
          markStackView.addArrangedSubview(afterMarkView)
        } else if model.fileName.contains("O") {
          markStackView.addArrangedSubview(originalMarkView)
        }
        
      } else {
        
        let channel = model.channelbits.attachChannel
        
        switch channel {
        case .F:
          markStackView.addArrangedSubview(frontMarkView)
        case .FI:
          markStackView.addArrangedSubview(frontMarkView)
          markStackView.addArrangedSubview(internalMarkView)
        case .FR:
          markStackView.addArrangedSubview(frontMarkView)
          markStackView.addArrangedSubview(rearMarkView)
        case .FRI:
          markStackView.addArrangedSubview(frontMarkView)
          markStackView.addArrangedSubview(rearMarkView)
          markStackView.addArrangedSubview(internalMarkView)
        }
      }


      fLogger.debug("fileName: \(model.fileName)")
      // 20241226_082754_EVT_F.mp4 --> AM 08:27
      if let time = model.fileName.split(separator: "_")[safe:1] {
        let hour = Int(time.prefix(2)) ?? 0
        let minute = String(time.dropFirst(2).prefix(2))
        timeLabel.text = "\(hour):\(minute)"
      } else {
        timeLabel.text = "00:00"
      }
//      cLogger.info("timeLabel.text: \(self.timeLabel.text ?? "")")
      // AM/PM 포맷으로 변경
      let inputFormatter = DateFormatter()
      inputFormatter.dateFormat = "HH:mm"

      let outputFormatter = DateFormatter()
      outputFormatter.locale = Locale(identifier: "en_US_POSIX")
      outputFormatter.dateFormat = "a hh:mm"

      if let timeText = timeLabel.text,
         let date = inputFormatter.date(from: timeText) {
          timeLabel.text = outputFormatter.string(from: date)
//        cLogger.info("timeLabel AM, PM: \(self.timeLabel.text ?? "")")
      }
      
      
      timeLabel.sizeToFit()

      if let obj = model.thumbimage {
        imageView.image = obj
        // 이미지가 설정되면 배경색 제거
        imageView.backgroundColor = .clear
      } else if let string = model.imageUrl, let url = URL(string: string) {
        showIndicator()
        imageView.imageDownload(url: url) { [weak self] image, error in
          guard let self = self else { return }
          self.closeIndicator()
          model.thumbimage = image
          // 이미지가 다운로드되면 배경색 제거
          if image != nil {
            self.imageView.backgroundColor = .clear
          }
        }
      } else if let thumbLocalUrl = model.filePath {
//        mLogger.debug("thumbLocalUrl: \(thumbLocalUrl)")
        imageView.backgroundColor = .grayD9
        let provider = AVAssetImageDataProvider(assetURL: thumbLocalUrl, seconds: 1.0)
        KF.dataProvider(provider)
          .fade(duration: 0.25)
          .set(to: imageView)
      }

      fLogger.debug("model.type: \(model.type.rawValue), model.fileName: \(model.fileName)")

      if model.type == .file {
        mediaTypeView.isHidden = false
        markStackView.isHidden = false
        timeLabel.isHidden = false
        mediaTypeView.type = .video
      } else if model.type == .screenshot {
        mediaTypeView.isHidden = false
        
        if model.fileName.contains("LA") || model.fileName.contains("LB") || model.fileName.contains("LO") {
          markStackView.isHidden = false
        } else {
          markStackView.isHidden = true
        }
        
        timeLabel.isHidden = true
        mediaTypeView.type = .image
      } else {
        mediaTypeView.isHidden = true
      }
      
      // bookmark tab
      let fileExtension = model.fileName.fileExtension.lowercased()
      if model.type == .wifi && (fileExtension == "jpg" || fileExtension == "png") {
        mediaTypeView.isHidden = false
        markStackView.isHidden = true
        timeLabel.isHidden = false
        mediaTypeView.type = .image
      }
      
      fLogger.debug("markStackView.isHidden: \(self.markStackView.isHidden), count: \(self.markStackView.arrangedSubviews.count)")
    }
  }
}
