//
//  FileDescriptionCell.swift
//  Hub
//
//  Created by ncn on 2023/08/04.
//

import UIKit

class FileDescriptionCell: UICollectionViewCell, Reusable {
  let descriptionView = VodDescriptionView()
  let iconImage = UIImageView()

  override init(frame: CGRect) {
    super.init(frame: frame)

    setComponet()
    setAutolayOut()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setComponet() {
    self.backgroundColor = .clear
    self.addSubview(descriptionView)

    iconImage.image = UIImage(named: "icon_fileview_play")
    self.addSubview(iconImage)
  }

  private func setAutolayOut() {
    descriptionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self)  //.offset(10)
      make.right.equalTo(self).offset(-60)
      make.height.equalTo(70)
    }

    iconImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self).offset(10)
      make.right.equalTo(self).offset(-10)
      make.width.height.equalTo(32)
    }
  }
}

extension FileDescriptionCell {
  func setData(model: FileListCellModel) {
    descriptionView.setData(model: model)

    if AppManager.shared.mode == .wifi {
      descriptionView.timeIcon.isHidden = true
      descriptionView.durationLabel.isHidden = true
    }
  }
}
enum VodRecordType: Int {
  case INF = 0
  case EVT
  case PRK
  case USR

  var string: String {
    switch self {
    case .INF:
      return "INF"
    case .EVT:
      return "EVT"
    case .PRK:
      return "PRK"
    case .USR:
      return "USR"
    }
  }
}
