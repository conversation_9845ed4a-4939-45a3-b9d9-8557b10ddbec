//
//  ZoomCollectionViewModel.swift
//  Hub
//
//  Created by ncn on 2023/06/08.
//

import UIKit

let _zoom_max_count = 4
let _zoom_min_count = 2

class ZoomCollectionViewModel {
  var list: [FileListSectionModel] = []
  //= IndexPath(row: -1, section: 0)
  var remain = 0
  var totalCount = 0
  var rowCount = 3

  var tooltipIndex: IndexPath?
  var selectedIndex: IndexPath?
  var insertedIndexArr = [IndexPath]()

  func descrease() {
    rowCount -= 1
    if rowCount < _zoom_min_count {
      rowCount = 2
    }
  }

  func increas() {
    rowCount += 1
    if rowCount > _zoom_max_count {
      rowCount = 4
    }
  }
}

extension ZoomCollectionViewModel {
  func getRowNumber(collectionView: UICollectionView) {
    if let tIndex = tooltipIndex,
      let sIndex = selectedIndex
    {
      if (sIndex.row >= tIndex.row) && (sIndex.section == tIndex.section) {
        self.selectedIndex = IndexPath(item: (sIndex.row - 1), section: sIndex.section)
      }
    }

    removeToolTipObj(collectionView: collectionView)
    guard let index = selectedIndex else { return }

    var models = list[index.section]
    var currentRow = 0
    let val = index.row + 1
    let item = val % rowCount
    if item != 0 {
      currentRow = currentRow + 1
    }

    let balance = models.items.count % rowCount
    if balance != 0 {
      let needToAdd = rowCount - balance
      if needToAdd != 0 {
        for _ in 0..<(needToAdd) {
          let dummyObj = FileListCellModel(type: .dummy, fileName: "")
          models.items.append(dummyObj)
        }
      }
    }

    let dVal = val / rowCount
    currentRow = currentRow + Int(dVal)

    let toolTipObj = FileListCellModel(type: .description, fileName: "", indexPath: selectedIndex)
    var newRow = currentRow * rowCount
    if newRow < models.items.count {
      models.items.insert(toolTipObj, at: newRow)
    } else {
      models.items.append(toolTipObj)
      newRow = models.items.count - 1
    }
    list[index.section] = models

    self.tooltipIndex = IndexPath(item: newRow, section: index.section)
  }

  func removeToolTipObj(collectionView: UICollectionView) {
    if let tIndex = tooltipIndex {
      var oldData = list[tIndex.section]
      oldData.items.remove(at: tIndex.row)
      oldData.items.removeAll(where: { $0.type == .dummy })
      list[tIndex.section] = oldData
      self.tooltipIndex = nil
      collectionView.performBatchUpdates(
        {
          if insertedIndexArr.isEmpty != true {
            collectionView.deleteItems(at: insertedIndexArr)
            insertedIndexArr.removeAll()
          }
        },
        completion: { _ in
          collectionView.reloadData()
        })
    }
  }

  func getItemListAtIndex(_ indexPath: IndexPath) -> [FileListCellModel] {
    let model = list[indexPath.section]
    return model.items
  }

  func collectionDidSelect(indexPath: IndexPath, collectionView: UICollectionView) {
    collectionView.performBatchUpdates {
      getRowNumber(collectionView: collectionView)
      if let index = tooltipIndex {
        let oldData = list[index.section]
        let dummies = oldData.items.filter({ $0.type == .dummy })
        if dummies.isEmpty != true {
          let actualCellCount = (oldData.items.count - 1) - dummies.count
          for val in actualCellCount..<(oldData.items.count - 1) {
            if val > index.item {
              insertedIndexArr.append(IndexPath(item: val + 1, section: index.section))
            } else {
              insertedIndexArr.append(IndexPath(item: val, section: index.section))
            }
          }
        }
        insertedIndexArr.append(index)
        collectionView.insertItems(at: insertedIndexArr)
      }
    } completion: { finish in
      if finish, let tIndex = self.tooltipIndex {
        let attributes = collectionView.layoutAttributesForItem(at: tIndex)
        if let frameValue = attributes?.frame,
          !collectionView.bounds.contains(frameValue)
        {
          collectionView.scrollToItem(at: tIndex, at: .bottom, animated: true)
          Log.debug(to: "scroll to item >>>>>> \(tIndex)")
        }
      }
    }
  }

  func getItemAtIndex(_ indexPath: IndexPath) -> FileListCellModel? {
    if list.count < indexPath.section {
      return nil
    }
    let assetModel = list[indexPath.section].items[indexPath.row]
    return assetModel
  }
}
