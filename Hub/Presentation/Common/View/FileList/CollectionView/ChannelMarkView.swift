//
//  ChannelMarkView.swift
//
//
//  Created by ncn on 10/16/24.
//


import UIKit

public class ChannelMarkView: UIView {
  public enum Style: String {
    case front = "F"
    case rear = "R"
    case internel = "I"
    case trim = "T"
    case before = "B"
    case after = "A"
    case `private` = "P"
    case license = "L"
    case original = "O"
    
    public var labelColor: UIColor {
      switch self {
      case .front, .rear, .internel, .before, .after, .original:
        return .mainBlue
      case .trim, .private, .license:
        return .mainGreen
      }
    }

    public var backgroundColor: UIColor {
      switch self {
      case .front, .rear, .internel, .before, .after, .original:
        return .blue10
      case .trim, .private, .license:
        return .green10
      }
    }
  }

  public enum VideoType {
    case drive
    case event

    public var labelColor: UIColor {
      switch self {
      case .drive:
        return .mainBlue
      case .event:
        return .mainRed
      }
    }

    public var backgroundColor: UIColor {
      switch self {
      case .drive:
        return .blue10
      case .event:
        return .red10
      }
    }
  }

  lazy var channelLabel: UILabel = {
    let label = UILabel()
    label.font = .pretendard(ofSize: 10, weight: .bold)
    return label
  }()

  var style: Style {
    didSet {
      channelLabel.text = style.rawValue
      channelLabel.textColor = style.labelColor
      backgroundColor = style.backgroundColor
    }
  }

  var type: VideoType {
    didSet {
      channelLabel.textColor = type.labelColor
      backgroundColor = type.backgroundColor
    }
  }


  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init(style: Style, type: VideoType = .drive) {
    self.type = type
    self.style = style
    super.init(frame: .zero)

    channelLabel.text = style.rawValue
    channelLabel.textColor = style.labelColor
    backgroundColor = style.backgroundColor
    roundCorners(.allCorners, radius: 2)

    if style == .internel {
      addBody(channelLabel, insets: .init(top: 4, left: 5, bottom: 4, right: 5))
    } else {
      addBody(channelLabel, insets: .init(top: 4, left: 3, bottom: 4, right: 3))
    }
  }
}
