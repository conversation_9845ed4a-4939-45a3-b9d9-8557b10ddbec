//
//  FileListHeader.swift
//  Hub
//
//  Created by ncn on 2023/08/01.
//

import UIKit
import SnapKit

class FileListHeader: UICollectionReusableView, Reusable {
  public static var identifier: String = "FileListHeader"
  let labelDate = UILabel()
  let labelDay = UILabel()
  
  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  func setComponent() {
    labelDate.font = .pretendard(ofSize: 14, weight: .bold)
    labelDate.textColor = .text
    self.addSubview(labelDate)
    
    labelDay.font = .pretendard(ofSize: 14, weight: .bold)
    labelDay.textColor = .gray
    self.addSubview(labelDay)
  }
  
  func setAutoLayout() {
    labelDate.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.bottom.equalTo(self)
      make.leading.equalTo(self)
    }
    labelDay.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.bottom.equalTo(self)
      make.leading.equalTo(labelDate.snp.trailing).offset(4)
    }
  }
  
  // 날짜 문자열 설정 함수
  func setText(with dateString: String) {
    // 날짜와 요일 분리 (예: "2023.08.01 (화)" → "2023.08.01"과 "(화)")
    let components = splitDateAndDay(from: dateString)
    
    labelDate.text = components.date
    labelDay.text = components.day
  }
  
  // 날짜 문자열을 날짜와 요일로 분리하는 함수
  private func splitDateAndDay(from dateString: String) -> (date: String, day: String) {
    let trimmed = dateString.trimmingCharacters(in: .whitespacesAndNewlines)

    if let range = trimmed.range(of: "(") {
      let dateComponent = String(trimmed[..<range.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
      let dayComponent = String(trimmed[range.lowerBound...]).trimmingCharacters(in: .whitespacesAndNewlines)
      return (date: dateComponent, day: dayComponent)
    }

    let components = trimmed.components(separatedBy: " ")
    if components.count > 1 {
      let dateComponent = components[0]
      let dayComponent = components[1...].joined(separator: " ")
      return (date: dateComponent, day: dayComponent)
    }

    return (date: trimmed, day: "")
  }
}
