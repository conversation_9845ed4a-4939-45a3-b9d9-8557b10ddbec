//
//  ZoomCollectionView.swift
//  Prepare
//
//  Created by ncn on 2023/02/01.
//

import RxCocoa
import RxDataSources
import RxSwift
import UIKit

public protocol ScalingLayoutProtocol {
  func getScale() -> CGFloat
  func setScale(_ scale: CGFloat)
  func contentSizeForScale(_ scale: CGFloat) -> CGSize
}

class ZoomCollectionView: BaseView {
  let collectionView = UICollectionView(
    frame: .zero,
    collectionViewLayout: UICollectionViewFlowLayout.init())

  // 하단으로 내려오는 툴팁을 제어할 수 있게 하는 변수 처리
  var isToolTip = true

  let viewModel = ZoomCollectionViewModel()

  var isSelectMode: Bool = false {
    didSet {
      if !isSelectMode {
        selectedIndexPathList.removeAll()
      }
      DispatchQueue.main.async {
        self.collectionView.reloadData()
      }
    }
  }

  var isVerticalIndicator: Bool = false {
    didSet {
      collectionView.showsVerticalScrollIndicator = isVerticalIndicator
    }
  }

  var selectedIndexPathList: [IndexPath] = []

  var list: [FileListSectionModel] = [] {
    didSet {
      viewModel.list = list
      viewModel.tooltipIndex = nil
      viewModel.selectedIndex = nil
      viewModel.insertedIndexArr.removeAll()
      collectionView.reloadData()
      mLogger.debug("🔃 ZoomCollectionView reloadData")
        
    }
  }
  var selectedIndexPath: IndexPath? {
    didSet {
      //      viewModel.selectedIndex
    }
  }
  var rxSelectedTooltip = PublishSubject<FileListCellModel>()
  var selectedTooltip: FileListCellModel? {
    didSet {
      if let tooltip = selectedTooltip {
        rxSelectedTooltip.onNext(tooltip)
      }
    }
  }

  public init(layout: UICollectionViewLayout) {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
    setRxFunction()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .clear
    setCollectionView()
  }

  private func setCollectionView() {
    collectionView.register(
      viewType: FileListHeader.self, ofKind: UICollectionView.elementKindSectionHeader)
    collectionView.register(cellType: FileListCell.self)
//    collectionView.register(cellType: FileDescriptionCell.self)
    collectionView.alwaysBounceHorizontal = false
    collectionView.showsHorizontalScrollIndicator = false
    //        collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.delegate = self
    collectionView.dataSource = self
    collectionView.backgroundColor = .clear
    self.addSubview(collectionView)

    let gesture = UIPinchGestureRecognizer(target: self, action: #selector(pinchHandler(_:)))
    collectionView.addGestureRecognizer(gesture)
  }

  @objc func pinchHandler(_ recognizer: UIPinchGestureRecognizer) {

    switch recognizer.state {
    case .began:
      if let index = viewModel.selectedIndex {
        (collectionView.cellForItem(at: index) as? FileListCell)?.imageView.layer.borderWidth = 0
      }
      viewModel.selectedIndex = nil
      viewModel.removeToolTipObj(collectionView: self.collectionView)
      self.selectedTooltip = nil
      self.collectionView.reloadData()
      break
    case .changed:
      break
    case .ended, .cancelled:
      if recognizer.scale < 1 {
        viewModel.increas()
      } else {
        viewModel.descrease()
      }
      let newLayout = UICollectionViewFlowLayout()
      self.collectionView.setCollectionViewLayout(newLayout, animated: true) { _ in
      }
      break
    default:
      break
    }
  }

  override func setAutoLayout() {
    collectionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }
  }

  override func setRxFunction() {

  }
}

extension ZoomCollectionView {
  func setDataSource(listItem: BehaviorRelay<[FileListCellModel]>) {
    listItem.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: FileListCell.self)
        //cell.setData(model: element)
        return cell
      }.disposed(by: self.disposedBag)
  }

  func setDataSoruce(sectionItem: BehaviorRelay<[FileListSectionModel]>) {
    let dataSource = RxCollectionViewSectionedReloadDataSource<FileListSectionModel>(
      configureCell: { [unowned self] (dataSource, cv, indexPath, element) in
#if EXPAND_CELL
//        guard let self else { return UICollectionViewCell() }
//        if indexPath.row == self.viewModel.tooltipIndex!.row
//          && indexPath.section == self.viewModel.tooltipIndex!.section
//        {
//          let cell = self.collectionView.dequeueReusableCell(
//            for: indexPath,
//            cellType: FileDescriptionCell.self
//          )
//          //cell.setData(model: element)x
//          return cell
//        } else
//        {
        #endif
          let cell = self.collectionView.dequeueReusableCell(for: indexPath, cellType: FileListCell.self)
//          cell.setData(model: element)
          return cell
//        }
      },
      configureSupplementaryView: { [weak self] dataSource, cv, string, indexPath in
        guard let self = self else { return UICollectionReusableView() }
        let header = self.collectionView.dequeueReusableView(
          for: indexPath,
          kind: UICollectionView.elementKindSectionHeader,
          viewType: FileListHeader.self
        )
        header.setText(with: dataSource.sectionModels[indexPath.section].header)
        return header
      })
    sectionItem
      .bind(to: collectionView.rx.items(dataSource: dataSource))
      .disposed(by: self.disposedBag)

  }
}

extension ZoomCollectionView: UICollectionViewDelegate, UICollectionViewDataSource {
  func numberOfSections(in collectionView: UICollectionView) -> Int {
    wLogger.debug("sections count: \(self.viewModel.list.count)")
    return viewModel.list.count
  }

  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
    if viewModel.list.count > section {
      let obj = viewModel.list[section]
      wLogger.debug("items count: \(obj.items.count)")
      return obj.items.count
    }
    return 1
  }

  func collectionView(
    _ collectionView: UICollectionView,
    viewForSupplementaryElementOfKind kind: String,
    at indexPath: IndexPath
  ) -> UICollectionReusableView {
    switch kind {
    case UICollectionView.elementKindSectionHeader:
      let header = self.collectionView.dequeueReusableView(
        for: indexPath,
        kind: UICollectionView.elementKindSectionHeader,
        viewType: FileListHeader.self
      )

      let hubHeaderDate = list[indexPath.section].header.toFormattedDateString()
      header.setText(with: hubHeaderDate)
      return header
    default:
      assert(false, "Unexpected element kind")
      return UIView(frame: .zero) as! UICollectionReusableView
    }
  }

  func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath)
    -> UICollectionViewCell
  {
    guard let model = viewModel.getItemAtIndex(indexPath) else { return UICollectionViewCell() }
    #if EXPAND_CELL
//    if model.type == .description {
//      let cell = self.collectionView.dequeueReusableCell(
//        for: indexPath, cellType: FileDescriptionCell.self)
//      guard let obj = viewModel.getItemAtIndex(model.indexPath!) else {
//        return UICollectionViewCell()
//      }
//      cell.setData(model: obj)
//      return cell
//    } else
//    {
    #endif
      let cell = self.collectionView.dequeueReusableCell(
        for: indexPath, cellType: FileListCell.self)
      cell.imageView.layer.borderWidth = 0
      cell.imageView.layer.borderColor = UIColor.clear.cgColor

      if isSelectMode {
        cell.selectIconButton.isHidden = false
        if selectedIndexPathList.contains(where: { $0 == indexPath }) {
          cell.selectIconButton.isSelected = true
        } else {
          cell.selectIconButton.isSelected = false
        }
      } else {
        cell.selectIconButton.isHidden = true
        if indexPath == viewModel.selectedIndex {
          cell.imageView.layer.borderWidth = 2
          cell.imageView.layer.cornerRadius = 2
          cell.imageView.clipsToBounds = true
          cell.imageView.layer.borderColor = UIColor.vueroidBlue.cgColor
        }
      }
      cell.setData(model: model)
      return cell
//    }
  }

  func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
    if isSelectMode {
      if let hasIndex = selectedIndexPathList.firstIndex(where: { $0 == indexPath }) {
        selectedIndexPathList.remove(at: hasIndex)
      } else {
        
        fLogger.debug("\(AppManager.shared.mode.debugDescription) count :\(self.selectedIndexPathList.count)")
        if AppManager.shared.mode == .wifi && selectedIndexPathList.count > Current.playbackSelectionLimit {
          LKPopupView.popup.toast(hit: L.warning_select_file.localized)
        } else {
          selectedIndexPathList.append(indexPath)
        }
      }
      collectionView.reloadData()
      return
    }

    if let _ = collectionView.cellForItem(at: indexPath) as? FileListCell {
      let section = viewModel.list[indexPath.section]
      viewModel.selectedIndex = indexPath
      let model = section.items[viewModel.selectedIndex?.row ?? 0]
      model.indexPath = viewModel.selectedIndex
      self.selectedTooltip = model
      return
    }

    if viewModel.selectedIndex == indexPath {
      removeToolTipView()
      if isToolTip { return }
    } else {
      if let index = viewModel.selectedIndex {
        (collectionView.cellForItem(at: index) as? FileListCell)?.contentView.layer.borderWidth = 0
      }
    }

    let assetModelArr = viewModel.getItemListAtIndex(indexPath)
    if assetModelArr[indexPath.row].type == .dummy {
      removeToolTipView()
      return
    }
#if EXPAND_CELL
//    viewModel.selectedIndex = indexPath
//    cell.contentView.layer.borderWidth = 1
//    cell.contentView.layer.borderColor = UIColor.defaultColor.cgColor
//    if isToolTip {
//      viewModel.collectionDidSelect(indexPath: indexPath, collectionView: self.collectionView)
//    } else {
//      let selectedModel = viewModel.getItemAtIndex(indexPath)
//      selectedModel?.indexPath = indexPath
//      self.selectedTooltip = selectedModel
//    }
#endif
  }
}

extension ZoomCollectionView: UICollectionViewDelegateFlowLayout {
  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumInteritemSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 2.0
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    referenceSizeForHeaderInSection section: Int
  ) -> CGSize {
    return CGSize(width: collectionView.width, height: 44.rv)
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    sizeForItemAt indexPath: IndexPath
  ) -> CGSize {
    if indexPath == viewModel.tooltipIndex {
      return CGSize(width: collectionView.width, height: 80.rv)
    } else {
      let width = min(UIScreen.width, UIScreen.height)
      // MARK:  54 = zoomview 좌우패딩(15 + 15) + 기존 값 (14)
      let cellWidth = (width - 54) / CGFloat(viewModel.rowCount)
      let cellHeight = ((cellWidth * 3) / 4)
      return CGSize(width: cellWidth, height: cellHeight)
    }
  }
  
  func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
    return 15.0
  }
  
}

extension ZoomCollectionView {
  func removeToolTipView() {
    DispatchQueue.main.async {
      let prevSelectedIndex = self.viewModel.selectedIndex ?? IndexPath(row: 0, section: 0)
      guard let cell = self.collectionView.cellForItem(at: prevSelectedIndex) as? FileListCell
      else { return }
      self.viewModel.selectedIndex = nil
      self.viewModel.removeToolTipObj(collectionView: self.collectionView)
      self.selectedTooltip = nil
      cell.imageView.layer.borderWidth = 0
      cell.imageView.layer.borderColor = UIColor.clear.cgColor
    }
  }
}
