//
//  DashcamStatusView.swift
//  Hub
//
//  Created by ncn on 2023/04/10.
//

import UIKit

enum DashcamStatusViewType {
  case side
  case home
  case cell
}

class DashcamStatusView: BaseView {
  //    let netStateLabel = UILabel()
  //    let driveStateLabel = UILabel()
  //    let voltageLabel = UILabel()
  let labelStack = UIStackView()
  let netStateLabel = PaddingLabel()
  let driveStateLabel = PaddingLabel()
  let voltageLabel = PaddingLabel()

  public init(type: DashcamStatusViewType) {
    super.init(frame: .zero)

    setComponent(type: type)
    setAutoLayout(type: type)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent(type: DashcamStatusViewType) {
    let spacing = getIntervalValue(type: type) * UIScreen.ratio
    labelStack.spacing = spacing
    labelStack.alignment = .fill
    labelStack.distribution = .equalSpacing
    labelStack.axis = .horizontal
    self.addSubview(labelStack)

    netStateLabel.layer.cornerRadius = getCornerRadiusValue(type: type)
    netStateLabel.layer.borderWidth = 2
    netStateLabel.layer.borderColor = UIColor.defaultColor.cgColor
    netStateLabel.textColor = .defaultColor  //.white
    netStateLabel.font = .MontserratSemiBold(ofSize: 14)
    netStateLabel.textAlignment = .center
    netStateLabel.text = "OFFINE"
    labelStack.addArrangedSubview(netStateLabel)

    driveStateLabel.layer.cornerRadius = getCornerRadiusValue(type: type)
    driveStateLabel.clipsToBounds = true
    driveStateLabel.backgroundColor = .customSkyBlue
    driveStateLabel.textColor = .defaultColor  //.white
    driveStateLabel.font = .MontserratSemiBold(ofSize: 14)
    driveStateLabel.textAlignment = .center
    driveStateLabel.text = "● PowerOff"
    labelStack.addArrangedSubview(driveStateLabel)

    voltageLabel.layer.cornerRadius = getCornerRadiusValue(type: type)
    voltageLabel.clipsToBounds = true
    voltageLabel.textColor = .text
    voltageLabel.font = .MontserratSemiBold(ofSize: 14)
    voltageLabel.backgroundColor = .customGray
    voltageLabel.textAlignment = .center
    voltageLabel.text = "00.0V"
    labelStack.addArrangedSubview(voltageLabel)
  }

  func setAutoLayout(type: DashcamStatusViewType) {
    labelStack.snp.makeConstraints { make in
      make.leading.top.bottom.equalToSuperview()
    }
  }

  func getWidthValue(type: DashcamStatusViewType) -> CGFloat {
    var value: CGFloat = 80
    if type == .cell {
      value = 72
    }

    return value
  }

  func getCornerRadiusValue(type: DashcamStatusViewType) -> CGFloat {
    var value: CGFloat = 10
    switch type {
    case .home:
      value = 12
      break
    case .cell:
      value = 11
      break
    case .side:
      value = 10
      break
    }

    return value
  }

  func getIntervalValue(type: DashcamStatusViewType) -> CGFloat {
    var value: CGFloat = 5
    if type == .home {
      value = 10
    }
    return value
  }
}
