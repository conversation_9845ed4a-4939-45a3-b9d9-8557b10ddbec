//
//  BlackBoxView.swift
//  Hub
//
//  Created by ncn on 2023/02/13.
//

import UIKit

class DashcamView: BaseView {
  let infoView = DashcamInfoView()
  let backgroundImage = UIImageView()
  let dashcamImage = UIImageView()
  let liveLabel = UILabel()

  lazy var offButton = UIButton()
  var isPowoff: Bool = false {
    didSet {
      if isPowoff == true {
        setPoweroffButton()
      }
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  override func setComponent() {
    self.addSubview(infoView)

    backgroundImage.image = UIImage(named: "img_model-bg")
    self.addSubview(backgroundImage)

    dashcamImage.image = UIImage(named: "product_img")
    dashcamImage.contentMode = .scaleAspectFit
    backgroundImage.addSubview(dashcamImage)

    liveLabel.text = "LIVE"
    liveLabel.textAlignment = .center
    liveLabel.textColor = .text
    liveLabel.font = .MontserratSemiBold(ofSize: 14)
    liveLabel.backgroundColor = .defaultColor
    liveLabel.layer.cornerRadius = 12
    liveLabel.clipsToBounds = true
    self.addSubview(liveLabel)
  }

  override func setAutoLayout() {
    infoView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.left.right.equalTo(self)
      make.height.equalTo(74)
    }

    var width = UIScreen.ratio * 211
    backgroundImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.bottom.equalTo(self).offset(-29)
      make.height.width.equalTo(width)
    }

    width = UIScreen.ratio * 66

    dashcamImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalToSuperview().inset(30)
      make.horizontalEdges.equalToSuperview()
      make.bottom.equalTo(self.liveLabel.snp.top)
    }

    let offset = 41 * UIScreen.ratio
    liveLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.width.equalTo(44)
      make.height.equalTo(25)
      make.bottom.equalTo(self.backgroundImage.snp.bottom).offset(-offset)
    }
  }

  func setPoweroffButton() {
    offButton.setImage(UIImage(named: "icon_power"), for: .normal)
    offButton.layer.cornerRadius = 29
    offButton.layer.borderColor = UIColor.defaultColor.cgColor
    offButton.layer.borderWidth = 1
    offButton.clipsToBounds = true
    self.addSubview(offButton)
    offButton.snp.makeConstraints { make in
      make.right.equalToSuperview().inset(10)
      make.bottom.equalToSuperview()
    }
  }
}

extension DashcamView {
  func setStatus(data: HomeDashcamModel) {

    if let v = data.voltage {
      infoView.statusView.voltageLabel.text = v
    }
    if let d = data.drivemode {
      infoView.statusView.driveStateLabel.text = "● " + d
    }

    if let n = data.netStatus {
      infoView.statusView.netStateLabel.text = n
    }
  }
}
