//
//  BalckBoxInfoView.swift
//  Hub
//
//  Created by ncn on 2023/02/13.
//

import UIKit

class DashcamInfoView: BaseView {
  let initSettingButton = UIButton()
  let nameLabel = UILabel()
  let statusView = DashcamStatusView(type: .home)
  let netStateLabel = UILabel()
  let driveStateLabel = UILabel()

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  override func setComponent() {
    self.backgroundColor = .clear

    initSettingButton.isHidden = true
    initSettingButton.setTitle("Init Setting", for: .normal)
    initSettingButton.setTitleColor(.white, for: .normal)
    self.addSubview(initSettingButton)

    nameLabel.text = "D21-000"
    nameLabel.textColor = .text
    nameLabel.font = .MontserratBold(ofSize: 24)
    self.addSubview(nameLabel)

    self.addSubview(statusView)
  }

  override func setAutoLayout() {
    nameLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.leading.equalToSuperview()
      make.trailing.equalTo(initSettingButton.snp.leading)
    }

    initSettingButton.snp.makeConstraints { make in
      make.top.trailing.equalToSuperview()
      make.bottom.equalTo(nameLabel)
      make.width.equalTo(80)
    }

    statusView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self)
      make.height.equalTo(25)
      make.right.equalTo(self).offset(-75)
      make.bottom.equalTo(self)
    }
  }
}
