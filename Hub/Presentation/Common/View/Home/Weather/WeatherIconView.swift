//
//  WeatherIconView.swift
//  Hub
//
//  Created by ncn on 2023/06/05.
//

import UIKit

class WeatherIconView: BaseView {
  let iconView = UIImageView()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    backgroundColor = .clear
    iconView.image = UIImage(named: "weather_1")
    self.addSubview(iconView)
  }

  override func setAutoLayout() {
    iconView.pin.all().activate()
  }
}
