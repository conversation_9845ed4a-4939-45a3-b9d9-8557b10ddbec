//
//  WeatherView.swift
//  Hub
//
//  Created by ncn on 2023/06/05.
//

import UIKit

class WeatherView: UIView {
  let iconView = WeatherIconView()

  let contentLabel = UILabel()
  let tagTempLabel = UILabel()
  let temperatureLabel = UILabel()
  let tagHumidityLabel = UILabel()
  let tagWindspeedLabel = UILabel()
  let humidityLabel = UILabel()
  let windspeedLabel = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
//    setAutoLayout()
    
    HStackView(spacing: 12) {
      iconView.withSize(57)
      VStackView(spacing: 8) {
        contentLabel
        HStackView(spacing: 8) {
          tagTempLabel
          temperatureLabel
          tagHumidityLabel
          humidityLabel
          tagWindspeedLabel
          windspeedLabel
          UIView()
        }
      }
    }.withMargins(.init(horizontal: 15, vertical: 15))
      
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent() {
    self.backgroundColor = .white.withAlphaComponent(0.95)

    contentLabel.font = .pretendard(ofSize: 14, weight: .bold)
    contentLabel.numberOfLines = 3
    contentLabel.textColor = .subText

    tagTempLabel.font = .body5
    tagTempLabel.text = "Temp"
    tagTempLabel.sizeToFit()
    tagTempLabel.textColor = .grayText

    tagHumidityLabel.font = .body5
    tagHumidityLabel.text = "RH"
    tagHumidityLabel.sizeToFit()
    tagHumidityLabel.textColor = .grayText

    tagWindspeedLabel.font = .body5
    tagWindspeedLabel.text = "WS"
    tagWindspeedLabel.sizeToFit()
    tagWindspeedLabel.textColor = .grayText

    temperatureLabel.font = .body5Bold
    temperatureLabel.textColor = .grayText
    temperatureLabel.sizeToFit()

    humidityLabel.font = .body5Bold
    humidityLabel.textColor = .grayText
    humidityLabel.sizeToFit()

    windspeedLabel.font = .body5Bold
    windspeedLabel.textColor = .grayText
    windspeedLabel.sizeToFit()
  }

  override func layoutSubviews() {
    super.layoutSubviews()
    self.layer.cornerRadius = 10
  }
}

extension WeatherView {
  func setData(model: WeatherModel) {
    iconView.iconView.image = model.image  //UIImage(named: "weather_6")
    let temp =
      AppManager.shared.accountTempUnit == "N"
      ? ((Double(model.temp) ?? 0.0) * 9 / 5) + 32 : Double(model.temp) ?? 0.0
    temperatureLabel.text =
      "\(temp)" + (AppManager.shared.accountTempUnit == "N" ? "°F" : "°C")
    //    (4°C × 9/5) + 32
    contentLabel.text = model.text
    humidityLabel.text = model.humidity
    windspeedLabel.text = model.speed
  }
}
