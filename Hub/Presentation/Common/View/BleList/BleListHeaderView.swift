//
//  BleListHeaderView.swift
//  Hub
//
//  Created by ncn on 2023/04/26.
//

import UIKit

enum BleListHeaderType {
  case normal
  case loading
}

class BleListHeaderView: BaseView {
  let label: UILabel = UILabel()
  lazy var indicatorView: LottieView = {
    let view = LottieView(name: "loading_darkgray_small")
    return view
  }()

  lazy var loadingLabel: UILabel = {
    let label = UILabel()
    label.textColor = .text
    label.font = .systemFont(ofSize: 12)
    label.text = L.progress_title.localized
    label.sizeToFit()
    label.adjustsFontSizeToFitWidth = true
    label.minimumScaleFactor = 0.5
    return label
  }()

  public init(type: BleListHeaderType = .normal) {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()

    if type == .loading {
      setLoading()
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    label.textColor = .text
    label.font = .h2
    label.sizeToFit()
    label.adjustsFontSizeToFitWidth = true
    self.addSubview(label)
  }

  override func setAutoLayout() {
    //label
    label.snp.makeConstraints { [weak self] v -> Void in
      guard let self = self else { return }
      v.left.equalTo(self).offset(20.0)
      //v.right.equalTo(self).offset(-20.0)
      v.height.equalTo(19.0.rv)
      v.bottom.equalTo(self).offset(-4.rv)
    }
  }

  func setLoading() {
    self.addSubview(indicatorView)
    self.addSubview(loadingLabel)

    indicatorView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.label.snp.centerY)
      make.left.equalTo(self.label.snp.right).offset(10)
      make.width.height.equalTo(20)
    }

    loadingLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.label.snp.centerY)
      make.left.equalTo(self.indicatorView.snp.right).offset(4)
      make.right.equalToSuperview().inset(15)
    }
  }
}

extension BleListHeaderView {
  func play() {
    indicatorView.isHidden = false
    loadingLabel.isHidden = false
    indicatorView.play()
  }

  func pause() {
    indicatorView.pause()
  }

  func stop() {
    indicatorView.stop()
    indicatorView.isHidden = true
    loadingLabel.isHidden = true
  }
}
