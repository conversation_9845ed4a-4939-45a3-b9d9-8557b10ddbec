//
//  BleEmptyView.swift
//  Hub
//
//  Created by ncn on 2023/04/17.
//

import UIKit

class BleEmptyView: BaseView {
  let emptyImage = UIImageView(image: UIImage(named: "list_nodata_icon"))
  let commentLabel = UILabel()

  public init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .background
    self.layer.cornerRadius = 10
    self.addSubview(emptyImage)

    commentLabel.text = L.dashcamlist_empty.localized
    commentLabel.font = .MontserratRegular(ofSize: 14)
    commentLabel.textColor = .text
    commentLabel.textAlignment = .center
    commentLabel.adjustsFontSizeToFitWidth = true
    commentLabel.minimumScaleFactor = 0.5
    self.addSubview(commentLabel)
  }

  override func setAutoLayout() {
    emptyImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.centerY.equalTo(self).offset(-30)
      make.width.equalTo(140)
      make.height.equalTo(120)
    }

    commentLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.width.equalTo(safeAreaLayoutGuide)
      make.top.equalTo(self.emptyImage.snp.bottom).offset(10)
    }
  }
}
