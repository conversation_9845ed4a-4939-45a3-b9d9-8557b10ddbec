//
//  MapControlView.swift
//  Hub
//
//  Created by ncn on 2023/07/05.
//

import UIKit

class MapControlView: BaseView {
  let slider = UISlider()
  #if false
  lazy var sizeButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "icon_full"), for: .normal)
    return button
  }()

  lazy var minLabel: UILabel = {
    let label = UILabel()
    label.font = .systemFont(ofSize: 10)
    label.textColor = .text
    label.text = "00:00"
    return label
  }()

  lazy var maxLabel: UILabel = {
    let label = UILabel()
    label.font = .systemFont(ofSize: 10)
    label.textColor = .text
    label.text = "00:00"
    return label
  }()
  #endif
  
  var minValue: Int = 0 {
    didSet {
      slider.minimumValue = Float(minValue)
    }
  }

  var maxValue: Int = 0 {
    didSet {
      slider.maximumValue = Float(maxValue)
    }
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .white
    self.addSubview(slider)
    setSlider()
  }

  override func setAutoLayout() {
    slider.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.right.equalTo(self).inset(10)
    }
  }

  private func setSlider() {
    let blueColor = UIColor(hex: "#00B9F2")
    let maxColor = UIColor(hex: "#0077FF")
    
    let image = UIImage().imageWithColor(color: blueColor)
    let maxImage = UIImage().imageWithColor(color: maxColor)
    slider.setMaximumTrackImage(maxImage, for: .normal)
    slider.setMinimumTrackImage(image, for: .normal)
    
    slider.minimumValue = 0
    slider.maximumValue = 100
    slider.isContinuous = true
    
    slider.setThumbImage(nil, for: .normal)
    
    slider.backgroundColor = .clear
  }
}
