//
//  TravelInfoView.swift
//  Hub
//
//  Created by ncn on 2023/07/05.
//

import UIKit

class TravelInfoItemView: BaseView {
  let itemContainerView = UIView()
  let itemLabel = UILabel()
  let valueContainerView = UIView()
  let valueLabel = UILabel()
  let unitLabel = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.addSubview(itemContainerView)

    itemLabel.textAlignment = .left
    itemLabel.textColor = .grayText
    itemLabel.font = .body2
    itemContainerView.addSubview(itemLabel)

    itemContainerView.addSubview(valueContainerView)

    valueLabel.textAlignment = .right
    valueLabel.textColor = .subText
    valueLabel.font = .body2
    valueLabel.sizeToFit()
    valueContainerView.addSubview(valueLabel)

    unitLabel.textAlignment = .left
    unitLabel.textColor = .subText
    unitLabel.font = .body2
    unitLabel.sizeToFit()
    valueContainerView.addSubview(unitLabel)
  }

  override func setAutoLayout() {
    itemContainerView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview()
      make.centerY.equalToSuperview().inset(20)
      make.height.equalTo(16)
    }

    itemLabel.snp.makeConstraints { make in
      make.left.right.top.equalToSuperview()
      make.height.equalTo(16)
    }

    valueContainerView.snp.makeConstraints { make in
      make.right.equalToSuperview().inset(0)
      make.centerY.equalTo(itemLabel.snp.centerY)
    }

    valueLabel.snp.makeConstraints { make in
      make.leading.equalToSuperview()
      make.top.equalToSuperview().inset(10)
      make.centerY.equalToSuperview()
    }

    unitLabel.snp.makeConstraints { make in
      make.trailing.equalToSuperview()
      make.leading.equalTo(valueLabel.snp.trailing)
      make.centerY.equalToSuperview()
    }

  }
}

class TravelInfoView: UIView {
  let verticalStackView = UIStackView()
  
  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent() {
    backgroundColor = .white
    roundCorners(.allCorners, radius: 10)
    
    verticalStackView.axis = .vertical
    verticalStackView.spacing = 14
    verticalStackView.distribution = .fillEqually
    verticalStackView.alignment = .fill
    self.addSubview(verticalStackView)
  }

  func setAutoLayout() {
    verticalStackView.pin.horizontally(offset: 20).top(offset: 54).bottom(offset: -14).activate()
  }

  func setInfoData(topStackData: [TravelInfoItemView]) {
    verticalStackView.removeAllSubviews()
    topStackData.forEach { verticalStackView.addArrangedSubview($0) }
  }
}
