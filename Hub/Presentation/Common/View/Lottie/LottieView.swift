//
//  LottieView.swift
//  Hub
//
//  Created by ncn on 2023/04/26.
//

import Lottie
import UIKit

class LottieView: BaseView {

  var animationView: LottieAnimationView?
  var mode: LottieLoopMode = .loop {
    didSet {
      if let view = animationView {
        view.loopMode = mode
      }
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init(name: String) {
    super.init(frame: .zero)

    initVariable(name)
    setupLayout()
  }

  private func initVariable(_ file: String) {
    self.backgroundColor = .clear

    if let animation = LottieAnimation.named(file) {
      self.animationView = LottieAnimationView(animation: animation)
      animationView?.contentMode = .scaleAspectFill
      animationView?.loopMode = mode
    }
  }

  func setupLayout() {
    if let view = animationView {
      view.frame = self.bounds
      view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
      self.addSubview(view)
    }
  }

  func play() {
    if let view = animationView {
      view.play()
    }
  }

  func play(completion: @escaping (Bool) -> Void) {
    if let view = animationView {
      view.play { isDone in
        completion(isDone)
      }
    }
  }

  func pause() {
    if let view = animationView {
      view.pause()
    }
  }

  func stop() {
    if let view = animationView {
      view.stop()
    }
  }
}
