//
//  TopView.swift
//  Hub
//
//  Created by ncn on 2023/01/02.
//

import SnapKit
import UIKit

enum HeaderMenuType {
  case `default`
  case navi
  case menu
  case modal
}

class HeaderMenuView: BaseView {
  let baseHeight = 68.0
  let titleLabel: UILabel = {
    var label = UILabel()
    label.textColor = .text
    label.adjustsFontSizeToFitWidth = true
    label.numberOfLines = 1
    return label
  }()

  lazy var leftButton: VueroidButton = {
    var button = VueroidButton()
    button.setTitleColor(.darkGray, for: .normal)
    button.backgroundColor = .clear
    return button
  }()

  lazy var rightButton: UIButton = {
    var button = UIButton()
    button.setTitleColor(.darkGray, for: .normal)
    button.backgroundColor = .clear
    return button
  }()

  lazy var badgeIcon: UIImageView = {
    let view = UIImageView(image: (UIImage(named: "icon_new-badge")))
    return view
  }()
  // history Header
  lazy var switchButton = UIButton()
  lazy var logButton = UIButton()

  // TravelLog Header
  lazy var checkButton = UIButton()
  lazy var deleteButton = UIButton()

  public init(type: HeaderMenuType = .default) {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()

    draw(type: type)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .clear
  }

  override func setAutoLayout() {
  }

  func draw(type: HeaderMenuType) {
    switch type {
    case .default:
      setTitle()
      break
    case .navi:
      setTitle()
      setBackButton()
      break
    case .menu:
      setTitle()
      setMenuButton()
      break
    case .modal:
      setTitle()
      setModalButton()
      break
    }
  }
}

extension HeaderMenuView {
  private func setTitle() {
    titleLabel.font = .MontserratMedium(ofSize: 22)
    titleLabel.text = "VUEROID"
    self.addSubview(titleLabel)

    titleLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.bottom.equalTo(self).offset(-20)
    }
    #if DEVELOP || QA
      setDebugGesture()
    #endif
  }

  private func setDebugGesture() {
    let recognizer = UITapGestureRecognizer(
      target: self, action: #selector(handleDebugGesture(sender:)))
    recognizer.numberOfTapsRequired = 5
    addGestureRecognizer(recognizer)
  }

  @objc private func handleDebugGesture(sender: UITapGestureRecognizer) {
    #if QA || DEV
      Current.console.isVisible.toggle()
    #endif
  }
}

extension HeaderMenuView {
  private func setBackButton() {
    leftButton.setImage(UIImage(named: "icon_back"), for: .normal)
    self.addSubview(leftButton)

    leftButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.left.equalTo(self).offset(20)
    }
  }
}

extension HeaderMenuView {
  private func setMenuButton() {
    leftButton.setImage(UIImage(named: "icon_menu"), for: .normal)
    self.addSubview(leftButton)
    rightButton.setImage(UIImage(named: "icon_alert"), for: .normal)
    self.addSubview(rightButton)
    rightButton.addSubview(badgeIcon)

    leftButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.left.equalTo(self).offset(20)
    }

    rightButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.right.equalTo(self).offset(-20)
    }

    badgeIcon.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.width.height.equalTo(15)
      make.right.equalTo(self.rightButton).offset(-4)
      make.bottom.equalTo(self.rightButton).offset(-4)
    }

    rightButton.isHidden = true
  }
}

extension HeaderMenuView {
  private func setModalButton() {
    leftButton.setImage(UIImage(named: "icon_back"), for: .normal)
    self.addSubview(leftButton)

    rightButton.setImage(UIImage(named: "icon_exit"), for: .normal)
    self.addSubview(rightButton)

    leftButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.left.equalTo(self).offset(20)
    }

    rightButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.right.equalTo(self).offset(-20)
    }
  }
}

extension HeaderMenuView {
  func setHistoryButton() {
    switchButton.setImage(UIImage(named: "icon_history_unit_mph"), for: .selected)
    switchButton.setImage(UIImage(named: "icon_history_unit_kmh"), for: .normal)
    self.addSubview(switchButton)

    logButton.setImage(UIImage(named: "icon_history_log"), for: .normal)
    self.addSubview(logButton)

    switchButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.right.equalTo(self.logButton.snp.left).offset(-10)
    }

    logButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.right.equalTo(self).offset(-20)
    }
  }

  func setTravelLogButton() {

    deleteButton.setImage(UIImage(named: "icon_del_w"), for: .normal)
    deleteButton.isHidden = true
    self.addSubview(deleteButton)

    checkButton.roundCorners(.allCorners, radius: 15)
    checkButton.setBackgroundColor(.customSteel, for: .normal)
    checkButton.setImage(UIImage(named: "icon_check_blue"), for: .normal)
    checkButton.setBackgroundColor(.customSoftBlue, for: .selected)
    checkButton.setImage(UIImage(named: "icon_check_blue"), for: .selected)
    checkButton.tintColor = .white
    self.addSubview(checkButton)

    deleteButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(40)
      make.right.equalTo(self.checkButton.snp.left).offset(-10)
    }

    checkButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.width.height.equalTo(30)
      make.right.equalTo(self).offset(-20)
    }
  }

}
