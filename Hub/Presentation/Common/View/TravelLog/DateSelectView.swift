//
//  DateSelectView.swift
//  Hub
//
//  Created by ncn on 2023/06/26.
//

import UIKit

class DateSelectView: BaseView {
  let leftButton = UIButton()
  let rightButton = UIButton()
  let dateLabel = UILabel()
  let pageControl = UIPageControl()
  let progressView = UIProgressView()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    backgroundColor = .clear
    
    leftButton.setImage(UIImage(named: "icon_t-day-prev1"), for: .normal)
    leftButton.tintColor = .white
    self.addSubview(leftButton)

    rightButton.setImage(UIImage(named: "icon_t-day-next1"), for: .normal)
    rightButton.tintColor = .white
    self.addSubview(rightButton)

    dateLabel.font = .h1
    dateLabel.textColor = .white
    dateLabel.textAlignment = .center
    dateLabel.numberOfLines = 2
    self.addSubview(dateLabel)

    pageControl.currentPageIndicatorTintColor = .vueroidBlue
    pageControl.pageIndicatorTintColor = UIColor.white.withAlphaComponent(0.3)
    pageControl.isUserInteractionEnabled = false
    self.addSubview(pageControl)
    
    progressView.progressTintColor = .vueroidBlue
    progressView.trackTintColor = UIColor.white.withAlphaComponent(0.3)
    progressView.layer.cornerRadius = 2
    progressView.clipsToBounds = true
    progressView.isHidden = true
    self.addSubview(progressView)
  }

  override func setAutoLayout() {
    dateLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.centerY.equalTo(self).offset(-10)
    }

    pageControl.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.top.equalTo(dateLabel.snp.bottom).offset(5)
      make.height.equalTo(20)
    }
    
    progressView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.top.equalTo(dateLabel.snp.bottom).offset(10)
      make.width.equalTo(200)
      make.height.equalTo(4)
    }

    leftButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(15)
      make.width.height.equalTo(24)
      make.top.bottom.equalTo(self)
    }

    rightButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self).offset(-15)
      make.width.height.equalTo(24)
      make.top.bottom.equalTo(self)
    }
  }
  
  func updatePageControl(with countText: String) {
    let components = countText.components(separatedBy: "/")
    guard components.count == 2,
          let currentPage = Int(components[0]),
          let totalPages = Int(components[1]) else {
      pageControl.isHidden = true
      progressView.isHidden = true
      return
    }
    
    
    // 16개 이하면 PageControl, 17개 이상이면 ProgressView
    if totalPages <= 16 {
      pageControl.numberOfPages = totalPages
      pageControl.currentPage = currentPage - 1
      pageControl.isHidden = false
      progressView.isHidden = true
    } else {
      let progress = Float(currentPage) / Float(totalPages)
      progressView.setProgress(progress, animated: true)
      progressView.isHidden = false
      pageControl.isHidden = true
    }
  }
}
