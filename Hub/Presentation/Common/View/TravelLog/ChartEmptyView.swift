//
//  ChartEmptyView.swift
//  Hub
//
//  Created by le<PERSON>ung<PERSON><PERSON> on 2023/10/16.
//

import Foundation
import SnapKit
import UIKit

class ChartEmptyView: BaseView {
  let emptyLabel = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .background

    emptyLabel.text = L.history_chart_empty_msg.localized
    emptyLabel.font = .h2Regular
    emptyLabel.textColor = .grayText
    emptyLabel.textAlignment = .center
    self.addSubview(emptyLabel)

  }

  override func setAutoLayout() {
    emptyLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.trailing.equalToSuperview().inset(10)
      make.center.equalTo(self)
    }
  }

}
