//
//  ParkingSnapshotView.swift
//  Hub
//
//  Created by ncn on 2023/02/14.
//

import UIKit

class ParkingSnapshotView: BaseView {
  let titleView = ContentTitleView(type: .gSensor)
  let imageView = UIImageView()
  let emptyLabel = UILabel()
  let dateLabel = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    // MARK: 영어 처리
    //        titleView.titleLabel.text = "주차 이미지"
//    titleView.drivingTitleLabel.text = "Parking image"
    self.addSubview(titleView)

    // 주차이미지 없음 처리
    imageView.image = UIImage()
    self.addSubview(imageView)

    //        emptyLabel.text = "주차이미지가 없습니다."
    emptyLabel.text = "There is no parking image."
    emptyLabel.textAlignment = .center
    emptyLabel.textColor = .text
    emptyLabel.font = .systemFont(ofSize: 20)
    self.addSubview(emptyLabel)

    dateLabel.text = Date().toString(format: "yyyy/MM/dd hh:mm:ss")
    dateLabel.textColor = .text
    dateLabel.font = .systemFont(ofSize: 14)
    self.addSubview(dateLabel)
  }

  override func setAutoLayout() {
    titleView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.top.right.equalTo(self)
      make.height.equalTo(60)
    }

    let width = UIScreen.width - (20 * 2)
    let height = width * (9 / 16)
    imageView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.titleView.snp.bottom)
      make.left.right.equalTo(self)
      make.height.equalTo(height)
    }

    emptyLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.imageView)
    }

    // 40
    dateLabel.snp.makeConstraints { make in
      make.left.equalTo(20)
      make.bottom.equalTo(-11)
    }

  }

}
