//
//  TravelMapContainView.swift
//  Hub
//
//  Created by ncn on 2023/07/05.
//

import GoogleMaps
import UIKit

class TravelMapContainView: BaseView {
  var mapGradientBGView: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "map_gradient_bg.png")
    return view
  }()
  
  let dateSelectView = DateSelectView()
  let countLabel = UILabel()
  let frameView = UIView()
  let traceButton = UIButton()
  #if !targetEnvironment(simulator)
    var mapView: GMSMapView?
    var changeMarker: GMSMarker = GMSMarker()
  #endif
  

  var items: [gpsLatlon]? {
    didSet {
//      controlView.slider.value = 0.0
//      controlView.maxValue = items?.count ?? 1
    }
  }

  var index = 0 {
    didSet {
      if let obj = items {
        DispatchQueue.main.async {
          self.setLineData(datas: obj)
        }
      }
    }
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    addSubview(frameView)
    dateSelectView.roundCorners(.allCorners, radius: 20)
    addSubview(mapGradientBGView)
    addSubview(dateSelectView)
    countLabel.text = "1/10"
    countLabel.font = .body4
    countLabel.textColor = .black
    dateSelectView.addSubview(countLabel)
    dateSelectView.updatePageControl(with: countLabel.text ?? "1/1")

    setMapView()

    traceButton.isHidden = true
    traceButton.setTitle(L.trace_on_txt.localized, for: .normal)
    traceButton.setTitle(L.trace_off_txt.localized, for: .selected)
    traceButton.setImage(.traceIcon, for: .normal)

    traceButton.semanticContentAttribute = .forceRightToLeft
    traceButton.imageView?.contentMode = .scaleAspectFit
    traceButton.setTitleColor(.init(hexCode: "3d4367"), for: .normal)
    traceButton.tintColor = .init(hexCode: "3d4367")
    frameView.addSubview(traceButton)
  }

  override func setAutoLayout() {
    mapGradientBGView.pin.top(safe: true).horizontally().height(327.rv).activate()
    dateSelectView.pin.top(offset: 10, safe: true).horizontally().height(90).activate()
    
    countLabel.snp.makeConstraints { make in
      make.trailing.equalToSuperview().inset(20)
      make.bottom.equalToSuperview().inset(5)
    }

    frameView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.equalTo(self)
      make.top.equalToSuperview().inset(10)
      make.bottom.equalToSuperview()
    }
    traceButton.snp.makeConstraints {
      $0.bottom.trailing.equalToSuperview().inset(10)
      $0.height.equalTo(30)
    }

//    controlView.snp.makeConstraints { [weak self] make in
//      guard let self = self else { return }
//      make.left.right.bottom.equalTo(self)
//      make.height.equalTo(60)
//    }
  }

  private func setMapView() {
    #if !targetEnvironment(simulator)
      var latitude: Double = 0.0
      var longtitude: Double = 0.0
      if let value = AppManager.shared.locationValue {
        latitude = value.latitude
        longtitude = value.longitude
      }

      let camera = GMSCameraPosition.camera(
        withLatitude: latitude,
        longitude: longtitude,
        zoom: 15.0
      )

      let options = GMSMapViewOptions()
      options.camera = camera
      // FIXME : .zero 로 설정하면 moveCamera가 동작하지 않느다.
      // https://github.com/flutter/flutter/issues/24806
      options.frame = .init(x: 0, y: 0, width: 300, height: 400)
      let mapView = GMSMapView(options: options)
      mapView.isUserInteractionEnabled = true
      self.mapView = mapView
      frameView.addSubview(mapView)

      self.mapView?.animate(to: camera)

      mapView.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.edges.equalTo(self.frameView)
      }
    #endif
  }
}

extension TravelMapContainView {
  func setLineData(datas: [gpsLatlon]) {
    self.items = datas
//    if datas.isEmpty {
//      controlView.slider.isHidden = true
//    } else {
//      controlView.slider.isHidden = false
//    }
    
#if !targetEnvironment(simulator)
    mapView?.clear()

    guard let start = datas.first,
          let end = datas.last
    else { return }

    let startLocation = CLLocationCoordinate2DMake(
      CLLocationDegrees( start.lat ),
      CLLocationDegrees( start.lon )
    )

    mapLogger.debug("startLocation: \(startLocation.latitude), \(startLocation.longitude)")
    let startMarker = GMSMarker(position: startLocation)
    startMarker.icon = UIImage(named: "icon_map_marker_point")
    startMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
    startMarker.zIndex = 0
    startMarker.map = mapView

    changeMarker = GMSMarker(position: startLocation)
    changeMarker.icon = UIImage(named: "icon_map_marker_car")
    changeMarker.zIndex = 1
    changeMarker.map = mapView

    let endLocation = CLLocationCoordinate2DMake(
      CLLocationDegrees( end.lat ),
      CLLocationDegrees( end.lon )
    )
    let endMarker = GMSMarker(position: endLocation)
    mapLogger.debug("endLocation: \(endLocation.latitude), \(endLocation.longitude)")
    endMarker.icon = UIImage(named: "icon_map_marker_point")
    endMarker.zIndex = 0
    endMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
    endMarker.map = mapView

    let path = GMSMutablePath()
    for data in datas {
      let coordi = CLLocationCoordinate2D(
        latitude: CLLocationDegrees( data.lat ),
        longitude: CLLocationDegrees( data.lon )
      )
      path.add(coordi)
    }

    let polyline = GMSPolyline(path: path)
    polyline.strokeWidth = 10.0
    polyline.strokeColor = .black
    polyline.map = mapView

    let camera = GMSCameraPosition(
      latitude: startLocation.latitude,
      longitude: startLocation.longitude,
      zoom: 15.0

    )
    let update = GMSCameraUpdate.setCamera(camera)
    mapView?.moveCamera(update)
    mapLogger.debug("map camera: \(self.mapView?.camera)")
#endif
  }

  func setEmptyPoistion() {
//    controlView.slider.isHidden = true
#if !targetEnvironment(simulator)
    mapView?.clear()

    let companyLocation = CLLocationCoordinate2DMake(
      CLLocationDegrees( Current.companyLocation.0 ),
      CLLocationDegrees( Current.companyLocation.1 )
    )

    let startMarker = GMSMarker(position: companyLocation)
    startMarker.icon = UIImage(named: "icon_map_marker_point")
    startMarker.groundAnchor = CGPoint(x: 0.5, y: 0.5)
    startMarker.zIndex = 0
    startMarker.map = mapView

    let camera = GMSCameraPosition(
      latitude: companyLocation.latitude,
      longitude: companyLocation.longitude,
      zoom: 15.0

    )
    let update = GMSCameraUpdate.setCamera(camera)
    mapView?.moveCamera(update)
    mapLogger.debug("map camera: \(self.mapView?.camera)")
#endif
  }

  func setChangeMarker(itemIndex: Int, isTraceOn: Bool) {
    
    if itemIndex <= 0 && items?.count ?? 0 > 0 {
      if let firstItem = items?.first {
        updateMarkerPosition(item: firstItem, isTraceOn: isTraceOn)
      }
      return
    }
    
    let adjustedIndex = itemIndex - 1
    guard let items = self.items, adjustedIndex >= 0, adjustedIndex < items.count else {
      return
    }
    
    let item = items[adjustedIndex]
    updateMarkerPosition(item: item, isTraceOn: isTraceOn)
  }
  
  private func updateMarkerPosition(item: gpsLatlon, isTraceOn: Bool) {
    #if !targetEnvironment(simulator)
      let changeLocation = CLLocationCoordinate2DMake(
        CLLocationDegrees(item.lat), CLLocationDegrees(item.lon))
      changeMarker.map = nil

      changeMarker = GMSMarker(position: changeLocation)
      changeMarker.icon = UIImage(named: "icon_map_marker_car")
      changeMarker.zIndex = 1
      changeMarker.map = self.mapView

      if isTraceOn {
        let nowZoom = self.mapView?.camera.zoom ?? 10.0
        mapLogger.debug("nowZoom: \(nowZoom)")
        let camera = GMSCameraPosition(
          latitude: changeLocation.latitude,
          longitude: changeLocation.longitude,
          zoom: nowZoom
        )

        let update = GMSCameraUpdate.setCamera(camera)
        mapView?.moveCamera(update)

        mapLogger.debug("마커 위치 업데이트: lat=\(changeLocation.latitude), lon=\(changeLocation.longitude)")
      }
    #endif
  }
}
