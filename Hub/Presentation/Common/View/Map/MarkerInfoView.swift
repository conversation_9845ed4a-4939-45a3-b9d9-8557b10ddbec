//
//  MarkerInfoView.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 2023/08/11.
//

import SnapKit
import UIKit

final class MarkerInfoView: BaseView {

  let icon = UIImageView()
  let address = UILabel()
  let coordinate = UILabel()
  let coordinateValue = UILabel()

  let temperature = UILabel()
  let temperatureValue = UILabel()
  let airCondition = UILabel()
  let airConditionValue = UILabel()

  public init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .black

    icon.image = .checkmark

    address.textColor = .text
    address.font = .MontserratBold(ofSize: 14)
    address.text = ";aslkjf;klasjdf;lk"

    coordinate.textColor = .text
    coordinate.font = .MontserratRegular(ofSize: 12)
    coordinate.text = "좌표"

    coordinateValue.textColor = .text
    coordinateValue.font = .MontserratRegular(ofSize: 12)
    coordinateValue.text = "***************"

    temperature.textColor = .text
    temperature.font = .MontserratRegular(ofSize: 9)
    temperature.text = "주변 날씨 온도"

    temperatureValue.textColor = .text
    temperatureValue.font = .MontserratRegular(ofSize: 9)
    temperatureValue.text = "27℃"

    airCondition.textColor = .text
    airCondition.font = .MontserratRegular(ofSize: 9)
    airCondition.text = "주변공기질"

    airConditionValue.textColor = .text
    airConditionValue.font = .MontserratRegular(ofSize: 9)
    airConditionValue.text = "10 pm"

    self.addSubview(icon)
    self.addSubview(address)
    self.addSubview(coordinate)
    self.addSubview(coordinateValue)
    self.addSubview(temperature)
    self.addSubview(temperatureValue)
    self.addSubview(airCondition)
    self.addSubview(airConditionValue)
  }

  override func setAutoLayout() {
    icon.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self).inset(15)
      make.top.equalTo(self).inset(18)
    }
    address.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self.icon.snp.trailing).offset(1)
      make.trailing.equalTo(self).inset(24)
      make.centerY.equalTo(self.icon)
    }
    coordinate.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self.icon)
      make.top.equalTo(self.icon.snp.bottom).offset(13)
    }
    coordinateValue.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self.coordinate.snp.trailing).offset(10)
      make.top.bottom.equalTo(self.coordinate)
      make.trailing.equalTo(self).inset(24)
    }
    temperature.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self.coordinate)
      make.top.equalTo(self.coordinate.snp.bottom).offset(6)

    }
    temperatureValue.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.bottom.equalTo(self.temperature)
      make.trailing.equalTo(self).inset(24)
    }
    airCondition.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.equalTo(self.temperature)
      make.top.equalTo(self.temperature.snp.bottom).offset(5)
      make.bottom.equalTo(self).offset(14)
    }
    airConditionValue.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.bottom.equalTo(self.airCondition)
      make.trailing.equalTo(self).inset(24)
    }
  }

}
