//
//  MapView.swift
//  Hub
//
//  Created by ncn on 2023/02/13.
//

import GoogleMaps
import UIKit

class MapContainView: BaseView {
//  let titleView = ContentTitleView(type: .refresh)
  let frameView = UIView()
  #if !targetEnvironment(simulator)
    var mapView: GMSMapView?
    var tappedMarker: GMSMarker = GMSMarker()

    var infoView = MarkerInfoView()

    lazy var infoWindow: UIView = {
      let view = UIView()
      let completedYearLbl = UILabel()
      let architectLbl = UILabel()
      view.frame = CGRect(x: 0, y: 0, width: 100, height: 80)
      view.backgroundColor = .darkGray

      return view
    }()
  #endif
//  var disableView = UIView()
//  var disableLabel: UILabel = {
//    let label = UILabel()
//    label.text = "Tracing.."
//    label.font = .systemFont(ofSize: 12, weight: .bold)
//    label.textColor = .black
//    return label
//  }()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.addSubview(frameView)
    setMapView()
  }

  override func setAutoLayout() {
    frameView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.left.right.bottom.equalTo(self)
    }
  }

  func setMapView() {
    #if !targetEnvironment(simulator)
      var latitude: Double = Current.companyLocation.0
      var longtitude: Double = Current.companyLocation.1
      if let value = AppManager.shared.locationValue {
        latitude = value.latitude
        longtitude = value.longitude
      }
      Log.info(to: "latitude: \(latitude), longtitude: \(longtitude)")
      let camera = GMSCameraPosition.camera(
        withLatitude: latitude,
        longitude: longtitude,
        zoom: 15.0
      )
    let mapView = GMSMapView.map(withFrame: CGRect(x: 0, y: 0, width: 100, height: 100), camera: camera)

      let location = CLLocationCoordinate2DMake(latitude, longtitude)
      self.tappedMarker = GMSMarker(position: location)
      self.tappedMarker.map = mapView
      self.tappedMarker.icon = UIImage(named: "main_mappin")
      self.tappedMarker.accessibilityLabel = "label"
      //        self.tappedMarker = maker
      mapView.delegate = self
      self.mapView = mapView
      frameView.addSubview(mapView)

      mapView.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.edges.equalTo(self.frameView)
      }
    #endif
  }
}

extension MapContainView: GMSMapViewDelegate {
  func setLocation(model: UdGModel, isTrace: Bool = true) {
    #if !targetEnvironment(simulator)
      if let la = model.latitude,
        let long = model.longitude,
        let map = mapView
      {
        let latitude: Double = la == 0.0 ? Current.companyLocation.0 : la
        let longtitude: Double = long == 0.0 ? Current.companyLocation.1 : long
        lLogger.debug("## la: \(la), long: \(long), latitude: \(latitude), longtitude: \(longtitude)")
        let location = CLLocationCoordinate2DMake(latitude, longtitude)
        tappedMarker.position = location

        if isTrace {
          let camera = GMSCameraPosition(target: location, zoom: 15.0)
          map.camera = camera
        }

      }
    #endif
  }

  #if !targetEnvironment(simulator)
    func mapView(_ mapView: GMSMapView, didTap marker: GMSMarker) -> Bool {
      //      let _: Int! = Int(marker.accessibilityLabel!)

      //let location = CLLocationCoordinate2D(latitude: marker.position.latitude, longitude: marker.position.longitude)
      #if DEBUG
        getAddress(position: marker.position) { [weak self] address in
          marker.title = address
          marker.snippet = "좌표: \(marker.position.latitude), \(marker.position.longitude)"
          self?.tappedMarker = marker
        }
      #endif
      //        marker.title = "title"
      //        marker.snippet = "position: \(marker.position)"

      return false
    }

    func mapView(_ mapView: GMSMapView, didChange position: GMSCameraPosition) {
      //guard let marker = tappedMarker else { return }
    }
  #endif

  func getAddress(position: CLLocationCoordinate2D, completion: @escaping (String) -> Void) {
    var latitude = position.latitude
    var longtitude = position.longitude
    if latitude <= 0.0 || longtitude <= 0.0 {
      if let value = AppManager.shared.locationValue {
        latitude = value.latitude
        longtitude = value.longitude
      }
    }

    let location = CLLocationCoordinate2DMake(latitude, longtitude)
    #if !targetEnvironment(simulator)
      let geocoder = GMSGeocoder()
      geocoder.reverseGeocodeCoordinate(location) { response, error in
        if error != nil {
          Log.message(to: "reverse geodcode fail: \(error!.localizedDescription)")
        } else {
          if let places = response?.results() {
            if let place = places.first {
              if let lines = place.lines {
                Log.message(to: "GEOCODE: Formatted Address: \(lines)")
                var string = ""
                for obj in lines {
                  if string.count > 0 {
                    string = String(format: "%@ %@", string, obj)
                  } else {
                    string = obj
                  }
                }
                completion(string)
              }
            } else {
              Log.message(to: "GEOCODE: nil first in places")
            }
          } else {
            Log.message(to: "GEOCODE: nil in places")
          }
        }
      }
    #endif
  }
}
