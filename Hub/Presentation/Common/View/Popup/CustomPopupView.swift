//
//  CustomPopupView.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 7/22/24.
//

import SwiftUI

struct CustomPopupView: UIViewControllerRepresentable {
  typealias UIViewControllerType = PopupViewController

  var title: String
  var desc: String
  var isCancel: Bool
  var tapConfirm: () -> Void = {}

  func makeUIViewController(context: Context) -> PopupViewController {
    let popup = PopupViewController()
    popup.dataSetting(
      title: self.title,
      desc: self.desc,
      isCancel: self.isCancel
    )
    popup.tapConfirm = self.tapConfirm
    return popup
  }

  func updateUIViewController(
    _ uiViewController: UIViewControllerType, context: Context
  ) {

  }
}
