//
//  InitSettingItemView.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 4/29/24.
//

import UIKit

final class InitSettingItemView: UIView {
  let settingLabel = UILabel()
  let settingButton = UIButton()

  override init(frame: CGRect) {
    super.init(frame: frame)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }

  func setComponent() {
    settingLabel.textColor = .black
    settingLabel.font = .MontserratRegular(ofSize: 18)
    self.addSubview(settingLabel)

    settingButton.titleLabel?.font = .MontserratBold(ofSize: 24)
    settingButton.setTitleColor(.white, for: .normal)
    settingButton.setBackgroundColor(.blue, for: .normal)
    settingButton.roundCorners(.allCorners, radius: 15)
    self.addSubview(settingButton)

  }

  func setAutoLayout() {
    settingLabel.snp.makeConstraints { make in
      make.top.centerX.equalToSuperview()
      make.height.equalTo(24)
    }
    settingButton.snp.makeConstraints { make in
      make.top.equalTo(settingLabel.snp.bottom).offset(5)
      make.leading.trailing.bottom.equalToSuperview()
      make.height.equalTo(30)
    }
  }

}
