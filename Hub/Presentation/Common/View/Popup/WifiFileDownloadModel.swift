//
//  FileDownloadModel.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/09/13.
//

import Foundation

enum WifiFileDownloadModel {
  struct Request: Codable {
    let header: HeaderModel
    let filedownload: FileDownload

    struct FileDownload: Codable {
      let path: String
      let name: String
    }
  }

  struct Response: Codable {
    let file: Data
  }
}

enum FirmwareDownloadModel {
  struct Request: Codable {
    let header: HeaderModel
    let newfirmware: NewFirmware

    struct NewFirmware: Codable {
      let path: String
    }
  }

  struct Response: Codable {
    let file: Data
  }
}
