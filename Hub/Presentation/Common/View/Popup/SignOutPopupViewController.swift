//
//  SignOutPopupViewController.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 3/18/24.
//

import RxCocoa
import RxSwift
import SnapKit
import UIKit

final class SignOutPopupViewController: UIViewController {
  let disposedBag = DisposeBag()
  let containerView = UIView()
  let titleLabel = UILabel()
  let descriptionLabel = UILabel()

  let signOutField = UITextField()
  let underLine = UIView()

  let buttonStack = UIStackView()
  let confirmBtn = UIButton()
  let cancelBtn = UIButton()

  let failSignOut = PublishSubject<Void>()

  override func viewDidLoad() {
    super.viewDidLoad()
    setComponent()
    setAutoLayout()
    bind()
  }

  func bind() {
    cancelBtn.rx.tap
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] in
        self?.willMove(toParent: nil)
        self?.view.removeFromSuperview()
        self?.removeFromParent()
      })
      .disposed(by: disposedBag)

    failSignOut
      .map { UIColor(hexCode: "ff744d") }
      .bind(to: underLine.rx.backgroundColor)
      .disposed(by: disposedBag)
  }

  func setComponent() {
    self.view.backgroundColor = .black.withAlphaComponent(0.7)

    containerView.roundCorners(.allCorners, radius: 10)
    containerView.backgroundColor = .customDarkGray
    self.view.addSubview(containerView)

    titleLabel.font = .MontserratBold(ofSize: 24)
    titleLabel.textColor = .customSoftBlue
    titleLabel.numberOfLines = 0
    titleLabel.text = L.withdraw_account_dialog_title.localized
    containerView.addSubview(titleLabel)

    descriptionLabel.font = .MontserratRegular(ofSize: 14)
    descriptionLabel.textColor = .text
    descriptionLabel.numberOfLines = 0
    descriptionLabel.text = L.withdraw_account_dialog_message.localized

    containerView.addSubview(descriptionLabel)

    signOutField.font = .MontserratRegular(ofSize: 14)
    signOutField.textColor = .text
    containerView.addSubview(signOutField)

    underLine.backgroundColor = .white
    containerView.addSubview(underLine)

    buttonStack.distribution = .fillEqually
    buttonStack.spacing = 10
    buttonStack.axis = .horizontal
    containerView.addSubview(buttonStack)

    confirmBtn.setTitle(L.confirm.localized, for: .normal)
    confirmBtn.setTitleColor(.white, for: .normal)
    confirmBtn.setBackgroundColor(.customSoftBlue, for: .normal)
    confirmBtn.roundCorners(.allCorners, radius: 24)
    buttonStack.addArrangedSubview(confirmBtn)

    cancelBtn.setTitle(L.cancel.localized, for: .normal)
    cancelBtn.setTitleColor(.white, for: .normal)
    cancelBtn.setBackgroundColor(.customSteel, for: .normal)
    cancelBtn.roundCorners(.allCorners, radius: 24)
    buttonStack.addArrangedSubview(cancelBtn)

  }
  func setAutoLayout() {
    containerView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      make.leading.equalToSuperview().inset(30)
    }
    titleLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalToSuperview().inset(40)
      make.height.greaterThanOrEqualTo(30)
    }
    descriptionLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalTo(titleLabel.snp.bottom).offset(22)
      make.height.greaterThanOrEqualTo(60)
    }
    signOutField.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalTo(descriptionLabel.snp.bottom).offset(22)
      make.height.equalTo(40)
    }
    underLine.snp.makeConstraints { make in
      make.leading.trailing.bottom.equalTo(signOutField)
      make.height.equalTo(1)
    }

    buttonStack.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalTo(signOutField.snp.bottom).offset(15)
      make.bottom.equalToSuperview().inset(20)
      make.height.equalTo(48)
    }
  }
}
