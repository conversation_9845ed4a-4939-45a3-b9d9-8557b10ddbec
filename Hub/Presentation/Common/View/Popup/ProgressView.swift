//
//  ProgressView.swift
//  Hub
//
//  Created by leejungchul on 2023/09/13.
//

import SnapKit
import UIKit

final class ProgressView: BaseView {
  let percentLabel = UILabel()
  let percentBar = UIView()

  init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .linePressed

    percentBar.backgroundColor = .vueroidBlue
    percentBar.roundCorners(.allCorners, radius: 7.5)
    self.addSubview(percentBar)

    percentLabel.textColor = .text
    percentLabel.textAlignment = .center
    percentLabel.font = .MontserratRegular(ofSize: 10)
    self.addSubview(percentLabel)
  }

  override func setAutoLayout() {
    percentBar.snp.makeConstraints { make in
      make.leading.top.bottom.equalToSuperview()
      make.width.equalTo(0)
    }

    percentLabel.snp.makeConstraints { make in
      make.centerY.top.bottom.equalTo(percentBar)
      make.trailing.equalToSuperview().offset(-10)
    }
  }

  // 퍼센트 업데이트 함수
  func updateProgress(percent: Double) {
    // 퍼센트 값을 업데이트하고 UI에 반영합니다.
    percentLabel.text = "\(String(Int(percent * 100)))%"
    percentLabel.sizeToFit()

    // percentBar의 너비를 퍼센트에 따라 업데이트합니다.
    let maxWidth = bounds.width  // 가장자리 여백을 고려합니다.
    let newWidth = maxWidth * CGFloat(percent)
    percentBar.snp.updateConstraints { make in
      make.width.equalTo(newWidth)
    }

//    percentLabel.snp.updateConstraints { make in
//      make.leading.equalTo(percentBar.snp.trailing).offset(5)
//    }

    // UI 업데이트를 애니메이션화할 수 있습니다.
    //    UIView.animate(withDuration: 0.1) {
    //      self.layoutIfNeeded()
    //    }
  }

}
