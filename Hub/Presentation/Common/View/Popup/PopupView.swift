//
//  PopupView.swift
//  Hub
//
//  Created by leejung<PERSON><PERSON> on 2023/09/07.
//

import SnapKit
import SwiftUI
import UIKit

final class PopupView: BaseView {

  let titleLabel = UILabel()
  let descriptionLabel = UILabel()
  let buttonStack = UIStackView()
  let confirmBtn = UIButton()
  let cancelBtn = UIButton()

  var tapConfirm: () -> Void = {}

  init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()

  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func dataSetting(title: String, desc: String, isCancel: Bool) {
    self.titleLabel.text = title
    self.descriptionLabel.text = desc
    self.cancelBtn.isHidden = !isCancel
  }

  @objc private func addConfirmAction() {
    tapConfirm()
    removeFromSuperview()
  }

  @objc private func addCancelAction() {
    removeFromSuperview()
  }

  override func setComponent() {
    self.roundCorners(.allCorners, radius: 10)
    self.backgroundColor = .customDarkGray
    //        self.view.addSubview(containerView)

    titleLabel.font = .MontserratBold(ofSize: 24)
    titleLabel.textColor = .customSoftBlue
    titleLabel.numberOfLines = 0
    self.addSubview(titleLabel)

    descriptionLabel.font = .MontserratRegular(ofSize: 14)
    descriptionLabel.textColor = .text
    descriptionLabel.numberOfLines = 0
    self.addSubview(descriptionLabel)

    buttonStack.distribution = .fillEqually
    buttonStack.spacing = 10
    buttonStack.axis = .horizontal
    self.addSubview(buttonStack)

    confirmBtn.setTitle(L.confirm.localized, for: .normal)
    confirmBtn.setTitleColor(.white, for: .normal)
    confirmBtn.setBackgroundColor(.customSoftBlue, for: .normal)
    confirmBtn.roundCorners(.allCorners, radius: 24)
    confirmBtn.addTarget(self, action: #selector(addConfirmAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(confirmBtn)

    cancelBtn.setTitle(L.cancel.localized, for: .normal)
    cancelBtn.setTitleColor(.white, for: .normal)
    cancelBtn.setBackgroundColor(.customSteel, for: .normal)
    cancelBtn.roundCorners(.allCorners, radius: 24)
    cancelBtn.addTarget(self, action: #selector(addCancelAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(cancelBtn)

  }

  override func setAutoLayout() {
    titleLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalToSuperview().inset(40)
      make.height.greaterThanOrEqualTo(30)
    }
    descriptionLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalTo(titleLabel.snp.bottom).offset(22)
      make.height.greaterThanOrEqualTo(60)
    }
    buttonStack.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(35)
      make.top.equalTo(descriptionLabel.snp.bottom).offset(15)
      make.bottom.equalToSuperview().inset(20)
      make.height.equalTo(48)
    }
  }
}
