//
//  PopupViewController.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 2023/09/07.
//

import SnapKit
import SwiftUI
import UIKit

final class PopupViewController: UIViewController {

  let containerView = UIView()
  let titleLabel = UILabel()
  let descriptionLabel = UILabel()
  let buttonStack = UIStackView()
  let confirmBtn = UIButton()
  let cancelBtn = UIButton()

  var tapConfirm: (() -> Void)?
  var textAlignment: NSTextAlignment = .center

  let padding = 20.0

  override func viewDidLoad() {
    super.viewDidLoad()
    setComponent()
    setAutoLayout()
  }

  func dataSetting(title: String, desc: String, isCancel: Bool) {
    self.titleLabel.text = title
    self.descriptionLabel.text = desc
    self.cancelBtn.isHidden = !isCancel
    
    // 특정 텍스트에 대해서만 좌측 정렬 적용
    //FIXME: - 정렬 조건 다시 봐야함. 팀장 말로는 대부분 우측 정렬이 맞음
//    if title == L.system_upgrade.localized {
//      self.titleLabel.textAlignment = .left
//      self.descriptionLabel.textAlignment = .left
//    } else {
//      self.titleLabel.textAlignment = .center
//      self.descriptionLabel.textAlignment = .center
//    }
    self.titleLabel.textAlignment = .left
    self.descriptionLabel.textAlignment = .left
  }

  @objc private func addConfirmAction() {
    self.dismiss(animated: true) { [weak self] in
      self?.tapConfirm?()
    }
  }

  @objc private func addCancelAction() {
    self.dismiss(animated: true)
  }

  func setComponent() {
    self.view.backgroundColor = .black.withAlphaComponent(0.6)

//    containerView.roundCorners(.allCorners, radius: padding)
    containerView.backgroundColor = .white
    containerView.layer.cornerRadius = 10
    containerView.layer.shadowColor = UIColor.black.cgColor
    containerView.layer.shadowOpacity = 0.2
    containerView.layer.shadowOffset = CGSize(width: 0, height: 2)

    self.view.addSubview(containerView)

    titleLabel.font = .pretendard(ofSize: 16, weight: .bold)
    titleLabel.textColor = .mainBlack
    titleLabel.numberOfLines = 1
    titleLabel.textAlignment = .left
    containerView.addSubview(titleLabel)

    descriptionLabel.font = .pretendard(ofSize: 15, weight: .regular)
    descriptionLabel.textColor = .mainBlack
    descriptionLabel.numberOfLines = 0
    descriptionLabel.textAlignment = .center
    containerView.addSubview(descriptionLabel)

    buttonStack.distribution = .fillEqually
    buttonStack.spacing = 10
    buttonStack.axis = .horizontal
    containerView.addSubview(buttonStack)

    cancelBtn.setTitle(L.cancel.localized, for: .normal)
    cancelBtn.setTitleColor(.mainBlack, for: .normal)
    cancelBtn.titleLabel?.font = .pretendard(ofSize: 13, weight: .bold)
    cancelBtn.setBackgroundColor(.grayButton, for: .normal)
    cancelBtn.roundCorners(.allCorners, radius: 5)
    cancelBtn.addTarget(self, action: #selector(addCancelAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(cancelBtn)

    confirmBtn.setTitle(L.ok.localized, for: .normal)
    confirmBtn.setTitleColor(.white, for: .normal)
    confirmBtn.titleLabel?.font = .pretendard(ofSize: 13, weight: .bold)
    confirmBtn.setBackgroundColor(.vueroidBlue, for: .normal)
    confirmBtn.roundCorners(.allCorners, radius: 5)
    confirmBtn.addTarget(self, action: #selector(addConfirmAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(confirmBtn)
  }

  func setAutoLayout() {
    containerView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      make.width.greaterThanOrEqualTo(260)
//      make.leading.greaterThanOrEqualToSuperview().inset(padding)
    }

    titleLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(padding)
      make.top.equalToSuperview().inset(padding)
      make.height.greaterThanOrEqualTo(24)
    }

    descriptionLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(padding)
      make.top.equalTo(titleLabel.snp.bottom).offset(padding)
      make.height.greaterThanOrEqualTo(24)
    }

    buttonStack.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(padding)
      make.top.equalTo(descriptionLabel.snp.bottom).offset(padding)
      make.centerX.equalToSuperview()
      make.bottom.equalToSuperview().inset(padding)
      make.height.equalTo(42)
    }
  }
}
