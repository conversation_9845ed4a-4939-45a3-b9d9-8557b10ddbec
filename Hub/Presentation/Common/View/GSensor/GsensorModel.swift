
//
//  GsensorModel.swift
//  Hub
//
//  Created by leejungchul on 2023/08/17.
//

import Foundation

enum WebRTC {
  struct Header: Codable {
    let from: String
    let type: String
    let res: Int
    let rmsg: String
    let cmd: String
    let serial: String?
    let time: String?
  }
}

enum Gsensor {
  struct Response: Codable {
    let header: WebRTC.Header
    let udata: Udata
  }

  struct Udata: Codable {
    let gps: GpsData
    let gsensor: GsensorData
  }

  struct GpsData: Codable {
    let lat: Double
    let long: Double
    let speed: Int
  }

  struct GsensorData: Codable {
    let x: Double
    let y: Double
    let z: Double
  }
}
