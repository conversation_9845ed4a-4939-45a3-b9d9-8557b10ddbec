//
//  GSensorContainView.swift
//  Hub
//
//  Created by ncn on 2023/06/01.
//

import Charts
import DGCharts
import UIKit

class GSensorContainView: BaseView {
  public let chartView = LineChartView()

  var chartData = [Gsensor.GsensorData]()
  var yVals1 = FixedFIFOArray<ChartDataEntry>(maxSize: 60)
  var yVals2 = FixedFIFOArray<ChartDataEntry>(maxSize: 60)
  var yVals3 = FixedFIFOArray<ChartDataEntry>(maxSize: 60)

  var xValueLabel: UILabel = {
    let label = UILabel()
    label.font = .body4
    label.textColor = .mainRed
    label.sizeToFit()
    label.text = "X -0.000"
    return label
  }()

  var yValueLabel: UILabel = {
    let label = UILabel()
    label.font = .body4
    label.textColor = .mainBlue
    label.sizeToFit()
    label.text = "Y -0.000"
    return label
  }()

  var zValueLabel: UILabel = {
    let label = UILabel()
    label.font = .body4
    label.textColor = .greenDE
    label.sizeToFit()
    label.text = "Z -0.000"
    return label
  }()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  deinit {
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .clear
    self.addBody(chartView)

    HStackView(spacing: 8, pinInSuperView: false) {
      WSpacer()
      xValueLabel
      yValueLabel
      zValueLabel
    }.pin.height(12).end(offset: -8).bottom().activate()

    setChart()
  }

  override func setAutoLayout() {}
}

extension GSensorContainView {
  func setChart() {
    chartView.delegate = self
    chartView.chartDescription.enabled = false
    chartView.dragEnabled = false
    chartView.setScaleEnabled(false)
    chartView.pinchZoomEnabled = false
    chartView.legend.enabled = false

    let xAxis = chartView.xAxis
    xAxis.labelFont = .pretendard(ofSize: 11)!
    xAxis.labelTextColor = .white
    xAxis.drawAxisLineEnabled = true
    xAxis.drawGridLinesEnabled = false

    let leftAxis = chartView.leftAxis
    leftAxis.axisMaximum = 2.5
    leftAxis.axisMinimum = -2.5
    leftAxis.drawLabelsEnabled = false
    leftAxis.drawZeroLineEnabled = true
    leftAxis.drawAxisLineEnabled = false
    leftAxis.drawGridLinesEnabled = true
    leftAxis.gridLineWidth = 1.0  // 그리드 라인 두께
    leftAxis.setLabelCount(5, force: true)  // 7개의 라인 강제 설정

    leftAxis.granularityEnabled = true

    //    chartView.rightAxis.enabled = false
    let rightAxis = chartView.rightAxis
    rightAxis.axisMaximum = 2.5
    rightAxis.axisMinimum = -2.5
    rightAxis.drawLabelsEnabled = false
    rightAxis.drawGridLinesEnabled = false
    rightAxis.drawZeroLineEnabled = true
    rightAxis.drawAxisLineEnabled = false
    rightAxis.granularityEnabled = true

    chartView.animate(xAxisDuration: 0.5)
  }

  func setData(data: Gsensor.GsensorData) {
    self.chartData.append(data)
    setDataLabel(data: data)
    let yValueOffset = data.y - 0.002
    self.yVals1.append(ChartDataEntry(x: Double(chartData.count), y: data.x))
    self.yVals2.append(ChartDataEntry(x: Double(chartData.count), y: yValueOffset))
    self.yVals3.append(ChartDataEntry(x: Double(chartData.count), y: data.z))
    setChartData()
  }

  func setDataLabel(data: Gsensor.GsensorData) {
//    lLogger.debug("#@ GSensor X: \(data.x), Y: \(data.y), Z: \(data.z)")

    // data.x를 0.xxx 형식으로
    let xValue = String(format: "%.3f", data.x)
    let yValue = String(format: "%.3f", data.y)
    let zValue = String(format: "%.3f", data.z)
    xValueLabel.text = "X \(xValue)"
    yValueLabel.text = "Y \(yValue)"
    zValueLabel.text = "Z \(zValue)"
  }

  private func setChartData() {
    let set1 = LineChartDataSet(entries: yVals1._array, label: "X")
    set1.axisDependency = .left
    set1.setColor(.mainRed)
    set1.setCircleColor(.white)
    set1.lineWidth = 1.5
    set1.circleRadius = 3
    set1.fillAlpha = 65 / 255
    set1.fillColor = .red
    set1.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set1.drawCircleHoleEnabled = false
    set1.drawCirclesEnabled = false
    set1.drawValuesEnabled = false

    let set2 = LineChartDataSet(entries: yVals2._array, label: "Y")
    let yColor: UIColor = .mainBlue
    set2.axisDependency = .right
    set2.setColor(yColor)
    set2.setCircleColor(.white)
    set2.lineWidth = 1.5
    set2.circleRadius = 3
    set2.fillAlpha = 65 / 255
    set2.fillColor = yColor
    set2.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set2.drawCircleHoleEnabled = false
    set2.drawCirclesEnabled = false
    set2.drawValuesEnabled = false

    let set3 = LineChartDataSet(entries: yVals3._array, label: "Z")
    let zColor: UIColor = .greenDE
    //    set3.axisDependency = .right
    set3.setColor(zColor)
    set3.setCircleColor(.white)
    set3.lineWidth = 1.5
    set3.circleRadius = 3
    set3.fillAlpha = 65 / 255
    set3.fillColor = zColor
    set3.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set3.drawCircleHoleEnabled = false
    set3.drawCirclesEnabled = false
    set3.drawValuesEnabled = false

    let data: LineChartData = [set1, set2, set3]
    data.setValueTextColor(.white)
    data.setValueFont(.body4)

    chartView.data = data
  }

  func setData(wifiVod: GSensorWifiVodModel?) {
    guard let gsensors = wifiVod else { return }

    chartData = []
    yVals1 = FixedFIFOArray<ChartDataEntry>(maxSize: 1200)
    yVals2 = FixedFIFOArray<ChartDataEntry>(maxSize: 1200)
    yVals3 = FixedFIFOArray<ChartDataEntry>(maxSize: 1200)

    for (idx, gsensor) in gsensors.gSensorData.enumerated() {
      self.yVals1.append(ChartDataEntry(x: Double(idx), y: gsensor.x))
      self.yVals2.append(ChartDataEntry(x: Double(idx), y: gsensor.y))
      self.yVals3.append(ChartDataEntry(x: Double(idx), y: gsensor.z))
    }
    setChartData()

    fLogger.info("gps data count: \(gsensors.gSensorData.count)")
    chartView.animate(xAxisDuration: 0.5)
  }

  func setData(wifiLive: GSensorWifiLiveModel?) {
    guard let gsensors = wifiLive else { return }
    let convData = Gsensor.GsensorData(
      x: gsensors.gsensor.x.toDouble(),
      y: gsensors.gsensor.y.toDouble(),
      z: gsensors.gsensor.z.toDouble()
    )
    setData(data: convData)
  }

  func setData(cloud: MetaDateModel) {
    self.yVals1.removeAll()
    self.yVals1._array = cloud.g_x.enumerated().map { ChartDataEntry(x: Double($0), y: $1) }

    self.yVals2.removeAll()
    self.yVals2._array = cloud.g_y.enumerated().map { ChartDataEntry(x: Double($0), y: $1) }

    self.yVals3.removeAll()
    self.yVals3._array = cloud.g_z.enumerated().map { ChartDataEntry(x: Double($0), y: $1) }

    let set1 = LineChartDataSet(entries: yVals1._array, label: "X")
    set1.axisDependency = .left
    set1.setColor(.mainRed)
    set1.setCircleColor(.white)
    set1.lineWidth = 2
    set1.circleRadius = 3
    set1.fillAlpha = 65 / 255
    set1.fillColor = .mainRed
    set1.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set1.drawCircleHoleEnabled = false
    set1.drawCirclesEnabled = false
    set1.drawValuesEnabled = false

    let set2 = LineChartDataSet(entries: yVals2._array, label: "Y")
    set2.axisDependency = .right
    let yColor: UIColor = .mainBlue
    set2.setColor(yColor)
    set2.setCircleColor(.white)
    set2.lineWidth = 2
    set2.circleRadius = 3
    set2.fillAlpha = 65 / 255
    set2.fillColor = yColor
    set2.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set2.drawCircleHoleEnabled = false
    set2.drawCirclesEnabled = false
    set2.drawValuesEnabled = false

    let set3 = LineChartDataSet(entries: yVals3._array, label: "Z")
    //    set3.axisDependency = .right
    set3.setColor(.greenDE)
    set3.setCircleColor(.white)
    set3.lineWidth = 2
    set3.circleRadius = 3
    set3.fillAlpha = 65 / 255
    set3.fillColor = .greenDE
    set3.highlightColor = UIColor(red: 244 / 255, green: 117 / 255, blue: 117 / 255, alpha: 1)
    set3.drawCircleHoleEnabled = false
    set3.drawCirclesEnabled = false
    set3.drawValuesEnabled = false

    let data: LineChartData = [set1, set2, set3]

    data.setValueTextColor(.white)
    data.setValueFont(.systemFont(ofSize: 9))

    chartView.data = data
  }

  func resetData() {
    self.yVals1._array.removeAll()
    self.yVals2._array.removeAll()
    self.yVals3._array.removeAll()
    chartView.data = nil
  }
}

extension GSensorContainView: ChartViewDelegate {
  func chartValueSelected(_ chartView: ChartViewBase, entry: ChartDataEntry, highlight: Highlight) {
    print("##@@ data: \(entry.data) x:\(entry.x) y: \(entry.y), highlight: \(highlight)")
  }

  func chartValueNothingSelected(_ chartView: ChartViewBase) {
    print("##@@ data: \(chartView.data)")
  }

  func chartScaled(_ chartView: ChartViewBase, scaleX: CGFloat, scaleY: CGFloat) {

  }

  func chartTranslated(_ chartView: ChartViewBase, dX: CGFloat, dY: CGFloat) {

  }
}
