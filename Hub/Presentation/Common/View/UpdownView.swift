//
//  UpdownView.swift
//  Hub
//
//  Created by leejung<PERSON>l on 2023/10/18.
//

import SnapKit
import UIKit

final class UpdownView: BaseView {

  let stackView = UIStackView()
  let upButton = UIButton()
  let downButton = UIButton()

  required init?(coder: NSCoder) {
    fatalError()
  }

  public init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }

  override func setComponent() {
    self.backgroundColor = .clear

    stackView.axis = .vertical
    stackView.distribution = .fillEqually
    stackView.alignment = .center
    stackView.spacing = 7
    self.addSubview(stackView)

    upButton.setImage(.pageUp, for: .normal)
    upButton.backgroundColor = .lightGray.withAlphaComponent(0.8)
    upButton.roundCorners(.allCorners, radius: 25)
    stackView.addArrangedSubview(upButton)

    downButton.setImage(.pageDown, for: .normal)
    downButton.backgroundColor = .lightGray.withAlphaComponent(0.8)
    downButton.roundCorners(.allCorners, radius: 25)
    stackView.addArrangedSubview(downButton)
  }

  override func setAutoLayout() {
    stackView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }

    upButton.snp.makeConstraints { make in
      make.width.height.equalTo(50)
    }

    downButton.snp.makeConstraints { make in
      make.width.height.equalTo(50)
    }
  }

}
