//
//  CalenderHeaderView.swift
//  Hub
//
//  Created by ncn on 2023/05/02.
//

import RxCocoa
import UIKit

class CalenderHeaderView: BaseView {
  let collectionView: UICollectionView = UICollectionView(
    frame: .zero,
    collectionViewLayout: UICollectionViewFlowLayout.init())
  let listItem: BehaviorRelay<[String]> = BehaviorRelay(value: [])
  var menuItems: [String]? {
    didSet {
      guard let items = self.menuItems else {
        return
      }
      self.listItem.accept(items)
      self.collectionView.reloadData()
    }
  }

  public init(items: [String]? = nil) {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  func setItems(items: [String]) {
    self.menuItems = items
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    collectionView.bounces = false
    collectionView.register(cellType: CollectionMenuCell.self)
    collectionView.alwaysBounceVertical = false
    collectionView.alwaysBounceHorizontal = false
    collectionView.isScrollEnabled = true
    collectionView.showsVerticalScrollIndicator = false
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.isPagingEnabled = true
    collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.backgroundColor = .clear
    self.addSubview(collectionView)
    setDataSource()
  }

  override func setAutoLayout() {
    collectionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }
  }
}

extension CalenderHeaderView {
  private func setDataSource() {
    listItem.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: CollectionMenuCell.self)
        cell.isEnableSelected = false
        cell.setData(name: element)
        return cell
      }.disposed(by: self.disposedBag)
  }
}

extension CalenderHeaderView: UICollectionViewDelegate {
  func numberOfSections(in collectionView: UICollectionView) -> Int {
    return 1
  }

  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    let list = self.listItem.value
    return list.count
  }
}

extension CalenderHeaderView: UICollectionViewDelegateFlowLayout {
  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    sizeForItemAt indexPath: IndexPath
  ) -> CGSize {
    let list = self.listItem.value
    let count: CGFloat = CGFloat(list.count)
    let size = CGSize(
      width: collectionView.bounds.width / count, height: collectionView.bounds.height)
    return size
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumLineSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumInteritemSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }
}
