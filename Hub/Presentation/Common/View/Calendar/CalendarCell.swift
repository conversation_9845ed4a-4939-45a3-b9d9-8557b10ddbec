//
//  CalenderCell.swift
//  Hub
//
//  Created by ncn on 2023/03/28.
//

import UIKit

class CalendarCell: BaseCollectionViewCell {
  public static var identifier: String = "CalendarCell"
  let label = UILabel()
  let tagView = UIView()
  let maker = UIView()

  override init(frame: CGRect) {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    tagView.backgroundColor = .background
    tagView.isHidden = true
    self.addSubview(tagView)

    label.textAlignment = .center
    label.font = .MontserratMedium(ofSize: 14)
    self.addSubview(label)

    maker.backgroundColor = .customOrange
    maker.layer.cornerRadius = 2
    maker.isHidden = true
    self.addSubview(maker)
  }

  override func setAutoLayout() {
    let size = 24 * UIScreen.ratio
    tagView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(size)
      make.center.equalTo(self)
    }

    label.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }

    maker.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerX.equalTo(self)
      make.width.height.equalTo(5)
      make.top.equalTo(self.label.snp.bottom).offset(-3)
    }
  }
}

extension CalendarCell {
  func configureCell(day: CalanderDayModel) {
    label.text = day.dayNumber
    label.textColor = !day.isNextMonth && !day.isPreviousMonth ? .white : .gray
    maker.isHidden = !day.isDrive
  }
}
