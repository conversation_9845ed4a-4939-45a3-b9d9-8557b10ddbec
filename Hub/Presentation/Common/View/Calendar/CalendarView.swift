//
//  CalendarView.swift
//  Hub
//
//  Created by ncn on 2023/03/28.
//

import RxCocoa
import RxSwift
import UIKit

enum CalendarViewType {
  case day
  case week
  case month
  case year
}

class CalendarView: BaseView {
  let headerView = CalenderHeaderView()
  let collectionView: UICollectionView = UICollectionView(
    frame: .zero,
    collectionViewLayout: UICollectionViewFlowLayout.init())
  let listItem: BehaviorRelay<[CalanderDayModel]> = BehaviorRelay(value: [])
  var menuItems: [CalanderDayModel]? {
    didSet {
      guard let items = self.menuItems else {
        return
      }
      self.listItem.accept(items)
      self.collectionView.reloadData()
      let weaks = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"]
      self.headerView.listItem.accept(weaks)
    }
  }

  var rxSelectedDay = PublishSubject<CalanderDayModel>()
  var type: CalendarViewType = .day {
    didSet {
    }
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
    setRxFunction()
    setDataSource()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    headerView.setAutoLayout()
    self.addSubview(headerView)

    collectionView.backgroundColor = .clear
    collectionView.translatesAutoresizingMaskIntoConstraints = false
    collectionView.alwaysBounceVertical = false
    collectionView.alwaysBounceHorizontal = false
    collectionView.isScrollEnabled = false
    collectionView.showsVerticalScrollIndicator = false
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.isPagingEnabled = false
    collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.register(CalendarCell.self, forCellWithReuseIdentifier: CalendarCell.identifier)
    addSubview(collectionView)
  }

  override func setAutoLayout() {
    let height = 16 * UIScreen.ratio
    headerView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.top.right.equalTo(self)
      make.height.equalTo(height)
    }

    collectionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.headerView.snp.bottom)
      make.left.right.bottom.equalTo(self)
    }
  }

  override func setRxFunction() {
    collectionView.rx.itemSelected
      .subscribe(onNext: { [weak self] indexPath in
        guard let self = self else { return }
        let array = self.listItem.value
        let item = array[indexPath.row]
        self.rxSelectedDay.onNext(item)
      })
      .disposed(by: self.disposedBag)
  }

  func setCalendarType(type: CalendarViewType) {

  }
}

extension CalendarView {
  private func setDataSource() {
    listItem.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: CalendarCell.self)
        cell.configureCell(day: element)
        return cell
      }.disposed(by: self.disposedBag)
  }
}

extension CalendarView: UICollectionViewDelegate {
  func numberOfSections(in collectionView: UICollectionView) -> Int {
    return 1
  }

  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    let list = self.listItem.value
    return list.count
  }
}

extension CalendarView: UICollectionViewDelegateFlowLayout {
  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    sizeForItemAt indexPath: IndexPath
  ) -> CGSize {
    let size = CGSize(
      width: collectionView.bounds.width / 7, height: collectionView.bounds.height / 6)
    return size
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumLineSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumInteritemSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }
}
