//
//  CalenderHeaderCell.swift
//  Hub
//
//  Created by ncn on 2023/03/28.
//

import UIKit

class CalendarHeaderCell: BaseCollectionViewCell {
  public static var identifier: String = "CalenderHeaderCell"
  let label = UILabel()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    label.textAlignment = .center
    label.font = .MontserratRegular(ofSize: 10)
    self.addSubview(label)
  }

  override func setAutoLayout() {
    label.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }
  }
}

extension CalendarHeaderCell {
  func configureCell(text: String) {
    label.text = text
  }
}
