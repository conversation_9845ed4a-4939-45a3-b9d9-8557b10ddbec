//
//  VodPlayerContainerView.swift
//  Hub
//
//  Created by ncn on 2023/03/03.
//

import UIKit

protocol PlayerContainerView { }

class VodPlayerContainerView: BaseView, UIGestureRecognizerDelegate, PlayerContainerView {
  let baseHeight = UIScreen.width * (9.0 / 16.0) + 40
  let baseWidth = UIScreen.width
  
  let handlerView: VodHandlerView
  let screenShotView = UIImageView()
  lazy var playerView: VLCPlayerView = {
    let rect = CGRect(x: 0, y: 0, width: UIScreen.width, height: baseHeight)
    let renderView = UIView(frame: rect)
    let view = VLCPlayerView(renderView: renderView)
    return view
  }()

  var shouldShowControlBar: Bool = true

  var timer: Timer?

  deinit {
    if timer != nil {
      timer?.invalidate()
      timer = nil
    }
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  public init(type: PlayerViewType) {
    handlerView = VodHandlerView(type: type)
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
    setTapGesture()
    beginTimer()
  }


  override func setComponent() {
    self.backgroundColor = .deepDarkGray
    self.addSubview(playerView)
    self.addSubview(screenShotView)
    self.addSubview(handlerView)
    handlerView.isHidden = false
  }

  override func setAutoLayout() {
    
    playerView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self)
      make.height.equalTo(baseHeight - 40)
      make.width.equalTo(baseWidth)
      make.centerX.equalTo(self)
    }

    screenShotView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.playerView)
    }

    handlerView.snp.makeConstraints { make in
      make.edges.equalToSuperview()
    }
  }
}

extension VodPlayerContainerView {
  /// string 형식의 영상 url을 주입하면, 영상을 재생
  /// startTime을 통해 영상 시작 시간을 조절할 수 있음
  /// - Parameters:
  ///   - url: 파일영상의 url
  ///   - startTime: 영상의 시작시간 (초)
  func openStream(url: String, startTime: Double? = nil) {
    vLogger.debug("open stream : \(url)")
    if playerView.isDefaultOption == false {
      playerView.isDefaultOption = true
    }
    playerView.opneStream(url: url, startTime: startTime, isLive: false)
  }

  func openScreenShot(image: UIImage) {
    if playerView.isDefaultOption == false {
      playerView.isDefaultOption = true
    }
    screenShotView.isHidden = false
    screenShotView.image = image
  }

  func openFile(path: URL, startTime: Double? = nil) {
    if playerView.isDefaultOption == true {
      playerView.isDefaultOption = false
    }
    playerView.opneFileStream(path: path, startTime: startTime)
  }

  func stop() {
    vLogger.info("player stop")
    playerView.stop()
  }
}

extension VodPlayerContainerView {
  private func setTapGesture() {
    let singleTap = UITapGestureRecognizer(target: self, action: #selector(tapHandler(_:)))
    singleTap.numberOfTapsRequired = 1
    singleTap.delegate = self

//    self.singleTap = singleTap
    self.addGestureRecognizer(singleTap)
    //        self.playerView.addGestureRecognizer(singleTap)
  }

  @objc func tapHandler(_ recognizer: UITapGestureRecognizer) {
    setHiddenHandlerView()
  }

  private func beginTimer() {
    invalidateTimer()
    timer = Timer.scheduledTimer(
      timeInterval: 5,
      target: self,
      selector: #selector( onTick(timer:) ),
      userInfo: nil,
      repeats: false
    )
  }

  private func invalidateTimer() {
    if timer != nil {
      timer?.invalidate()
      timer = nil
    }
  }

  @objc private func onTick(timer: Timer) {
    if handlerView.isHidden { return }
    setHiddenHandlerView()
  }

  private func setHiddenHandlerView() {
    // PlayerViewType에 따라 버튼 표시 여부 설정
    switch handlerView.type {
    case .vod:
      // vod일 때는 5개 버튼 모두 표시
      let isHidden = !handlerView.playButton.isHidden
      handlerView.playButton.isHidden = isHidden
      handlerView.backwardButton.isHidden = isHidden
      handlerView.forwardButton.isHidden = isHidden
      handlerView.sizeButton.isHidden = isHidden
      handlerView.controlBarView.slider.isHidden = isHidden
      handlerView.controlBarView.durationLabel.isHidden = isHidden
      handlerView.nextButton.isHidden = isHidden
      handlerView.previousButton.isHidden = isHidden
      handlerView.channelInfoImage.isHidden = true
    case .localVod:
      // localVod일 때는 play, backward, forward 버튼만 표시
      let isHidden = !handlerView.playButton.isHidden
      handlerView.playButton.isHidden = isHidden
      handlerView.backwardButton.isHidden = isHidden
      handlerView.forwardButton.isHidden = isHidden
      handlerView.sizeButton.isHidden = isHidden
      handlerView.controlBarView.slider.isHidden = isHidden
      handlerView.controlBarView.durationLabel.isHidden = isHidden
      handlerView.channelInfoImage.isHidden = isHidden
      handlerView.nextButton.isHidden = true
      handlerView.previousButton.isHidden = true
    case .snapShot, .bookmark:
      // snapshot일 때는 5개 버튼 모두 숨김
      handlerView.playButton.isHidden = true
      handlerView.backwardButton.isHidden = true
      handlerView.forwardButton.isHidden = true
      handlerView.nextButton.isHidden = true
      handlerView.previousButton.isHidden = true
      handlerView.channelInfoImage.isHidden = true
    default:
      break
    }
    
    // 버튼이 숨겨져 있을 때는 배경을 투명하게
    if handlerView.playButton.isHidden {
      handlerView.backgroundColor = .clear
      invalidateTimer()
    } else {
      handlerView.backgroundColor = UIColor.black.withAlphaComponent(0.40)
      beginTimer()
    }
  }
}

extension VodPlayerContainerView {
  func setOrientationViews(isLandscape: Bool) {
    beginTimer()
    self.backgroundColor = isLandscape ? .black : .colorRGB(47, 48, 52)

    let width = (baseWidth * 16) / 9 
    playerView.snp.updateConstraints { make in
      make.height.equalTo(isLandscape ? baseWidth : baseHeight - 40)
      make.width.equalTo(isLandscape ? width : baseWidth)
    }

    playerView.renderView?.snp.updateConstraints { make in
      make.height.equalTo(isLandscape ? baseWidth : baseHeight - 40)
      make.width.equalTo(isLandscape ? width : baseWidth)
    }
    
    // 화면 전환 시 controlBarView는 항상 보이도록
    handlerView.controlBarView.isHidden = false
  }
}

// MARK: VLC가 탭 제스쳐 막는 현상 복구
extension VodPlayerContainerView {
  func gestureRecognizer(
    _ gestureRecognizer: UIGestureRecognizer,
    shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer
  ) -> Bool {
    return true
  }
}
