//
//  EditPlayerContainerView.swift
//  Hub
//
//  Created by ncn on 2023/03/14.
//

import UIKit
import RxSwift

class EditPlayerContainerView: BaseView {
  let baseHeight = UIScreen.width * (9.0 / 16.0)
  let baseWidth = UIScreen.width
  private var disposeBag = DisposeBag()

  let sliderContainerView = RangeSliderContainerView()

  let handlerView = EditHandlerView()
  
  // 마지막 프레임을 표시할 이미지 뷰
  private let lastFrameImageView: UIImageView = {
    let imageView = UIImageView()
    imageView.contentMode = .scaleAspectFit
    imageView.backgroundColor = .black
    imageView.isHidden = true
    return imageView
  }()
  
  lazy var playerView: VLCPlayerView = {
    let rect: CGRect = CGRect(x: 0, y: 0, width: UIScreen.width, height: baseHeight)
    let renderView = UIView(frame: rect)
    renderView.backgroundColor = .mainBlack
    let view = VLCPlayerView(renderView: renderView)
    return view
  }()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()  // + 35
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.addSubview(playerView)
    self.addSubview(lastFrameImageView)
    self.addSubview(handlerView)
    self.addSubview(sliderContainerView)
    
    // 플레이어 상태 변경 구독
    playerView.rxIsState
      .subscribe(onNext: { [weak self] state in
        guard let self = self else { return }
        
        // 재생 중일 때는 마지막 프레임 이미지 뷰를 숨김
        if state == .playing {
          self.lastFrameImageView.isHidden = true
          return
        }
        
        // 재생이 끝났거나 정지되었을 때 마지막 프레임 캡처
        if state == .ended || state == .stopped {
          self.captureLastFrame()
        }
      })
      .disposed(by: disposeBag)
  }

  override func setAutoLayout() {
    Pin.activate([
      playerView.pin.height(baseHeight).width(baseWidth).centerX().centerY(offset: -50.0),
      lastFrameImageView.pin.height(baseHeight).width(baseWidth).centerX().centerY(offset: -50.0),
      handlerView.pin.size(52).below(playerView, offset: 80).start(offset: 15),
      sliderContainerView.pin.after(handlerView, offset: 15).end(offset: -15).below(playerView, offset: 80).height(52),
    ])
  }
}

extension EditPlayerContainerView {
  func openStream(url: URL) {
    lastFrameImageView.isHidden = true
    playerView.opneStream(url: url, isLive: false)
  }

  func openStreamWithoutPlay(url: URL) {
    lastFrameImageView.isHidden = true
    playerView.openStreamWithoutPlay(url: url)
  }
  
  func pause() {
    playerView.pause()
  }
  
  // 마지막 프레임 캡처 메서드
  private func captureLastFrame() {
    guard let renderView = playerView.renderView else { return }
    
    // 현재 화면을 캡처
    UIGraphicsBeginImageContextWithOptions(renderView.bounds.size, false, UIScreen.main.scale)
    renderView.drawHierarchy(in: renderView.bounds, afterScreenUpdates: true)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    
    // 캡처한 이미지가 있으면 이미지 뷰에 설정하고 표시
    if let capturedImage = image, capturedImage.size.width > 0 {
      lastFrameImageView.image = capturedImage
      lastFrameImageView.isHidden = false
    }
  }
}
