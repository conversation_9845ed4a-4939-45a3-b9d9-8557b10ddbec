//
//  PromtionView.swift
//  Hub
//
//  Created by ncn on 2023/05/03.
//

import UIKit

class PromtionDescriptionView: BaseView {
  let timeTagLabel = UILabel()
  let timeLabel = UILabel()
  let countTagLabel = UILabel()
  let countLabel = UILabel()

  var progressTime: Int = 0 {
    didSet {
      setTime()
    }
  }

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    timeTagLabel.textColor = .text
    timeTagLabel.font = .MontserratMedium(ofSize: 10)
    timeTagLabel.text = L.live_playable_time.localized
    self.addSubview(timeTagLabel)

    timeLabel.textColor = .text
    timeLabel.font = .MontserratMedium(ofSize: 10)
    timeLabel.text = "00:03:00"
    self.addSubview(timeLabel)

    countTagLabel.textColor = .text
    countTagLabel.font = .MontserratMedium(ofSize: 10)
    countTagLabel.text = L.possible_video_play_count.localized
    self.addSubview(countTagLabel)

    countLabel.textColor = .text
    countLabel.font = .MontserratMedium(ofSize: 10)
    countLabel.text = "3/5"
    self.addSubview(countLabel)
  }

  override func setAutoLayout() {
    let width = (UIScreen.width - 40 - 12) / 4

    timeTagLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(20)
      make.centerY.equalTo(self)
      make.width.equalTo(width)
    }

    timeLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.timeTagLabel.snp.right).offset(4)
      make.centerY.equalTo(self)
      make.width.equalTo(width)
    }

    countTagLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.timeLabel.snp.right).offset(4)
      make.centerY.equalTo(self)
      make.width.equalTo(width)
    }

    countLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.countTagLabel.snp.right).offset(4)
      make.centerY.equalTo(self)
      make.width.equalTo(width)
    }
  }
}

class PromtionView: BaseView {
  let subTitleLabel = UILabel()
  let titleLabel = UILabel()
  let imageView = UIImageView(image: UIImage(named: "promotion_img"))
  let descriptionView = PromtionDescriptionView()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .background

    imageView.contentMode = .scaleToFill
    self.addSubview(imageView)

    subTitleLabel.font = .MontserratBold(ofSize: 14)
    subTitleLabel.textColor = .black
    self.addSubview(subTitleLabel)

    titleLabel.font = .MontserratBold(ofSize: 17)
    titleLabel.textColor = .text
    titleLabel.textAlignment = .center
    self.addSubview(titleLabel)

    self.addSubview(descriptionView)
  }

  override func setAutoLayout() {
    //let offset =
    subTitleLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self).offset(13)
      make.centerX.equalTo(self)
    }

    let leading = 69 * UIScreen.ratio
    let trailling = 36 * UIScreen.ratio
    titleLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self).offset(42)
      //make.centerY.equalTo(self)
      make.left.equalTo(self).offset(leading)
      make.right.equalTo(self).offset(-trailling)
    }

    imageView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.top.right.equalTo(self)
      make.height.equalTo(80)
    }

    descriptionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.bottom.right.equalTo(self)
      make.height.equalTo(34)
    }
  }
}

extension PromtionDescriptionView {
  func setTime() {
    //    DispatchQueue.main.async {
    //      let mode = AppManager.shared.mode
    //      if mode == .cloud {
    //        let remainTime = (5 * 60 * 1000) - self.progressTime
    //        self.timeLabel.text = "0:" + remainTime.getTimeString()
    //      }
    //    }
  }
}
