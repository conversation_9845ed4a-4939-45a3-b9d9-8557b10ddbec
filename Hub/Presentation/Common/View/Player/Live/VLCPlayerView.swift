//
//  RTSPPlayerView.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import RxSwift
import UIKit
import VLCKitSPM
import OSLog

enum RTSPPlayerState: Int, CustomDebugStringConvertible {
  case stopped
  case opening
  case buffering
  case ended
  case error
  case playing
  case paused
  case esadded
  case esdeleted
  case changed
  
  var debugDescription: String {
    switch self {
    case .stopped:
      return "stopped"
    case .opening:
      return "opening"
    case .buffering:
      return "buffering"
    case .ended:
      return "ended"
    case .error:
      return "error"
    case .playing:
      return "playing"
    case .paused:
      return "paused"
    case .esadded:
      return "esadded"
    case .esdeleted:
      return "esdeleted"
    case .changed:
      return "changed"
    }
  }
}

class VLCPlayerView: BaseLivePlayerView {
  var mediaPlayer: VLCMediaPlayer!
  var isExpanded: Bool = true
  var state: VLCMediaPlayerState = .stopped
  var isAfterPlayingState = false

  var media: VLCMedia?

  let rxIsState = PublishSubject<RTSPPlayerState>()
  let rxTime = PublishSubject<Int>()
  let rxRemainingTime = PublishSubject<Int>()
  var remainTime: Int = 0
  var pauseTime: VLCTime?
  var pausePosition: Float = 0.0

  var isDefaultOption: Bool = true {
    didSet {
      self.mediaPlayer =
        isDefaultOption
        ? VLCMediaPlayer() : VLCMediaPlayer()
      mediaPlayer.delegate = self
    }
  }

  public init(renderView: UIView) {
    super.init()
    self.renderView = renderView
    mediaPlayer = VLCMediaPlayer()
    mediaPlayer.delegate = self
    setVlcLogger(level: .debug)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  deinit {
    self.mediaPlayer.stop()
    self.media = nil
    self.mediaPlayer = nil
    Log.info(category: .App, to: "deinit \(Self.self)")
  }

  override func setComponent() {
    renderView!.backgroundColor = .clear
    self.scrollView.delegate = self
    self.scrollView.addSubview(renderView!)
  }

  override func setAutoLayout() {
    let width = UIScreen.width
    let height = width * (9.0 / 16.0)

    renderView!.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.scrollView)
      make.height.equalTo(height)
      make.width.equalTo(width)
    }
  }

  override func initializeOpen() {
    initializeScrollView()
    rxIsState.onNext(.opening)
    rxTime.onNext(0)
    rxRemainingTime.onNext(0)

    mediaPlayer.position = 0
  }

  override func opneStream(url: String, startTime: Double? = nil, isLive: Bool) {
    if let path = URL(string: url) {
      initializeOpen()
      setVlcPlayer(path: path, startTime: startTime, isLive: isLive)
    } else {
      Log.error(category: .VLC, to: "Invalide stream url: \(url)")
      rxIsState.onNext(.error)
    }
  }

  override func opneStream(url: URL, isLive: Bool) {
    initializeOpen()
    setVlcPlayer(path: url, isLive: isLive)
  }

  override func opneFileStream(path: URL, startTime: Double? = nil) {
    if mediaPlayer == nil {
      mediaPlayer = VLCMediaPlayer()
      mediaPlayer.delegate = self
      setVlcLogger(level: .debug)
    }
    Log.message(to: path)
    initializeOpen()
    mediaPlayer.drawable = self.renderView
    let media = VLCMedia(url: path)

    mediaPlayer.media = media
    mediaPlayer.media?.addOptions([
      "vv": "",
      "rtsp-tcp": true,
      "rtsp-frame-buffer-size": 2500000,
      "network-caching": 1000,
      "play-and-pause": true,
    ])

    if let time = startTime {
      mediaPlayer.media?.addOptions(["start-time": time])
    }

    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
      self.mediaPlayer.play()
    }
  }

  override func play() {
    DispatchQueue.main.async { [weak self] in
      self?.mediaPlayer.play()
    }
  }

  func resume() {
    DispatchQueue.main.async { [weak self] in
      guard let self else { return }
      LKPopupView.popup.loading()
      mediaPlayer.time = self.pauseTime ?? VLCTime(int: 0)
      debugPrint("resume() : pauseTime: \(pauseTime), seekable: \(mediaPlayer.isSeekable)")
      debugPrint("resume() : position: \(pausePosition)")
      mediaPlayer.position = self.pausePosition
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
        self.mediaPlayer.play()
      }
    }
  }
  
  override func pause() {
    DispatchQueue.main.async { [weak self] in
      self?.pauseTime = self?.mediaPlayer.time
      self?.pausePosition = self?.mediaPlayer.position ?? 0.0
      self?.mediaPlayer.pause()
    }
  }

  override func stop() {
    DispatchQueue.main.async { [weak self] in
      self?.mediaPlayer.stop()
    }
  }

  override func seek(time: Double) {
    mediaPlayer.time = VLCTime(number: NSNumber(value: time * 1000))
  }

  override func startWith(sec time: Double) {
    mediaPlayer.media?.addOptions(["start-time": time])
    mediaPlayer.play()
  }
  
  func seekToPause() {
    guard let pauseTime = self.pauseTime else {
      vLogger.error("pause time is nil")
      return
    }
    mediaPlayer.time = pauseTime
  }

  func openStreamWithoutPlay(url: URL) {
    initializeOpen()
    setVlcPlayer(path: url, isPlay: false, isLive: false)
  }
}

extension VLCPlayerView {
  func seek(value: Float) {
    mediaPlayer.position = value
  }


  func jumpBackworad(interval: Int) {
    mediaPlayer.jumpBackward(Int32(interval))
    LKPopupView.popup.loading()
    // 0.5초후 play
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
      guard let self else { return }
      if mediaPlayer.state != .playing {
        mediaPlayer.play()
      }
    }
  }

  func jumpForward(interval: Int) {
    mediaPlayer.jumpForward(Int32(interval))
    LKPopupView.popup.loading()
    // 0.5초후 play
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
      guard let self else { return }
      if mediaPlayer.state != .playing {
        mediaPlayer.play()
      }
    }
  }
  
  func seekToTimestamp(seconds: Int32) {
    guard mediaPlayer.isPlaying else { return }
    let newTime = VLCTime(int: seconds * 1000) // 초를 밀리초로 변환
    mediaPlayer.time = newTime
  }
}

extension VLCPlayerView: VLCMediaPlayerDelegate {
  enum LoggingLevel: Int {
    case info
    case error
    case warning
    case debug

    public init?(rawValue: Int) {
      switch rawValue {
      case 0:
        self = .info
      case 1:
        self = .error
      case 2:
        self = .warning
      case 3, 4:
        self = .debug
      default:
        return nil
      }
    }
  }

  private func setVlcLogger(level: LoggingLevel) {
    #if DEBUG
    let logger = VLCConsoleLogger()
    logger.level = .debug
    logger.formatter.contextFlags = .levelContextModule
    mediaPlayer.libraryInstance.loggers = [logger]
    #endif
  }

  private func setVlcPlayer(path: URL, isPlay: Bool = true, startTime: Double? = nil, isLive: Bool) {
    if mediaPlayer == nil {
      mediaPlayer = VLCMediaPlayer()
      mediaPlayer.delegate = self
      setVlcLogger(level: .debug)
    }

    mediaPlayer.drawable = self.renderView

    let media = VLCMedia(url: path)
    Log.error(category: .VLC, to: "\(path.debugDescription)")
    mediaPlayer.media = media
    mediaPlayer.media?.addOptions([
      "vv": "",
      "rtsp-tcp": true,
      "rtsp-frame-buffer-size": 2500000,
      "network-caching": 1000,
    ])

    if let time = startTime {
      mediaPlayer.media?.addOptions(["start-time": time])
    }

    if isPlay {
      self.mediaPlayer.play()
    }

  }

  func hasAudioTrack(media: VLCMedia) -> Bool {
    guard let tracks = media.tracksInformation as? [[String: Any]] else {
      return false
    }

    for track in tracks {
      if let type = track["type"] as? Int, type == 1 {  // 1은 오디오 트랙을 의미
        return true
      }
    }
    return false
  }

  // MARK: VLCMediaPlayer Delegate
  func mediaPlayerStateChanged(_ aNotification: Notification) {
    guard let player = aNotification.object as? VLCMediaPlayer else { return }
    if self.state == player.state { return }

    if player.state == .playing  {
      isAfterPlayingState = true
    }
    
    if player.state == .paused || player.state == .stopped {
      LKPopupView.popup.hideLoading()
    }
    
    if player.state == .buffering && isAfterPlayingState {
      isAfterPlayingState = false
      DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
        LKPopupView.popup.hideLoading()
      }
    }
    
    cLogger.log("mediaPlayerStateChanged: state: \(player.state.rawValue)")
    self.state = player.state
    
    if let state = RTSPPlayerState(rawValue: player.state.rawValue) {
      rxIsState.onNext(state)
    }
  }

  func mediaPlayerTimeChanged(_ aNotification: Notification) {
    guard let player = aNotification.object as? VLCMediaPlayer else { return }
    DispatchQueue.main.async { [weak self] in
      guard let self = self else { return }
      rxTime.onNext(Int(player.time.intValue))
      rxRemainingTime.onNext(Int(abs(player.media?.length.intValue ?? 0)) - Int(player.time.intValue))
      remainTime = (Int(abs(player.media?.length.intValue ?? 0)) - Int(player.time.intValue))
    }
  }
}

// MARK: - VLCLibraryLogReceiverProtocol
extension VLCPlayerView: VLCLibraryLogReceiverProtocol {
  func handleMessage(_ message: String, debugLevel level: Int32) {
    let level = VLCPlayerView.LoggingLevel(rawValue: level.asInt) ?? .info
    switch level {
    case .warning:
      Log.warning(category: .VLC, to: "\(message)")
    case .info:
      Log.info(category: .VLC, to: "\(message)")
    case .error:
      Log.error(category: .VLC, to: "\(message)")
    case .debug:
      Log.debug(category: .VLC, to: "\(message)")
    }
  }
}

// MARK: - UIScrollViewDelegate
extension VLCPlayerView: UIScrollViewDelegate {
  func viewForZooming(in scrollView: UIScrollView) -> UIView? {
    if !isExpanded { return nil }
    return renderView
  }

}
