//
//  PlayerControlBarView.swift
//  Hub
//
//  Created by ncn on 2023/02/21.
//

import UIKit

class PlayerControlBarView: BaseView {
  let type: PlayerViewType

  var fullshotWidth: CGFloat {
    var spacing: CGFloat = 32.0.wrv
    let margin: CGFloat = 30.0.wrv
    var iconCount: CGFloat
    switch type {
    case .live:
      spacing = 68.0.wrv
      iconCount = 4.0
    case .vod:
      spacing = 32.0.wrv
      iconCount = 6.0
    case .localVod:
      spacing = 43.0.wrv
      iconCount = 4.0
    case .snapShot:
      spacing = 64.0.wrv
      iconCount = 3.0
    case .bookmark:
      spacing = 60.0.wrv
      iconCount = 4.0
    case .default:
      iconCount = 5.0
    }

    let iconSize: CGFloat = 24.0.wrv

    return (iconSize * iconCount) + (spacing * (iconCount - 1)) + (margin * 2)
  }

  enum s1PlayChannel: Int {
    case front = 1
    case rear = 2
    case `internal` = 3

    var iconImageName: String {
      switch self {
      case .front:
        "icon_player_front_ch"
      case .rear:
        "icon_player_rear_ch"
      case .`internal`:
        "icon_player_in_ch"
      }
    }
  }

  lazy var slider: NCSlider = {
    let slider = NCSlider()
    return slider
  }()

  lazy var vReversButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_vflip"), for: .normal)
    button.setImage(UIImage(named: "icon_player_vflip_pressed"), for: .highlighted)
    button.setImage(UIImage(named: "icon_player_vflip_selected"), for: .selected)
    button.addAction( UIAction { [weak self] _ in
      self?.isVReversed.toggle()
    }, for: .touchUpInside)
    return button
  }()

  lazy var hReversButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_hflip"), for: .normal)
    button.setImage(UIImage(named: "icon_player_hflip_pressed"), for: .highlighted)
    button.setImage(UIImage(named: "icon_player_hflip_selected"), for: .selected)
    button.addAction( UIAction { [weak self] _ in
      self?.isHReversed.toggle()
    }, for: .touchUpInside)
    return button
  }()

  lazy var snapButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_snap"), for: .normal)
    button.setImage(UIImage(named: "icon_player_snap_pressed"), for: .highlighted)
    return button
  }()

  lazy var channelButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_front_ch"), for: .normal)
    return button
  }()

  lazy var downloadButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_download"), for: .normal)
    button.setImage(UIImage(named: "icon_player_download_pressed"), for: .highlighted)
    return button
  }()

  lazy var deleteButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_player_delete"), for: .normal)
    return button
  }()

  var currentChannel: Int {
    didSet {
      let imageName = s1PlayChannel(rawValue: currentChannel)?.iconImageName ?? "icon_player_front_ch"
      channelButton.setImage(UIImage(named: imageName), for: .normal)
    }
  }

  var isVReversed: Bool = false {
    didSet {
      vReversButton.isSelected = isVReversed
    }
  }

  var isHReversed: Bool = false {
    didSet {
      hReversButton.isSelected = isHReversed
    }
  }

  lazy var durationLabel: UILabel = {
    let label = UILabel()
    label.font = .body4
    label.textColor = .white
    label.text = "0:00:00 / 0:00:00"
    return label
  }()

  #if HUB
    lazy var adasButton: UIButton = {
      var button = UIButton()
      button.setImage(UIImage(named: "icon_player_adas"), for: .normal)
      return button
    }()

    lazy var sizeButton: UIButton = {
      let button = UIButton()
      button.setImage(UIImage(named: "icon_player_full"), for: .normal)
      return button
    }()
    var adasButtonHiddenIfRear: Bool = false {
      didSet {
        adasButton.isHidden = adasButtonHiddenIfRear
      }
    }
  #endif

  var duration: Int = 0 {
    didSet {
      setTime()
    }
  }

  var progressTime: Int = 0 {
    didSet {
      setTime()
    }
  }

  public init(type: PlayerViewType) {
    self.type = type

    currentChannel = 1
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
    draw(type: type)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .white
    self.layer.borderColor = UIColor.linePressed.cgColor
    self.layer.borderWidth = 1
  }

  override func setAutoLayout() {
  }

  private func draw(type: PlayerViewType) {
    switch type {
    case .live:
      setLiveComponent()
      break
    case .vod:
      setFileVideoComponent()
      setSlider()
      break
    case .localVod:
      setLocalFileVideoComponent()
      setSlider()
      break
    case .snapShot:
      setFileImageComponent()
    case .bookmark:
      setBookmarkImageComponent()
    case .default:
      break
    }
  }
}

extension PlayerControlBarView {
  private func setLiveComponent(_ isLanscape: Bool = false) {
    let stackSpace: CGFloat = 68.0.wrv
    HStackView(spacing: stackSpace, pinInSuperView: false) {
      channelButton.withSize(24)
      hReversButton.withSize(24)
      vReversButton.withSize(24)
      snapButton.withSize(24)
    }
    .pin.center().activate()
  }
}

extension PlayerControlBarView {
  private func setSlider() {
    let thumbSize = CGSize(width: 12, height: 12)
    let renderer = UIGraphicsImageRenderer(size: thumbSize)
    let image = renderer.image { context in
      let rect = CGRect(origin: .zero, size: thumbSize)
      UIColor.vueroidBlue.setFill()
      context.cgContext.addEllipse(in: rect)
      context.cgContext.fillPath()
    }
    let maxImage = UIImage().imageWithColor(color: UIColor.white.withAlphaComponent(0.4))
    slider.setMaximumTrackImage(maxImage, for: .normal)
    slider.setMinimumTrackImage(image, for: .normal)
    slider.minimumValue = 0.0
    slider.maximumValue = 1.0
    slider.isContinuous = true
    slider.setThumbImage(image, for: UIControl.State.normal)
    slider.backgroundColor = .clear
    slider.value = 0
    slider.isUserInteractionEnabled = false
    //    self.addSubview(slider)
    // isLandScape 일때 slider 보여줌
    //    slider.isHidden = true
  }

  private func setFileVideoComponent() {
    let stackSpace: CGFloat = 32.0.wrv
    HStackView(spacing: stackSpace, pinInSuperView: false) {
      channelButton.withHeight(31).withWidth(24)
      hReversButton.withSize(24)
      vReversButton.withSize(24)
      snapButton.withSize(24)
      downloadButton.withSize(24)
      deleteButton.withSize(24)
    }
    .pin.center().activate()
  }

  private func setLocalFileVideoComponent() {
    let stackSpace: CGFloat = 43.0.wrv
    HStackView(spacing: stackSpace, pinInSuperView: false) {
      hReversButton.withSize(24)
      vReversButton.withSize(24)
      snapButton.withSize(24)
      deleteButton.withSize(24)
    }
    .pin.center().activate()
  }

  private func setFileImageComponent() {
    let stackSpace: CGFloat = 64.0.wrv
    HStackView(spacing: stackSpace, pinInSuperView: false) {
      hReversButton.withSize(24)
      vReversButton.withSize(24)
      deleteButton.withSize(24)
    }
    .pin.center().activate()
  }

  private func setBookmarkImageComponent() {
    let stackSpace: CGFloat = 60.0.wrv
    HStackView(spacing: stackSpace, pinInSuperView: false) {
      hReversButton.withSize(24)
      vReversButton.withSize(24)
      downloadButton.withSize(24)
      deleteButton.withSize(24)
    }
    .pin.center().activate()
  }

  private func setFileAutoLayout() {
    #if HUB
      durationLabel.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.centerY.equalTo(self)
        make.left.equalTo(self).offset(20)
      }

      slider.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.height.equalTo(8)
        make.left.bottom.right.equalTo(self)
      }
    #endif
    let space = (UIScreen.width - (24 + 24 + (24 * 6))) / 5
    channelButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self).offset(30)
      make.width.height.equalTo(24)
    }

    hReversButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self.channelButton.snp.right).offset(space)
      make.width.height.equalTo(24)
    }

    vReversButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self.hReversButton.snp.right).offset(space)
      make.width.height.equalTo(24)
    }

    snapButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self.vReversButton.snp.right).offset(space)
      make.width.height.equalTo(24)
    }

    downloadButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self.snapButton.snp.right).offset(space)
      make.width.height.equalTo(24)
    }

    deleteButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self)
      make.left.equalTo(self.downloadButton.snp.right).offset(space)
      make.width.height.equalTo(24)
    }
  }
}

extension PlayerControlBarView {
  func setTime() {
    DispatchQueue.main.async {
      if self.type == .live {
        let mode = AppManager.shared.mode
        if mode == .wifi {
          self.durationLabel.text = self.progressTime.getTimeString()
        }
      } else if self.type == .vod || self.type == .localVod {
        if self.progressTime <= self.duration {
          self.durationLabel.text =
            self.progressTime.getTimeString() + " / " + self.duration.getTimeString()
        } else {
          self.durationLabel.text =
            self.duration.getTimeString() + " / " + self.duration.getTimeString()
        }
        self.slider.value = Float(self.progressTime) / Float(self.duration)
      }
    }
  }
}
