//
//  PlayerMenuView.swift
//  Hub
//
//  Created by ncn on 2023/02/15.
//

import UIKit

public enum PlayerViewType: String {
  case live
  case vod
  case localVod
  case snapShot
  case bookmark
  case `default`
}

class PlayerMenuView: BaseView {
  private var type: PlayerViewType
  let nameLabel = UILabel()

  let itemStackView = UIStackView()

  lazy var channelButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_liveview_ch"), for: .normal)
    return button
  }()

  lazy var adasButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_liveview_adas"), for: .normal)
    return button
  }()

  lazy var reversButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_liveview_reverse"), for: .normal)
    return button
  }()

  lazy var snapButton: UIButton = {
    var button = UIButton()
    button.setImage(UIImage(named: "icon_liveview_snap"), for: .normal)
    return button
  }()

  let countView = UIView()
  lazy var countLabel: PaddingLabel = {
    let label = PaddingLabel()
    label.backgroundColor = .customOrange
    label.textAlignment = .center
    label.textColor = .text
    label.text = "3/3회"
    label.font = .MontserratRegular(ofSize: 12)
    label.layer.cornerRadius = 8.0
    label.clipsToBounds = true
    return label
  }()

  public init(type: PlayerViewType) {
    self.type = type
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
    draw(type: type)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func draw(type: PlayerViewType) {
    switch type {
    case .live:
      setLiveMenu()
      break
    case .vod:
      setVodMenu()
      break
    case .snapShot, .bookmark:
      setSnapShotMenu()
      break
    default:
      break
    }
  }

  override func setComponent() {
    itemStackView.spacing = 4
    itemStackView.alignment = .fill
    itemStackView.distribution = .equalSpacing
    itemStackView.axis = .horizontal
    self.addSubview(itemStackView)

    nameLabel.font = .systemFont(ofSize: 16)
    nameLabel.textColor = .text
    self.addSubview(nameLabel)
  }

  override func setAutoLayout() {
    nameLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(20)
      make.centerY.equalTo(self)
    }

    itemStackView.snp.makeConstraints { make in
      make.trailing.equalToSuperview().inset(20)
      make.top.bottom.equalToSuperview()
    }
  }
}

extension PlayerMenuView {
  func setLiveMenu() {
    itemStackView.addArrangedSubview(channelButton)
    itemStackView.addArrangedSubview(adasButton)
    itemStackView.addArrangedSubview(reversButton)
    itemStackView.addArrangedSubview(snapButton)
    itemStackView.addArrangedSubview(countView)
    countView.addSubview(countLabel)

    channelButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    adasButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    reversButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    snapButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    countView.snp.makeConstraints { make in
      make.width.greaterThanOrEqualTo(74)
    }
    countLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview()
      make.height.equalTo(28)
      make.centerY.equalToSuperview()
    }
  }
}

extension PlayerMenuView {
  func setVodMenu() {
    itemStackView.addArrangedSubview(channelButton)
    itemStackView.addArrangedSubview(reversButton)
    itemStackView.addArrangedSubview(snapButton)

    channelButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    reversButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
    snapButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
  }

  func setSnapShotMenu() {
    itemStackView.addArrangedSubview(reversButton)
    reversButton.snp.makeConstraints { make in
      make.width.equalTo(44)
    }
  }
}
