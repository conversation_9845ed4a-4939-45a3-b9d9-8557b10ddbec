//
//  EditHandlerView.swift
//  Hub
//
//  Created by ncn on 2023/03/14.
//

import UIKit

class EditHandlerView: UIView {
  let playButton = UIButton()

  public init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func setComponent() {
    self.backgroundColor = .icon
    self.roundCorners(.allCorners, radius: 10)

    let pauseImage = UIImage(systemName: "pause.fill")?.withRenderingMode(.alwaysTemplate)
    let playImage = UIImage(systemName: "play.fill")?.withRenderingMode(.alwaysTemplate)
    playButton.setImage(pauseImage, for: .normal)
    playButton.setImage(playImage, for: .selected)
    playButton.tintColor = .white
    self.addSubview(playButton)
  }

  func setAutoLayout() {
    playButton.pin.size(24).center().activate()
  }
}
