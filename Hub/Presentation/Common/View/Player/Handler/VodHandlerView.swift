//
//  VodHandlerView.swift
//  Hub
//
//  Created by ncn on 2023/03/03.
//

import UIKit

class VodHandlerView: BaseView {
  let controlBarView: PlayerControlBarView
  let type: PlayerViewType
  
  lazy var channelInfoImage: UIImageView = {
    let image = UIImageView()
    image.contentMode = .scaleAspectFit
    image.backgroundColor = .clear
    image.translatesAutoresizingMaskIntoConstraints = false
    image.isHidden = AppManager.shared.mode == .wifi
    return image
  }()
  
  var currentChannel: Int = 1 {
    didSet {
      updateChannelImage(channel: currentChannel)
    }
  }
    
  lazy var playButton: UIButton = {
    let button = UIButton()
    button.setCircularBackground(size: 50)
    button.setImage(UIImage(named: "icon_pause_off"), for: .normal)
    button.setImage(UIImage(named: "icon_play3_off"), for: .selected)
    return button
  }()

  lazy var previousButton: UIButton = {
    let button = UIButton()
    button.setCircularBackground(size: 40)
    button.setImage(UIImage(named: "icon_previous_off"), for: .normal)  // |<
    return button
  }()

  lazy var nextButton: UIButton = {
    let button = UIButton()
    button.setCircularBackground(size: 40)
    button.setImage(UIImage(named: "icon_next_off"), for: .normal)  // >|
    return button
  }()

  lazy var backwardButton: UIButton = {
    let button = UIButton()
    button.setCircularBackground(size: 40)
    button.setImage(UIImage(named: "icon_backward_off"), for: .normal)  // <<
    return button
  }()

  lazy var forwardButton: UIButton = {
    let button = UIButton()
    button.setCircularBackground(size: 40)
    button.setImage(UIImage(named: "icon_forward_off"), for: .normal)  // >>
    return button
  }()

  lazy var sizeButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "icon_player_full"), for: .normal)
    button.setImage(UIImage(named: "icon_player_full_select"), for: .selected)
    return button
  }()

  public init(type: PlayerViewType = .vod) {
    self.type = type
    controlBarView = PlayerControlBarView(type: type)
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  func updateChannelImage(channel: Int) {
    var imageName = ""
    switch channel {
    case 1:
      imageName = "front_ch"
    case 2:
      imageName = "rear_ch"
    case 3:
      imageName = "interior_ch"
    default:
      imageName = "front_ch"
    }
    channelInfoImage.image = UIImage(named: imageName)
  }

  override func setComponent() {
    self.backgroundColor = UIColor.black.withAlphaComponent(0.40)
    self.addSubview(previousButton)
    self.addSubview(backwardButton)
    self.addSubview(playButton)
    self.addSubview(forwardButton)
    self.addSubview(nextButton)
    self.addSubview(sizeButton)
    self.addSubview(controlBarView.slider)
    self.addSubview(controlBarView.durationLabel)
    self.addSubview(controlBarView)
    self.addSubview(channelInfoImage)
    updateChannelImage(channel: currentChannel)
  }

  override func setAutoLayout() {
    previousButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(40)
      make.centerY.equalTo(self).offset(-10)
      make.right.equalTo(self.backwardButton.snp.left).offset(-30)
    }

    backwardButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(40)
      make.centerY.equalTo(self).offset(-10)
      make.right.equalTo(self.playButton.snp.left).offset(-30)
    }
    
    playButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(50)
      make.centerX.equalTo(self)
      make.centerY.equalTo(self).offset(-10)
    }

    forwardButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(40)
      make.centerY.equalTo(self).offset(-10)
      make.left.equalTo(self.playButton.snp.right).offset(30)
    }

    nextButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.width.equalTo(40)
      make.centerY.equalTo(self).offset(-10)
      make.left.equalTo(self.forwardButton.snp.right).offset(30)
    }

    controlBarView.pin.bottom().horizontally().height(40).activate()
    sizeButton.pin.size(24).bottom(offset: -50).end(offset: -10).activate()
    controlBarView.slider.pin.height(20).bottom(offset: -52).start(offset: 15).before(sizeButton, offset: -10).activate()
    controlBarView.durationLabel.pin.start(offset: 20).height(20).width(100).above(controlBarView.slider, offset: -5).activate()
    controlBarView.roundCorners(.allCorners, radius: 0)
    
    channelInfoImage.pin.size(40).top(offset: -8).start(offset: 0).activate()
  }
  
  public func hideMoveButton() {
    nextButton.isHidden = true
    previousButton.isHidden = true
  }

  func updateLayout(_ isLandscape: Bool) {
    sizeButton.isSelected = isLandscape
    
    if isLandscape == false {
      // Portrait 모드일 때 버튼들을 위로 10포인트 올림
      previousButton.snp.updateConstraints { make in
        make.centerY.equalTo(self).offset(-10)
      }
      backwardButton.snp.updateConstraints { make in
        make.centerY.equalTo(self).offset(-10)
      }
      playButton.snp.updateConstraints { make in
        make.centerY.equalTo(self).offset(-10)
      }
      forwardButton.snp.updateConstraints { make in
        make.centerY.equalTo(self).offset(-10)
      }
      nextButton.snp.updateConstraints { make in
        make.centerY.equalTo(self).offset(-10)
      }
      
      controlBarView.removeFromSuperview()
      sizeButton.removeFromSuperview()
      controlBarView.slider.removeFromSuperview()
      controlBarView.durationLabel.removeFromSuperview()
      channelInfoImage.removeFromSuperview()
      
      addSubviews([controlBarView, sizeButton, controlBarView.slider, controlBarView.durationLabel, channelInfoImage])
      setAutoLayout()
      
      controlBarView.durationLabel.isHidden = controlBarView.slider.isHidden
    } else {
      // Landscape 모드일 때 버튼들을 중앙에 위치
      previousButton.snp.updateConstraints { make in
        make.centerY.equalTo(self)
      }
      backwardButton.snp.updateConstraints { make in
        make.centerY.equalTo(self)
      }
      playButton.snp.updateConstraints { make in
        make.centerY.equalTo(self)
      }
      forwardButton.snp.updateConstraints { make in
        make.centerY.equalTo(self)
      }
      nextButton.snp.updateConstraints { make in
        make.centerY.equalTo(self)
      }
      
      // Remove all existing constraints
      controlBarView.removeFromSuperview()
      controlBarView.slider.removeFromSuperview()
      controlBarView.durationLabel.removeFromSuperview()
      sizeButton.removeFromSuperview()
      channelInfoImage.removeFromSuperview()
      
      addSubviews([controlBarView, sizeButton, controlBarView.slider, controlBarView.durationLabel, channelInfoImage])

      controlBarView.roundCorners(.allCorners, radius: 20)
      controlBarView.pin.deactivate()
      
      let fullWidth = controlBarView.fullshotWidth
      fLogger.debug("fullWidth: \(fullWidth), type: \(self.controlBarView.type.rawValue)")
      
      Pin.activate([
        controlBarView.pin.height(40.0).width(controlBarView.fullshotWidth).centerX().bottom(offset: -25),
        sizeButton.pin.size(30).above(controlBarView, offset: -12).end(offset: -55),
        controlBarView.slider.pin.height(10).centerY(sizeButton).start(offset: 50).before(sizeButton, offset: -10),
        controlBarView.durationLabel.pin.start(offset: 50).height(20).width(100).above(controlBarView.slider, offset: -5),
        channelInfoImage.pin.size(40).top(offset: 20).start(offset: 20),
      ])
      controlBarView.durationLabel.isHidden = controlBarView.slider.isHidden
      
      setNeedsLayout()
      layoutIfNeeded()
    }
  }
}
