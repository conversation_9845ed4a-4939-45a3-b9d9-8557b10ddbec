//
//  CollectionMenuCell.swift
//  Hub
//
//  Created by ncn on 2023/02/27.
//

import UIKit

class CollectionMenuCell: BaseCollectionViewCell {
  public static var identifier: String = "CollectionMenuCell"
  lazy var menuButton: UIButton = {
    let button = UIButton()
    button.setBackgroundColor(.clear, for: .selected)
    button.setBackgroundColor(.clear, for: .normal)
    button.setTitleColor(.grayText, for: .normal)
    button.setTitleColor(.vueroidBlue, for: .selected)
    button.titleLabel?.font = .sub1
    button.titleLabel?.textAlignment = .center
    return button
  }()

  var bottomSelectBar: UIView = {
    let view = UIView()
    view.backgroundColor = .vueroidBlue
    view.isHidden = true
    return view
  }()

  var isEnableSelected = true
  var tapHandler: (() -> Void)?

  override var isSelected: Bool {
    didSet {
      if isEnableSelected == false { return }
      menuButton.isSelected = isSelected
      bottomSelectBar.isHidden = !isSelected
      self.setNeedsLayout()
    }
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    contentView.backgroundColor = .clear
    contentView.addSubview(menuButton)
    contentView.addSubview(bottomSelectBar)
    menuButton.addTarget(self, action: #selector(buttonTapped), for: .touchUpInside)
  }

  override func setAutoLayout() {
    menuButton.pin.horizontally().top().bottom(offset: -3).activate()
    bottomSelectBar.pin.below(menuButton).horizontally().height(3).activate()
  }

  @objc private func buttonTapped() {
    tapHandler?()  // Call the tapHandler when button is tapped
  }
}

extension CollectionMenuCell {
  func setData(name: String) {
    menuButton.setTitle(name, for: .normal)
  }
}
