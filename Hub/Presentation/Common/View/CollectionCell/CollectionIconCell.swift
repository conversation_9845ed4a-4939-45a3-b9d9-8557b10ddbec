//
//  CollectionIconCell.swift
//  Hub
//
//  Created by ncn on 2023/05/19.
//

import UIKit

enum FileActionType {
  //  case dashcam
  case check
  case download
  //  case external
  //  case cloud
  case upload
  case share
  case delete
  case toCloud
}
public enum FileStorageType: Int {
  case dashcam = 0
  case download = 1
  case external = 2
  case cloud = 3
  case autoUpload = 4
}

struct MenuIconModel {
  let fileActionType: FileActionType?
  let fileStorageType: FileStorageType
  let normal: UIImage
  let highlight: UIImage
  let title: String
}

class CollectionIconCell: BaseCollectionViewCell {
  public static var identifier: String = "CollectionIconCell"

  let storageLabel: UILabel = {
    let label = UILabel()
    label.font = .systemFont(ofSize: 16)
    label.textColor = .vueroidBlue
    label.textAlignment = .center
    return label
  }()

  let tagView = UIView()

  override var isSelected: Bool {
    didSet {
      storageLabel.textColor = isSelected ? .vueroidBlue : .lightGray
      tagView.isHidden = !isSelected
    }
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .white

    self.addSubview(storageLabel)

    tagView.backgroundColor = .vueroidBlue
    tagView.isHidden = true
    self.addSubview(tagView)
  }

  override func setAutoLayout() {

    storageLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.center.equalTo(self)
      make.width.equalTo(100)
      make.height.equalTo(49)
    }

    tagView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.right.bottom.equalTo(self)
      make.height.equalTo(3)
    }
  }
}

extension CollectionIconCell {
  func setData(model: MenuIconModel) {
    storageLabel.text = model.fileStorageType.rawValue == 0 ? "Device" : "Cloud"
  }
}
