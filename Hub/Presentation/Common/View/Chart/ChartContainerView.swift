//
//  ChartContainerView.swift
//  Hub
//
//  Created by ncn on 2023/03/15.
//


import RxSwift
import UIKit
import DGCharts

open class IntValueFormatter: NSObject, ValueFormatter, AxisValueFormatter {

  fileprivate func format(value: Double) -> String {
    let r = value == 0 ? "" : String(format: "%2.f", value)
    return r
  }

  public func stringForValue(
    _ value: Double,
    entry: DGCharts.ChartDataEntry,
    dataSetIndex: Int,
    viewPortHandler: DGCharts.ViewPortHandler?
  ) -> String {
    return format(value: value)
  }

  public func stringForValue(_ value: Double, axis: AxisBase?) -> String {
    return "\(Int(value))"
  }
}

public class DayAxisValueFormatter: NSObject, AxisValueFormatter {
  let values: [String]

  init(values: [String]) {
    self.values = values
  }

  public func stringForValue(_ value: Double, axis: AxisBase?) -> String {
    let index = Int(value)
    if index < 0 || index > values.count - 1 {
      Log.message(to: "Invalid index for axis index: \(index) - \(value)")
      return ""
    }
    return values[index]
  }

}

class BarChartDataSet_ct: BarChartDataSet {
  override func valueTextColorAt(_ index: Int) -> NSUIColor {
    if index > 0 {
      return super.valueTextColorAt(index)
    } else {
      return UIColor.clear
    }
  }
}

class ChartContainerView: BaseView {
  let menuView = ChartMenuView()
  let chartView = BarChartView()
  let chartEmptyView = ChartEmptyView()

  let calendarView = CalendarView()
  var containView: FlipView?
  private var isShowBack = false

  var rxSelectedIndex = PublishSubject<Int>()
  var selectedIndex = 0 {
    didSet {
      rxSelectedIndex.onNext(selectedIndex)
    }
  }

  private var chartItems: [EventChartModel]?
  var rxSelectChartItem = PublishSubject<EventChartModel>()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .background
    self.layer.cornerRadius = 10
    self.addSubview(menuView)

    containView = FlipView(toView: calendarView, fromView: chartView)
    containView?.backgroundColor = .white
    self.addSubview(containView!)

    setChartAttribute()
    calendarView.isHidden = true
    containView!.addSubview(calendarView)
    containView!.addSubview(chartView)

    chartEmptyView.isHidden = true
    containView!.addSubview(chartEmptyView)
  }

  override func setAutoLayout() {
    menuView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.left.right.equalTo(self)
      make.height.equalTo(64)
    }

    containView!.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.top.equalTo(self.menuView.snp.bottom)
      make.bottom.left.right.equalTo(self)
    }

    calendarView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.containView!)
    }

    chartView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.containView!)
    }
    chartEmptyView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self.chartView)
    }
  }
}

extension ChartContainerView {}

extension ChartContainerView: ChartViewDelegate {
  func chartValueSelected(_ chartView: ChartViewBase, entry: ChartDataEntry, highlight: Highlight) {
    Log.message(to: " entry:\(entry), highlight: \(highlight)")
    if entry.y > 0 {
      if let dataSet = chartView.data?.dataSets[highlight.dataSetIndex] {
        let index: Int = dataSet.entryIndex(entry: entry)
        self.selectedIndex = index
        if let items = self.chartItems {
          let obj = items[index]
          self.rxSelectChartItem.onNext(obj)
        }
        Log.message(to: "Chart selected index: \(index)")
      }
    }
  }

  func chartValueNothingSelected(_ chartView: ChartViewBase) {
    NSLog("chartValueNothingSelected")
  }

  func chartScaled(_ chartView: ChartViewBase, scaleX: CGFloat, scaleY: CGFloat) {

  }

  func chartTranslated(_ chartView: ChartViewBase, dX: CGFloat, dY: CGFloat) {

  }
}

// MARK: Cloud Chart Update
extension ChartContainerView {
  private func setChartAttribute() {
    chartView.delegate = self
    chartView.chartDescription.enabled = false
    chartView.pinchZoomEnabled = false
    chartView.scaleXEnabled = false
    chartView.scaleYEnabled = false
    chartView.doubleTapToZoomEnabled = false
    chartView.drawBarShadowEnabled = false

    let l = chartView.legend
    l.horizontalAlignment = .left
    l.verticalAlignment = .bottom
    l.form = .circle
    l.orientation = .horizontal
    l.textColor = .mainBlack
    l.drawInside = false
    l.font = .pretendard(ofSize: 10) ?? .systemFont(ofSize: 10)
    l.yOffset = 0
    l.xOffset = 0
    l.yEntrySpace = 0
    l.xEntrySpace = 20

    chartView.xAxis.labelFont = .body4
    chartView.xAxis.labelTextColor = .grayText
    chartView.xAxis.labelPosition = .bottomInside
    chartView.xAxis.drawGridLinesEnabled = false
    chartView.xAxis.granularityEnabled = true
    
    // 왼쪽 Y축 설정 - 7개의 수평 그리드 라인 표시
    chartView.leftAxis.enabled = true
    chartView.leftAxis.drawLabelsEnabled = false  // 라벨은 표시하지 않음
    chartView.leftAxis.drawZeroLineEnabled = true 
    chartView.leftAxis.drawAxisLineEnabled = false // 축 라인은 표시하지 않음
    chartView.leftAxis.drawGridLinesEnabled = true // 그리드 라인 표시
    chartView.leftAxis.gridColor = .line
    chartView.leftAxis.gridLineWidth = 1.0 // 그리드 라인 두께
    chartView.leftAxis.setLabelCount(7, force: true) // 7개의 라인 강제 설정
    
    // 오른쪽 Y축 비활성화
    chartView.rightAxis.enabled = false
  }

  func setMultiChartData(models: [EventChartModel], type: HistoryCellType, speedType: SpeedType) {
    self.chartItems = models
    chartView.xAxis.centerAxisLabelsEnabled = true
    chartView.clear()
    chartView.clearValues()
    Log.message(to: models)
    let data = BarChartData()
    var set1: BarChartDataSet = BarChartDataSet()
    var set2: BarChartDataSet = BarChartDataSet()
    var set3: BarChartDataSet = BarChartDataSet()
    switch type {
    case .Distance:
      let entries1 = models.enumerated().map {
        BarChartDataEntry(
          x: Double($0),
          y: speedType == .mph ? Double($1.distance) : Double(NCUtil.mileToKilo(mile: $1.distance)))
      }
      set1 = BarChartDataSet(entries: entries1, label: L.distance_drive_txt.localized)
      set1.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set1.valueTextColor = .white
      set1.drawValuesEnabled = true
      data.append(set1)
    case .DrivingEvent:
      let entries1 = models.enumerated().map {
        BarChartDataEntry(x: Double($0), y: Double($1.cnt1))
      }
      set1 = BarChartDataSet(entries: entries1, label: L.history_permanent_shock_event.localized)
      set1.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set1.valueTextColor = .white
      set1.drawValuesEnabled = true
      data.append(set1)
    case .ParkingMotion:
      let entries2 = models.enumerated().map {
        BarChartDataEntry(x: Double($0), y: Double($1.cnt2))
      }
      set2 = BarChartDataSet(
        entries: entries2, label: L.history_park_motion_event.localized)
      set2.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set2.valueTextColor = .white
      data.append(set2)
    case .ParkingEvent:
      let entries3 = models.enumerated().map {
        BarChartDataEntry(x: Double($0), y: Double($1.cnt3))
      }
      set3 = BarChartDataSet(entries: entries3, label: L.history_park_shock_event.localized)
      set3.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set3.valueTextColor = .white
      data.append(set3)
    case .DrivingTime:
      let entries1 = models.enumerated().map {
        BarChartDataEntry(x: Double($0), y: Double($1.sumtime1.toMinute()))
      }
      set1 = BarChartDataSet(entries: entries1, label: L.history_always_recording_time.localized)
      set1.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set1.valueTextColor = .white
      data.append(set1)
    case .ParkingTime:
      let entries2 = models.enumerated().map {
        BarChartDataEntry(x: Double($0), y: Double($1.sumtime2.toMinute()))
      }
      set2 = BarChartDataSet(entries: entries2, label: L.history_park_recording_time.localized)
      set2.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set2.valueTextColor = .white
      data.append(set2)
    case .AvgSpeed:
      let entries1 = models.enumerated().map {
        BarChartDataEntry(
          x: Double($0),
          y: speedType == .mph ? Double($1.avgspeed) : Double(NCUtil.mileToKilo(mile: $1.avgspeed)))
      }
      set1 = BarChartDataSet(entries: entries1, label: L.average_speed_txt.localized)
      set1.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set1.valueTextColor = .white
      data.append(set1)
    case .MaxSpeed:
      let entries2 = models.enumerated().map {
        return BarChartDataEntry(
          x: Double($0),
          y: speedType == .mph ? Double($1.maxspeed) : Double(NCUtil.mileToKilo(mile: $1.maxspeed)))
      }
      set2 = BarChartDataSet(entries: entries2, label: L.history_max_speed.localized)
      set2.setColor(UIColor.mainBlue.withAlphaComponent(0.7))
      set2.valueTextColor = .white
      data.append(set2)
    case .nothing: break
    }

    var isEmpty = false
    if (set1.entries.allSatisfy({ $0.y == 0 }) || set1.entries.isEmpty)
      && (set2.entries.allSatisfy({ $0.y == 0 }) || set2.entries.isEmpty)
      && (set3.entries.allSatisfy({ $0.y == 0 }) || set3.entries.isEmpty)
    {
      isEmpty = true
    }

    self.chartEmptyView.isHidden = !isEmpty

    let groupSpace = 0.1
    let barSpace = 0.2
    let barWidth = (1.0 - groupSpace) / Double(data.count) - barSpace
    // (0.3 + 0.05) * 3 + 0.28 = 1.00 -> interval per "group"
    let groupCount = models.count
    let startYear = 0

    data.barWidth = barWidth
    chartView.xAxis.axisMinimum = Double(startYear)
    let gg = data.groupWidth(groupSpace: groupSpace, barSpace: barSpace)
    chartView.xAxis.axisMaximum = Double(startYear) + gg * Double(groupCount)
    data.groupBars(fromX: Double(startYear), groupSpace: groupSpace, barSpace: barSpace)

    data.setValueFont(.systemFont(ofSize: 10, weight: .light))
    data.setValueFormatter(IntValueFormatter())
    data.setDrawValues(true)
    data.setValueTextColor(.mainBlack)

    let xValues = models.map { $0.date }

    chartView.xAxis.labelCount = xValues.count
    chartView.xAxis.valueFormatter = DayAxisValueFormatter(values: xValues)
    chartView.data = data
    chartView.reloadInputViews()
  }
}

extension ChartContainerView {
  func flip() {
    if let view = containView {
//      self.menuView.calendarButton.isSelected = !view.isShowBack
      view.flip()
    }
  }
}

class FlipView: BaseView {
  var isShowBack = false
  private var toView: UIView
  private var fromView: UIView

  init(toView: UIView, fromView: UIView) {
    self.toView = toView
    self.fromView = fromView
    super.init(frame: .zero)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func flip() {
    let to = isShowBack ? fromView : toView
    let from = isShowBack ? toView : fromView
    let flipDirection: UIView.AnimationOptions =
      isShowBack ? .transitionFlipFromRight : .transitionFlipFromLeft
    let options: UIView.AnimationOptions = [flipDirection, .showHideTransitionViews]
    UIView.transition(from: from, to: to, duration: 0.6, options: options) {
      finished in
      self.isShowBack = !self.isShowBack
    }
  }
}
