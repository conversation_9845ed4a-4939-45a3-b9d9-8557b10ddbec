//
//  ScrollingView.swift
//  Hub
//
//  Created by ncn on 2023/07/05.
//

import RxCocoa
import RxSwift
import UIKit

class ScrollingView: BaseView {
  let scrollView: UIScrollView = UIScrollView()
  var pageSize: CGSize = CGSize(width: 0.0, height: 0.0)

  var viewList: [UIView]? {
    didSet {
      guard let list = self.viewList else {
        return
      }
      self.viewList = list
      self.setList(list: list)
    }
  }

  public init(size: CGSize = CGSize(width: UIScreen.width, height: UIScreen.height)) {
    super.init(frame: .zero)
    pageSize = size

    scrollView.contentInsetAdjustmentBehavior = .never
    scrollView.showsHorizontalScrollIndicator = false
    scrollView.showsVerticalScrollIndicator = false
    scrollView.bounces = false
    scrollView.isPagingEnabled = true
    scrollView.alwaysBounceVertical = false
    scrollView.isScrollEnabled = true
    scrollView.isDirectionalLockEnabled = true
    scrollView.maximumZoomScale = 1.0
    scrollView.minimumZoomScale = 1.0
    scrollView.delegate = self

    self.addSubview(scrollView)
    scrollView.snp.makeConstraints { view in
      view.top.bottom.left.right.equalTo(self)
    }
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

}

extension ScrollingView: UIScrollViewDelegate {
  private func setList(list: [UIView]) {
    let count = CGFloat(list.count)
    let aWitdh = self.pageSize.width
    let aHeight = self.pageSize.height

    scrollView.contentSize = CGSize(
      width: aWitdh * count,
      height: aHeight)

    list.enumerated().forEach { index, view in
      let v = view
      let offSet = CGFloat(index) * aWitdh
      scrollView.addSubview(v)
      v.tag = index + 10000
      v.translatesAutoresizingMaskIntoConstraints = false
      v.snp.makeConstraints { [weak self] view in
        guard let self = self else { return }
        view.top.equalTo(self.scrollView)
        view.left.equalTo(self.scrollView).offset(offSet)
        view.height.equalTo(aHeight)
        view.width.equalTo(aWitdh)
      }
    }
  }

  func scrollViewDidScroll(_ scrollView: UIScrollView) {
    if scrollView.contentOffset.y > 0 {
      var offset = scrollView.contentOffset
      offset.y = 0.0
      scrollView.contentOffset = offset
    }
  }

  func scrollToPage(page: Int, animated: Bool) {
    var frame: CGRect = self.scrollView.frame
    frame.origin.x = frame.size.width * CGFloat(page)
    frame.origin.y = 0
    self.scrollView.scrollRectToVisible(frame, animated: animated)
  }
}

extension Reactive where Base: ScrollingView {
  var scrollToPage: Binder<Int> {
    return Binder(base) { view, page in
      view.scrollToPage(page: page, animated: true)
    }
  }
}
