//
//  ButtonMenuView.swift
//  Hub
//
//  Created by ncn on 2023/02/27.
//

import RxRelay
import RxSwift
import UIKit

class ButtonMenuView: BaseView {
  var items: [String]? {
    didSet {
      setButton()
    }
  }
  var iconTitles: [MenuIconModel]? {
    didSet {
      setIconButton()
    }
  }

  lazy var toplineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()

  lazy var centerVerticalLineView: UIView = {
    let view = UIView()
    view.backgroundColor = .white
    return view
  }()

  lazy var bottomlineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()

  var isTopLine = false {
    didSet {
      setTopLine(isShow: isTopLine)
    }
  }

  var isBottomLine = false {
    didSet {
      setBottomLine(isShow: isTopLine)
    }
  }

  let rxSelectedTitle: PublishRelay<String> = PublishRelay()
  let rxSelectedType: PublishRelay<(FileStorageType, FileActionType)> = PublishRelay()
  var checkButton = UIButton()

  public init(items: [String]) {
    super.init(frame: .zero)

    self.iconTitles = nil
    self.items = items
    setComponent()
    setAutoLayout()
  }

  public init(items: [MenuIconModel]) {
    super.init(frame: .zero)

    self.iconTitles = items
    self.items = nil
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .subText
  }

  override func setAutoLayout() {

  }

  private func setButton() {
    guard let titles = items else { return }

    let height = 35
    var width: Int = Int(UIScreen.width - (20 * 2))
    width = (width - (10 * (titles.count - 1))) / titles.count
    var offset: Int = 20
    var buttons: [UIButton] = [].map { $0! }

    for item in titles {
      let button = UIButton()
      button.setTitle(item, for: .normal)
      button.titleLabel?.font = .pretendard(ofSize: 13, weight: .bold)
      button.setTitleColor(.white, for: .normal)
      button.setBackgroundColor(.customGray, for: .normal)
      button.setBackgroundColor(.colorRGB(83, 182, 237), for: .selected)
      button.layer.cornerRadius = 17
      button.clipsToBounds = true
      self.addSubview(button)

      button.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.left.equalTo(self).offset(offset)
        make.height.equalTo(height)
        make.width.equalTo(width)
        make.centerY.equalTo(self)
      }
      offset = offset + width + 10

      button.rx.tap
        .map { _ in
          button.titleLabel?.text ?? ""
        }
        .bind(to: self.rxSelectedTitle)
        .disposed(by: disposedBag)

      buttons.append(button)
    }

    if let btn = buttons.first {
      btn.isSelected = true
    }
    let selectedButton = Observable.from(
      buttons.map { button in button.rx.tap.map { button } }
    ).merge()
    buttons.reduce(Disposables.create()) { disposable, button in
      let subscription = selectedButton.map { $0 == button }
        .bind(to: button.rx.isSelected)
      return Disposables.create(disposable, subscription)
    }
    .disposed(by: self.disposedBag)
  }

  private func setIconButton() {
    guard let models = iconTitles else { return }

    for view in subviews {
      if view.tag != 1001
        || view.tag != 1002
      {
        view.removeFromSuperview()
      }
    }

//    let height = 35
    let width: Int = Int(UIScreen.width / 2)
//    width = (width - (10 * (models.count - 1))) / models.count
//    var offset: Int = Int(UIScreen.width - (35 * 3 + 10) )
    var offset = 0
    var buttons: [UIButton] = [].map { $0! }

    var tag = 0


    for item in models {
      let button = MenuButton()
      button.setImage(item.normal, for: .normal)
      button.setImage(item.highlight, for: .highlighted)
      button.setTitle(item.title, for: .normal)
      button.setTitleColor(.white, for: .normal)

      button.fileActionType = item.fileActionType
      button.fileStorageType = item.fileStorageType

      if button.fileActionType == .check {
        self.checkButton = button
      }
      self.addSubview(button)

      button.snp.makeConstraints { [weak self] make in
        guard let self = self else { return }
        make.left.equalTo(self).offset(offset)
        make.height.equalTo(24)
        make.width.equalTo(width)
        make.centerY.equalTo(self)
      }
      offset = offset + width

      button.rx.tap
        .compactMap { _ in
          button.fileActionType
        }
        .map {
          (button.fileStorageType, $0)
        }
        .bind(to: rxSelectedType)
        .disposed(by: disposedBag)

      button.tag = tag
      buttons.append(button)
      tag += 1
    }

    self.addSubview(centerVerticalLineView)
    centerVerticalLineView.pin.center().width(1).height(20).activate()

    let selectedButton = Observable.from(
      buttons.map { button in button.rx.tap.map { button } }
    ).merge()
    buttons.reduce(Disposables.create()) { disposable, button in
      let subscription = selectedButton.map { $0 == button }
        .bind(to: button.rx.isSelected)
      return Disposables.create(disposable, subscription)
    }
    .disposed(by: self.disposedBag)
  }

  func setTopLine(isShow: Bool) {
    toplineView.backgroundColor = .black
    toplineView.tag = 1001
    self.addSubview(toplineView)
    toplineView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.equalTo(1)
      make.left.right.top.equalTo(self)
    }
  }

  func setBottomLine(isShow: Bool) {
    bottomlineView.backgroundColor = .black
    bottomlineView.tag = 1002
    self.addSubview(bottomlineView)
    bottomlineView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.height.equalTo(1)
      make.left.right.bottom.equalTo(self)
    }
  }
}
