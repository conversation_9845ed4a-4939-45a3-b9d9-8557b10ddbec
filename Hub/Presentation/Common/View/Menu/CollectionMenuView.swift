//
//  CollectionMenuView.swift
//  Hub
//
//  Created by ncn on 2023/02/27.
//

import RxCocoa
import RxSwift
import UIKit

class CollectionMenuView: BaseView {
  let collectionView: UICollectionView = UICollectionView(
    frame: .zero,
    collectionViewLayout: UICollectionViewFlowLayout.init())

  let selectItemWithDelegate = PublishSubject<IndexPath>()

  var defaultHieght: CGFloat = 60
  let listItem: BehaviorRelay<[String]> = BehaviorRelay(value: [])
  var menuItems: [String]? {
    didSet {
      guard let items = self.menuItems else {
        return
      }
      self.menuIcons = nil
      self.listItem.accept(items)
      self.collectionView.reloadData()
    }
  }

  let listIcon: BehaviorRelay<[MenuIconModel]> = BehaviorRelay(value: [])
  var menuIcons: [MenuIconModel]? {
    didSet {
      guard let items = self.menuIcons else {
        return
      }
      self.menuItems = nil
      self.listIcon.accept(items)
      self.collectionView.reloadData()
    }
  }

  public init(items: [String]? = nil) {
    super.init(frame: .zero)

    setComponent()
    setTextMenu()
    setAutoLayout()
    setItems(items: items!)
  }

  public init(icon: [MenuIconModel]) {
    super.init(frame: .zero)

    setComponent()
    setIconMenu()
    setAutoLayout()
    setIconMenu(icons: icon)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
  }

  override func setAutoLayout() {
    collectionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }
  }
}

extension CollectionMenuView {
  private func setIconMenu(icons: [MenuIconModel]) {
    self.menuIcons = icons
  }

  private func setIconMenu() {
    collectionView.bounces = false
    collectionView.register(cellType: CollectionIconCell.self)
    collectionView.alwaysBounceVertical = false
    collectionView.alwaysBounceHorizontal = false
    collectionView.isScrollEnabled = true
    collectionView.showsVerticalScrollIndicator = false
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.isPagingEnabled = true
    collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.backgroundColor = .clear
    self.addSubview(collectionView)
    setIconDataSource()
  }

  private func setIconDataSource() {
    listIcon.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: CollectionIconCell.self)
        cell.setData(model: element)
        return cell
      }.disposed(by: self.disposedBag)
  }
}

extension CollectionMenuView {
  private func setItems(items: [String]) {
    self.menuItems = items
  }

  private func setTextMenu() {
    collectionView.bounces = false
    collectionView.register(cellType: CollectionMenuCell.self)
    collectionView.alwaysBounceVertical = false
    collectionView.alwaysBounceHorizontal = false
    collectionView.isScrollEnabled = true
    collectionView.showsVerticalScrollIndicator = false
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.isPagingEnabled = true
    collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.backgroundColor = .clear
    self.addSubview(collectionView)
    setTextDataSource()
  }

  private func setTextDataSource() {
    listItem.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: CollectionMenuCell.self)
        cell.setData(name: element)
        return cell
      }.disposed(by: self.disposedBag)
  }
}

extension CollectionMenuView: UICollectionViewDelegate {
  func numberOfSections(in collectionView: UICollectionView) -> Int {
    return 1
  }

  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    let list = self.listItem.value
    return list.count
  }

  func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
    selectItemWithDelegate.onNext(indexPath)
  }
}

extension CollectionMenuView: UICollectionViewDelegateFlowLayout {
  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumInteritemSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    sizeForItemAt indexPath: IndexPath
  ) -> CGSize {
    let hieght = defaultHieght
    if let count = menuItems?.count {
      let width = (UIScreen.width - 40) / CGFloat(count)
      return CGSize(width: width, height: hieght)
    } else if let count = menuIcons?.count {
      let width = (UIScreen.width - 40) / CGFloat(count)
      return CGSize(width: width, height: hieght)
    } else {
      return CGSize(width: 0.0, height: hieght)
    }
  }
}
