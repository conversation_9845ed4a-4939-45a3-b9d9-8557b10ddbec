//
//  ChartMenuView.swift
//  Hub
//
//  Created by ncn on 2023/03/20.
//

import UIKit

class ChartMenuView: BaseView {
  let dateLabel = UILabel()

  lazy var previousButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "day_prev"), for: .normal)  // <<
    button.tintColor = .mediumGray
    return button
  }()

  lazy var previousMonthButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "month_prev"), for: .normal)  // <<
    button.tintColor = .mediumGray
    return button
  }()

  lazy var nextButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "day_next"), for: .normal)  // >>
    button.tintColor = .mediumGray
    return button
  }()

  lazy var nextMonthButton: UIButton = {
    let button = UIButton()
    button.setImage(UIImage(named: "month_next"), for: .normal)  // >>
    button.tintColor = .mediumGray
    return button
  }()

  public init() {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    self.backgroundColor = .background
    dateLabel.font = .sub2
    dateLabel.textColor = .mainBlack
    dateLabel.text = "2024/09/04"
    dateLabel.sizeToFit()
    dateLabel.adjustsFontSizeToFitWidth = true
    dateLabel.text = Date().toString(format: "yyyy/MM/dd")
    self.addSubview(dateLabel)
    self.addSubview(previousButton)
    self.addSubview(previousMonthButton)
    self.addSubview(nextButton)
    self.addSubview(nextMonthButton)
  }

  override func setAutoLayout() {
    dateLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.center.equalTo(self)
    }

    previousMonthButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.dateLabel.snp.centerY)
      make.width.height.equalTo(24)
      make.left.equalToSuperview().offset(15)
    }

    previousButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.dateLabel.snp.centerY)
      make.width.height.equalTo(24)
      make.left.equalTo(self.previousMonthButton.snp.right).offset(20)
    }

    nextMonthButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.dateLabel.snp.centerY)
      make.width.height.equalTo(24)
      make.right.equalToSuperview().offset(-15)
    }

    nextButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.centerY.equalTo(self.dateLabel.snp.centerY)
      make.width.height.equalTo(24)
      make.right.equalTo(self.nextMonthButton.snp.left).offset(-20)
    }
  }

}
