//
//  FileEditMenuView.swift
//  Hub
//
//  Created by ncn on 2023/03/14.
//

import UIKit

class FileEditMenuView: UIView {
  let nameLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .body2
    label.sizeToFit()
    return label
  }()

  let dateLabel: UILabel = {
    let label = UILabel()
    label.textColor = .grayText
    label.font = .body5
    label.textAlignment = .center
    label.sizeToFit()
    return label
  }()
  
  let dateLabelView: UIView = {
    let containerView = UIView()
    containerView.backgroundColor = .background
    containerView.roundCorners(.allCorners, radius: 5)
    containerView.pin.width(140).activate()
    return containerView
  }()

  #if false
  let channelButton: UIButton = {
    let button = UIButton()
    button.setTitle("1ch", for: .normal)
    button.setTitleColor(.mainBlack, for: .normal)
    button.setImage(UIImage(systemName: "chevron.down")?.withTintColor(.mainBlack, renderingMode: .alwaysOriginal), for: .normal)
    
    // Configure image position
    button.semanticContentAttribute = .forceRightToLeft  // Image on right
    button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8)  // Space between text and image
    
    button.roundCorners(.allCorners, radius: 6)
    button.setBackgroundColor(.grayButton, for: .normal)
    return button
  }()
  #endif
  
  public init() {
    super.init(frame: .zero)
    self.backgroundColor = .white
    dateLabelView.addSubview(dateLabel)
    dateLabel.pin.horizontally(offset: 8).vertically(offset: 4).activate()

    HStackView(alignment: .center) {
      VStackView(spacing: 6) {
        nameLabel
        HStackView {
          dateLabelView.withHeight(26)
          UIView()
        }
      }
      UIView()
    }.withMargins(.init(horizontal: 15, vertical: 10))
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
