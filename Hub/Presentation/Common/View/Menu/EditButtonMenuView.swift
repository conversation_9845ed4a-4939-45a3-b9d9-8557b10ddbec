//
//  EditButtonMenuView.swift
//  Hub
//
//  Created by leejungchul on 2023/10/12.
//

import RxRelay
import RxSwift
import SnapKit
import UIKit

class EditButtonMenuView: BaseView {

  let horizonStackView = UIStackView()

  let channelButton = UIButton()
  let gpsButton = UIButton()
  let audioButton = UIButton()

  let rxSelectedTitle: PublishRelay<String> = PublishRelay()
  let rxSelectedIndex: PublishRelay<Int> = PublishRelay()

  public init() {
    super.init(frame: .zero)
    setComponent()
    setAutoLayout()
  }
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
    horizonStackView.alignment = .fill
    horizonStackView.axis = .horizontal
    horizonStackView.distribution = .fillEqually
    horizonStackView.spacing = 10
    self.addSubview(horizonStackView)

    channelButton.setTitle("1ch", for: .normal)
    gpsButton.setTitle("GPS", for: .normal)
    audioButton.setTitle("AUDIO", for: .normal)
    audioButton.isHidden = true

    [channelButton, gpsButton, audioButton].forEach { button in
      button.titleLabel?.font = .MontserratBold(ofSize: 15)
      button.setBackgroundColor(.customCharcoalGray, for: .normal)
      button.setTitleColor(.white, for: .normal)
      button.setTitleColor(.customSoftBlue, for: .selected)

      button.tintColor = .customSteel

      button.layer.borderColor = UIColor.customSteel.cgColor
      button.layer.borderWidth = 1
      button.layer.cornerRadius = 19
      button.clipsToBounds = true
      horizonStackView.addArrangedSubview(button)
    }
  }

  override func setAutoLayout() {
    horizonStackView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.bottom.equalToSuperview().inset(11)
    }
  }

}
