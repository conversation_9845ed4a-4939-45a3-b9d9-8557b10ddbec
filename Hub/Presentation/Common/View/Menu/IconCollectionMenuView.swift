//
//  IconCollectionMenuView.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 2023/09/01.
//

import RxCocoa
import RxSwift
import UIKit

class IconCollectionMenuView: BaseView {
  let collectionView: UICollectionView = UICollectionView(
    frame: .zero,
    collectionViewLayout: UICollectionViewFlowLayout.init())

  var defaultHeight: CGFloat = 50
  let listIcon: BehaviorRelay<[MenuIconModel]> = BehaviorRelay(value: [])
  var menuIcons: [MenuIconModel] = [] {
    didSet {
      self.listIcon.accept(menuIcons)
      self.collectionView.reloadData()
    }
  }

  var disableIndex: [Int] = []

  let selectIndexPathWithDelegate = PublishSubject<IndexPath>()
  let selectStorageTypeWithDelegate = PublishSubject<FileStorageType>()

  public init(icon: [MenuIconModel] = []) {
    super.init(frame: .zero)

    setComponent()
    setIconMenu()
    setAutoLayout()
    setIconMenu(icons: icon)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {
  }

  override func setAutoLayout() {
    collectionView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.edges.equalTo(self)
    }
  }
}

extension IconCollectionMenuView {
  private func setIconMenu(icons: [MenuIconModel]) {
    self.menuIcons = icons
  }

  private func setIconMenu() {
    collectionView.bounces = false
    collectionView.register(cellType: CollectionIconCell.self)
    collectionView.alwaysBounceVertical = false
    collectionView.alwaysBounceHorizontal = false
    collectionView.isScrollEnabled = true
    collectionView.showsVerticalScrollIndicator = false
    collectionView.showsHorizontalScrollIndicator = false
    collectionView.isPagingEnabled = true
    collectionView.rx.setDelegate(self).disposed(by: self.disposedBag)
    collectionView.backgroundColor = .clear
    self.addSubview(collectionView)
    setIconDataSource()
  }

  private func setIconDataSource() {
    listIcon.asObservable()
      .bind(to: collectionView.rx.items) { [weak self] (collectionView, row, element) in
        guard let self = self else { return UICollectionViewCell() }
        let indexPath = IndexPath(item: row, section: 0)
        let cell = self.collectionView.dequeueReusableCell(
          for: indexPath,
          cellType: CollectionIconCell.self)
        cell.setData(model: element)
        return cell
      }.disposed(by: self.disposedBag)
  }
}

extension IconCollectionMenuView: UICollectionViewDelegate {
  func numberOfSections(in collectionView: UICollectionView) -> Int {
    return 1
  }

  func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int)
    -> Int
  {
    let list = self.listIcon.value
    return list.count
  }

  func collectionView(_ collectionView: UICollectionView, shouldSelectItemAt indexPath: IndexPath)
    -> Bool
  {
    return !(disableIndex.contains(indexPath.row))
  }

  func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
    let fileStorageType = listIcon.value[indexPath.row].fileStorageType
    selectIndexPathWithDelegate.onNext(indexPath)
    selectStorageTypeWithDelegate.onNext(fileStorageType)
  }
}

extension IconCollectionMenuView: UICollectionViewDelegateFlowLayout {
  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    minimumInteritemSpacingForSectionAt section: Int
  ) -> CGFloat {
    return 0.0
  }

  func collectionView(
    _ collectionView: UICollectionView,
    layout collectionViewLayout: UICollectionViewLayout,
    sizeForItemAt indexPath: IndexPath
  ) -> CGSize {
    let height = defaultHeight
    let count = menuIcons.count
    let width = UIScreen.width / CGFloat(count)
    return CGSize(width: width, height: height)
  }
}
