//
//  FileEiditMenuView.swift
//  Hub
//
//  Created by ncn on 2023/02/28.
//

import RxSwift
import UIKit

class FileListMenuView: BaseView {
  let allButton = UIButton()
  let sortButton = UIButton()
  let downloadButton = UIButton()
  let moveButton = UIButton()
  let deleteButton = UIButton()

  let rxSelectedIndex = PublishSubject<String>()
  var sortItems: [String] = [] {
    didSet {
      setSortItems()
    }
  }

  public init(sortItems: [String] = []) {
    super.init(frame: .zero)

    setComponent()
    setAutoLayout()
    self.sortItems = sortItems
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setComponent() {

    let color: UIColor = .customGray

    allButton.layer.borderColor = color.cgColor
    allButton.layer.borderWidth = 1.0
    allButton.layer.cornerRadius = 14
    allButton.setTitle("전체선택", for: .normal)
    allButton.setTitleColor(color, for: .normal)
    self.addSubview(allButton)

    sortButton.layer.borderColor = color.cgColor
    sortButton.layer.borderWidth = 1.0
    sortButton.layer.cornerRadius = 14
    sortButton.setTitle("정렬", for: .normal)
    sortButton.setTitleColor(color, for: .normal)
    sortButton.setImage(UIImage(systemName: "chevron.down"), for: .normal)
    sortButton.setImage(UIImage(systemName: "chevron.up"), for: .highlighted)
    sortButton.semanticContentAttribute = .forceRightToLeft
    self.addSubview(sortButton)

    downloadButton.layer.cornerRadius = 14
    downloadButton.setTitle("다운로드", for: .normal)
    downloadButton.setImage(UIImage(systemName: "arrow.down.app"), for: .normal)
    downloadButton.setTitleColor(.white, for: .normal)
    downloadButton.setBackgroundColor(.colorRGB(83, 182, 237), for: .normal)
    downloadButton.clipsToBounds = true
    self.addSubview(downloadButton)

    moveButton.setTitle("이동", for: .normal)
    moveButton.setTitleColor(.colorRGB(83, 182, 237), for: .normal)
    moveButton.setBackgroundColor(.black, for: .normal)
    moveButton.layer.cornerRadius = 14
    moveButton.clipsToBounds = true
    self.addSubview(moveButton)

    // .colorRGB(83, 182, 237) , .colorRGB(32, 33, 36)
    deleteButton.setTitle("삭제", for: .normal)
    deleteButton.setTitleColor(.colorRGB(83, 182, 237), for: .normal)
    deleteButton.setBackgroundColor(.black, for: .normal)
    deleteButton.layer.cornerRadius = 14
    deleteButton.clipsToBounds = true
    self.addSubview(deleteButton)
  }

  override func setAutoLayout() {
    var width = UIScreen.ratio * 70
    allButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self).offset(20)
      make.height.equalTo(30)
      make.width.equalTo(width)
      make.centerY.equalTo(self)
    }

    width = UIScreen.ratio * 68
    sortButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.allButton.snp.right).offset(5)
      make.height.equalTo(30)
      make.width.equalTo(width)
      make.centerY.equalTo(self)
    }

    width = UIScreen.ratio * 90
    downloadButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self.moveButton.snp.left).offset(-5)
      make.height.equalTo(30)
      make.width.equalTo(width)
      make.centerY.equalTo(self)
    }

    width = UIScreen.ratio * 40
    moveButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self.deleteButton.snp.left).offset(-5)
      make.height.equalTo(30)
      make.width.equalTo(width)
      make.centerY.equalTo(self)
    }

    deleteButton.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self).offset(-20)
      make.height.equalTo(30)
      make.width.equalTo(width)
      make.centerY.equalTo(self)
    }
  }
}

extension FileListMenuView {
  private func setSortItems() {
    if #available(iOS 14.0, *) {
      sortButton.addTarget(
        self, action: #selector(buttonTouchUpInside(sender:)), for: .touchUpInside)
    } else {
      sortButton.addTarget(
        self, action: #selector(buttonTouchUpInside(sender:)), for: .touchUpInside)
    }
  }

  private func createMenu() {
    var isSelected: Bool = false
    var idx = 0
    var actions: [UIAction] = []
    for string in sortItems {
      isSelected = (idx == -1)
      let action = UIAction(
        title: string, state: isSelected ? .on : .off,
        handler: { [weak self] action in
          guard let self = self else { return }
          self.rxSelectedIndex.onNext(action.title)
        })
      actions.append(action)
      idx += 1
    }

    if #available(iOS 14.0, *) {
      sortButton.menu = UIMenu(children: actions)
      sortButton.showsMenuAsPrimaryAction = true
    }
  }

  @objc func buttonTouchUpInside(sender: Any?) {
    let alert = UIAlertController(title: nil, message: "정렬", preferredStyle: .actionSheet)
    for string in self.sortItems {
      let action = UIAlertAction(
        title: string, style: .default,
        handler: { [weak self] action in
          guard let self = self else { return }
          if let value = action.title {
            self.rxSelectedIndex.onNext(value)
          }
        })
      alert.addAction(action)
    }
    let action = UIAlertAction(title: "닫기", style: .default, handler: { _ in })
    alert.addAction(action)

    self.parentViewController?.present(alert, animated: true, completion: nil)
  }

  private func updateActionState(actionTitle: String? = nil, menu: UIMenu) -> UIMenu {

    if #available(iOS 15.0, *) {
      sortButton.menu = UIMenu(children: [
        UIDeferredMenuElement.uncached { completion in
          var actions = [UIMenuElement]()
          actions.append(
            UIAction(title: "Test Action") { action in
            })
          completion(actions)
        }
      ])
    } else {
      // Fallback on earlier versions
    }

    if let actionTitle = actionTitle {
      menu.children.forEach { action in
        guard let action = action as? UIAction else {
          return
        }
        if action.title == actionTitle {
          action.state = .on
        }
      }
    } else {
      let action = menu.children.first as? UIAction
      action?.state = .on
    }
    return menu
  }
}
