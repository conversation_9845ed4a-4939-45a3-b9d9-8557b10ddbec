//
//  File.swift
//  Hub
//
//  Created by iosDev on 2/17/25.
//

import UIKit
import SnapKit

class SheetMenuCustomCell: BaseTableViewCell {
  public static var identifier: String = "SheetMenuCustomCell"
  
  
  let leftButton = UIButton()
  let rightButton = UIButton()
  let selectRoateView = UIImageView()
  let selectMirrorView = UIImageView()
  let valueLabel = UILabel()
  let lineView = UIView()
  var RoateMirrorFalg: Bool = false

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
    super.setSelected(selected, animated: animated)
    if RoateMirrorFalg == true {
      
      
    }
    
  }

  override func setComponent() {
    self.backgroundColor = .white

//    selectRoateView.image = UIImage(named: "tabler_icon_uncheck")
//    contentView.addSubview(selectRoateView)
    leftButton.setImage(UIImage(systemName: "tabler_icon_uncheck"), for: .normal)
    leftButton.setTitle("Rotate", for: .normal)
    leftButton.titleLabel?.font = .pretendard(ofSize: 15, weight: .bold)
    leftButton.roundCorners(.allCorners, radius: 6)
    leftButton.backgroundColor = .grayButton
    leftButton.setTitleColor(.grayText, for: .normal)
//    leftButton.imageEdgeInsets = .init(top: 0, left: 0, bottom: 0, right: 2)
    contentView.addSubview(leftButton)
    
//    datePickerConfirmButton.titleLabel?.font = .pretendard(ofSize: 18, weight: .bold)
//    datePickerConfirmButton.roundCorners(.allCorners, radius: 30)
    selectMirrorView.image = UIImage(named: "tabler_icon_uncheck")
    contentView.addSubview(selectMirrorView)
    
    rightButton.setTitle("Mirror", for: .normal)
    rightButton.setImage(UIImage(systemName: "tabler_icon_uncheck"), for: .normal)
    rightButton.titleLabel?.font = .pretendard(ofSize: 15, weight: .bold)
    rightButton.roundCorners(.allCorners, radius: 6)
    rightButton.backgroundColor = .grayButton
    rightButton.setTitleColor(.grayText, for: .normal)
    contentView.addSubview(rightButton)

    valueLabel.font = .pretendard(ofSize: 15)
    valueLabel.textColor = .mainBlack
    valueLabel.sizeToFit()
    valueLabel.adjustsFontSizeToFitWidth = true
    contentView.addSubview(valueLabel)

    lineView.backgroundColor = .divider
    contentView.addSubview(lineView)

  }

  override func setAutoLayout() {
    rightButton.pin.size(28).centerY().end(offset: -15).activate()
    leftButton.pin.size(28).centerY().end(offset: -105).activate()
    valueLabel.pin.centerY().start(offset: 15).activate()
    
    
  }
}

extension SheetMenuCustomCell {
  func setData(item: String) {
    valueLabel.text = item
  }
}
