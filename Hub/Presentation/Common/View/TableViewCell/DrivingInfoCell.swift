//
//  DriveInfoCell.swift
//  Hub
//
//  Created by ncn on 2023/02/14.
//

import UIKit

class DrivingInfoCell: BaseTableViewCell {
  public static var identifier: String = "DrivingInfoCell"
  let itemLabel = UILabel()
  let contentLabel = UILabel()
  let lineView = UIView()

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)

    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: Bool, animated: Bool) {
    super.setSelected(selected, animated: animated)
  }

  override func setComponent() {
    self.backgroundColor = .white

    itemLabel.font = .boldSystemFont(ofSize: 16)
    itemLabel.textColor = .cellText
    itemLabel.numberOfLines = 1
    itemLabel.adjustsFontSizeToFitWidth = true
    itemLabel.minimumScaleFactor = 0.5
    self.contentView.addSubview(itemLabel)

    contentLabel.textAlignment = .right
    contentLabel.font = .systemFont(ofSize: 16)
    contentLabel.textColor = .cellText
    contentLabel.adjustsFontSizeToFitWidth = true
    contentLabel.minimumScaleFactor = 0.5
    self.contentView.addSubview(contentLabel)

    lineView.backgroundColor = .background
    self.contentView.addSubview(lineView)
  }

  override func setAutoLayout() {
    itemLabel.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.leading.equalToSuperview().inset(18)
      make.trailing.equalTo(contentLabel.snp.leading).offset(-15)
      make.width.equalTo(220)
    }

    contentLabel.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.trailing.equalToSuperview().inset(18)
      make.width.equalTo(50)
    }

    lineView.snp.makeConstraints { make in
      make.height.equalTo(1)
      make.left.right.bottom.equalToSuperview()
    }
  }
}

extension DrivingInfoCell {
  func setData(data: DriveingCellModel) {
    iLogger.info("setData: \(data.item), value: \(data.value)")
    itemLabel.text = data.item
    contentLabel.text = data.value
  }
  
  func setHistoryData(data: HistoryCellModel) {
    iLogger.info("setData: \(data.item), value: \(data.value)")
    itemLabel.text = data.item
    contentLabel.text = data.value
  }
}
