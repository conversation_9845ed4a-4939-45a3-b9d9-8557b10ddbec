//
//  SettingViewCell.swift
//  Hub
//
//  Created by ncn on 2023/04/19.
//

import UIKit

class SettingCell: BaseTableViewCell {
  public static var identifier: String = "SettingCell"

  let titleLabel = UILabel()
  let valueLabel = UILabel()
  let selectChevronView = UIImageView()
  let onOff = UISwitch()
  var switchValue: Bool = false {
    didSet {
      onOff.isOn = switchValue
    }
  }
  let firmwareDot = UIView()

  var onSwitchValueChanged: ((Bool) -> Void)?
  
  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: <PERSON><PERSON>, animated: Bool) {
    super.setSelected(selected, animated: animated)
  }

  override func setComponent() {
    contentView.backgroundColor = .white

    titleLabel.textColor = .mainBlack
    titleLabel.font = .pretendard(ofSize: 15, weight: .bold)
    titleLabel.numberOfLines = 1
    titleLabel.adjustsFontSizeToFitWidth = true
    titleLabel.minimumScaleFactor = 0.5
    titleLabel.sizeToFit()

    valueLabel.textColor = .grayText
    valueLabel.textAlignment = .left
    valueLabel.numberOfLines = 1
    valueLabel.adjustsFontSizeToFitWidth = true
    valueLabel.minimumScaleFactor = 0.01
    valueLabel.font = .pretendard(ofSize: 15)
    valueLabel.sizeToFit()
    valueLabel.textAlignment = .right

    // titleLabel이 잘라거나 불필요하게 늘어나지 않도록 우선순위 높임
    titleLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
    titleLabel.setContentHuggingPriority(.defaultHigh + 1, for: .horizontal) // valueLabel보다 약간 높게 설정

    // valueLabel이 titleLabel에 맞춰 크기가 조절되도록 우선순위를 설정.
    valueLabel.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)
    valueLabel.setContentHuggingPriority(.defaultHigh, for: .horizontal)

    firmwareDot.roundCorners(.allCorners, radius: 2.5)
    firmwareDot.backgroundColor = UIColor(hexCode: "53b6ed")
    firmwareDot.isHidden = true

    let image = #imageLiteral(resourceName: "day_next.pdf").withTintColor(.icon)
    selectChevronView.image = image
    selectChevronView.tintColor = .icon
    selectChevronView.isHidden = true

    onOff.onTintColor = .vueroidBlue
    onOff.isHidden = true
    onOff.addTarget(self, action: #selector(switchValueChanged), for: .valueChanged)
  }

  override func setAutoLayout() {
    HStackView(alignment: .center, spacing: 4) {
      titleLabel
      valueLabel
      onOff.withWidth(50)
      selectChevronView.withSize(.init(width: 24, height: 24))
    }.withMargins(.horizontal(12))
    
    contentView.addSubview(firmwareDot)
    firmwareDot.pin.size(5).top(offset: 8).start(offset: 8).activate()
  }
  
  @objc private func switchValueChanged(_ sender: UISwitch) {
    switchValue = sender.isOn
    onSwitchValueChanged?(sender.isOn)
  }
  
  override func prepareForReuse() {
    super.prepareForReuse()
    setComponent()
  }
}

extension SettingCell {
  func setData(model: SettingCellPresentable, isMakeDot: Bool) {
    switch model.viewType {
    case .sheetSelect:
      titleLabel.isHidden = false
      valueLabel.isHidden = false
      selectChevronView.isHidden = false
      valueLabel.text = model.value
      onOff.isHidden = true
      
    case .sheetSelectNoValue:
      titleLabel.isHidden = false
      selectChevronView.isHidden = false
      valueLabel.isHidden = true
      onOff.isHidden = true
      
    case .sheetInfo:
      titleLabel.isHidden = false
      valueLabel.isHidden = true
      selectChevronView.isHidden = false
      onOff.isHidden = true

    case .onOffSwitch:
      titleLabel.isHidden = false
      valueLabel.isHidden = true
      valueLabel.text = model.title
      onOff.isOn = (model.value == "On")
      onOff.isHidden = false
      selectChevronView.isHidden = true
    
    case .textField:
      titleLabel.isHidden = false
      valueLabel.isHidden = false
      valueLabel.text = model.value
      onOff.isHidden = true
      selectChevronView.isHidden = false
    }

    switch model.type {
    case .fwUpdate, .scUpdate:
      firmwareDot.isHidden = !isMakeDot
    default:
      firmwareDot.isHidden = true
    }
    
    titleLabel.text = model.title
    self.backgroundColor = .background
  }

  func disableData(model: SettingCellPresentable, value: String) {
    sLogger.info("disable data: \(model.title)")
    titleLabel.text = model.title
    titleLabel.textColor = .disable
    valueLabel.textColor = .disable
    
    if model.viewType == .onOffSwitch {
      onOff.isEnabled = false
      onOff.isOn = false
    } else {
      valueLabel.text = value
    }
  }
}
