//
//  NIghtTime.swift
//  Hub
//
//  Created by iosDev on 2/17/25.
//

import UIKit
import SnapKit

struct NightTimeModel {
  static let one = "1"
  static let two = "2"
  
  
}

class NightTimeCell: BaseTableViewCell {
  public static var identifier: String = "SheetMenuCell"

  let selectImageView = UIImageView()
  let valueLabel = UILabel()
  let lineView = UIView()
  let waveString = UILabel()
  let startTimeBtn = UIButton()
  let endTimeBtn = UIButton()
  
  
  
  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: Bool, animated: Bool) {
    super.setSelected(selected, animated: animated)
    selectImageView.isHidden = !selected
  }

  override func setComponent() {
    self.backgroundColor = .white


    valueLabel.font = .pretendard(ofSize: 15)
    valueLabel.textColor = .mainBlack
    valueLabel.sizeToFit()
    valueLabel.adjustsFontSizeToFitWidth = true
    contentView.addSubview(valueLabel)

    lineView.backgroundColor = .divider
    contentView.addSubview(lineView)
    
    waveString.font = .pretendard(ofSize: 15)
    waveString.textColor = .mainBlack
    waveString.adjustsFontSizeToFitWidth = true
    contentView.addSubview(waveString)
    
    startTimeBtn.titleLabel?.font = .pretendard(ofSize: 15, weight: .bold)
    startTimeBtn.roundCorners(.allCorners, radius: 6)
    startTimeBtn.layer.borderColor = UIColor.line.cgColor
    startTimeBtn.backgroundColor = .background
    startTimeBtn.setTitleColor(.mainBlack, for: .normal)
    contentView.addSubview(startTimeBtn)
    
    endTimeBtn.titleLabel?.font = .pretendard(ofSize: 15, weight: .bold)
    endTimeBtn.roundCorners(.allCorners, radius: 6)
    endTimeBtn.layer.borderColor = UIColor.line.cgColor
    endTimeBtn.backgroundColor = .background
    endTimeBtn.setTitleColor(.mainBlack, for: .normal)
    contentView.addSubview(endTimeBtn)
    
    
    

  }

  override func setAutoLayout() {
    startTimeBtn.pin.size(28).centerY().end(offset: -114).activate()
    waveString.pin.size(16).centerY().end(offset: -98).activate()
    endTimeBtn.pin.size(24).centerY().end(offset: -15).activate()
    valueLabel.pin.centerY().start(offset: 15).activate()
    
  }
}

extension NightTimeCell {
  func setData(item: String) {
    valueLabel.text = item
    startTimeBtn.setTitle(item, for: .normal)
    endTimeBtn.setTitle(item, for: .normal)
  }
}
