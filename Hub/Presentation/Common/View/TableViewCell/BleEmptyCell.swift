//
//  BleEmptyCell.swift
//  Hub
//
//  Created by ncn on 2023/04/17.
//

import UIKit

class BleEmptyCell: BaseTableViewCell {
  let emptyView = BleEmptyView()

  public static var identifier: String = "BleEmptyCell"

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON>ol) {
    super.setSelected(selected, animated: animated)
  }

  override func setComponent() {
    self.backgroundColor = .background
    self.contentView.addSubview(emptyView)
  }

  override func setAutoLayout() {
    emptyView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.contentView).offset(20)
      make.right.equalTo(self.contentView).offset(-20)
      make.top.bottom.equalTo(self.contentView)
    }
  }
}
