//
//  SettingSectionCell.swift
//  Hub
//
//  Created by ncn on 2023/04/20.
//

import SnapKit
import UIKit

class SettingSectionCell: BaseTableViewCell {
  public static var identifier: String = "SettingCectionCell"

  let tagLineView = UIView()
  let iconImage = UIImageView()
  let titleLabel = UILabel()
  let descLabel = UILabel()
  let arrawImage = UIImageView()
  let lineView = UIView()

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: Bool, animated: Bool) {
    super.setSelected(selected, animated: animated)
  }

  override func setComponent() {
    self.backgroundColor = .colorRGB(47, 48, 52)

    tagLineView.backgroundColor = .defaultColor
    self.contentView.addSubview(tagLineView)
    
    self.contentView.addSubview(iconImage)

    titleLabel.textColor = .defaultColor
    titleLabel.font = .MontserratBold(ofSize: 18)
    self.contentView.addSubview(titleLabel)

    descLabel.textColor = .init(hexCode: "B71C1C")
    descLabel.font = .MontserratBold(ofSize: 18)
    self.contentView.addSubview(descLabel)

    arrawImage.image = UIImage(named: "icon_line-arrow_down")
    arrawImage.highlightedImage = UIImage(named: "icon_line-arrow_up")
    self.contentView.addSubview(arrawImage)

    lineView.backgroundColor = .colorRGB(38, 40, 43)
    self.contentView.addSubview(lineView)
  }

  override func setAutoLayout() {
    tagLineView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.top.bottom.equalTo(self.contentView)
      make.width.equalTo(3)
    }

    iconImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.contentView).offset(20)
      make.centerY.equalTo(self.contentView)
      make.width.height.equalTo(32)
    }

    titleLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.iconImage.snp.left).offset(32 + 10)
      make.right.equalTo(self.contentView).offset(-80)
      make.top.equalTo(self.contentView).offset(17)
      make.bottom.equalTo(self.contentView).offset(-14)
    }

    descLabel.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.trailing.equalToSuperview().inset(20)
      make.top.bottom.equalTo(self.titleLabel)
    }

    arrawImage.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.right.equalTo(self.contentView).offset(-20)
      make.centerY.equalTo(self.contentView)
      make.width.height.equalTo(22)
    }

    lineView.snp.makeConstraints { [weak self] make in
      guard let self = self else { return }
      make.leading.bottom.trailing.equalTo(self.contentView)
      make.height.equalTo(1)
    }
  }
}

extension SettingSectionCell {
  func setData(model: SettingCellSectionModel) {
    if model.sectionType == .dashcam {
      setFirstDepth(model: model)
    } else {
      setSecondDepth(model: model)
    }
  }

  private func setFirstDepth(model: SettingCellSectionModel) {
    iconImage.image = nil
    arrawImage.isHidden = true
    tagLineView.isHidden = true
    titleLabel.textColor = .text
    titleLabel.font = .MontserratBold(ofSize: 16)
    titleLabel.text = model.sectionType.name
    descLabel.font = .MontserratBold(ofSize: 16)
    descLabel.text = model.desc
    arrawImage.isHighlighted = !model.isFold
    self.backgroundColor = .colorRGB(32, 33, 36)

    titleLabel.snp.updateConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.iconImage.snp.left).offset(0)
    }
  }

  private func setSecondDepth(model: SettingCellSectionModel) {
//    if let name = model.image {
//      iconImage.image = UIImage(named: name)
//    }

    tagLineView.isHidden = false
    //    arrawImage.isHidden = fall.text = model.sectionType.name
    titleLabel.textColor = .defaultColor
    titleLabel.font = .MontserratRegular(ofSize: 16)
    titleLabel.text = model.sectionType.name
    descLabel.font = .MontserratRegular(ofSize: 16)
    descLabel.text = model.desc
    self.backgroundColor = model.isFold ? .colorRGB(47, 48, 52) : .colorRGB(32, 33, 36)

    titleLabel.snp.updateConstraints { [weak self] make in
      guard let self = self else { return }
      make.left.equalTo(self.iconImage.snp.left).offset(32 + 10)
    }
  }
}
