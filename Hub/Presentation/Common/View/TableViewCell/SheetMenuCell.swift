//
//  SheetMenuCell.swift
//  Hub
//
//  Created by ncn on 2023/04/24.
//

import UIKit

class SheetMenuCell: BaseTableViewCell {
  public static var identifier: String = "SheetMenuCell"

  let selectImageView = UIImageView()
  let valueLabel = UILabel()
  let lineView = UIView()
  let switchControl = UISwitch()
  
  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: Bool, animated: Bool) {
    super.setSelected(selected, animated: animated)
    selectImageView.isHidden = !selected
  }

  override func setComponent() {
    self.backgroundColor = .white

    selectImageView.image = UIImage(named: "sheet_cell_check")
    contentView.addSubview(selectImageView)

    valueLabel.font = .pretendard(ofSize: 15)
    valueLabel.textColor = .mainBlack
    valueLabel.sizeToFit()
    valueLabel.adjustsFontSizeToFitWidth = true
    contentView.addSubview(valueLabel)

    lineView.backgroundColor = .divider
    contentView.addSubview(lineView)
    
    switchControl.addTarget(self, action: #selector(switchValueChanged), for: .valueChanged)
    contentView.addSubview(switchControl)
  }

  override func setAutoLayout() {
    selectImageView.pin.size(24).centerY().end(offset: -15).activate()
    valueLabel.pin.centerY().start(offset: 15).activate()
    switchControl.pin.size(CGSize(width: 51, height: 31)).centerY().end(offset: -15).activate()
  }
  
  @objc private func switchValueChanged() {
    onSwitchValueChanged?(switchControl.isOn)
  }
  
  var onSwitchValueChanged: ((Bool) -> Void)?
}

extension SheetMenuCell {
  func setData(item: String, isSwitchEnabled: Bool = false) {
    valueLabel.text = item
    switchControl.isHidden = !isSwitchEnabled
    selectImageView.isHidden = isSwitchEnabled
  }
}
