//
//  HistorySheetMenuCell.swift
//  Hub
//
//  Created by leej<PERSON><PERSON><PERSON> on 5/23/24.
//

import SnapKit
import UIKit

final class HistorySheetMenuCell: BaseTableViewCell {
  public static var identifier: String = "HistorySheetMenuCell"
  var cellState: HistoryCellState = .Normal

  let valueLabel = UILabel()
  let selectImageView = UIImageView()

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
    setComponent()
    setAutoLayout()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func setSelected(_ selected: Bool, animated: Bool) {
    super.setSelected(selected, animated: animated)
    selectImageView.isHidden = !selected
//    if cellState == .Normal {
//      valueLabel.textColor = .text
//      selectedIcon.isHidden = true
//    } else {
//      valueLabel.textColor = selected ? .customDarkSkyBlue : .customSteel
//      selectedIcon.isHidden = !selected
//    }
  }

  override func setComponent() {
    self.backgroundColor = .white
    selectImageView.image = UIImage(named: "sheet_cell_check")
    contentView.addSubview(selectImageView)

    valueLabel.font = .pretendard(ofSize: 15)
    valueLabel.textColor = .mainBlack
    valueLabel.sizeToFit()
    valueLabel.adjustsFontSizeToFitWidth = true
    contentView.addSubview(valueLabel)
  }

  override func setAutoLayout() {
    selectImageView.pin.size(24).centerY().end(offset: -15).activate()
    valueLabel.pin.centerY().start(offset: 15).activate()
  }

  func setData(item: String, state: HistoryCellState) {
    cellState = state
    valueLabel.text = item
  }
}
