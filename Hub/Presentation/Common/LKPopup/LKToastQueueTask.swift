//
//  LKToastQueueTask.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//

import UIKit

class LKToastQueueTask: NSObject {
  var config: LKPopupConfig?
  var toastConfig: LKToastConfig?
  weak var mainContainer: UIView?
  weak var popupView: LKPopupView?

  override init() {
    super.init()
  }

  convenience init(
    with config: LKPopupConfig,
    toastConfig: LKToastConfig?,
    mainContainer: UIView?,
    popupView: LKPopupView?
  ) {
    self.init()
    self.config = config
    self.toastConfig = toastConfig
    self.mainContainer = mainContainer
    self.popupView = popupView
  }
}
