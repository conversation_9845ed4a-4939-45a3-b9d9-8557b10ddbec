//
//  LKPopup.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//

import UIKit

public struct LKPopup<Base> {
  var popupViewController = PopupViewController()
  public let base: Base
  public init(_ base: Base) {
    self.base = base
  }
}

public protocol LKPopupCompatible {}
public extension LKPopupCompatible {
  static var popup: LKPopup<Self>.Type {
    get { LKPopup<Self>.self }
    set {}
  }

  var popup: LKPopup<Self> {
    get { LKPopup(self) }
    set {}
  }
}

public enum ToastLocation {
  case top, bottom, center
}

extension UIViewController: LKPopupCompatible{}
extension UIView: LKPopupCompatible {}



public extension LKPopup where Base:UIViewController {
  // System Alert
  // BaseController 의  showPopup, showMessageAlert 제거

  func showMessageAlert(message: String, title: String, actions: [UIAlertAction]? = nil) {
    let controller = UIAlertController(
      title: title,
      message: message,
      preferredStyle: .alert
    )

    if let acts = actions {
      for action in acts {
        controller.addAction(action)
      }
    } else {
      let action = UIAlertAction.init(title: "CLOSE", style: UIAlertAction.Style.default) {
        (action) in
      }
      controller.addAction(action)
    }

    if let vc = UIApplication.topViewController(), vc.isKind(of: UIAlertController.self) != true {
      DispatchQueue.main.async {
        vc.present(controller, animated: true, completion: nil)
      }
    }
  }
  
  func showToSetting(message: String, title: String) {
    let goToSetting: ((UIAlertAction) -> Void) = { _ in
      guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else { return }
      if UIApplication.shared.canOpenURL(settingsUrl) {
        UIApplication.shared.open(settingsUrl, completionHandler: nil)
      }
    }
    let actions = [
      UIAlertAction(title: L.cancel.localized, style: .cancel, handler: { _ in }),
      UIAlertAction(title: L.ok.localized, style: .default, handler: goToSetting)
    ]
    showMessageAlert(message: message, title: title, actions: actions)
  }

  func showPopup(title: String, desc: String, isCancel: Bool, confirmAction: (() -> Void)?) {
    self.popupViewController.dataSetting(title: title, desc: desc, isCancel: isCancel)
    self.popupViewController.tapConfirm = confirmAction
    self.popupViewController.modalTransitionStyle = .crossDissolve
    self.popupViewController.modalPresentationStyle = .overCurrentContext

    self.popupViewController.dismiss(animated: true) {
      DispatchQueue.main.async {
        self.base.present(self.popupViewController, animated: true)
      }
    }
  }

  /// popup a bottomSheet with your custom view
  /// - Parameters:
  ///   - isDismissible: default true, will tap bg auto dismiss
  ///   - enableDrag: default true, will enable drag animate
  ///   - bgColor: background view color
  ///   - container: your custom view
  func bottomSheet(
    with isDismissible: Bool = true,
    enableDrag: Bool = true,
    bgColor: UIColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.4),
    container: () -> UIView
  ) {
    var config: LKPopupConfig = .bottomSheet
    config.isDismissible = isDismissible
    config.enableDrag = enableDrag
    config.bgColor = bgColor
    let view = container()
    let vc = LKPopupController(with: config) {
      view
    }
    vc.show(with: base)
  }

  func dismissPopup() {
    self.popupViewController.dismiss(animated: true)
      //      if let popupVC = self.popupViewController {
      //        popupVC.dismiss(animated: true) {
      //          self.popupViewController = nil
      //        }
      //      }
//    LKPopupView.popup.dismissPopupView { isFinish in }
  }
}
