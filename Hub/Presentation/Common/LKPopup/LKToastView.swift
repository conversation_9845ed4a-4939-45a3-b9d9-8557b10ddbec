//
//  LKToastView.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//


import UIKit
import Toast

private var LKLoadingViewsQueue: [LKToastQueueTask] = []

public enum LKToastOption {
  case hit(String)
  case icon(UIImage.LKPopupAssetIcon)
  case enableAutoDismiss(Bool)
  case enableUserInteraction(Bool)
  case autoDismissDuration(LKTimerDuration)
  case bgColor(UIColor)
  case mainContainer(UIView)
  case withoutAnimation(Bool)
  case position(LKToastPosition)
  case enableRotation(Bool)
  case contentInset(UIEdgeInsets)
  case itemSpacing(CGFloat)
}

public struct LKToastConfig {
  var title: String?
  var assetIcon: UIImage.LKPopupAssetIcon?
  var enableRotation: Bool = false
  var contentInset: UIEdgeInsets = .init(top: 12, left: 25, bottom: 12, right: 25)
  var itemSpacing: CGFloat = 5.0
}

public class LKToastView: UIView {
  let screenSize = UIScreen.main.bounds.size

  let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .white
    label.numberOfLines = 0
    label.textAlignment = .center
    label.font = .pretendard(ofSize: 16, weight: .regular)
    label.isHidden = true
    return label
  }()

  let iconImgView: UIImageView = {
    let imageView = UIImageView()
    imageView.contentMode = .scaleAspectFit
    imageView.isHidden = true
    return imageView
  }()

  lazy var verStackView: UIStackView = {
    let stackView = UIStackView(arrangedSubviews: [self.iconImgView,self.titleLabel])
    stackView.alignment = .center
    stackView.spacing = self.config.itemSpacing
    stackView.axis = .vertical
    stackView.distribution = .fill
    return stackView
  }()

  var config: LKToastConfig = LKToastConfig()

  public convenience init?(with config: LKToastConfig) {
    //assetIcon or title must have one value
    guard config.assetIcon != nil || config.title != nil else {
      return nil
    }
    self.init(frame: .zero)
    self.config = config
    self.configSubview()
  }

  override init(frame: CGRect) {
    let rect: CGRect = .init(
      x: screenSize.width,
      y: screenSize.height,
      width: screenSize.width,
      height: screenSize.height
    )

    super.init(frame: rect)
    self.backgroundColor = .black.withAlphaComponent(0.5)
    self.layer.cornerRadius = 10
  }

  func configSubview() {
    self.addSubview(verStackView)
    titleLabel.snp.makeConstraints { make in
      make.width.lessThanOrEqualTo(
        screenSize.width - 30 - self.config.contentInset.left - self.config.contentInset.right
      )
    }
    verStackView.snp.makeConstraints { make in
      make.centerY.equalToSuperview()
      make.centerX.equalToSuperview()
    }

    if let title = self.config.title {
      self.titleLabel.text =  title
      self.titleLabel.isHidden = false
    }

    if let assetIconType = self.config.assetIcon {
      self.iconImgView.image = assetIconType.image?.withTintColor(.defaultColor)
      self.iconImgView.isHidden = false
    }

    self.layoutIfNeeded()
    let titleSize = self.titleLabel.frame.size
    let iconSize  = self.iconImgView.frame.size
    var height: CGFloat = self.config.contentInset.bottom + self.config.contentInset.top
    let horizontalInset = CGFloat(self.config.contentInset.left + self.config.contentInset.right)
    var contentWidth = max(titleSize.width, iconSize.width)
    if iconSize.width > 0 && iconSize.width + horizontalInset > titleSize.width {
      contentWidth = iconSize.width
    }
    var width = contentWidth + horizontalInset

    if  titleSize != .zero {
      height += titleSize.height
    }

    if iconSize != .zero {
      height += titleSize != .zero ? self.config.itemSpacing : 0
      height += iconSize.height
      if titleSize == .zero {
        width = height
      }
    }
    self.frame = .init(x: 0, y: 0, width: width, height: height)
    if config.enableRotation {
      self.addRotationAnimation()
    }
  }

  func addRotationAnimation() {
    if self.iconImgView.layer.animationKeys() == nil {
      let baseAni = CABasicAnimation(keyPath: "transform.rotation.z")
      let toValue: CGFloat = .pi * 2.0
      baseAni.toValue = toValue
      baseAni.duration = 1.0
      baseAni.isCumulative = true
      baseAni.repeatCount = MAXFLOAT
      baseAni.isRemovedOnCompletion = false
      self.iconImgView.layer.add(baseAni, forKey: "rotationAnimation")
    }
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

public extension LKPopup where Base: LKPopupView {

  static func hideLoading(filePath: String = #file, line: UInt = #line, funcName: String = #function) {
#if DEBUG
  let fileName = URL(fileURLWithPath: filePath).lastPathComponent
    cLogger.log("hideLoading  file:\(fileName), line: \(line) -\(funcName)")
#endif

    let work = DispatchWorkItem {
      let firstTask = LKLoadingViewsQueue.first
      if firstTask != nil {
        LKLoadingViewsQueue.removeFirst()
      }
      if let v = firstTask?.popupView {
        v.dismissPopupView { _ in }
      }
    }

    if Thread.current == Thread.main {
      work.perform()
    } else {
      DispatchQueue.main.sync(execute: work)
    }
  }

  static func loading(filePath: String = #file, line: UInt = #line, funcName: String = #function) {
#if DEBUG
  let fileName = URL(fileURLWithPath: filePath).lastPathComponent
    cLogger.log("loading  file:\(fileName), line: \(line) -\(funcName)")
#endif
    self.loading(hit: nil)
  }

  static func loading(autoDismiss: Bool) {
    self.loading(hit: nil, autoDismiss: autoDismiss, inView: nil)
  }

  static func loading(autoDismissDuraion: LKTimerDuration) {
    self.loading(hit: nil, autoDismiss: true, autoDismissDuraion: autoDismissDuraion, inView: nil)
  }

  static func loading(hit: String?) {
    self.loading(hit: hit, autoDismiss: false, inView: nil)
  }

  static func loading(
    hit: String?,
    autoDismiss: Bool = false,
    autoDismissDuraion: LKTimerDuration = .seconds(value: 3),
    inView: UIView?
  ) {
    cLogger.trace("LKPopup loading : dismiss: \(autoDismiss)")
    var options: [LKToastOption] = [
      .enableAutoDismiss(autoDismiss),
      .autoDismissDuration(autoDismissDuraion),
      .bgColor(UIColor.clear),
      .icon(UIImage.LKPopupAssetIcon.loading),
      .enableRotation(true),
      .itemSpacing(15)
    ]

    options += []
    options += [.enableUserInteraction(true)]

    if let hit = hit {
      options += [.hit(hit)]
      options += [.contentInset(.init(top: 30, left: 47, bottom: 30, right: 47))]
    } else {
      options += [.contentInset(.init(top: 35, left: 35, bottom: 35, right: 35))]
    }
    LKPopupView.popup.toast { options }
  }

  static func debugToast(hit: String) {
    #if DEBUG
    if !hit.isEmpty && hit != "nil" {
//      self.toast { [.hit("🐞 \(hit)"), .position(position)] }
      Toast.text("🐞 \(hit)").show()
    }
    #endif
  }


  static func toast(hit: String, position: Toast.Direction = .bottom, durationTime: TimeInterval = 3.0) {
    /// Toast configuration
    let toastConfig = ToastConfiguration(
      direction: position,
      dismissBy: [.time(time: durationTime), .swipe(direction: .natural), .longPress],
      animationTime: 0.2,
      allowToastOverlap: false
    )
    
    Toast.text("\(hit)", config: toastConfig).show()
  }

  static func toast(hit: String, icon: UIImage.LKPopupAssetIcon, position: LKToastPosition = .center) {
    MainAsync {
      Toast.text("\(hit)").show()      
    }
  }

  @discardableResult static func toast(options: () -> [LKToastOption]) -> LKPopupView? {
    let allOptions = options()
    var mainView: UIView?
    var config: LKPopupConfig = .dialog
    var toastConfig = LKToastConfig()
    config.bgColor = .clear
    config.enableUserInteraction = false
    config.enableAutoDismiss = true
    config.isDismissible = false
    for option in allOptions {
      switch option {
      case .hit(let hit):
        toastConfig.title = hit
        break
      case .icon(let icon):
        toastConfig.assetIcon = icon
        break
      case .enableUserInteraction(let enable):
        config.enableUserInteraction = enable
        break
      case .enableAutoDismiss(let autoDismiss):
        config.enableAutoDismiss = autoDismiss
        break
      case .autoDismissDuration(let duration):
        config.autoDismissDuration = duration
        break
      case .bgColor(let bgColor):
        config.bgColor = bgColor
        break
      case .mainContainer(let view):
        mainView = view
        break
      case .withoutAnimation(let without):
        config.withoutAnimation = without
        break
      case .position(let pos):
        config.toastPosition = pos
        break
      case .enableRotation(let enable):
        toastConfig.enableRotation = enable
        break
      case .contentInset(let inset):
        toastConfig.contentInset = inset
        break
      case .itemSpacing(let spcaing):
        toastConfig.itemSpacing = spcaing
        break
      }
    }

    guard toastConfig.title != nil || toastConfig.assetIcon != nil else {
      assert(toastConfig.title != nil || toastConfig.assetIcon != nil, "title or assetIcon only can one value nil")
      return nil
    }
    guard LKLoadingViewsQueue.count == 0 else {
      Log.warning(to: "only can show single loading need manual dismiss) view in the same time")
      return nil
    }

    let popupView = self.custom(with: config, yourView: mainView) { mainContainer in
      LKToastView(with: toastConfig)
    }

    if config.enableAutoDismiss == false {
      Self.safeAppendToastTask(
        task: LKToastQueueTask(
          with: config,
          toastConfig: toastConfig,
          mainContainer: mainView,
          popupView: popupView
        )
      )
    }
    return popupView
  }

  private static func safeAppendToastTask(task: LKToastQueueTask) {
    let work = DispatchWorkItem {
      LKLoadingViewsQueue.append(task)
    }
    if Thread.current == Thread.main {
      work.perform()
    } else {
      DispatchQueue.main.sync(execute: work)
    }
  }
}
