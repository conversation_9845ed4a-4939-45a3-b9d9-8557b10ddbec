//
//  LKAlertView.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public enum LKAlertOption {
  case title(String)
  case titleColor(UIColor)
  case subTitle(String)
  case subTitleColor(UIColor)
  case detail(String)
  case detailColor(UIColor)
  case showCancel(Bool)
  case showMiddleButton(Bool) // 중간 버튼을 표시할지 여부
  case cancelAction([LKAlertActionOption])
  case middleAction([LKAlertActionOption])
  case confirmAction([LKAlertActionOption])
  case confirmActionWithText(((String?) -> Void)?) // TextField 값을 받는 확인 액션
  case withoutAnimation(Bool)
  case textAlignment(NSTextAlignment)
  case textFieldPlaceholder(String?)
  case textFieldText(String?)
}

public struct LKAlertConfig {
  var title: String?
  var titleColor: UIColor = .mainBlack
  var subTitle: String?
  var subTitleColor: UIColor = .mainBlack
  var detail: String?
  var detailColor: UIColor = .vueroidBlue
  var showCancel = true
  var showMiddleButton = false
  var cancelAction: [LKAlertActionOption]?
  var middleAction: [LKAlertActionOption]?
  var confirmAction: [LKAlertActionOption]?
  var confirmActionWithText: ((String?) -> Void)? // TextField 값을 받는 확인 액션 콜백
  var itemSpacing: CGFloat = 10
  var contentInset = UIEdgeInsets.init(top: 20, left: 20, bottom: 20, right: 20)
  var textAlignment: NSTextAlignment = .center
  var textFieldPlaceholder: String? // TextField placeholder
  var textFieldText: String? // TextField 초기 텍스트
}

class LKAlertView: UIView {

  let margin: CGFloat = 40

  var cancelAction: LKAlertAction?
  var middleAction: LKAlertAction?
  var confirmAction: LKAlertAction?

  lazy var titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = self.config.titleColor
    label.numberOfLines = 1
    label.textAlignment = .left
    label.font = .h1
    label.isHidden = true
    label.sizeToFit()
    return label
  }()

  lazy var subTitleLabel: UILabel = {
    let label = UILabel()
    label.textColor = self.config.subTitleColor
    label.numberOfLines = 0
    label.textAlignment = .left
    label.font = .sub3
    label.isHidden = true
    label.sizeToFit()
    return label
  }()

  lazy var detailLabel: UILabel = {
    let label = UILabel()
    label.textColor = self.config.detailColor
    label.numberOfLines = 0
    label.textAlignment = .justified
    label.font = .sub3
    label.isHidden = true
    label.sizeToFit()
    return label
  }()
  
  lazy var inputTextField: UITextField = {
    let textField = UITextField()
    textField.borderStyle = .roundedRect
    textField.font = .body1
    textField.isHidden = true // 기본적으로 숨김
    textField.placeholder = self.config.textFieldPlaceholder
    textField.text = self.config.textFieldText
    textField.delegate = self
    return textField
  }()

  lazy var verStackView: UIStackView = {
    let stackView = UIStackView(arrangedSubviews: [self.titleLabel, self.subTitleLabel, self.detailLabel, self.inputTextField])
    stackView.alignment = .fill // textField가 늘어나도록 fill로 변경
    stackView.spacing = self.config.itemSpacing
    stackView.axis = .vertical
    stackView.distribution = .fill
    return stackView
  }()

  let bottomView: UIView = {
    let view = UIView()
    view.backgroundColor = .white
    return view
  }()

  var config: LKAlertConfig = LKAlertConfig()
  var clickActionHandle: (() -> ())?

  public convenience init?(with config: LKAlertConfig) {
    //subTitle or title must have one value
    guard config.subTitle != nil || config.title != nil || config.detail != nil else {
      return nil
    }
    self.init(frame: .zero)
    self.config = config
    self.configSubview()
  }

  override init(frame: CGRect) {
    super.init(
      frame: CGRect(
        x: CGSize.popup.screenWidth(),
        y: CGSize.popup.screenHeight(),
        width: CGSize.popup.screenWidth(),
        height: CGSize.popup.screenHeight()
      )
    )
    self.layer.cornerRadius = 10
    self.layer.masksToBounds = true
    self.backgroundColor = .white
  }

//  func configAutolayout() {
//    self.addSubview(self.verStackView)
//    self.addSubview(self.bottomView)
//
//    let width = CGSize.popup.screenWidth() - (
//      margin * 2 + self.config.contentInset.left + self.config.contentInset.right
//    )
//
//    titleLabel.pin.width(width).activate()
//    subTitleLabel.pin.width(width).activate()
//    
//    verStackView.pin.centerX().centerY(offset: -28).activate()
//    bottomView.pin
//      .horizontally(offset: 20)
//      .below(verStackView, offset: 20)
//      .bottom(offset: -20)
//      .height(42)
//      .activate()
//  }

  func configAutolayout() {
    self.bottomView.translatesAutoresizingMaskIntoConstraints = false
    self.addSubview(self.bottomView)
    self.addConstraints([
      NSLayoutConstraint(item: self.bottomView, attribute: .left, relatedBy: .equal, toItem: self, attribute: .left, multiplier: 1, constant: 0),
      NSLayoutConstraint(item: self.bottomView, attribute: .right, relatedBy: .equal, toItem: self, attribute: .right, multiplier: 1, constant: 0),
      NSLayoutConstraint(item: self.bottomView, attribute: .bottom, relatedBy: .equal, toItem: self, attribute: .bottom, multiplier: 1, constant: 0),
      NSLayoutConstraint(item: self.bottomView, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: 42 + 20),
    ])

    self.addSubview(self.verStackView)
    self.verStackView.translatesAutoresizingMaskIntoConstraints = false
    self.verStackView.addConstraints(
      [
        NSLayoutConstraint(
          item: self.titleLabel,
          attribute: .width,
          relatedBy: .lessThanOrEqual,
          toItem: nil,
          attribute: .width,
          multiplier: 1,
          constant: CGSize.popup.screenWidth() - (
            margin * 2 + self.config.contentInset.left + self.config.contentInset.right
          )
        ),
        NSLayoutConstraint(
          item: self.subTitleLabel,
          attribute: .width,
          relatedBy: .lessThanOrEqual,
          toItem: nil,
          attribute: .width,
          multiplier: 1,
          constant: CGSize.popup.screenWidth() - (
            margin * 2 + self.config.contentInset.left + self.config.contentInset.right
          )
        ),
        NSLayoutConstraint(
          item: self.detailLabel,
          attribute: .width,
          relatedBy: .lessThanOrEqual,
          toItem: nil,
          attribute: .width,
          multiplier: 1,
          constant: CGSize.popup.screenWidth() - (
            margin * 2 + self.config.contentInset.left + self.config.contentInset.right
          )
        ),
        NSLayoutConstraint(
          item: self.inputTextField,
          attribute: .width,
          relatedBy: .equal,
          toItem: nil,
          attribute: .width,
          multiplier: 1,
          constant: CGSize.popup.screenWidth() - (
            margin * 2 + self.config.contentInset.left + self.config.contentInset.right
          )
        ),
        NSLayoutConstraint(
          item: self.inputTextField,
          attribute: .height,
          relatedBy: .equal,
          toItem: nil,
          attribute: .height,
          multiplier: 1,
          constant: 40
        ),
      ]
    )
    
    self.addConstraints(
      [
        NSLayoutConstraint(item: self.verStackView, attribute: .left, relatedBy: .equal, toItem: self, attribute: .left, multiplier: 1, constant: config.contentInset.left),
        NSLayoutConstraint(item: self.verStackView, attribute: .right, relatedBy: .equal, toItem: self, attribute: .right, multiplier: 1, constant: -config.contentInset.right),
        NSLayoutConstraint(item: self.verStackView, attribute: .centerX, relatedBy: .equal, toItem: self, attribute: .centerX, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.verStackView, attribute: .centerY, relatedBy: .equal, toItem: self, attribute: .centerY, multiplier: 1, constant: -27.5),
      ]
    )
  }

  func configSubview() {

    self.configAutolayout()
    var cancelActionOptions: [LKAlertActionOption]? = LKAlertActionOption.cancel
    var middleActionOptions: [LKAlertActionOption]? = LKAlertActionOption.middle
    if let cancel = self.config.cancelAction {
      cancelActionOptions = cancel
    }

    if let middle = self.config.middleAction {
      middleActionOptions = middle
    }

    if self.config.showCancel == false {
      cancelActionOptions = nil
    }
    
    if self.config.showMiddleButton == false {
      middleActionOptions = nil
    }

    var arrangedSubviews: [UIView] = []
    if let cancel = cancelActionOptions {
      let action = LKAlertAction(with: cancel, defaultColor: LKAlertCancelColor)
      action.clickBtnCallBack = { [weak self] in
        if let supV = self?.superview as? LKPopupView {
          supV.dismissPopupView { isFinished in

          }
        }
      }
      self.cancelAction = action
      if let btn = action.buildActionButton() {
        arrangedSubviews.append(btn)
      }
    }

    if let middle = middleActionOptions {
      let action = LKAlertAction(with: middle, defaultColor: LKAlertOkColor)
      action.clickBtnCallBack = { [weak self] in
        if let supV = self?.superview as? LKPopupView {
          supV.dismissPopupView { isFinished in

          }
        }
      }
      self.middleAction = action
      if let btn = action.buildActionButton() {
        arrangedSubviews.append(btn)
      }
    }

    // TextField를 사용하는 확인 액션 처리
    if let confirmWithTextCallback = config.confirmActionWithText {
      let confirmOptions = config.confirmAction ?? LKAlertActionOption.ok // 기본 확인 버튼 옵션 사용 또는 커스텀 옵션
      let action = LKAlertAction(with: confirmOptions, defaultColor: LKAlertOkColor)
      action.tapActionCallback = { [weak self] in // tapActionCallback을 사용하여 텍스트 전달
        let text = self?.inputTextField.text
        confirmWithTextCallback(text)
        if let supV = self?.superview as? LKPopupView {
          supV.dismissPopupView { isFinished in }
        }
      }
      self.confirmAction = action
      if let btn = action.buildActionButton() {
        arrangedSubviews.append(btn)
      }
    } else if let confirm = config.confirmAction { // 기존 확인 액션 처리
      let action = LKAlertAction(with: confirm, defaultColor: LKAlertOkColor)
      action.clickBtnCallBack = { [weak self] in
        if let supV = self?.superview as? LKPopupView {
          supV.dismissPopupView { isFinished in

          }
        }
      }
      self.confirmAction = action
      if let btn = action.buildActionButton() {
        arrangedSubviews.append(btn)
      }
    }

    if arrangedSubviews.count > 0 {
      if let cancelButton = arrangedSubviews.first,
         let okButton = arrangedSubviews.last {
              
        let stackView = UIStackView(arrangedSubviews: arrangedSubviews)
        stackView.backgroundColor = .clear
        stackView.alignment = .center
        stackView.spacing = 10
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually

        cancelButton.layer.cornerRadius = 5

        okButton.layer.cornerRadius = 5

        var constraints: [NSLayoutConstraint] = []
        constraints.append(NSLayoutConstraint(item: cancelButton, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: 42))

        if arrangedSubviews.count == 3 {
          if let middleButton = arrangedSubviews.dropFirst().first {
            middleButton.layer.cornerRadius = 5
            
            constraints.append(NSLayoutConstraint(item: middleButton, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: 42))
          }
        }

        if cancelButton != okButton {
          constraints.append(NSLayoutConstraint(item: okButton, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: 42))
        }
        self.bottomView.addSubview(stackView)
        stackView.translatesAutoresizingMaskIntoConstraints = false
        stackView.addConstraints(constraints)

        self.addConstraints([
          NSLayoutConstraint(item: stackView, attribute: .left, relatedBy: .equal, toItem: self.bottomView, attribute: .left, multiplier: 1, constant: config.contentInset.left),
          NSLayoutConstraint(item: stackView, attribute: .right, relatedBy: .equal, toItem: self.bottomView, attribute: .right, multiplier: 1, constant: -config.contentInset.right),
          NSLayoutConstraint(item: stackView, attribute: .bottom, relatedBy: .equal, toItem: self.bottomView, attribute: .bottom, multiplier: 1, constant: 0),
          NSLayoutConstraint(item: stackView, attribute: .top, relatedBy: .equal, toItem: self.bottomView, attribute: .top, multiplier: 1, constant: 1),
        ])      }
    }

    if let title = self.config.title {
      self.titleLabel.text =  title
      self.titleLabel.isHidden = false
    }

    if let subTitle = self.config.subTitle {
      self.subTitleLabel.text =  subTitle
      self.subTitleLabel.isHidden = false
    }
    
    if let detailTitle = self.config.detail {
      self.detailLabel.text =  detailTitle
      self.detailLabel.isHidden = false
    }

    if let placeholder = self.config.textFieldPlaceholder {
      self.inputTextField.placeholder = placeholder
      self.inputTextField.text = self.config.textFieldText
      self.inputTextField.keyboardType = .numberPad
      // textfield max length

//      self.inputTextField.autocapitalizationType = self.config.textFieldAutocapitalizationType
//      self.inputTextField.autocorrectionType = self.config.textFieldAutocorrectionType
      self.inputTextField.isSecureTextEntry = true
      self.inputTextField.isHidden = false
    }
    
    self.layoutIfNeeded()
    let titleSize = self.titleLabel.frame.size
    let subTitleSize  = self.subTitleLabel.frame.size
    let detailSize = self.detailLabel.frame.size
    var height: CGFloat = self.config.contentInset.bottom + self.config.contentInset.top
    
    let screenWidth = CGSize.popup.screenWidth()
    let screenHeight = CGSize.popup.screenHeight()
    let isLandscape = screenWidth > screenHeight
    
    cLogger.debug("Screen Width: \(screenWidth), Height: \(screenHeight), Landscape: \(isLandscape)")
    // 가로 모드일 때는 최대 너비를 제한

    //FIXME: - 현재 가로모드의 모든 팝업이 길어지게 되어 있습니다. (AI 번호판식별 강화 ->Info에 있는 부분때문에
    let maxWidth: CGFloat = isLandscape ? min(screenHeight * 1.35, 650) : screenWidth - margin * 2

    let width = maxWidth
//    let width = CGSize.popup.screenWidth() - margin * 2

    // subTitleLabel의 실제 height 계산
    var subTitleHeight: CGFloat = 0
    if let subTitle = self.config.subTitle, !subTitle.isEmpty {
      let constraintRect = CGSize(width: width - self.config.contentInset.left - self.config.contentInset.right, height: .greatestFiniteMagnitude)
      let boundingBox = subTitle.boundingRect(
        with: constraintRect,
        options: [.usesLineFragmentOrigin, .usesFontLeading],
        attributes: [.font: self.subTitleLabel.font ?? UIFont.systemFont(ofSize: 15)],
        context: nil)
      subTitleHeight = ceil(boundingBox.height)
    }
    
    var detailHeight: CGFloat = 0
    if let detail = self.config.detail, !detail.isEmpty {
      let constraintRect = CGSize(width: width - self.config.contentInset.left - self.config.contentInset.right, height: .greatestFiniteMagnitude)
      let boundingBox = detail.boundingRect(
        with: constraintRect,
        options: [.usesLineFragmentOrigin, .usesFontLeading],
        attributes: [.font: self.detailLabel.font ?? UIFont.systemFont(ofSize: 15)],
        context: nil)
      detailHeight = ceil(boundingBox.height)
    }
    
    if titleSize != .zero {
      height += titleSize.height
    }

    if subTitleSize != .zero {
      height += titleSize != .zero ? self.config.itemSpacing : 0
      height += subTitleHeight
    }
    
    if detailSize != .zero {
      height += (titleSize != .zero || subTitleSize != .zero) ? self.config.itemSpacing : 0
      height += detailHeight
    }
    
    // 텍스트 필드가 표시되는 경우 높이 추가
    if !self.inputTextField.isHidden {
      height += self.config.itemSpacing + 40 // 텍스트 필드 높이
    }
    
    height += 42
    self.frame = CGRect(x: 0, y: 0, width: width, height: height)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

}

extension LKAlertView: UITextFieldDelegate {
  // MARK: - UITextFieldDelegate
  func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
    guard textField == inputTextField else { return true }
    
    let currentText = textField.text ?? ""
    guard let stringRange = Range(range, in: currentText) else { return false }
    let updatedText = currentText.replacingCharacters(in: stringRange, with: string)
    
    // Allow only digits and limit to 4 characters
    let allowedCharacters = CharacterSet.decimalDigits
    let characterSet = CharacterSet(charactersIn: string)
    
    return updatedText.count <= 4 && allowedCharacters.isSuperset(of: characterSet)
  }
}
