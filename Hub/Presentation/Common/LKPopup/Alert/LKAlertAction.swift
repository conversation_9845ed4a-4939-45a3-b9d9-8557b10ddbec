//
//  LKAlertAction.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public typealias TapActionCallBack = () -> ()

let LKAlertCancelColor = UIColor.mainBlack
let LKAlertOkColor = UIColor.white

public enum LKAlertActionOption {
  case textColor(UIColor)
  case bgColor(UIColor)
  case text(String)
  case tapActionCallback(TapActionCallBack)

  static var cancel: [LKAlertActionOption] = [
    .text(L.cancel.localized),
    .textColor(LKAlertCancelColor),
    .bgColor(.grayButton)
  ]

  static var middle: [LKAlertActionOption] = [
    .text(L.ok.localized),
    .textColor(LKAlertOkColor),
    .bgColor(.vueroidBlue)
  ]

  static var ok: [LKAlertActionOption] = [
    .text(L.ok.localized),
    .textColor(LKAlertOkColor),
    .bgColor(.vueroidBlue)
  ]
}

class LKAlertAction: NSObject {

  var options: [LKAlertActionOption] = []
  var clickBtnCallBack: (() -> ())?
  var tapActionCallback: (() -> ())? // ((String?) -> Void)? 로 변경하지 않고, LKAlertView에서 직접 처리
  var defaultColor: UIColor = .vueroidBlue

  convenience init(with options: [LKAlertActionOption], defaultColor: UIColor) {
    self.init()
    self.options = options
    self.defaultColor = defaultColor
  }

  override init() {
    super.init()
  }

  deinit {
    print("LKAlertAction dealloc")
  }

  @objc func clickAction() {
    self.clickBtnCallBack?()
    self.tapActionCallback?() // tapActionCallback 호출 추가
  }

  func buildActionButton() -> UIButton? {
    var color = self.defaultColor
    var backgroundColor: UIColor = .background
    var text: String?

    for option in options {
      switch option {
      case .textColor(let textColor):
        color = textColor
      case .bgColor(let bgColor):
        backgroundColor = bgColor
      case .text(let t):
        text = t
      case .tapActionCallback(let tap):
        self.tapActionCallback = tap
      }
    }

    guard let text = text else {
      return nil
    }

    let button: UIButton = {
      let btn = UIButton(type: .custom)
      btn.setTitle(text, for: .normal)
      btn.titleLabel?.font = .pretendard(ofSize: 13)
      btn.setTitleColor(color, for: .normal)
      btn.roundCorners(.allCorners, radius: 5)
      btn.titleLabel?.adjustsFontSizeToFitWidth = true
      btn.titleLabel?.minimumScaleFactor = 0.5
      btn.setBackgroundColor(backgroundColor, for: .normal)
      btn.addTarget(self, action: #selector(clickAction), for: .touchUpInside)
      return btn
    }()
    return button
  }
}
