//
//  LKAlertView+PopupView.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

public extension LKPopup where Base: LKPopupView {

  @discardableResult static func alert(options: () -> [LKAlertOption]) -> LKPopupView? {
    let allOptions = options()
    var config: LKPopupConfig = .dialog
    var alertConfig = LKAlertConfig()
    config.enableUserInteraction = true
    config.enableAutoDismiss = false
    config.isDismissible = false
    config.toastPosition = .center

    for option in allOptions {
      switch option {
      case .title(let string):
        alertConfig.title = string
      case .titleColor(let uIColor):
        alertConfig.titleColor = uIColor
      case .subTitle(let string):
        alertConfig.subTitle = string
      case .subTitleColor(let uIColor):
        alertConfig.subTitleColor = uIColor
      case .detail(let string):
        alertConfig.detail = string
      case .detailColor(let uIColor):
        alertConfig.detailColor = uIColor
      case .showCancel(let bool):
        alertConfig.showCancel = bool
      case .showMiddleButton(let bool):
        alertConfig.showMiddleButton = bool
      case .cancelAction(let actions):
        alertConfig.cancelAction = actions
      case .middleAction(let actions):
        alertConfig.middleAction = actions
      case .confirmAction(let actions):
        alertConfig.confirmAction = actions
      case .withoutAnimation(let bool):
        config.withoutAnimation = bool
      case .textAlignment(let alignment):
        alertConfig.textAlignment = alignment
      case .confirmActionWithText(let action):
        alertConfig.confirmActionWithText = action
      case .textFieldPlaceholder(let placeholder):
        alertConfig.textFieldPlaceholder = placeholder
      case .textFieldText(let text):
        alertConfig.textFieldText = text

      }
    }

    guard alertConfig.title != nil || alertConfig.subTitle != nil else {
      assert(alertConfig.title != nil || alertConfig.subTitle != nil, "title or subTitle only can one value nil")
      return nil
    }

    let popupView = self.custom(with: config, yourView: nil) { mainContainer in
      LKAlertView(with: alertConfig)
    }
    return popupView
  }

  @discardableResult static func alertWithTextField(options: () -> [LKAlertOption]) -> LKPopupView? {
    let allOptions = options()
    var config: LKPopupConfig = .dialog
    var alertConfig = LKAlertConfig()
    config.enableUserInteraction = true
    config.enableAutoDismiss = false
    config.isDismissible = false
    config.toastPosition = .center

    for option in allOptions {
      switch option {
      case .title(let string):
        alertConfig.title = string
      case .titleColor(let uIColor):
        alertConfig.titleColor = uIColor
      case .subTitle(let string):
        alertConfig.subTitle = string
      case .subTitleColor(let uIColor):
        alertConfig.subTitleColor = uIColor
      case .detail(let string):
        alertConfig.detail = string
      case .detailColor(let uIColor):
        alertConfig.detailColor = uIColor
      case .showCancel(let bool):
        alertConfig.showCancel = bool
      case .showMiddleButton(let bool):
        alertConfig.showMiddleButton = bool
      case .cancelAction(let actions):
        alertConfig.cancelAction = actions
      case .middleAction(let actions):
        alertConfig.middleAction = actions
      case .confirmAction(let actions):
        alertConfig.confirmAction = actions
      case .confirmActionWithText(let callback):
        alertConfig.confirmActionWithText = callback
      case .withoutAnimation(let bool):
        config.withoutAnimation = bool
      case .textAlignment(let alignment):
        alertConfig.textAlignment = alignment
      case .textFieldPlaceholder(let placeholder):
        alertConfig.textFieldPlaceholder = placeholder
      case .textFieldText(let text):
        alertConfig.textFieldText = text

      }
    }

    // title, subTitle, 또는 textField 옵션 중 하나라도 있어야 함
    guard alertConfig.title != nil || alertConfig.subTitle != nil || alertConfig.textFieldPlaceholder != nil || alertConfig.textFieldText != nil else {
      assertionFailure("title, subTitle, or textField options must be provided")
      return nil
    }
    
    // 확인 액션 콜백은 필수 (텍스트 필드 사용 시)
    guard alertConfig.confirmActionWithText != nil else {
        assertionFailure("confirmActionWithText callback must be provided when using a text field")
        return nil
    }

    let popupView = self.custom(with: config, yourView: nil) { mainContainer in
      LKAlertView(with: alertConfig)
    }
    return popupView
  }
}
