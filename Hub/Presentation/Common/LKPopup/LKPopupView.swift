//
//  LKPopupView.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//


import UIKit

/// popup via custom view use in view, not controller
public extension LKPopup where Base: LKPopupView {

  /// popup a bottomSheet with your custom view
  /// - Parameters:
  ///   - isDismissible: default true, will tap bg auto dismiss
  ///   - enableDrag: default true, will enable drag animate
  ///   - bgColor: background view color
  ///   - container: your custom view
  ///   - onDismissPopupView:  call when popup view dismissed (dealloc)
  @discardableResult static func bottomSheet(
    with isDismissible: Bool = true,
    enableDrag: Bool = true,
    bgColor: UIColor = UIColor( red: 0, green: 0, blue: 0, alpha: 0.4 ),
    yourView: UIView? = nil,
    container: @escaping (_ mainContainer: LKPopupView?) -> UIView,
    onDismissPopupView: ((_ mainContainer: LKPopupView?) -> Void)? = nil
  ) -> LKPopupView? {
    var config: LKPopupConfig = .bottomSheet
    config.isDismissible = isDismissible
    config.enableDrag = enableDrag
    config.bgColor = bgColor
    return self.custom(with: config, yourView: yourView) { mainContainer in
      container(mainContainer)
    } onDismissPopupView: { mainContainer in
      onDismissPopupView?(mainContainer)
    }
  }
}

public class LKPopupView: UIView {

  var beginTouchPoint: CGPoint = .zero
  var beginFrame: CGRect = .zero

  public weak var dataSource: LKPopupDataSource?
  public weak var popupProtocol: LKPopupAnimationProtocol?
  public var container: UIView?
  public var config: LKPopupConfig = .dialog

  public var isClosedFromTapBackground = false

  typealias DismissHandler = () -> Void
  var dismissHandler: DismissHandler?

  deinit {
    //Log.debug(category: .App, to: "deinit \(Self.self)")
  }

  public init(with config: LKPopupConfig, container: ((_ mainContainer: LKPopupView?) -> UIView?)?) {
    super.init(frame: UIScreen.main.bounds)
    self.container = container?(self)
    self.config = config
    self.configSubview()
    self.configGesture()
  }

  public init(
    with config: LKPopupConfig,
    popupProtocol: LKPopupAnimationProtocol? = nil,
    container: ( ( _ mainContainer: LKPopupView? ) -> UIView? )?
  ) {
    super.init(frame: UIScreen.main.bounds)
    self.popupProtocol = popupProtocol
    self.container = container?(self)
    self.config = config
    self.configSubview()
    self.configGesture()
  }

  func configSubview() {
    self.isUserInteractionEnabled = self.config.enableUserInteraction
    self.backgroundColor = self.config.bgColor
    if self.popupProtocol == nil {
      self.popupProtocol = self
    }
    if let container = self.container {
      self.addSubview(container)
    } else {
      if self.dataSource == nil {
        self.dataSource = self
      }
      if let v = self.dataSource?.viewForContainer() {
        self.addSubview(v)
        self.container = v
      }
    }
  }

  func configGesture() {
    let panGesture = UIPanGestureRecognizer(target: self, action: #selector(onPan(gesture:)))
    panGesture.delegate = self
    self.addGestureRecognizer(panGesture)
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tapBGAction))
    tapGesture.delegate = self
    self.addGestureRecognizer(tapGesture)
  }

  @objc func tapBGAction() {
    guard self.config.isDismissible else { return }
    self.isClosedFromTapBackground = true
    self.dismissPopupView { isFinished in
      
    }
  }

  func popup(into yourView: UIView? = nil) {
    guard let view = self.container else { return }
    guard let window = UIApplication.shared.keyWindow else { return }
    if let view = yourView {
      view.addSubview(self)
    } else {
      window.addSubview(self)
    }
    self.popupProtocol?.present(with: nil,
                                config: self.config,
                                containerView: view,
                                completion: { [weak self] isFinished in
      guard let self, self.config.enableAutoDismiss == true else { return }
      self.autoDismissHandle()
    })
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

}

// MARK: UIGestureRecognizerDelegate
extension LKPopupView: UIGestureRecognizerDelegate {

  @objc private func onPan(gesture: UIPanGestureRecognizer) {
    guard self.config.enableDrag else {
      self.tapBGAction()
      return
    }
    guard let container = self.container else { return }
    switch gesture.state {
    case .began:
      self.beginFrame = container.frame
      self.beginTouchPoint = gesture.location(in: self)
      break
    case .changed:
      guard self.config.animationType != .dialog else { break }
      self.container?.frame = self.getRectForPan(pan: gesture)
      break
    case .ended,.cancelled:
      guard self.config.animationType != .dialog else {
        self.tapBGAction()
        break
      }
      self.container?.frame = self.getRectForPan(pan: gesture)
      let isClosed: Bool = self.checkGestureToClose(gesture: gesture)
      if isClosed == true {
        self.tapBGAction()
      } else {
        UIView.animate(withDuration: 0.2) {
          self.container?.frame = self.beginFrame
        }
      }
      break

    default:
      break
    }
  }

  private func checkGestureToClose(gesture: UIPanGestureRecognizer) -> Bool {
    if self.config.animationType == .drawer {
      if self.config.direction == .left {
        return gesture.velocity(in: container).x < 0
      } else {
        return gesture.velocity(in: container).x > 0
      }
    } else if self.config.animationType == .bottomSheet {
      return gesture.velocity(in: container).y > 0
    }
    return false
  }

  private func getRectForPan(pan: UIPanGestureRecognizer) -> CGRect {
    guard let view = self.container else { return .zero }
    var rect: CGRect = view.frame
    let currentTouch = pan.location(in: self)
    if self.config.animationType == .drawer {
      let xRate = (self.beginTouchPoint.x - self.beginFrame.origin.x) / self.beginFrame.size.width
      let currentTouchDeltaX = xRate * view.width
      var x = currentTouch.x - currentTouchDeltaX
      if x < self.beginFrame.origin.x && self.config.direction == .right {
        x = self.beginFrame.origin.x
      } else if x > self.beginFrame.origin.x && self.config.direction == .left {
        x = self.beginFrame.origin.x
      }

      rect.origin.x = x
    } else if self.config.animationType == .bottomSheet {
      let yRate = (self.beginTouchPoint.y - self.beginFrame.origin.y) / self.beginFrame.size.height
      let currentTouchDeltaY = yRate * view.height
      var y = currentTouch.y - currentTouchDeltaY
      if y < self.beginFrame.origin.y {
        y = self.beginFrame.origin.y
      }
      rect.origin.y = y
    }
    return rect
  }

  public override func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
    let isTapGesture = gestureRecognizer is UITapGestureRecognizer
    let point = gestureRecognizer.location(in: gestureRecognizer.view)
    let rect = self.container?.frame ?? .zero
    let inContainer = rect.contains(point)
    if isTapGesture {
      if inContainer {
        return false
      }
      if self.config.isDismissible == false {
        return false
      }
    } else {
      if self.config.enableDrag == false || self.config.isDismissible == false {
        return false
      }
    }
    return true
  }
}

// MARK: LKPopupDataSource
extension LKPopupView: LKPopupDataSource {
  public func viewForContainer() -> UIView? {
    return nil
  }
}

// MARK: LKPopupProtocol
extension LKPopupView: LKPopupProtocol {

  public func dismissPopupView(completion: @escaping ((Bool) -> ())) {
    self.popupProtocol?.dismiss(with: nil,
                                config: self.config,
                                containerView: self.container,
                                completion: { [weak self] isFinished in
      guard let self else { return }
      self.removeFromSuperview()
      completion(isFinished)
      self.dismissHandler?()
    })
  }

  public func autoDismissHandle() {
    DispatchQueue.main.asyncAfter(deadline: .now() + self.config.autoDismissDuration.timeDuration()) {
      self.dismissPopupView { isFinished in

      }
    }
  }
}

// MARK: LKPopupAnimationProtocol
extension LKPopupView: LKPopupAnimationProtocol {
  public func dismiss(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView?,
    completion: ( (Bool) -> () )?) {

      LKPopupAnimation.dismiss(
        with: transitionContext,
        config: self.config,
        containerView: containerView,
        completion: completion
      )
  }

  public func present(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView,
    completion: ( (Bool) -> () )?
  ) {

    LKPopupAnimation.present(
      with: transitionContext,
      config: self.config,
      containerView: containerView,
      completion: completion
    )
  }
}

// MARK: LKPopupCompatible
public extension LKPopup where Base:LKPopupView {

  ///   - config: popup config
  ///   - container: custom view
  ///   - onDismissPopupView:  call when popup view dismissed (dealloc)
  @discardableResult static func custom(
    with config: LKPopupConfig,
    yourView: UIView? = nil,
    container: @escaping ( _ mainContainer: LKPopupView? ) -> UIView?,
    onDismissPopupView:  ( ( _ mainContainer: LKPopupView? ) -> Void )? = nil
  ) -> LKPopupView? {
    if Thread.current != Thread.main {
      return DispatchQueue.main.sync {
        let v = LKPopupView(with: config) { mainContainer in
          container(mainContainer)
        }
        v.dismissHandler = { [weak v] in
          onDismissPopupView?(v)
        }
        v.popup(into: yourView)
        return v
      }
    } else {
      let v = LKPopupView(with: config) { mainContainer in
        container(mainContainer)
      }
      v.dismissHandler = { [weak v] in
        onDismissPopupView?(v)
      }
      v.popup(into: yourView)
      return v
    }
  }

  @discardableResult static func drawer(
    with direction: LKPopupAnimationDirection = .left,
    isDismissible: Bool = true,
    enableDrag: Bool = true,
    bgColor: UIColor = .init( red: 0, green: 0, blue: 0, alpha: 0.4 ),
    yourView: UIView? = nil,
    container: @escaping ( _ mainContainer: LKPopupView? ) -> UIView,
    onDismissPopupView: ( ( _ mainContainer: LKPopupView? ) -> Void )? = nil
  ) -> LKPopupView? {
    var config: LKPopupConfig = .drawer
    config.direction = direction
    config.isDismissible = isDismissible
    config.enableDrag = enableDrag
    config.bgColor = bgColor
    return self.custom(with: config, yourView: yourView) { mainContainer in
      container(mainContainer)
    } onDismissPopupView: { mainContainer in
      onDismissPopupView?(mainContainer)
    }
  }
}
