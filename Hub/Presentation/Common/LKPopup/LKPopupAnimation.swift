//
//  LKPopupAnimation.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//

import UIKit
import QuartzCore

class LKPopupAnimation: NSObject {

  static func present(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView,
    completion: ( ( _ isFinished: Bool ) -> () )?
  ) {
    let type = config.animationType
    let screenSize = UIScreen.main.bounds.size
    let navigationHeight = 68.0

    switch type {
    case .bottomSheet:
      do {
        containerView.frame.origin.y = screenSize.height
        containerView.layoutIfNeeded()
        UIView.animate(withDuration: 0.25, animations: {
          containerView.frame.origin.y = screenSize.height - containerView.frame.size.height
          containerView.layoutIfNeeded()
        }) { (finished) in
          transitionContext?.completeTransition(true)
          completion?(finished)
        }
      }
      break
    case .dialog:
      do {
        let originSize = containerView.frame.size
        containerView.layoutIfNeeded()
        let updateView = {
          containerView.center = CGPoint(x: screenSize.width / 2, y: screenSize.height / 2)
          if let absoluteRect = config.absoluteRect {
            containerView.frame = absoluteRect
          } else if config.toastPosition == .top {
            containerView.top = navigationHeight + 15
          } else if config.toastPosition == .bottom {
            containerView.bottom = screenSize.height - containerView.safeAreaInsets.bottom - 35
          }
          containerView.layoutIfNeeded()
        }

        updateView()
        containerView.alpha = 0
        UIView.animate(withDuration: config.withoutAnimation ? 0 : 0.25, delay: 0, options: .curveEaseInOut) {
          containerView.alpha = 1
        } completion: { finished in
          transitionContext?.completeTransition(true)
          completion?(true)
        }
      }
      break
    case .drawer:
      do {
        var originY: CGFloat = -containerView.frame.size.width
        var targetY: CGFloat = 0
        if config.direction == .right {
          originY = screenSize.width
          targetY =  screenSize.width - containerView.frame.size.width
        }
        containerView.frame.origin.x = originY
        containerView.center.y = screenSize.height / 2
        containerView.layoutIfNeeded()
        UIView.animate(withDuration: 0.25, animations: {
          containerView.frame.origin.x = targetY
          containerView.layoutIfNeeded()
        }) { (finished) in
          transitionContext?.completeTransition(true)
          completion?(finished)
        }
      }
      break

    }
  }

  static func dismiss(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView?,
    completion: ( ( _ isFinished: Bool ) -> () )?
  ) {
    let type = config.animationType
    let screenSize = UIScreen.main.bounds.size

    switch type {
    case .bottomSheet:
      do {
        guard let _ = containerView else {
          transitionContext?.completeTransition(true)
          completion?(true)
          return
        }
        UIView.animate(withDuration: 0.25, animations: {
          containerView?.superview?.alpha = 0
          containerView?.frame.origin.y = screenSize.height
          containerView?.layoutIfNeeded()
        }) { (finished) in
          transitionContext?.completeTransition(true)
          completion?(finished)
        }
      }
      break
    case .dialog:
      do {
        UIView.animate(withDuration: 0.25, animations: {
          containerView?.subviews.forEach({ v in
            v.alpha = 0
          })
          containerView?.alpha = 0
        }) { (finished) in
          transitionContext?.completeTransition(true)
          completion?(finished)
        }
      }
      break
    case .drawer:
      do {
        guard let view = containerView else {
          transitionContext?.completeTransition(true)
          completion?(true)
          return
        }
        var targetY: CGFloat = -view.frame.size.width
        if config.direction == .right {
          targetY = screenSize.width
        }
        UIView.animate(withDuration: 0.25, animations: {
          containerView?.superview?.alpha = 0
          containerView?.frame.origin.x = targetY
          containerView?.layoutIfNeeded()
        }) { finished in
          transitionContext?.completeTransition(true)
          completion?(finished)
        }
      }
      break
    }
  }

}
