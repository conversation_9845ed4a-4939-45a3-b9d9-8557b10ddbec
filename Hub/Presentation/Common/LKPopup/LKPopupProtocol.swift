//
//  LKPopupProtocol.swift
//  Hub
//
//  Created by ncn on 7/30/24.
//

import UIKit

public protocol LKPopupAnimationProtocol: AnyObject {
  func present(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView,
    completion: ( ( _ isFinished: Bool ) -> () )?
  )
  func dismiss(
    with transitionContext: UIViewControllerContextTransitioning?,
    config: LKPopupConfig,
    containerView: UIView?,
    completion: ( ( _ isFinished: Bool ) -> () )?
  )
}

public enum LKPopupAnimationType: Int {
  case dialog
  case bottomSheet
  case drawer
}

public enum LKPopupAnimationDirection: Int {
  case left
  case right
}

public enum LKTimerDuration {
  case nanoseconds(value: UInt32)
  case microseconds(value: UInt32)
  case milliseconds(value: UInt32)
  case seconds(value: UInt32)
  case minutes(value: UInt32)

  public func timeDuration() -> DispatchTimeInterval {
    switch self {
    case .nanoseconds(value: let value):
      return .nanoseconds(Int(value))
    case .microseconds(value: let value):
      return .microseconds(Int(value))
    case .milliseconds(value: let value):
      return .milliseconds(Int(value))
    case .seconds(value: let value):
      return .seconds(Int(value))
    case .minutes(value: let value):
      return .seconds(Int(value) * 60)
    }
  }
}


public protocol LKPopupDataSource: AnyObject {
  func viewForContainer() -> UIView?
}

public enum LKToastPosition {
  case center
  case top
  case bottom
}

public struct LKPopupConfig {
  public var bgColor: UIColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.4)
  public var enableUserInteraction = true
  ///popup style
  public var animationType: LKPopupAnimationType = .dialog
  ///popup the view without animation
  public var withoutAnimation = false
  ///if trie tap background can dismiss popup view
  public var isDismissible = true
  /// enable drag gesture
  public var enableDrag = true
  /// use in drawer type , from left or right direction
  public var direction: LKPopupAnimationDirection = .left

  ///if true will auto dismiss popup view, use duration, default is false
  public var enableAutoDismiss = false
  ///auto dismiss duration, default is 3 seconds
  public var autoDismissDuration: LKTimerDuration = .seconds(value: 3)

  ///toast view position
  public var toastPosition: LKToastPosition = .center

  ///show with a absolute rect , only use in dialog mode popup
  public var absoluteRect: CGRect?

  /// static style config
  public static var dialog = LKPopupConfig(enableDrag: false)
  public static var bottomSheet = LKPopupConfig(animationType: .bottomSheet)
  public static var drawer = LKPopupConfig(animationType: .drawer, direction: .left)
}


public protocol LKPopupProtocol {
  var dataSource: LKPopupDataSource? { get set }
  var popupProtocol: LKPopupAnimationProtocol? { get set }
  var container: UIView? { get set }
  var config: LKPopupConfig { get set }
  func autoDismissHandle()
  func dismissPopupView(completion: @escaping ((_ isFinished: Bool) -> ()))
}
