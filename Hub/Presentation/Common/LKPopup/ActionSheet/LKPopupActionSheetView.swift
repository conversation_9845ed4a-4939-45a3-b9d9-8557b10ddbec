//
//  LKPopupActionSheetView.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public extension LKPopup where Base: UIViewController {
  /// popup a actionSheet
  /// - Parameters:
  ///   - autoCancelAction: is true, will add cancel action in the last
  ///   - actions: the actions item
  func actionSheet(with autoCancelAction: Bool = false, actions: (() -> [LKPopupAction])) {
    self.bottomSheet(with: true, enableDrag: false) {
      let v = LKPopupActionSheetView(with: actions(), autoCancelAction: autoCancelAction)
      v.autoDismissHandle = {
        dismissPopup()
      }
      return v
    }
  }
}

public extension LKPopup where Base: UIView {
  /// popup a actionSheet
  /// - Parameters:
  ///   - autoCancelAction: is true, will add cancel action in the last
  ///   - actions: the actions item
  @discardableResult func actionSheet(
    with autoCancelAction: Bool = false,
    actions: @escaping (() -> [LKPopupAction])
  ) -> LKPopupView? {
    return LKPopupView.popup
      .bottomSheet(
        with: true,
        enableDrag: false,
        yourView: base
      ) { mainContainer in
      let v = LKPopupActionSheetView(with: actions(), autoCancelAction: autoCancelAction)
      v.autoDismissHandle = { [weak mainContainer] in
        mainContainer?.dismissPopupView(completion: { isFinished in

        })
      }
      return v
    }
  }
}

public extension LKPopup where Base: LKPopupView {
  /// popup a actionSheet
  /// - Parameters:
  ///   - autoCancelAction: is true, will add cancel action in the last
  ///   - actions: the actions item
  @discardableResult static func actionSheet(
    with autoCancelAction: Bool = false,
    yourView: UIView? = nil,
    actions: @escaping (() -> [LKPopupAction])
  ) -> LKPopupView? {
    return self.bottomSheet(with: true, enableDrag: false, yourView: yourView) { mainContainer in
      let v = LKPopupActionSheetView(with: actions(), autoCancelAction: autoCancelAction)
      v.autoDismissHandle = { [weak mainContainer] in
        mainContainer?.dismissPopupView(completion: { isFinished in

        })
      }
      return v
    }
  }
}

class LKPopupActionView: UIView, LKPopupActionViewProtocol {

  var clickActionHandle: (() -> ())?

  let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .h2
    label.textAlignment = .left
    return label
  }()

  let subTitleLabel: UILabel = {
    let label = UILabel()
    label.textColor =  .disable
    label.font = .body2
    return label
  }()

  let valueLabel: UILabel = {
    let label = UILabel()
    label.textColor =  .mainBlack
    label.font = .sub2
    return label
  }()

  lazy var verStackView: UIStackView = {
    let subTitleStackView = UIStackView(arrangedSubviews: [subTitleLabel])
    subTitleStackView.axis = .vertical
    subTitleStackView.alignment = .leading
    
    let valueStackView = UIStackView(arrangedSubviews: [valueLabel])
    subTitleStackView.axis = .vertical
    subTitleStackView.alignment = .leading

    let stackView = UIStackView(arrangedSubviews: [valueStackView, self.titleLabel, subTitleStackView])
    stackView.alignment = .center
    stackView.spacing = 15
    stackView.distribution = .fillProportionally
    stackView.axis = .vertical
    return stackView
  }()

  var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .line
    return view
  }()
  
  var preferredHeight: CGFloat {
    return UITableView.automaticDimension
  }

  lazy var clickBtn: UIButton = {
    let button = UIButton(type: .custom)
    button.addAction(UIAction { [weak self] _ in
      self?.clickActionHandle?()
    }, for: .touchUpInside)

    button.setBackgroundColor(.clear, for: .highlighted)
    return button
  }()

  override init(frame: CGRect) {
    super.init(frame: frame)
    self.backgroundColor = .white
    self.addSubview(self.verStackView)
    self.verStackView.translatesAutoresizingMaskIntoConstraints = false
    self.addConstraints(
      [
        NSLayoutConstraint(item: self.verStackView, attribute: .leading, relatedBy: .equal, toItem: self, attribute: .leading, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.verStackView, attribute: .trailing, relatedBy: .equal, toItem: self, attribute: .trailing, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.verStackView, attribute: .centerY, relatedBy: .equal, toItem: self, attribute: .centerY, multiplier: 1, constant: 0)
      ]
    )
    self.addSubview(self.lineView)
    self.lineView.translatesAutoresizingMaskIntoConstraints = false
    self.addConstraints(
      [
        NSLayoutConstraint(item: self.lineView, attribute: .leading, relatedBy: .equal, toItem: self, attribute: .leading, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.lineView, attribute: .trailing, relatedBy: .equal, toItem: self, attribute: .trailing, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.lineView, attribute: .bottom, relatedBy: .equal, toItem: self, attribute: .bottom, multiplier: 1, constant: 0),
        NSLayoutConstraint(item: self.lineView, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .height, multiplier: 1, constant: 0.5)
      ]
    )
    self.addSubview(self.clickBtn)
    clickBtn.pin.all().activate()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

public class LKPopupActionSheetView: UIView {

  var actions: [LKPopupAction]?
  var autoDismissHandle: (() -> ())?
  var autoCancelAction: Bool = false

  public convenience init(with actions: [LKPopupAction], autoCancelAction: Bool = false) {
    self.init(frame: .zero)
    self.autoCancelAction = autoCancelAction
    self.actions = actions
    self.configSubview()
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
  }

  required init(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  func configSubview() {
    self.backgroundColor = .white
    guard let actions = self.actions, actions.count > 0 else { return }
    var originY: CGFloat = 0
    var views: [UIView] = []

    for element in actions.enumerated() {
      let action = element.element
      
      // Handle custom view if provided
      if let customView = action.customView {
        customView.clickActionHandle = { [weak self] in
          if action.autoDismiss {
            self?.autoDismissHandle?()
          }
          action.clickActionCallBack?()
        }
        
        customView.lineView.isHidden = element.offset == (actions.count - 1)
        
        let actionHeight = action.customViewHeight ?? customView.preferredHeight
        customView.frame = CGRect(
          origin: CGPoint(x: 0, y: originY),
          size: CGSize(width: CGSize.popup.screenWidth(), height: actionHeight)
        )
        
        originY += actionHeight
        self.addSubview(customView)
        views.append(customView)
        continue
      }
      
      // Handle standard action view
      var actionHeight: CGFloat = 0
      let view = LKPopupActionView()
      view.clickActionHandle = { [weak self] in
        if action.autoDismiss {
          self?.autoDismissHandle?()
        }
        action.clickActionCallBack?()
      }

      if let title = action.title {
        view.titleLabel.text = title
        view.titleLabel.isHidden = false
        view.titleLabel.sizeToFit()
        actionHeight += view.titleLabel.frame.size.height
      }

      if let subTitle = action.subTitle {
        view.subTitleLabel.text = subTitle
        view.subTitleLabel.isHidden = false
        view.subTitleLabel.sizeToFit()
        actionHeight += 5
        actionHeight += view.subTitleLabel.frame.size.height
      } else if let subAttTitle = action.subAttributedTitle {
        view.subTitleLabel.attributedText = subAttTitle
        view.subTitleLabel.isHidden = false
        view.subTitleLabel.sizeToFit()
        actionHeight += 5
        actionHeight += view.subTitleLabel.frame.size.height
      }

      if let value = action.value {
        view.valueLabel.text = value
        view.valueLabel.isHidden = false
        view.valueLabel.sizeToFit()
        actionHeight += view.valueLabel.frame.size.height
      }

      view.lineView.isHidden = element.offset == (actions.count - 1)
      actionHeight += 30
      view.frame = CGRect(
        origin: CGPoint(x: 0, y: originY),
        size: CGSize(width: CGSize.popup.screenWidth(), height: actionHeight)
      )

      originY += actionHeight
      self.addSubview(view)
      views.append(view)
    }

    if var view = views.last, self.autoCancelAction == false {
      view.popup.height += CGFloat.popup.safeAreaBottomHeight()
      self.frame.size.height = view.popup.bottom
    } else {
      var view = LKPopupActionView()
      view.titleLabel.text = L.cancel.localized
      view.clickActionHandle = { [weak self] in
        self?.autoDismissHandle?()
      }
      view.titleLabel.sizeToFit()
      view.popup.left = 0
      view.popup.top = originY + 5
      view.popup.width = CGSize.popup.screenWidth()
      view.popup.height = view.titleLabel.popup.height + 30 + CGFloat.popup.safeAreaBottomHeight()
      for constraint in view.constraints {
        if let first = constraint.firstItem,
            first.isEqual( view.verStackView ) &&
            constraint.firstAttribute == .centerY {
          constraint.constant = -CGFloat.popup.safeAreaBottomHeight() / 2
          break
        }
      }
      self.addSubview(view)
      self.frame.size.height = view.popup.bottom
    }
    
    self.frame.size.width = CGSize.popup.screenWidth()

    let path = UIBezierPath(
      roundedRect: self.bounds,
      byRoundingCorners: [ .topLeft, .topRight ],
      cornerRadii: CGSize(width: 12, height: 12)
    )
    let maskLayer = CAShapeLayer()
    maskLayer.frame = self.bounds
    maskLayer.path = path.cgPath
    self.layer.mask = maskLayer
  }

}
