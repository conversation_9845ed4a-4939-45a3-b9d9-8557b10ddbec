//
//  LKPopupAction.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public class LKPopupAction {

  var title: String?
  var subTitle: String?
  var value: String?
  var subAttributedTitle: NSAttributedString?
  var clickActionCallBack: (() -> ())?
  var autoDismiss: Bool = false
  
  // Custom view support
  var customView: LKPopupActionViewProtocol?
  var customViewHeight: CGFloat?

  public convenience init(
    with title: String?,
    subTitle: String? = nil,
    with value: String? = nil,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping (() -> ())
  ) {
    self.init()
    self.title = title
    self.subTitle = subTitle
    self.value = value
    self.autoDismiss = autoDismiss
    self.clickActionCallBack = clickActionCallBack
  }

  public convenience init(
    with title: String?,
    subAttributedTitle: NSAttributedString?,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping (() -> ())
  ) {
    self.init()
    self.title = title
    self.subAttributedTitle = subAttributedTitle
    self.autoDismiss = autoDismiss
    self.clickActionCallBack = clickActionCallBack
  }

  public convenience init(
    with value: String?,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping (() -> ())
  ) {
    self.init()
    self.value = value
    self.autoDismiss = autoDismiss
    self.clickActionCallBack = clickActionCallBack
  }
  
  // New initializer for custom views
  public convenience init(
    with customView: LKPopupActionViewProtocol,
    height: CGFloat? = nil,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping (() -> ())
  ) {
    self.init()
    self.customView = customView
    self.customViewHeight = height
    self.autoDismiss = autoDismiss
    self.clickActionCallBack = clickActionCallBack
  }
  
  public convenience init(
    withSwitchView view: LKPopupSwitchActionable,
    height: CGFloat? = nil,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping ((Bool) -> ())
  ) {
    self.init()
    self.customView = view
    self.customViewHeight = height
    self.autoDismiss = autoDismiss
    
    view.clickSwitchActionHandle = { isOn in
      clickActionCallBack(isOn)
    }
  }
  
  public convenience init(
    withBadgeView view: LKPopupBadgeSelectActionable,
    height: CGFloat? = nil,
    autoDismiss: Bool = false,
    clickActionCallBack: @escaping ((BadgeSelectType, Bool) -> ())
  ) {
    self.init()
    self.customView = view
    self.customViewHeight = height
    self.autoDismiss = autoDismiss
    
    view.clickBadgeSelectActionHandle = { type, isOn in
      clickActionCallBack(type, isOn)
    }
  }
}
