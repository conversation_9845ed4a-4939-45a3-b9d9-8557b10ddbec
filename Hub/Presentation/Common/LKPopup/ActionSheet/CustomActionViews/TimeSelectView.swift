//
//  TimeSelectView.swift
//  Hub
//
//  Created by ncn on 6/20/25.
//
import UIKit

// TimeSelectViewDelegate protocol
public protocol TimeSelectViewDelegate: AnyObject {
  func timeSelectView(_ view: TimeSelectView, didSelectHour hour: Int)
}

public class TimeSelectView: UIView {
  public let timeLabel: UILabel = {
    let label = UILabel()
    label.font = .sub1
    label.textColor = .mainBlack
    return label
  }()

  private var pickerView: UIPickerView?
  private var overlayView: UIView?

  public weak var delegate: TimeSelectViewDelegate?
  private var selectedHour: Int = 0

  var isEnabled: Bool = false {
    didSet {
      self.isUserInteractionEnabled = isEnabled
      timeLabel.textColor = isEnabled ? .mainBlack : .disable
      updateBorderColor()
    }
  }

  private func updateBorderColor() {
    self.layer.borderWidth = 1
    self.layer.borderColor = isEnabled ? UIColor.line.cgColor : UIColor.disable.cgColor
    self.layer.cornerRadius = 6
  }

  init(title: String) {
    super.init(frame: .zero)
    timeLabel.text = title
    addSubview(timeLabel)
    timeLabel.pin.center().horizontally(offset: 24).activate()

    // Extract hour value from title (e.g., "18H" -> 18)
    let hourString = title.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
    if let hour = Int(hourString) {
      selectedHour = hour
    }


    // Add tap gesture recognizer
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
    self.addGestureRecognizer(tapGesture)
    self.isUserInteractionEnabled = true
  }

  @objc private func handleTap() {
    guard isEnabled else { return }
    showTimePicker()
  }

  private func showTimePicker() {
    // Create overlay view that covers the entire screen
    if let window = UIApplication.shared.keyWindow {
      overlayView = UIView(frame: window.bounds)
      overlayView?.backgroundColor = UIColor.black.withAlphaComponent(0.5)

      // Add tap gesture to dismiss when tapping outside
      let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissPicker))
      overlayView?.addGestureRecognizer(tapGesture)

      // Create picker view
      pickerView = UIPickerView()
      pickerView?.delegate = self
      pickerView?.dataSource = self

      // Create container view for picker
      let containerView = UIView()
      containerView.backgroundColor = .white
      containerView.layer.cornerRadius = 10

      // Create toolbar with Done button
      let toolbar = UIToolbar()
      toolbar.barStyle = .default
      toolbar.isTranslucent = true
      // toolbar is background image transprenct and remove border line
      toolbar.setBackgroundImage(UIImage(), forToolbarPosition: .any, barMetrics: .default)
      toolbar.sizeToFit()

      let doneButton = UIBarButtonItem(title: "\(L.ok.localized)  ", style: .done, target: self, action: #selector(doneButtonTapped))
      doneButton.setTitleTextAttributes([.font: UIFont.body2, .foregroundColor: UIColor.vueroidBlue], for: .normal)
      let cancelButton = UIBarButtonItem(title: L.cancel.localized, style: .done, target: self, action: #selector(cancelButtonTapped))
      cancelButton.setTitleTextAttributes([.font: UIFont.body2, .foregroundColor: UIColor.vueroidBlue], for: .normal)
      let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
      toolbar.setItems([flexSpace, cancelButton, doneButton], animated: false)

      // Add views to hierarchy
      window.addSubview(overlayView!)
      overlayView?.addSubview(containerView)
      containerView.addSubview(toolbar)
      containerView.addSubview(pickerView!)

      // Set up constraints
      containerView.translatesAutoresizingMaskIntoConstraints = false
      toolbar.translatesAutoresizingMaskIntoConstraints = false
      pickerView?.translatesAutoresizingMaskIntoConstraints = false

      NSLayoutConstraint.activate([
        containerView.centerXAnchor.constraint(equalTo: overlayView!.centerXAnchor),
        containerView.centerYAnchor.constraint(equalTo: overlayView!.centerYAnchor),
        containerView.widthAnchor.constraint(equalToConstant: 250),

        toolbar.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
        toolbar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
        toolbar.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
        toolbar.heightAnchor.constraint(equalToConstant: 42),

        pickerView!.topAnchor.constraint(equalTo: toolbar.bottomAnchor),
        pickerView!.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
        pickerView!.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
        pickerView!.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
        pickerView!.heightAnchor.constraint(equalToConstant: 200)
      ])

      // Set initial selection
      pickerView?.selectRow(selectedHour, inComponent: 0, animated: false)
    }
  }

  @objc private func doneButtonTapped() {
    if let pickerView = pickerView {
      selectedHour = pickerView.selectedRow(inComponent: 0)
      timeLabel.text = String(format: "%02dH", selectedHour)
      delegate?.timeSelectView(self, didSelectHour: selectedHour)
    }
    dismissPicker()
  }

  @objc private func cancelButtonTapped() {
    dismissPicker()
  }

  @objc private func dismissPicker() {
    overlayView?.removeFromSuperview()
    overlayView = nil
    pickerView = nil
  }

  public func setHour(_ hour: Int) {
    selectedHour = hour
    timeLabel.text = String(format: "%02dH", hour)
  }

  required init(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

// MARK: - UIPickerViewDelegate, UIPickerViewDataSource
extension TimeSelectView: UIPickerViewDelegate, UIPickerViewDataSource {
  public func numberOfComponents(in pickerView: UIPickerView) -> Int {
    return 1
  }

  public func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
    return 25 // 0-24 hours
  }

  public func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
    return String(format: "%02d", row)
  }

  public func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
    selectedHour = row
  }
}
