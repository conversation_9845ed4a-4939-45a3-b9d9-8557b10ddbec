//
//  LKPopupBadgeSelectActionView.swift
//  Hub
//
//  Created by ncn on 5/15/25.
//
import UIKit

public enum RotateSelectType: Int {
  case rotate = 0
  case mirror
  case all
  case none
}

public enum BadgeSelectType: Int {
  case rotate = 0
  case mirror
  
  var title: String {
    switch self {
    case .rotate:
      return L.rotate_text.localized
    case .mirror:
      return L.mirror_text.localized
    }
  }
}

public class LKPopupBadgeSelectActionView: UIView, LKPopupBadgeSelectActionable {
  public var clickBadgeSelectActionHandle: ((BadgeSelectType, Bool) -> ())?
  
  public var preferredHeight: CGFloat {
    return 60.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .sub2
    label.sizeToFit()
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()
  
  var rotateBadgeView: SelectBadgeView = {
    let view = SelectBadgeView(type: .rotate)
    return view
  }()

  var mirrorBadgeView: SelectBadgeView = {
    let view = SelectBadgeView(type: .mirror)
    return view
  }()

  var switchValue: RotateSelectType = .rotate {
    didSet {
      if switchValue == .none {
        rotateBadgeView.isSelect = false
        mirrorBadgeView.isSelect = false
      } else if switchValue == .all {
        rotateBadgeView.isSelect = true
        mirrorBadgeView.isSelect = true
      } else {
        rotateBadgeView.isSelect = switchValue == .rotate ? true : false
        mirrorBadgeView.isSelect = switchValue == .mirror ? true : false
      }
    }
  }
  
  var isEnable: Bool = true {
    didSet {
      rotateBadgeView.isEnable = isEnable
      mirrorBadgeView.isEnable = isEnable
      titleLabel.textColor = isEnable ? .mainBlack : .disable
    }
  }
  
  public init(title: String, selectValue: RotateSelectType = .none) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
    self.switchValue = selectValue
    
    rotateBadgeView.clickBadgeSelectActionHandle = { [weak self] isOn in
      guard let self else { return }
      rotateBadgeView.isSelect.toggle()
      self.clickBadgeSelectActionHandle?(.rotate, isOn)
    }
    
    mirrorBadgeView.clickBadgeSelectActionHandle = { [weak self] isOn in
      guard let self else { return }
      mirrorBadgeView.isSelect.toggle()
      self.clickBadgeSelectActionHandle?(.mirror ,isOn)
    }
    
    addSubviews([titleLabel, rotateBadgeView, mirrorBadgeView, lineView])
    titleLabel.pin.centerY().start(offset: 15).activate()
    mirrorBadgeView.pin.centerY().width(84).height(28).end(offset: -15).activate()
    rotateBadgeView.pin.centerY().width(84).height(28).before(mirrorBadgeView, offset: -10).activate()
    lineView.pin.horizontally().height(1).bottom().activate()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

public class SelectBadgeView: UIView {
  public var clickBadgeSelectActionHandle: ((Bool) -> ())?
  var titleLabel: UILabel = {
    let view = UILabel()
    view.font = .sub1
    view.textColor = .grayText
    return view
  }()

  public var selectImageView: UIImageView = {
    let view = UIImageView()
    view.image = UIImage(named: "sheet_cell_check")
    return view
  }()
  
  var isSelect: Bool = false {
    didSet {
      titleLabel.textColor = isSelect ? .vueroidBlue : .grayText
      selectImageView.image = isSelect ? UIImage(named: "cell_icon_check") : UIImage(named: "cell_icon_uncheck")
      self.backgroundColor = isSelect ? .blue10 : .grayButton
    }
  }

  var isEnable: Bool = true {
    didSet {
      if !isEnable {
        titleLabel.textColor = .disable
        selectImageView.image = UIImage(named: "cell_icon_uncheck")
        self.backgroundColor = .disable
      }
    }
  }
  
  init(type: BadgeSelectType, isSelect: Bool = false) {
    super.init(frame: .zero)
    self.backgroundColor = .grayButton
    self.titleLabel.text = type.title
    self.isSelect = isSelect
    
    // add gesture handler
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapBadge))
    self.addGestureRecognizer(tapGesture)
    
    HStackView(spacing: 2) {
      selectImageView.withSize(18)
      titleLabel
    }.withMargins(.init(horizontal: 8, vertical: 5))
  }
  
  public override func layoutSubviews() {
    super.layoutSubviews()
    self.layer.cornerRadius = 10
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  @objc func didTapBadge() {
    if isEnable {
      self.clickBadgeSelectActionHandle?(!isSelect)
    }
  }
}
