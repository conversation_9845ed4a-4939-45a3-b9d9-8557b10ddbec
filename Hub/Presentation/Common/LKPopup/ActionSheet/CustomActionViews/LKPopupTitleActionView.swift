//
//  LKPopupTitleActionView.swift
//  Hub
//
//  Created by ncn on 5/13/25.
//

import UIKit

public class LKPopupTitleActionView: UIView, LKPopupActionViewProtocol {
  public var clickActionHandle: (() -> ())?
  
  public var preferredHeight: CGFloat {
    return 72.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .h2
    label.sizeToFit()
    return label
  }()

  private let subTitleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .disable
    label.font = .body5
    label.sizeToFit()
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    view.isHidden = true
    return view
  }()
  
  public var alertIconImageView: UIImageView = {
    let view = UIImageView()
    view.image = #imageLiteral(resourceName: "alert_circle.pdf")
    return view
  }()
  
  public var closeButton: UIButton = {
    let button = UIButton(type: .custom)
    button.setImage(#imageLiteral(resourceName: "sheet_close.pdf"), for: .normal)
    return button
  }()
  
  public init(title: String, subTitle: String? = nil, showCloseButton: Bool? = true) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
    self.closeButton.isHidden = !(showCloseButton ?? true)
    
    addSubviews([titleLabel, closeButton])
    titleLabel.pin.center().activate()
    closeButton.pin.size(24).top(offset: 10).end(offset: -10).activate()
    
    if let subTitle = subTitle {
      self.subTitleLabel.text = subTitle
      addSubviews([alertIconImageView, subTitleLabel])
      alertIconImageView.pin.size(16).start(offset: 15).below(titleLabel, offset: 10).activate()
      subTitleLabel.pin.after(alertIconImageView, offset: 2).below(titleLabel, offset: 10).end(offset: -10).activate()
    }
    
    closeButton.addAction(UIAction { [weak self] _ in
      self?.clickActionHandle?()
    }, for: .touchUpInside)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

public class LKPopupSectionTitleActionView: UIView, LKPopupActionViewProtocol {
  
  public var preferredHeight: CGFloat {
    return 46.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .grayText
    label.font = .body1
    label.textAlignment = .left
    label.sizeToFit()
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    view.isHidden = true
    return view
  }()
  
  var isEnabled: Bool = false {
    didSet {
      titleLabel.textColor = isEnabled ? .mainBlack : .grayText
    }
  }
  
  public init(title: String) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
    
    addSubviews([titleLabel, lineView])
    titleLabel.pin.horizontally(offset: 15).bottom(offset: -10).activate()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

