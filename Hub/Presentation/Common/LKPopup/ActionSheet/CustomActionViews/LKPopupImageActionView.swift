//
//  LKPopupImageActionView.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public class LKPopupImageActionView: UIView, LKPopupActionViewProtocol {
  
  public var clickActionHandle: (() -> ())?
  
  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .line
    return view
  }()
  
  public var preferredHeight: CGFloat {
    return 60.0 // Custom height for this view
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .h2
    return label
  }()
  
  private let iconImageView: UIImageView = {
    let imageView = UIImageView()
    imageView.contentMode = .scaleAspectFit
    return imageView
  }()
  
  private lazy var clickBtn: UIButton = {
    let button = UIButton(type: .custom)
    button.addAction(UIAction { [weak self] _ in
      self?.clickActionHandle?()
    }, for: .touchUpInside)
    button.setBackgroundColor(.clear, for: .highlighted)
    return button
  }()
  
  public init(title: String, image: UIImage) {
    super.init(frame: .zero)
    self.titleLabel.text = title
    self.iconImageView.image = image
    setupView()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  private func setupView() {
    backgroundColor = .white
    
    addSubview(iconImageView)
    addSubview(titleLabel)
    addSubview(lineView)
    addSubview(clickBtn)
    
    iconImageView.translatesAutoresizingMaskIntoConstraints = false
    titleLabel.translatesAutoresizingMaskIntoConstraints = false
    lineView.translatesAutoresizingMaskIntoConstraints = false
    clickBtn.translatesAutoresizingMaskIntoConstraints = false
    
    NSLayoutConstraint.activate([
      iconImageView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 20),
      iconImageView.centerYAnchor.constraint(equalTo: centerYAnchor),
      iconImageView.widthAnchor.constraint(equalToConstant: 40),
      iconImageView.heightAnchor.constraint(equalToConstant: 40),
      
      titleLabel.leadingAnchor.constraint(equalTo: iconImageView.trailingAnchor, constant: 15),
      titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor),
      titleLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -20),
      
      lineView.leadingAnchor.constraint(equalTo: leadingAnchor),
      lineView.trailingAnchor.constraint(equalTo: trailingAnchor),
      lineView.bottomAnchor.constraint(equalTo: bottomAnchor),
      lineView.heightAnchor.constraint(equalToConstant: 0.5)
    ])
    
    clickBtn.pin.all().activate()
  }
}
