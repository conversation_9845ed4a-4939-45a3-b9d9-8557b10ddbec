//
//  LKPopupSwitchActionView.swift
//  Hub
//
//  Created by ncn on 5/13/25.
//


import UIKit

public class LKPopupSwitchActionView: UIView, LKPopupSwitchActionable {
  public var clickSwitchActionHandle: ((Bool) -> ())?
  
  public var preferredHeight: CGFloat {
    return 60.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .sub2
    label.sizeToFit()
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()
  
  var switchView: UISwitch = {
    let view = UISwitch()
    view.onTintColor = .vueroidBlue
    return view
  }()
  
  var switchValue: Bool = false {
    didSet {
      switchView.isOn = switchValue
    }
  }
  
  var isEnabled: Bool = false {
    didSet {
      titleLabel.textColor = isEnabled ? .mainBlack : .grayText
      switchView.isEnabled = isEnabled
      switchView.onTintColor = isEnabled ? .vueroidBlue : .grayText
    }
  }

  public init(title: String, onOff: Bool = false) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
    self.switchValue = onOff
    
    switchView.addAction(UIAction { [weak self] _ in
      guard let self else { return }
      self.clickSwitchActionHandle?(switchView.isOn)
    }, for: .valueChanged)
    
    addSubviews([titleLabel, switchView, lineView])
    titleLabel.pin.centerY().start(offset: 15).activate()
    switchView.pin.centerY().end(offset: -15).activate()
    lineView.pin.horizontally().height(1).bottom().activate()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
