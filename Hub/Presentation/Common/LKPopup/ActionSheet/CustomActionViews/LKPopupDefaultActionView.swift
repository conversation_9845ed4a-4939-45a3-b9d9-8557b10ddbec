//
//  LKPopupDefaultActionView.swift
//  Hub
//
//  Created by ncn on 5/14/25.
//
import UIKit

public class LKPopupDefaultActionView: UIView, LKPopupActionViewProtocol {
  public var clickActionHandle: (() -> ())?
  
  public var preferredHeight: CGFloat {
    return 60.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .sub2
    label.sizeToFit()
    return label
  }()

  private let subTitleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .subText
    label.font = .body5
    label.sizeToFit()
    return label
  }()

  private let valueLabel: UILabel = {
    let label = UILabel()
    label.textColor = .vueroidBlue
    label.font = .sub2
    label.sizeToFit()
    label.isHidden = true
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()
  
  public var selectImageView: UIImageView = {
    let view = UIImageView()
    view.image = UIImage(named: "sheet_cell_check")
    view.isHidden = true
    return view
  }()
  
  var isSelected: Bool = false {
    didSet {
      selectImageView.isHidden = !isSelected
    }
  }
  
  private lazy var clickButton: UIButton = {
    let button = UIButton(type: .custom)
    button.addAction(UIAction { [weak self] _ in
      self?.clickActionHandle?()
    }, for: .touchUpInside)
    button.setBackgroundColor(.clear, for: .highlighted)
    return button
  }()
  
  public init(
    title: String,
    subTitle: String? = nil,
    value: String? = nil,
    isSelected: Bool? = false,
    showCloseButton: Bool? = true
  ) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
        
    self.isSelected = isSelected ?? false
    
    addSubviews([titleLabel, selectImageView, lineView, clickButton])
    titleLabel.pin.centerY().start(offset: 15).activate()
    selectImageView.pin.size(24).end(offset: -15).centerY().activate()
    lineView.pin.horizontally().height(1).bottom().activate()
    clickButton.pin.all().activate()
    
    if let subTitle = subTitle {
      self.subTitleLabel.text = subTitle
      addSubview(subTitleLabel)
      titleLabel.pin.deactivate()
      titleLabel.pin.centerY(offset: -6).start(offset: 15).activate()
      subTitleLabel.pin.below(titleLabel, offset: 8).start(offset: 15).activate()
    }

    if let value = value {
      addSubview(valueLabel)
      valueLabel.text = value
      valueLabel.isHidden = false
      valueLabel.pin.end(offset: -15).centerY().activate()
    }
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
