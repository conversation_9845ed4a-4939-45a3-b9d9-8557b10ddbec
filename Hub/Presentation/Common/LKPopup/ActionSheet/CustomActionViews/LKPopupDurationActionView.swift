//
//  LKPopupDurationActionView.swift
//  Hub
//
//  Created by ncn on 5/14/25.
//
import UIKit

public class LKPopupDurationActionView: UIView, LKPopupActionViewProtocol {
  public var clickActionHandle: (() -> ())?
  public var onTimeChanged: ((Int, Int) -> Void)?

  public var preferredHeight: CGFloat {
    return 60.0
  }
  
  private let titleLabel: UILabel = {
    let label = UILabel()
    label.textColor = .mainBlack
    label.font = .sub2
    label.sizeToFit()
    return label
  }()

  public var lineView: UIView = {
    let view = UIView()
    view.backgroundColor = .background
    return view
  }()
  
  public var sunsetView: TimeSelectView = {
    let view = TimeSelectView(title: "18H")
    return view
  }()

  public var sunriseView: TimeSelectView = {
    let view = TimeSelectView(title: "9H")
    return view
  }()

  public var betweenLabel: UILabel = {
    let label = UILabel()
    label.font = .h2
    label.text = "~"
    label.textColor = .mainBlack
    return label
  }()
  
  var isSelected: Bool = false {
    didSet {
  
    }
  }
  
  var isEnabled: Bool = false {
    didSet {
      titleLabel.textColor = isEnabled ? .mainBlack : .disable
      betweenLabel.textColor = isEnabled ? .mainBlack: .disable
      sunsetView.isEnabled = isEnabled
      sunriseView.isEnabled = isEnabled
    }
  }
    
  public init(title: String, isSelected: Bool? = false, showCloseButton: Bool? = true) {
    super.init(frame: .zero)
    self.backgroundColor = .white
    self.titleLabel.text = title
    self.isSelected = isSelected ?? false

    // Set up delegates
    sunsetView.delegate = self
    sunriseView.delegate = self

    HStackView {
      titleLabel
      UIView()
      HStackView(spacing: 5) {
        sunsetView.withSize(.init(width: 80, height: 38))
        betweenLabel.withSize(.init(width: 12, height: 18))
        sunriseView.withSize(.init(width: 80, height: 38))
      }.withMargins(.vertical(12))
    }.withMargins(.horizontal(15))
  }

  public func setTimes(sunset: Int, sunrise: Int) {
    sunsetView.setHour(sunset)
    sunriseView.setHour(sunrise)
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

// MARK: - TimeSelectViewDelegate
extension LKPopupDurationActionView: TimeSelectViewDelegate {
  public func timeSelectView(_ view: TimeSelectView, didSelectHour hour: Int) {
    // Extract current hours from both views
    let sunsetHour = extractHour(from: sunsetView.timeLabel.text ?? "18H")
    let sunriseHour = extractHour(from: sunriseView.timeLabel.text ?? "9H")

    // Call the callback with the updated times
    onTimeChanged?(sunsetHour, sunriseHour)
  }

  private func extractHour(from timeString: String) -> Int {
    let hourString = timeString.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
    if let hour = Int(hourString) {
      return hour
    }
    return 0
  }
}

