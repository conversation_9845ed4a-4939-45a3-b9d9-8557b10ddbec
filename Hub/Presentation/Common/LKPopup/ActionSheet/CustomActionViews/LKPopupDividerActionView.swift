//
//  LKPopupDividerActionView.swift
//  Hub
//
//  Created by ncn on 5/13/25.
//


import UIKit

public class LKPopupEmptyActionView: UIView, LKPopupActionViewProtocol {
  public var clickActionHandle: (() -> ())?
  
  public var lineView: UIView

  public var preferredHeight: CGFloat {
    return 8.0
  }

  public override init(frame: CGRect) {
    lineView = UIView()
    super.init(frame: frame)
    lineView.backgroundColor = .white
    
    addSubview(lineView)
    lineView.pin.all().activate()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

public class LKPopupDividerActionView: UIView, LKPopupActionViewProtocol {
  public var clickActionHandle: (() -> ())?
  
  public var lineView: UIView

  public var preferredHeight: CGFloat {
    return 8.0
  }

  public override init(frame: CGRect) {
    lineView = UIView()
    super.init(frame: frame)
    lineView.backgroundColor = .background
    
    addSubview(lineView)
    lineView.pin.all().activate()
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}
