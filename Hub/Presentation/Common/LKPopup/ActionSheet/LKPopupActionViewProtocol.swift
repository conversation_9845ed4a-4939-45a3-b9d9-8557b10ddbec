//
//  LKPopupActionViewProtocol.swift
//  Hub
//
//  Created by ncn on 2/11/25.
//

import UIKit

public protocol LKPopupActionViewProtocol: UIView {
  var clickActionHandle: (() -> ())? { get set }
  var lineView: UIView { get }
  
  // Optional height for custom views
  var preferredHeight: CGFloat { get }
}


// 스위치 액션 뷰를 위한 특별한 프로토콜 추가
public protocol LKPopupSwitchActionable: LKPopupActionViewProtocol {
  var clickSwitchActionHandle: ((Bool) -> ())? { get set }
}

public protocol LKPopupBadgeSelectActionable: LKPopupActionViewProtocol {
  var clickBadgeSelectActionHandle: ((BadgeSelectType, Bool) -> ())? { get set }
}

// Default implementation for optional methods
public extension LKPopupActionViewProtocol {
  var preferredHeight: CGFloat {
    return UITableView.automaticDimension
  }
  
  var clickActionHandle: (() -> ())? {
    get { return nil }
    set { }
  }
}
