//
//  PhotoPermissionHandler.swift
//  Hub
//
//  Created on 2024/07/25.
//

import UIKit
import Photos

/// A class that handles photo permission checks and shows appropriate alerts
class PhotoPermissionHandler {
    
    /// Checks photo permission and shows settings alert if needed
    /// - Parameters:
    ///   - viewController: The view controller to present alerts on
    ///   - completion: Called with true if permission is granted, false otherwise
    static func checkPhotoPermission(on viewController: UIViewController, completion: @escaping (Bool) -> Void) {
        viewController.requestPhotosPermission(completion: completion)
    }

    
    /// Saves a photo to the photo library, handling permission checks
    /// - Parameters:
    ///   - image: The image to save
    ///   - albumTitle: The album title to save to
    ///   - viewController: The view controller to present alerts on
    ///   - completion: Called with error if any
    static func savePhoto(image: UIImage?, toAlbum albumTitle: String, on viewController: UIViewController, completion: @escaping (Error?) -> Void) {
        checkPhotoPermission(on: viewController) { granted in
            if granted {
                PhotoUtils.savePhoto(image: image, toAlbum: albumTitle, completion: completion)
            } else {
                // Permission denied, call completion with error
                completion(NSError(domain: "PhotoPermissionDenied", code: 403, userInfo: [NSLocalizedDescriptionKey: "Photo permission denied"]))
            }
        }
    }
}
