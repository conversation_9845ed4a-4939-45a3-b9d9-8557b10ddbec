//
//  Coordinator.swift
//  Hub
//
//  Created by ncn on 2022/11/01.
//

import UIKit

public enum CoordinatorType {
  case onboarding
  case mode
  case ble, wifi
  case login, register, qr, form
  case contain
  case dashList, dashcamManage
  case home, notice, terms, noticeDetail
  case live
  case file, vod, edit, download, upload, updownload
  case history, travelLog, travelMap
  case setting, cloudSetting
}

#if false
  protocol Coordinator: AnyObject {
    var navigationController: UINavigationController? { get set }
    var type: CoordinatorType { get }
    var childCoordinators: [Coordinator] { get set }

    func start()
    func finish(from: Coordinator?)
    func findCoordinator(type: CoordinatorType) -> Coordinator?
  }

  extension Coordinator {
    func findCoordinator(type: CoordinatorType) -> Coordinator? {
      var stack: [Coordinator] = [self]

      while !stack.isEmpty {
        let currentCoordinator = stack.removeLast()
        if currentCoordinator.type == type {
          return currentCoordinator
        }
        currentCoordinator.childCoordinators.forEach({ child in
          stack.append(child)
        })
      }
      return nil
    }
  }
#endif

public protocol Coordinator: AnyObject {
  func start(with step: Step)
  func start()

  //  var childCoordinators: [Coordinator] { get set }
  //  var type: CoordinatorType { get }
  //  func findCoordinator(type: CoordinatorType) -> Coordinator?
}

extension Coordinator {
  public func start(with step: Step = DefaultStep()) {}
  public func start() {}
}

public protocol NavigationCoordinator: Coordinator {
  var navigationController: UINavigationController { get }
}

// MARK: - Step Protocol
/// inspired by RxFlow
public protocol Step {}

public struct DefaultStep: Step {
  public init() {}
}
