//
//  PopupUpProcessingDownViewModel.swift
//  Hub
//
//  Created by ncn on 6/2/25.
//

import Foundation
import UIKit
import RxCocoa
import RxSwift

final class PopupUpProcessingDownViewModel: BaseViewModel {
  let aiUseCase: AIUseCase
  let url: URL?
  let taskType: TaskType
  var restoreImage: UIImage? = nil
  
  init(aiUseCase: AIUseCase, url: URL?, taskType: TaskType) {
//    self.useCase = useCase
    self.aiUseCase = aiUseCase
    self.url = url
    self.taskType = taskType
//    self.fileModel = fileModel
  }

  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxCheckServerStatus = PublishRelay<Int>()
    
    let rxUploadStart = PublishRelay<String>()
    let rxUploadPercent = PublishRelay<Double>()
    let rxUploadEnd = PublishRelay<Void>()

    let rxProcessingStart = PublishRelay<Void>()
    let rxProcessingEnd = PublishRelay<Void>()

    let rxDownloadStart = PublishRelay<String>()
    let rxDownloadPercent = PublishRelay<Double>()
    let rxDownloadEnd = PublishRelay<Bool>()
    
    let rxPushRegisterEnd = PublishRelay<(Bool, FileListCellModel?)>()
    let rxError = PublishRelay<Void>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.viewDidLoad
      .subscribe(onNext: { [weak self] fileModel in
        guard let self else { return }
        checkServerStatus(type: taskType, output: output)
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupUpProcessingDownViewModel {

  func checkServerStatus(type: TaskType, output: Output) {
    AIServiceManager.shared.initializeAccessKey { [weak self] success in
      guard let self else { return }
      if success {
        aiUseCase.checkCondition(version: .v2, taskType: type) { result in
          switch result {
          case .success(let condition):
            output.rxCheckServerStatus.accept(condition.waitSec)
            
          case .failure(let error):
            mLogger.error("AI Server status check failed: \(error.localizedDescription)")
            output.rxError.accept(())
          }
        }
      } else {
        mLogger.error("AI Server initializeAccessKey failed")
        output.rxError.accept(())
      }
    }
  }

  func processImageRestoration(output: Output, disposedBag: DisposeBag) {
    guard let image = restoreImage,  let imageData = image.jpegData(compressionQuality: 0.8) else {
      return
    }
    guard let fileName = AIServiceManager.shared.currentFileName else {
      return
    }
    output.rxUploadStart.accept(fileName)
    AIServiceManager.shared.restoreImage(
      imageData: imageData,
      uploadProgress: { uploadPercent in
        DispatchQueue.main.async {
          output.rxUploadPercent.accept(uploadPercent)
        }
      },
      progressCallback: { status in
        DispatchQueue.main.async {
          switch status {
          case .upload, .registered:
            mLogger.info("작업이 등록.")
            output.rxUploadEnd.accept(())
          case .inProgress, .download:
            output.rxProcessingStart.accept(())
            mLogger.info("처리 중.")
          case .completed:
            output.rxProcessingEnd.accept(())
            mLogger.info("이미지를 복원 작업이 완료되었습니다.")
          case .failed:
            mLogger.error("실패")
            output.rxError.accept(())
          }
        }
      },
      completion: { [weak self] result in
        DispatchQueue.main.async { [weak self] in
          guard let self else { return }
          switch result {
          case .success(let downloadUrlString):
            do {
              let saveFileName = try HubFileNameGenerator.generateLicensePlateFileName(baseFileName: fileName, stage: .afterRestoration)
              mLogger.log("saveFileName : \(saveFileName)")
              output.rxDownloadStart.accept(saveFileName)
              fileDownload(url: downloadUrlString, fileName: saveFileName, output: output, disposedBag: disposedBag)
            } catch {
              mLogger.error("Error generating AI service file names: \(error)")
            }
          case .failure(let error):
            mLogger.error("이미지 복원 실패: \(error.localizedDescription)")
            output.rxError.accept(())
          }
        }
      }
    )
  }
  
  func pushImageRestoration(output: Output, disposedBag: DisposeBag) {
    guard let image = restoreImage,  let imageData = image.jpegData(compressionQuality: 0.8) else {
      return
    }
    guard let fileName = AIServiceManager.shared.currentFileName else {
      return
    }
    output.rxUploadStart.accept(fileName)
    AIServiceManager.shared.restoreImage(
      imageData: imageData,
      uploadProgress: { uploadPercent in
        DispatchQueue.main.async {
          output.rxUploadPercent.accept(uploadPercent)
        }
      },
      progressCallback: { status in
        DispatchQueue.main.async {
          switch status {
          case .upload, .registered:
            mLogger.info("작업이 등록.")
            if AIServiceManager.shared.isPushResult {
              AIServiceManager.shared.isPushResult = false
              output.rxPushRegisterEnd.accept((true, nil))
            } else {
              output.rxUploadEnd.accept(())
            }
          case .inProgress, .download:
            output.rxProcessingStart.accept(())
            mLogger.info("처리 중.")
          case .completed:
            output.rxProcessingEnd.accept(())
            mLogger.info("이미지를 복원 작업이 완료되었습니다.")
          case .failed:
            mLogger.error("실패")
            output.rxError.accept(())
          }
        }
      },
      completion: { _ in }
    )
  }
  /// 선택된 비디오를 비식별화 처리
  func processVideoDeIdentification(output: Output, disposedBag: DisposeBag) {
    guard let videoUrl = self.url else { return }
    
    mLogger.info("processVideoDeIdentification videoUrl: \(videoUrl)")
    guard let videoData = try? Data(contentsOf: videoUrl) else {
      mLogger.error("비디오 데이터를 읽을 수 없습니다: \(videoUrl)")
      return
    }
    
    // get fileName
    let fileName = videoUrl.lastPathComponent
    mLogger.info("fileName: \(fileName)")
    output.rxUploadStart.accept(fileName)
    AIServiceManager.shared.deIdentifyVideoAuto(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { uploadPercent in
        DispatchQueue.main.async {
          output.rxUploadPercent.accept(uploadPercent)
        }
      },
      progressCallback: { status in
        DispatchQueue.main.async {
          switch status {
          case .upload, .registered:
            mLogger.info("deIdentification registered")
            output.rxUploadEnd.accept(())
          case .inProgress, .download:
            output.rxProcessingStart.accept(())
            mLogger.info("비식별화 처리 중.")
          case .completed:
            output.rxProcessingEnd.accept(())
            mLogger.info("비식별화 완료.")
          case .failed:
            mLogger.error("작업이 실패.")
          }
        }
      },
      completion: { [weak self] result in
        DispatchQueue.main.async {
          switch result {
          case .success(let urlString):
            mLogger.info("비식별화 비디오 다운로드 URL: \(urlString)")
            do {
              let saveFileName = try HubFileNameGenerator.generatePrivacyBlurFileName(baseFileName: fileName, stage: .afterProcessing)
              mLogger.info("saved fileName: \(saveFileName)")
              output.rxDownloadStart.accept(saveFileName)
              self?.fileDownload(url: urlString, fileName: saveFileName, output: output, disposedBag: disposedBag)
            } catch {
              mLogger.error("Error generating AI service file names: \(error)")
            }
            
          case .failure(_):
            output.rxError.accept(())
          }
        }
      }
    )
  }
  
  func pushVideoDeIdentification(output: Output, disposedBag: DisposeBag) {
    guard let videoUrl = self.url else { return }
    
    mLogger.info("processVideoDeIdentification videoUrl: \(videoUrl)")
    guard let videoData = try? Data(contentsOf: videoUrl) else {
      mLogger.error("비디오 데이터를 읽을 수 없습니다: \(videoUrl)")
      return
    }
    
    // get fileName
    let fileName = videoUrl.lastPathComponent
    mLogger.info("fileName: \(fileName)")
    output.rxUploadStart.accept(fileName)
    AIServiceManager.shared.isPushResult = true
    AIServiceManager.shared.deIdentifyVideo(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { uploadPercent in
        DispatchQueue.main.async {
          output.rxUploadPercent.accept(uploadPercent)
        }
      },
      progressCallback: { status in
        DispatchQueue.main.async {
          switch status {
          case .upload ,.registered:
            mLogger.info("deIdentification registered")
            if AIServiceManager.shared.isPushResult {
              AIServiceManager.shared.isPushResult = false
              output.rxPushRegisterEnd.accept((true, nil))
            } else {
              output.rxUploadEnd.accept(())
            }

          case .inProgress, .download:
            output.rxProcessingStart.accept(())
            mLogger.info("비식별화 처리 중.")
          case .completed:
            AIServiceManager.shared.isPushResult = false
            output.rxProcessingEnd.accept(())
            mLogger.info("비식별화 완료.")
          case .failed:
            output.rxError.accept(())
            mLogger.error("작업이 실패.")
          }
        }
      },
      completion: { _ in }
    )
  }
  
  private func fileDownload(url: String, fileName: String, output: Output, disposedBag: DisposeBag) {
    self.aiUseCase.download(
      url: url,
      download: { percent in
        mLogger.info("Download percent: \(percent)")
        output.rxDownloadPercent.accept(percent)
      },
      completion: { result in
        switch result {
        case .success(let data):
          mLogger.info("fileDownload saved fileName: \(fileName), taskType: \(self.taskType.rawValue)")
          
          if self.taskType == .deIdentified {
            self.saveDeIdentifyVodFile(data: data, fileName: fileName)
              .subscribe(onNext: { url in
                output.rxDownloadEnd.accept(true)
              })
              .disposed(by: disposedBag)
          } else {
            self.saveImageRestorationFile(data: data, fileName: fileName)
              .subscribe(onNext: { _ in
                output.rxDownloadEnd.accept(true)
              })
              .disposed(by: disposedBag)
          }
        case .failure(let error):
          Log.error(to: error.localizedDescription)
          output.rxDownloadPercent.accept(1.0)
          output.rxError.accept(())
        }
      }
    )
  }

  func saveDeIdentifyVodFile(data: Data?, fileName: String) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let path = UrlList.downloadedPath() else {
      Log.message(to: "Fail to get path.")
      return Observable.just(nil)
    }

    mLogger.info("saveDeIdentifyVodFile fileName: \(fileName), data: \(data.count)")
    if data.count == 1024 * 10 {
      mLogger.error("No data to save for file: \(fileName) - data size is too small.")
      return Observable.just(nil)
    }
    FileManager.writeToFile(data: data, atPath: path, name: fileName)
    let mp4Path =  path.appendingPathComponent(fileName)
    return Observable.just(mp4Path)
  }
  
  func saveImageRestorationFile(data: Data?, fileName: String) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let path = UrlList.screenshotPath() else {
      return Observable.just(nil)
    }

    mLogger.info("saveImageRestorationFile fileName: \(fileName), data: \(data.count)")
    FileManager.writeToFile(data: data, atPath: path, name: fileName)
    let jpgPath =  path.appendingPathComponent(fileName)
    return Observable.just(jpgPath)
  }
  
  #if false
  private func getUniqueHubAiImageName(fileName: String, path: URL, suffix: AiSuffix) -> String {
    mLogger.log("before fileName : \(fileName)")
    let noExtensionName = fileName.replacingOccurrences(of: ".jpg", with: "").replacingOccurrences(
      of: ".JPG", with: "")
    let noDotFileName = noExtensionName.replacingOccurrences(of: ".", with: "")
    let noBracketName = noDotFileName.components(separatedBy: "(").first ?? ""
    let noNewName = noBracketName.components(separatedBy: "_N").first ?? ""
    let removeSuffix = noNewName.replacingOccurrences(of: "LB", with: "").replacingOccurrences(of: "LA", with: "")
      .replacingOccurrences(of: "PB", with: "").replacingOccurrences(of: "PA", with: "").replacingOccurrences(of: "O", with: "")
    var imageFileName = removeSuffix + "\(suffix.rawValue).jpg"
    var uniqueFileURL = path.appendingPathComponent(imageFileName)
  
    // 중복 파일 처리
    var newFileName = imageFileName
    var index = 1
    while FileManager.default.fileExists(atPath: uniqueFileURL.path) {
      // 중복 파일이 이미 존재하는 경우, 새로운 파일 이름을 생성.
      if newFileName.contains("(") {
        let noBracketName = fileName.components(separatedBy: "(").first ?? ""
        newFileName = noBracketName + "(\(index)).jpg"
      } else {
        let noExtensionNewName = newFileName.replacingOccurrences(of: ".jpg", with: "")
        newFileName = noExtensionNewName + "(\(index)).jpg"
      }

      uniqueFileURL = path.appendingPathComponent(newFileName)
      index += 1
    }
    let logPath = uniqueFileURL.absoluteString
    mLogger.log("getUniqueHubAiImageName fileName : \(newFileName), uniqueFileURL: \(logPath)")
    return newFileName
  }
  #endif
}
