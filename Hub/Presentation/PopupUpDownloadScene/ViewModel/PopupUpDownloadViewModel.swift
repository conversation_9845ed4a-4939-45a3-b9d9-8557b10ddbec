//
//  PopupUpDownloadViewModel.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 12/5/23.
//

import Foundation
import RxCocoa
import RxSwift

final class PopupUpDownloadViewModel: BaseViewModel {
  let useCase: PopupDownloadUseCase
  let vodUseCase: VodUseCase
  let fileModel: [FileListCellModel]

  init(useCase: PopupDownloadUseCase, vodUseCase: VodUseCase, fileModel: [FileListCellModel]) {
    self.useCase = useCase
    self.vodUseCase = vodUseCase
    self.fileModel = fileModel
  }

  struct Input {
    let viewDidLoad: Observable<Void>
  }

  struct Output {
    let rxUploadStart = PublishRelay<Void>()
    let rxUploadEnd = PublishRelay<Void>()

    let rxDownloadStart = PublishRelay<Void>()
    let rxDownloadPercent = PublishRelay<Double>()
    let rxDownloadEnd = PublishRelay<Bool>()

    let rxSaveEndOnlyOne = PublishRelay<FileListCellModel?>()
    let rxError = PublishRelay<Void>()
  }

  func bind(input: Input, disposedBag: DisposeBag) -> Output {
    let output = Output()

    input.viewDidLoad
      .compactMap { [weak self] in self?.fileModel }
      .subscribe(onNext: { [weak self] fileModels in
        guard let self = self else { return }
        self.upDownloadFiles(
          cellModels: fileModels, output: output)
      })
      .disposed(by: disposedBag)

    return output
  }
}

extension PopupUpDownloadViewModel {

  func downloadFile(
    fileModel: FileListCellModel, output: Output, completion: @escaping (Bool) -> Void
  ) {
    guard let downloadPath = UrlList.downloadedPath() else {
      completion(false)
      return
    }
    Log.message(to: "Downloading file: \(fileModel.fileName)")
    let fileUrlModel = CloudFileUrlModel.Request(
      fileName: fileModel.fileName, serial: AppManager.shared.dashcam?.serial ?? "")
    let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
    #if false
    useCase.request(
      fileUrl: fileUrlModel,
      completion: { [weak self] result in
        switch result {
        case .success(let url):
          Log.info(to: "\(url)")
          self?.useCase.request(
            url: url,
            download: { percent in
              output.rxDownloadPercent.accept(percent)
            },
            completion: { result in
              switch result {
              case .success(let data):
                let fileName =
                  fileModel.fileName.replacingOccurrences(of: ".avi", with: "") + ".avi"
                FileManager.writeToFile(data: data, atPath: downloadPath, name: fileName)
                let mp4Path = FFMpegUtils.encodeToMP4(
                  aviFullPath: downloadPath.appendingPathComponent(fileName))
                FileManager.remove(file: downloadPath.appendingPathComponent(fileName))
                AVFoundationUtils.makeListThumbnail(cropVodUrl: mp4Path)
                PhotoUtils.saveVideo(
                  url: mp4Path,
                  toAlbum: modelName,
                  completion: { isSuccess, error in
                    if !isSuccess || error != nil {
                      Log.message(to: "isSuccess == false or error == nil \(isSuccess), \(error)")
                      completion(false)
                    } else {
                      completion(true)
                      Log.info(to: "end ======================= end")
                    }
                  }
                )
              case .failure(let error):
                completion(false)
                Log.error(to: error.localizedDescription)
              }
            })
        case .failure(let error):
          completion(false)
          Log.error(to: error.localizedDescription)
        }
      })
    #endif
  }

  private func upDownloadFiles(cellModels: [FileListCellModel], output: Output) {
    output.rxUploadStart.accept(())
    guard let serial = AppManager.shared.dashcam?.serial else {
      output.rxUploadEnd.accept(())
      return
    }
    if cellModels.isEmpty {
      output.rxDownloadEnd.accept(true)
      return
    }

    var cellModels = cellModels
    let cellModel = cellModels.removeFirst()
    let header = HeaderCloud(type: "uploadfile", serial: serial)
    let data = UploadFileCloud.Send.SendData(
      filename: cellModel.fileName, type: cellModel.vodType, videotime: 10000, to: L.download.localized)
    let model = UploadFileCloud.Send(header: header, data: data)
    vodUseCase.send(uploadFile: model) { [weak self] result in
      output.rxUploadEnd.accept(())
      switch result {
      case .success(_):
        self?.downloadFile(fileModel: cellModel, output: output) { isSuccess in
          if isSuccess {
            self?.upDownloadFiles(cellModels: cellModels, output: output)
            return
          }
          output.rxError.accept(())
          return
        }
      case .failure(let connectError):
        Log.warning(to: connectError)
        output.rxError.accept(())
        return
      }
    }
  }

//  private func getFileUrl(fileModel: FileListCellModel) -> Observable<String> {
//    let fileUrlModel = CloudFileUrlModel.Request(
//      fileName: fileModel.fileName, serial: AppManager.shared.dashcam?.serial ?? "")
//    return Observable.create { [weak self] emitter in
//      self?.useCase.request(
//        fileUrl: fileUrlModel,
//        completion: { result in
//          switch result {
//          case .success(let url):
//            emitter.onNext(url)
//            emitter.onCompleted()
//          case .failure(let error):
//            Log.error(to: error.localizedDescription)
//            emitter.onError(error)
//          }
//        })
//      return Disposables.create()
//    }
//  }

  private func fileDownload(url: String) -> Observable<(Double, Data?)> {
    return Observable.create { [weak self] emitter in
      self?.useCase.request(
        url: url,
        download: { percent in
          emitter.onNext((percent, nil))
        },
        completion: { result in
          switch result {
          case .success(let data):
            emitter.onNext((1.0, data))
            emitter.onCompleted()
          case .failure(let error):
            Log.error(to: error.localizedDescription)
            emitter.onNext((1.0, nil))
            emitter.onCompleted()
          }
        })
      return Disposables.create()
    }
  }

  func saveTempVodFile(data: Data?) -> Observable<URL?> {
    guard let data = data else { return Observable.just(nil) }
    guard let path = UrlList.vodTemp() else {
      Log.message(to: "Fail to get path.")
      return Observable.just(nil)
    }
    if FileManager.loadFileAtFolder(path: path) != [] {
      FileManager.remove(folder: path)
    }
    let name = "tempVOD.avi"
    FileManager.writeToFile(data: data, atPath: path, name: name)
    guard let aviFullPath = FileManager.loadFileAtFolder(path: path).first else {
      return Observable.just(nil)
    }
    return Observable.just(aviFullPath)
  }

  func encodeVideo(aviFullPath: URL?) -> Observable<Bool> {
    // avi와 같은곳에 생성
    guard let aviFullPath = aviFullPath else { return Observable.just(false) }
    let channelCount = fileModel.first?.channelbits.channelBitMaskingCount ?? 1
    // avi -> MP4 or MOV파일로 변환
    return Observable.create { emitter in
      var isAllComplete: [String: Bool] = [:]
      for channel in 0..<channelCount {
        let state = FFMpegUtils.encodeToTempMP4(aviFullPath: aviFullPath, channel: channel)
        switch state {
        case .completed:
          Log.message(to: "completed")
          isAllComplete.updateValue(true, forKey: "ch_\(channel)_end")
        default:
          Log.message(to: "fail")
          isAllComplete.updateValue(false, forKey: "ch_\(channel)_end")
        }
      }
      Log.message(
        to: "isAllComplete.values.allSatisfy \(isAllComplete.values.allSatisfy({ $0 == true }))")
      if isAllComplete.values.allSatisfy({ $0 == true }) {
        FileManager.remove(file: aviFullPath)
        emitter.onNext(true)
      } else {
        emitter.onNext(false)
      }
      emitter.onCompleted()
      return Disposables.create()
    }
  }
}
