//
//  PopupUpDownloadCoordinator.swift
//  Hub
//
//  Created by ncn on 7/29/24.
//

import UIKit

protocol PopupUpDownloadCoordinator: AnyObject {
  func navigate(to step: PopupUpDownloadSteps)
}

public protocol PopupUpDownloadCoordinatorFinishDelegate: AnyObject {
  func popupUpDownloadCoordinatorDidFinish()
}

public enum PopupUpDownloadSteps: Step {
  case showPopupUpProcessingDownload(parent: UIViewController?, restoreImage: UIImage?, url: URL?, taskType: TaskType)
  case edit(fileModel: FileListCellModel, editType: FileEditType)
  case popupUpDownloadDidFinish
}

public enum PopupUpDownloadChildCoordinator {
  case edit
}
