//
//  DefaultPopupUpDownloadCoordinator.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 12/5/23.
//

import Foundation
import UIKit

final class DefaultPopupUpDownloadCoordinator: NavigationCoordinator {
  var navigationController: UINavigationController
  weak var delegate: PopupUpDownloadCoordinatorFinishDelegate?
  var childCoordinators = [PopupUpDownloadChildCoordinator : Coordinator]()

  init(navigationController: UINavigationController) {
    self.navigationController = navigationController
  }

  func start(with steps: PopupUpDownloadSteps) {
    navigate(to: steps)
  }
}

extension DefaultPopupUpDownloadCoordinator: PopupUpDownloadCoordinator {
  func navigate(to step: PopupUpDownloadSteps) {
    switch step {
    case let .edit(fileModel: model, editType: type):
      pushEdit(fileModel: model, editType: type)
    case .popupUpDownloadDidFinish:
      navigationController.dismiss(animated: true) { [weak self] in
        AppUtility.lockOrientation(.portrait)
        self?.delegate?.popupUpDownloadCoordinatorDidFinish()
      }
    case let .showPopupUpProcessingDownload(parent: vc, restoreImage: image, url: url, taskType: taskType):
      popupUpProcessingDownload(parent: vc, restoreImage: image, url: url, taskType: taskType)
    }
  }
  
  func popupUpProcessingDownload(parent: UIViewController?, restoreImage: UIImage?, url: URL?, taskType: TaskType) {
    let vm = PopupUpProcessingDownViewModel(
      aiUseCase: Composers.aiUseCase,
      url: url,
      taskType: taskType
    )
    if let iamge = restoreImage {
      vm.restoreImage = iamge
    }
    let vc = PopupUpDownloadViewController(coordinator: self, viewModel: vm)
    if let parent = parent {
      parent.present(vc, animated: true)
    } else {
      navigationController.present(vc, animated: true)
    }
  }

  func pushEdit(fileModel: FileListCellModel, editType: FileEditType) {
    let editCoordinator = DefaultFileEditCoordinator(navigationController: navigationController)
    editCoordinator.delegate = self
    childCoordinators[.edit] = editCoordinator
    editCoordinator.start(with: .showFileEdit(model: fileModel, editType: editType))
  }
}

// MARK: Finish Delegate
extension DefaultPopupUpDownloadCoordinator: FileEditCoordinatorFinishDelegate {
  func fileEditCoordinatorDidFinish() {
    childCoordinators[.edit] = nil
  }
}

