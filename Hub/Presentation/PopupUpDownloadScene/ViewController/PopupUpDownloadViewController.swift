
//
//  PopupUpDownloadViewController.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/5/23.
//

import RxCocoa
import RxSwift
import SnapKit
import UIKit

final class PopupUpDownloadViewController: UIViewController {

  let containerView = UIView()
  let titleLabel = UILabel()
  let descriptionLabel = UILabel()

  let progressView = ProgressView()
  let loadingView = LottieView(name: "ai_analyze_blue")

  let buttonStack = UIStackView()
  let cancelBtn = UIButton()

  weak var coordinator: PopupUpDownloadCoordinator? = nil
  var viewModel: PopupUpProcessingDownViewModel?
  var disposedBag = DisposeBag()

  init(
    coordinator: PopupUpDownloadCoordinator? = nil,
    viewModel: PopupUpProcessingDownViewModel
  ) {
    super.init(nibName: nil, bundle: nil)
    self.modalTransitionStyle = .crossDissolve
    self.modalPresentationStyle = .overCurrentContext
    self.titleLabel.text = L.upload_title.localized
    self.descriptionLabel.text = L.dialog_uploading_msg.localized

    self.viewModel = viewModel
    self.coordinator = coordinator

  }
  required init?(coder: NSCoder) {
    super.init(coder: coder)
  }

  deinit {
    Log.info(category: .App, to: "deinit")
  }

  override func viewDidDisappear(_ animated: Bool) {
    super.viewDidDisappear(animated)
    Log.info(category: .App, to: "viewDidDisappear")
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    setComponent()
    setPopupHideden(true, isLoadingHidden: true)
    setAutoLayout()
    bindViewModel(to: viewModel)
  }

  @objc private func addCancelAction() {
    Log.info(
      category: .App, to: "\(self.navigationController?.viewControllers.debugDescription ?? "")")
    self.dismiss(animated: true)
  }

  func bindViewModel(to viewModel: BaseViewModel?) {
    guard let viewModel = viewModel as? PopupUpProcessingDownViewModel else { return }

    let input = PopupUpProcessingDownViewModel.Input(viewDidLoad: Observable.just(()))

    let output = viewModel.bind(input: input, disposedBag: disposedBag)

    output.rxCheckServerStatus
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] sec in
        var waitSec = sec.secondsToFormattedTime
        mLogger.info("showAiPopup waitSec: \(waitSec)")
        
        let msg = String(format: L.ai_waiting_time_msg.localized, waitSec)
        LKPopupView.popup.alert {[
          .subTitle(msg),
          .showCancel(true),
          .showMiddleButton(true),
          .cancelAction([
            .text(L.cancel.localized),
            .textColor(.black),
            .bgColor(.grayF2),
            .tapActionCallback({
              self?.dismiss(animated: true)
            })
          ]),
          .middleAction([
            .text(L.ai_push_btn_text.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              guard let self else { return }
              AIServiceManager.shared.isPushResult = true
              if viewModel.taskType == .deIdentified {
                viewModel.pushVideoDeIdentification(output: output, disposedBag: self.disposedBag)
              } else if viewModel.taskType == .restored {
                viewModel.pushImageRestoration(output: output, disposedBag: self.disposedBag)
              }
            })
          ]),
          .confirmAction([
            .text(L.ai_wait_btn_text.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({ [weak self] in
              guard let self else { return }
              AIServiceManager.shared.isPushResult = false
              if viewModel.taskType == .deIdentified {
                viewModel.processVideoDeIdentification(output: output, disposedBag: self.disposedBag)
              } else if viewModel.taskType == .restored {
                viewModel.processImageRestoration(output: output, disposedBag: self.disposedBag)
              }
            })
          ])
        ]}
      })
      .disposed(by: disposedBag)

    /// upload
    output.rxUploadStart
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] fileName in
        guard let self else { return }
        mLogger.info("rxUploadStart")
        setPopupHideden(false, isLoadingHidden: true)
        titleLabel.text = L.upload_title.localized
        descriptionLabel.isHidden = false
        descriptionLabel.text = "\(fileName)"
      })
      .disposed(by: disposedBag)

    output.rxUploadPercent
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] percent in
        self?.progressView.updateProgress(percent: percent)
      })
      .disposed(by: disposedBag)

    output.rxUploadEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        guard let self else { return }
        mLogger.info("rxUploadEnd")
        progressView.isHidden = true
        descriptionLabel.isHidden = false
        loadingView.isHidden = false
        if !AIServiceManager.shared.isPushResult {
          titleLabel.text = L.ai_analyzing_txt.localized
          descriptionLabel.text = L.ai_analyzing_desc_txt.localized
        }
      })
      .disposed(by: disposedBag)

    /// processing
    output.rxProcessingStart
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        guard let self else { return }
        mLogger.info("rxProcessingStart")
        titleLabel.text = L.ai_analyzing_txt.localized
        descriptionLabel.text = L.ai_analyzing_desc_txt.localized
        progressView.isHidden = true
        loadingView.isHidden = false
      })
      .disposed(by: disposedBag)

    output.rxProcessingEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        guard let self else { return }
        mLogger.info("rxProcessingEnd")
        LKPopupView.popup.hideLoading()
        if AIServiceManager.shared.isPushResult == true {
          loadingView.isHidden = true
          if let coordinator = coordinator {
            coordinator.navigate(to: .popupUpDownloadDidFinish)
          } else {
            self.dismiss(animated: true)
          }
        } else {
          setPopupHideden(false, isLoadingHidden: true)
        }
      })
      .disposed(by: disposedBag)


    /// download
    output.rxDownloadStart
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] fileName in
        guard let self else { return }
        mLogger.info("rxDownloadStart")
        descriptionLabel.isHidden = false
        progressView.isHidden = false
        cancelBtn.isHidden = false
        titleLabel.text = L.dialog_save_ing.localized
        descriptionLabel.text = "\(fileName)"

      })
      .disposed(by: disposedBag)

    output.rxDownloadPercent
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] percent in
        guard let self else { return }
        progressView.updateProgress(percent: percent)
      })
      .disposed(by: disposedBag)

    output.rxDownloadEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] isUpload in
        guard let self else { return }
        mLogger.info("rxDownloadEnd")
        setPopupHideden(true, isLoadingHidden: true)
        if let coordinator = coordinator {
          coordinator.navigate(to: .popupUpDownloadDidFinish)
        } else {
          self.dismiss(animated: true)
        }
      })
      .disposed(by: disposedBag)

    output.rxError
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] _ in
        guard let self else { return }
        mLogger.error("ai upload rxError")
        setPopupHideden(true, isLoadingHidden: true)
        LKPopupView.popup.alert {[
          .subTitle(L.ai_server_error_msg.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({ })
          ])
        ]}
        if let coordinator = coordinator {
          coordinator.navigate(to: .popupUpDownloadDidFinish)
        } else {
          self.dismiss(animated: true)
        }
      })
      .disposed(by: disposedBag)

    output.rxPushRegisterEnd
      .observe(on: MainScheduler.asyncInstance)
      .bind(onNext: { [weak self] fileModel in
        guard let self else { return }
        mLogger.info("rxPushRegisterEnd")
        setPopupHideden(true, isLoadingHidden: true)
        LKPopupView.popup.alert {[
          .subTitle(L.ai_push_alert_msg.localized),
          .showCancel(false),
          .confirmAction([
            .text(L.ok.localized),
            .bgColor(.vueroidBlue),
            .tapActionCallback({
              self.coordinator?.navigate(to: .popupUpDownloadDidFinish)
            })
          ])
        ]}
      })
      .disposed(by: disposedBag)
  }

  func setComponent() {
    self.view.backgroundColor = .black.withAlphaComponent(0.7)

    containerView.roundCorners(.allCorners, radius: 15)
    containerView.backgroundColor = .white
    containerView.layer.shadowColor = UIColor.black.cgColor
    containerView.layer.shadowOpacity = 0.2
    containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
    self.view.addSubview(containerView)

    titleLabel.font = .h1
    titleLabel.textColor = .vueroidBlue
    titleLabel.numberOfLines = 0
    containerView.addSubview(titleLabel)

    descriptionLabel.font = .sub3
    descriptionLabel.textColor = .text
    descriptionLabel.numberOfLines = 0
    descriptionLabel.textAlignment = .center
    containerView.addSubview(descriptionLabel)

    loadingView.animationView?.loopMode = .loop
    loadingView.animationView?.animationSpeed = 1.0
    loadingView.isHidden = true
    loadingView.play()
    containerView.addSubview(loadingView)

    progressView.roundCorners(.allCorners, radius: 7.5)
    progressView.isHidden = true
    containerView.addSubview(progressView)

    buttonStack.distribution = .fillEqually
    buttonStack.spacing = 10
    buttonStack.axis = .horizontal
    containerView.addSubview(buttonStack)

    cancelBtn.setTitle(L.cancel.localized, for: .normal)
    cancelBtn.setTitleColor(.white, for: .normal)
    cancelBtn.setBackgroundColor(.vueroidBlue, for: .normal)
    cancelBtn.roundCorners(.allCorners, radius: 5)
    cancelBtn.addTarget(self, action: #selector(addCancelAction), for: .touchUpInside)
    buttonStack.addArrangedSubview(cancelBtn)
  }

  func setPopupHideden(_ isHidden: Bool, isLoadingHidden: Bool) {
    containerView.isHidden = isHidden
    titleLabel.isHidden = isHidden
    descriptionLabel.isHidden = isLoadingHidden
    loadingView.isHidden = isLoadingHidden
    progressView.isHidden = isHidden
    buttonStack.isHidden = isHidden
    cancelBtn.isHidden = isHidden
  }

  func setAutoLayout() {
    containerView.snp.makeConstraints { make in
      make.center.equalToSuperview()
      if traitCollection.verticalSizeClass == .compact {
        // Landscape mode
        make.leading.trailing.equalToSuperview().inset(250)
      } else {
        // Portrait mode
        make.leading.trailing.equalToSuperview().inset(20)
      }
    }
    titleLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalToSuperview().inset(40)
      make.height.greaterThanOrEqualTo(30)
    }
    descriptionLabel.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(titleLabel.snp.bottom).offset(12)
      make.height.greaterThanOrEqualTo(18)
    }

    loadingView.snp.makeConstraints { make in
      make.centerX.equalToSuperview()
      make.top.equalTo(descriptionLabel.snp.bottom).offset(6)
      make.width.height.equalTo(82)
    }

    progressView.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(descriptionLabel.snp.bottom).offset(20)
      make.height.equalTo(15)
    }

    buttonStack.snp.makeConstraints { make in
      make.leading.trailing.equalToSuperview().inset(20)
      make.top.equalTo(progressView.snp.bottom).offset(48)
      make.bottom.equalToSuperview().inset(20)
      make.height.equalTo(42.rv)
    }
  }
}
