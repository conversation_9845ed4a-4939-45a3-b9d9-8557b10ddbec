//
//  HotspotManager.swift
//  Hub
//
//  Created by ncn on 2022/12/06.
//

import CoreLocation
import NetworkExtension
import RxSwift
import SystemConfiguration.CaptiveNetwork
import UIKit

private class SSID {
  class func fetchNetworkInfo() -> [NetworkInfo]? {
    if let interfaces: NSArray = CNCopySupportedInterfaces() {
      var networkInfos = [NetworkInfo]()
      for interface in interfaces {
        let interfaceName = interface as! String
        var networkInfo = NetworkInfo(
          interface: interfaceName,
          success: false,
          ssid: nil,
          bssid: nil)
        if let dict = CNCopyCurrentNetworkInfo(interfaceName as CFString) as NSDictionary? {
          networkInfo.success = true
          networkInfo.ssid = dict[kCNNetworkInfoKeySSID as String] as? String
          networkInfo.bssid = dict[kCNNetworkInfoKeyBSSID as String] as? String
        }
        networkInfos.append(networkInfo)
        hLogger.info("<<<<<< \(String(describing: networkInfo))")
      }
      return networkInfos
    }
    return nil
  }
}

struct NetworkInfo {
  var interface: String
  var success: Bool = false
  var ssid: String? /* Service Set Identifier */
  var bssid: String? /* Basic Service Set Identifier */
}

class HotspotManager: NSObject {
  var ssid: String?
  var password: String?
  var locationManager = CLLocationManager()

  var currentNetworkInfos: [NetworkInfo]? {
    return SSID.fetchNetworkInfo()
  }

  var connectHandler: ((Bool, Error?) -> Void)? /* isconnect, error */

  var rxIsConnect = PublishSubject<Bool>()
  var rxError = PublishSubject<Error>()

  private func checkConnectWifi(isCheck: Bool) {
    let status = locationManager.authorizationStatus
    switch status {
    case .notDetermined, .restricted:
      locationManager.delegate = self
      locationManager.requestWhenInUseAuthorization()
    case .denied:
      break
    case .authorizedAlways, .authorizedWhenInUse, .authorized:
      updateWiFi()
    }
  }

  private func clearConfigured() {
    NEHotspotConfigurationManager.shared.getConfiguredSSIDs { (wifiList) in
      wifiList.forEach {
        NEHotspotConfigurationManager.shared.removeConfiguration(forSSID: $0)
      }
    }
  }
}

extension HotspotManager {
  func updateWiFi() {
    hLogger.info("currentNetworkInfos : \(self.currentNetworkInfos?.debugDescription ?? ""))")
    if let ssid = currentNetworkInfos?.first?.ssid {
      hLogger.info("updateWiFi ssid: \(ssid)")
      Current.connectedWifiSsid = ssid
      let ret = (ssid == self.ssid)
      self.rxIsConnect.onNext(ret)
      self.connectHandler?(ret, nil)
    } else {
      Current.connectingWifiHotspot = .failed
      hLogger.error("updateWiFi fail")
      self.rxIsConnect.onNext(false)
      self.connectHandler?(false, nil)
    }
  }
}

extension HotspotManager: CLLocationManagerDelegate {
  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    if locations.last != nil {
      hLogger.info(">>>>>> \(locations)")
    }
  }

  func locationManager(
    _ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus
  ) {
    /* Detect the CLAuthorizationStatus and enable the capture of associated SSID. */
    hLogger.info(">>>>>> \(String(describing: status))")
    if status == CLAuthorizationStatus.authorizedAlways
      || status == CLAuthorizationStatus.authorizedWhenInUse
    {
    }

    if status == .authorizedWhenInUse {
      updateWiFi()
    }
  }

  func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    hLogger.error(">>>>>> \(error)")
    if let error = error as? CLError, error.code == .denied {
      manager.stopUpdatingLocation()
    }
  }
}
//start connect to: param:VUEROID_S1-4K_3E3012_5G, password: "wert2345"
extension HotspotManager {
  func connect(ssid: String, reconnectCount: Int, password: String) {
    hLogger.info("Current.connectingWifiHotspot : \(Current.connectingWifiHotspot.rawValue)")
    Current.connectingWifiHotspot = .connecting
    hLogger.info("start connect to: param:\(ssid), password: \(password)")
    Current.connectedWifiPassWord = password
    self.ssid = ssid
    clearConfigured()
    let delayTime = TimeInterval(3 + reconnectCount)

    DispatchQueue.main.asyncAfter(
      deadline: .now() + delayTime,
      execute: {
        let configuration = NEHotspotConfiguration(
          ssid: ssid,
          passphrase: password,
          isWEP: false
        )
        
        configuration.joinOnce = false
        NEHotspotConfigurationManager.shared.apply(configuration) { (error) in
          hLogger.error("apply completion \(error?.localizedDescription ?? "")")

          if let e = error {
            hLogger.error("\(String(describing: error?.localizedDescription))")
            if e.localizedDescription == "already associated." {
              Current.connectingWifiHotspot = .connected
              self.rxIsConnect.onNext(true)
              self.connectHandler?(true, nil)
            } else {
              Current.connectingWifiHotspot = .failed
              self.rxError.onNext(e)
              self.rxIsConnect.onNext(false)
              self.connectHandler?(false, e)
            }
          } else {
            self.checkConnectWifi(isCheck: true)
          }
        }
      })
  }

  func disconnect(ssid: String? = nil) {
    if let target = ssid ?? self.ssid {
      NEHotspotConfigurationManager.shared.removeConfiguration(forSSID: target)
    }
  }

  func dicconnect(ssid target: String) {
    NEHotspotConfigurationManager.shared.removeConfiguration(forSSID: target)
  }
}

extension HotspotManager {

  func setHotspot() {
  }

  func checkNetwork() {
  }

  func getAllWiFiNameList() -> String? {
    return ""
  }
}
