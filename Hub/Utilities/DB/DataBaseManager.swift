//
//  DataBaseManager.swift
//  Hub
//
//  Created by ncn on 2023/03/17.
//

import GRDB
import UIKit

let _DB_NAME = "main.db"
let _DB_HISTORY = "history.db"
let _DB_GPS = "gps.db"

let _TAG_SSID = "SSID"

let _TAG_DRIVE_TYPE = "TYPE"
let _TAG_DRIVE_START = "START"
let _TAG_DRIVE_END = "END"
let _TAG_DRIVE_DISTANCE = "DISTANCE"
let _TAG_DRIVE_SPEEDMAX = "SPEEDMAX"
let _TAG_DRIVE_SPEEDAVG = "SPEEDAVG"
let _TAG_DRIVE_EVTNUM = "EVTNUM"
let _TAG_DRIVE_MOTNUM = "MOTNUM"
let _TAG_DRIVE_OFFTYPE = "OFFTYPE"
let _TAG_DRIVE_RESERVED = "RESERVED"

let _TAG_GPS_TIME = "TIME"
let _TAG_GPS_LATITUDE = "LATITUDE"
let _TAG_GPS_LONGITUDE = "LONGITUDE"

class DataBaseManager: NSObject {
  static let shared: DataBaseManager = {
    let instance = DataBaseManager()
    return instance
  }()

  //    var dataBaseQueue: DatabaseQueue?
  var dbPath: String? = {
    guard let docPath = UrlList.dbPath() else { return nil }
    var path = ""
    if #available(iOS 16.0, *) {
      path = docPath.appendingPathComponent(_DB_NAME).path(percentEncoded: true)
    } else {
      path = docPath.appendingPathComponent(_DB_NAME).path
    }
    return path
  }()

  private override init() {
    Log.message(to: "")
    super.init()
    self.initDataBase()
  }

  private func initDataBase() {
    Log.message(to: "")
    copyDBFile()
  }

  private func copyDBFile() {
    Log.message(to: "")
    guard let dbFolderPath = UrlList.dbPath() else { return }

    if FileManager.default.fileExists(atPath: dbFolderPath.path) != true {
      do {
        try FileManager.default.createDirectory(
          atPath: dbFolderPath.path,
          withIntermediateDirectories: true,
          attributes: nil)
      } catch {
        Log.error(to: error.localizedDescription)
      }
    }

    let folderDbCount = try? FileManager.default.contentsOfDirectory(atPath: dbFolderPath.path)
      .count
    if folderDbCount == 0 {
      try? FileManager.default.copyItem(
        at: Bundle.main.url(forResource: "main", withExtension: "db")!,
        to: (dbFolderPath.appendingPathComponent("main.db")))

      do {

        let dbQueue = try DatabaseQueue(path: dbFolderPath.appendingPathComponent("main.db").path)

        try dbQueue.read { db in
          print("조회")
          let isTue = try db.tableExists("DEVICE")
          let isfalse = try db.tableExists("DEVICE2")
          print("조회 1 = \(isTue)")
          print("조회 2 = \(isfalse)")
        }

      } catch {
        Log.error(to: "db read error")
      }
    } else {
      do {
        let dbQueue = try DatabaseQueue(path: dbFolderPath.appendingPathComponent("main.db").path)
        try dbQueue.read { db in
          let rows = try Row.fetchCursor(db, sql: "SELECT * FROM sqlite_master WHERE type='table'")
          while let row = try rows.next() {
            print("show table : \(row.debugDescription)")
          }
        }
      } catch {
        Log.error(to: "db read error")
      }
    }
  }
}

extension DataBaseManager {
  private func getTableName(completion: (String?) -> Void) {
    Log.message(to: "")
    guard let macAddress = AppManager.shared.deviceInfo?.macaddress else {
      completion(nil)
      return
    }

    let macadr = macAddress.replacingOccurrences(of: ":", with: "")

    var tableName: String? = nil
    do {
      guard let mainDbPath = UrlList.dbPath() else { return }
      let path = mainDbPath.appendingPathComponent("main.db").path
      let dbQueue = try DatabaseQueue(path: path)
      try dbQueue.read { db in
        let query = "SELECT * FROM DEVICE WHERE MACADR ='\(macadr)'"
        let rows = try Row.fetchCursor(db, sql: query)
        while let row = try rows.next() {
          tableName = row[_TAG_SSID]
        }
        completion(tableName)
      }
    } catch let err {
      Log.message(to: "DB Error: \(err)")
      completion(nil)
    }
  }

  // case 1 : Empty Main DB
  private func createTable(name: String) {
    Log.message(to: "")
    self.initDataBase()
    Log.message(to: "Create to \(name)")
    guard let macAddress = AppManager.shared.dashcam?.macaddress
    else {
      let msg = "Fail to get databaese or macaddress."
      Log.message(to: msg)
      _ = NCError(title: "DB error", description: msg, code: 3011)
      return
    }

    let ssid = name
    let timeStamp = Date().timeIntervalSince1970
    let macadr = macAddress.replacingOccurrences(of: ":", with: "")

    do {
      guard let mainDbPath = UrlList.dbPath() else { return }
      let path = mainDbPath.appendingPathComponent("main.db").path
      let dbQueue = try DatabaseQueue(path: path)
      let deviceCount = try dbQueue.read { db in
        let query = """
          SELECT *
          FROM DEVICE
          WHERE MACADR = '\(macadr)'
          """
        let rows = try Row.fetchAll(db, sql: query)
        return rows.count
      }

      if deviceCount != 0 {
        return
      }

      let writeEnd: () = try dbQueue.write { db in
        try db.execute(
          sql: """
            INSERT INTO
            DEVICE (SSID, MACADR, CREATE_DATE, UPDATE_DATE)
            VALUES (?,?,?,?)
            """,
          arguments: [ssid, macadr, timeStamp, timeStamp])
        let isDrive = try db.tableExists("DRIVE_\(macadr)")
        if isDrive != true {
          try db.create(table: "DRIVE_\(macadr)") { t in
            t.column(_TAG_DRIVE_TYPE, .integer)
            t.column(_TAG_DRIVE_START, .integer)
            t.column(_TAG_DRIVE_END, .integer)
            t.column(_TAG_DRIVE_DISTANCE, .integer)
            t.column(_TAG_DRIVE_SPEEDMAX, .integer)
            t.column(_TAG_DRIVE_SPEEDAVG, .integer)
            t.column(_TAG_DRIVE_EVTNUM, .integer)
            t.column(_TAG_DRIVE_MOTNUM, .integer)
            t.column(_TAG_DRIVE_OFFTYPE, .integer)
            t.column(_TAG_DRIVE_RESERVED, .integer)
          }
        }
        let isGps = try db.tableExists("GPS_\(macadr)")
        if isGps != true {
          try db.create(table: "GPS_\(macadr)") { t in
            t.column(_TAG_GPS_TIME, .integer)
            t.column(_TAG_GPS_LATITUDE, .integer)
            t.column(_TAG_GPS_LONGITUDE, .integer)
          }
        }
      }
      Log.message(to: writeEnd)
      return writeEnd
    } catch let error {
      Log.error(to: "\(error)")
    }
  }

  //case 2: DB UPDATE - Sync From Device
  private func dataBaseUpdate() -> Result<Bool, Error> {
    Log.message(to: "")
    guard let tempPath = UrlList.tempPath() else {
      let msg = "Fail to get path."
      let ncError = NCError(title: "Custom error", description: msg, code: 3012)
      Log.error(to: msg)
      return .failure(ncError)
    }

    Log.info(category: .DB, to: "temppath: \(tempPath)")

    guard let macAddress = AppManager.shared.dashcam?.macaddress
    else {
      let msg = "Fail to get db or mac address."
      let ncError = NCError(title: "Custom error", description: msg, code: 3014)
      Log.error(to: msg)
      return .failure(ncError)
    }

    let macadr = macAddress.replacingOccurrences(of: ":", with: "")

    let lastDriveEnd = UserDefaults.standard.integer(forKey: "DRIVE_\(macadr)")
    /* Select drive table. */
    Log.message(to: lastDriveEnd)
    let historyRows = SqlQuery.selectAllFromHistory().filter { history in
      lastDriveEnd < history.START
    }
    Log.message(to: historyRows)
    /* Insert drive table */
    if historyRows.count > 0 {
      _ = SqlQuery.insertIntoDrive(drives: historyRows, macadr: macadr)
      UserDefaults.standard.setValue(historyRows.last?.END ?? 0, forKey: "DRIVE_\(macadr)")
    }
    // ---------------------------

    let lastGpsEnd = UserDefaults.standard.integer(forKey: "GPS_\(macadr)")
    /* Select gps table. */
    Log.message(to: lastGpsEnd)
    let gpsRows = SqlQuery.selectAllFromGps().filter { gps in
      lastGpsEnd < gps.TIME
    }
    Log.message(to: gpsRows)
    /* Insert gps table */
    if gpsRows.count > 0 {
      _ = SqlQuery.insertIntoGps(gpss: gpsRows, macadr: macadr)
      UserDefaults.standard.setValue(gpsRows.last?.TIME ?? 0, forKey: "GPS_\(macadr)")
    }
    return .success(true)
  }

  func migration() -> Result<Bool, Error> {
    Log.message(to: "")
    guard let netInfo = HotspotManager().currentNetworkInfos?.first,
      let ssid = netInfo.ssid,
      let macAddress = AppManager.shared.deviceInfo?.macaddress
    else {
      Log.message(to: "Not found ssid.")
      return .failure(NCError(title: "Not found ssid", description: "Not found ssid", code: 999))
    }
    if SqlQuery.getTableName(macAddress: macAddress) == "" {
      createTable(name: ssid)
    }
    return dataBaseUpdate()
  }

}

extension DataBaseManager {
  func deleteData(items: [TravelLogCellModel], isDeleteAll: Bool = false) {

    guard let macAddress = AppManager.shared.dashcam?.macaddress else { return }
    _ = macAddress.replacingOccurrences(of: ":", with: "")

    for item in items {
      let startDate: String = item.date + item.startTm
      let endDate: String = item.date + item.endTm
      print("\(startDate) :: \(endDate)")
      let start = startDate.toDate(format: "yyyy-MM-ddHH:mm:ss")
      let end = endDate.toDate(format: "yyyy-MM-ddHH:mm:ss")
      Log.message(to: "\(start) :: \(end)")
      let s = Int(start?.timeIntervalSince1970 ?? 0)
      let e = Int(end?.timeIntervalSince1970 ?? 0)
      Log.message(to: "\(s) :: \(e)")
      // Drive, GPS 삭제 쿼리 실행
      _ = SqlQuery.deleteDriveData(startEpoch: s, endEpoch: e)
      _ = SqlQuery.deleteGpsData(startEpoch: s, endEpoch: e)
    }
  }
}
