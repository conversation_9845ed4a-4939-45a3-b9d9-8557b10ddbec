//
//  NCUtil.swift
//  Hub
//
//  Created by ncn on 2023/07/03.
//

import UIKit

class NCUtil: NSObject {
  static func currentAppVersion() -> String {
    if let info: [String: Any] = Bundle.main.infoDictionary,
      let currentVersion: String = info["CFBundleShortVersionString"] as? String
    {
      return currentVersion
    }
    return "nil"
  }

  static func currentBuildNumber() -> String {
    if let info: [String: Any] = Bundle.main.infoDictionary,
      let buildNumber: String = info["CFBundleVersion"] as? String
    {
      return buildNumber
    }
    return "nil"
  }

  static func kiloToMile(kilo: Int) -> Int {
    return Int((Double(kilo) / 1.60934).rounded())
  }

  static func mileToKilo(mile: Int) -> Int {
    return Int((Double(mile) * 1.60934).rounded())
  }

  static func getDuration(times: [String]) -> String {
    var second = 0
    for t in times {
      let ret = calculateSum("00:00:00", t)
      second += ret.hour * 3600
      second += ret.minute * 60
      second += ret.second
    }
    return second.getDurationString()
  }

  static func formattedTime(_ time: String, form: String = "HH:mm:ss") -> Date? {
    let formatter = DateFormatter()
    formatter.locale = Locale(identifier: "en_US_POSIX")
    formatter.dateFormat = form
    return formatter.date(from: time)
  }

  static func calculateDifference(_ from: String, _ to: String) -> (
    hour: Int, minutes: Int, second: Int
  ) {
    guard let fromTime = formattedTime(from),
      let toTime = formattedTime(to)
    else {
      return (0, 0, 0)
    }
    let calendar = Calendar.current

    let components = calendar.dateComponents([.hour, .minute, .second], from: fromTime, to: toTime)
    return (components.hour ?? 0, components.minute ?? 0, components.second ?? 0)
  }

  static func calculateSum(_ time1: String, _ time2: String) -> (
    hour: Int, minute: Int, second: Int
  ) {
    let defaultTime = "00:00:00"
    let calculatedTime1 = calculateDifference(defaultTime, time1)
    let calcaultedTime2 = calculateDifference(defaultTime, time2)
    return (
      calculatedTime1.hour + calcaultedTime2.hour,
      calculatedTime1.minutes + calcaultedTime2.minutes,
      calculatedTime1.second + calcaultedTime2.second
    )
  }

  static func videoFileSectionDict(vodArray: [URL]) -> [String:
    [FileListCellModel]]
  {
    var dict: [String: [FileListCellModel]] = [:]
    for path in vodArray {
      let obj = FileListCellModel(
        type: .file,
        fileName: path.lastPathComponent,
        filePath: path
      )
      let fileName = path.lastPathComponent
      let isBookmarkFile = fileName.isBookmarkFileNamePattern()
      let dayString = isBookmarkFile ? fileName.components(separatedBy: "_")[1] :  fileName.components(separatedBy: "_").first ?? ""
//      mLogger.info("### isBookmarkFile: \(isBookmarkFile), dayString: \(dayString)")
      var sectionName = ""
      if dayString.count != 8 {
        sectionName = "External"
      } else {
        let year = dayString[0..<4]
        let month = dayString[4..<6]
        let day = dayString[6..<8]
        sectionName = "\(year) / \(month) / \(day)"
      }

      if dict[sectionName] == nil {
        dict[sectionName] = [obj]
      } else {
        dict[sectionName]?.append(obj)
      }
    }
    return dict
  }

  static func snapShotFileSectionDict(array: [URL]) -> [String: [FileListCellModel]] {
    var dict: [String: [FileListCellModel]] = [:]
    for path in array {
      do {
        let data = try Data(contentsOf: path)
        let image = UIImage(data: data)
        Log.message(to: data.description)
        let obj = FileListCellModel(
          type: .screenshot, image: image, fileName: path.lastPathComponent, filePath: path)

        let timeString = path.lastPathComponent.components(separatedBy: ".").first ?? ""
        // FIXME:  은 "001_20250702_134921_INF_F_N.jpg" 이런 형식
        // TODO: `HubNameGenerator` 에 통합
        let array = timeString.components(separatedBy: "_")
        let dateString: String
        if array.count > 5 && array[0].count == 3 {
          dateString = timeString.components(separatedBy: "_")[1]
        } else {
          dateString = timeString.components(separatedBy: "_").first ?? ""
        }

        let sectionName: String

        if dateString.count < 8 {
          sectionName = timeString.components(separatedBy: "_").prefix(3).joined(separator: " / ")
        } else {
          var dateList = dateString.map { String($0) }
          dateList.insert(" / ", at: 6)
          dateList.insert(" / ", at: 4)
          sectionName = dateList.joined()
        }

        if dict[sectionName] == nil {
          dict[sectionName] = [obj]
        } else {
          dict[sectionName]?.append(obj)
        }
      } catch {
        Log.error(to: "Error loading image : \(error)")
        break
      }
    }
    return dict
  }

}

extension String {
  func isBookmarkFileNamePattern() -> Bool {
    guard self.count >= 4 else { return false }
    let chars = Array(self)
    return chars[0].isNumber && chars[1].isNumber && chars[2].isNumber && chars[3] == "_"
  }
}
