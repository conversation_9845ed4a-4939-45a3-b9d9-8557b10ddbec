//
//  BaseTableViewCell.swift
//  Hub
//
//  Created by ncn on 2022/12/01.
//

import UIKit

class BaseTableViewCell: UITableViewCell, Reusable {

  public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
    super.init(style: style, reuseIdentifier: reuseIdentifier)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  override func awakeFromNib() {
    super.awakeFromNib()
  }

  override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON>ol) {
    super.setSelected(selected, animated: animated)
  }

  override func setHighlighted(_ highlighted: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
    super.setHighlighted(highlighted, animated: animated)
  }

  func setComponent() {

  }

  func setAutoLayout() {

  }
}
