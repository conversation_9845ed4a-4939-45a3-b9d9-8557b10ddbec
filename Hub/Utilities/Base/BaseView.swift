//
//  BaseView.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import RxCocoa
import RxSwift
import SnapKit
import UIKit

class BaseView: UIView {
  let screenWidth = UIScreen.width
  let screenHeight = UIScreen.height
  let disposedBag = DisposeBag()

  // MARK: - Templet Method
  internal func initVariable() {
  }

  func setComponent() {
  }

  func setAutoLayout() {
  }

  func setRxFunction() {
  }
}
