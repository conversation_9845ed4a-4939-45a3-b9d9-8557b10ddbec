//
//  BaseScrollViewContoller.swift
//  Hub
//
//  Created by ncn on 2023/05/30.
//

import UIKit

class BaseScrollViewController: BaseViewController {
  lazy var scrollView: UIScrollView = {
    let view = UIScrollView()
    view.showsHorizontalScrollIndicator = false
    view.alwaysBounceHorizontal = false
    view.zoomScale = 1.0
    view.translatesAutoresizingMaskIntoConstraints = false
    view.backgroundColor = .colorRGB(32, 33, 36)
    return view
  }()

}
