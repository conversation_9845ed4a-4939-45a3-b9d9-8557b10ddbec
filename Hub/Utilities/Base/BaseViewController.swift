//
//  BaseViewController.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import RxCocoa
import RxRelay
import RxSwift
import SnapKit
import UIKit

let _indicator_timeout = 180

class BaseViewController: UIViewController {
  let screenWidth = UIScreen.width
  let screenHeight = UIScreen.height
  var keyboardHeight: CGFloat = 0.0
  let ratio = UIScreen.ratio
  var disposedBag = DisposeBag()
  var disclaimerHasBeenDisplayed = false

  var popupView: PopupView?
  var popupViewController: PopupViewController = PopupViewController()

  var safeAreaSize: UIEdgeInsets {
    return UIApplication.shared.windows.first?.safeAreaInsets ?? .zero
  }

  private var alert: UIAlertController?

  lazy var indicatorView: UIView = {
    let baseView = UIView()
    baseView.backgroundColor = UIColor.black.withAlphaComponent(0.4)
    self.view.addSubview(baseView)
    baseView.snp.makeConstraints { [weak self] v in
      guard let self = self else { return }
      v.top.bottom.left.right.equalTo(self.view)
    }

    let indicator = UIActivityIndicatorView(style: UIActivityIndicatorView.Style.large)
    indicator.tag = 777
    indicator.color = .white
    baseView.addSubview(indicator)
    indicator.snp.makeConstraints { [weak self] v in
      guard let self = self else { return }
      v.center.equalTo(self.view)
    }
    return baseView
  }()

  var rxKeyboard = PublishSubject<Bool>()

  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)

    setDefaultOrientaion()
  }

  override func viewWillDisappear(_ animated: Bool) {
    super.viewWillDisappear(animated)
    if isMovingFromParent {
      RestfulManager.cancellAll()
    }
  }

  override func viewDidAppear(_ animated: Bool) {
    super.viewDidAppear(animated)
    #if QA
      let appVersion = AppManager.appVersion
      let currentBuildNumber = AppManager.currentBuildNumber
      let versionLabel = UILabel()
      versionLabel.text = "\(appVersion) (\(currentBuildNumber))"
      versionLabel.textColor = .darkGray
      versionLabel.font = .boldSystemFont(ofSize: 16)
      self.view.addSubview(versionLabel)
      versionLabel.snp.makeConstraints { make in
        make.leading.equalToSuperview().inset(10)
        make.bottom.equalTo(self.view.safeAreaLayoutGuide)
      }
    #endif
    #if DEBUG
      let label = UILabel()
      label.text = String(describing: type(of: self))
      label.textColor = .darkGray
      label.font = .boldSystemFont(ofSize: 16)
      self.view.addSubview(label)
      label.snp.makeConstraints { make in
        make.trailing.equalToSuperview().inset(10)
        make.bottom.equalTo(self.view.safeAreaLayoutGuide)
      }
    #endif
  }

  override func viewDidLoad() {
    super.viewDidLoad()
    self.view.backgroundColor = .background
    NotificationCenter.default.addObserver(
      self, selector: #selector(orientationChanged),
      name: UIDevice.orientationDidChangeNotification, object: nil)
  }

  override func viewDidDisappear(_ animated: Bool) {
    super.viewDidDisappear(animated)
    NotificationCenter.default.removeObserver(self)
  }

  deinit {
  }

  // MARK: - Templet Method
  internal func initVariable() {
  }

  internal func setupLayout() {
  }

  internal func setComponent() {
  }

  internal func setAutoLayout() {
  }

  internal func setRxFunction() {
  }

  internal func bindViewModel(to viewModel: BaseViewModel?) {
  }

}

extension BaseViewController {
  internal func setDefaultOrientaion() {
    AppUtility.lockOrientation(.portrait)
    setOrientaion(isLandscape: false)
  }

  func setOrientaion(isLandscape: Bool, completion: @escaping () -> Void = {}) {
    if #available(iOS 16, *) {
      DispatchQueue.main.async {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
          self.setNeedsUpdateOfSupportedInterfaceOrientations()
          if let navi = self.navigationController {
            navi.setNeedsUpdateOfSupportedInterfaceOrientations()
          }
          let orientation: UIInterfaceOrientationMask = isLandscape ? .landscapeRight : .portrait
          completion()
          windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: orientation)) { error in
            DispatchQueue.main.async {
              Log.info(to: "geometry: \(windowScene.effectiveGeometry), error: \(error)")

            }
          }
        }
      }
    } else {

      DispatchQueue.main.async {
        let orientation: UIInterfaceOrientation = isLandscape ? .landscapeRight : .portrait
        UIDevice.current.setValue(orientation.rawValue, forKey: "orientation")
        completion()
      }
      //
    }
  }
}

// MAKR: Alert, Toast
extension BaseViewController {
  func dismissController(_ isAnimate: Bool = true) {
    if let nv = self.navigationController {
      nv.popViewController(animated: isAnimate)
    } else {
      self.dismiss(animated: true, completion: nil)
    }
  }

  @objc internal func orientationChanged(_ noti: NSNotification) {
  }

  public func showPopup(title: String, desc: String, isCancel: Bool, confirmAction: (() -> Void)?, textAlignment: NSTextAlignment = .center) {
    DispatchQueue.main.async { [weak self] in
      guard let self = self else { return }

      self.popupViewController.dataSetting(title: title, desc: desc, isCancel: isCancel)
      self.popupViewController.tapConfirm = confirmAction
      self.popupViewController.modalTransitionStyle = .crossDissolve
      self.popupViewController.modalPresentationStyle = .overCurrentContext
      self.popupViewController.textAlignment = textAlignment

      self.popupViewController.dismiss(animated: true) {
        self.present(self.popupViewController, animated: true)
      }
    }
  }

  public func dismissPopup() {
    DispatchQueue.main.async { [weak self] in
      guard let self = self else { return }
      self.popupViewController.dismiss(animated: true)
      //      if let popupVC = self.popupViewController {
      //        popupVC.dismiss(animated: true) {
      //          self.popupViewController = nil
      //        }
      //      }
    }
  }

  public func showMessageAlert(message: String, title: String, actions: [UIAlertAction]? = nil) {
    let controller = UIAlertController.init(
      title: title,
      message: message,
      preferredStyle: UIAlertController.Style.alert)
    if let acts = actions {
      for action in acts {
        controller.addAction(action)
      }
    } else {
      let action = UIAlertAction.init(title: "CLOSE", style: UIAlertAction.Style.default) {
        (action) in
      }
      controller.addAction(action)
    }

    if let vc = UIApplication.topViewController(),
      vc.isKind(of: UIAlertController.self) != true
    {
      DispatchQueue.main.async {
        vc.present(controller, animated: true, completion: nil)
      }
    }
    self.alert = controller
  }

  public func dismissAlert(completion: @escaping () -> Void = {}) {
    if let vc = alert {
      vc.dismiss(animated: false, completion: nil)
      //vc.dismiss(animated: true) {
      completion()
      //}
    } else {
      completion()
    }
  }

  func showIndicator(
    runLocation: String = "",
    timeout: Int = _indicator_timeout,
    completion: @escaping (_ isClose: Bool) -> Void = { _ in }
  ) {
    Log.trace(to: "show indicator -> \(runLocation)")
    DispatchQueue.main.async {
      self.indicatorView.isHidden = false
      self.view.bringSubviewToFront(self.indicatorView)
      if let i: UIActivityIndicatorView = self.indicatorView.viewWithTag(777)
        as? UIActivityIndicatorView
      {
        i.startAnimating()
        if timeout > 0 {
          let delay = timeout
          DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(delay)) {
            completion(self.indicatorView.isHidden)
            self.closeIndicator(runLocation: "timeOut")
          }
        }
      }
    }
  }

  func closeIndicator(runLocation: String = "") {
    Log.trace(to: "close indicator -> \(runLocation)")
    DispatchQueue.main.async {
      self.indicatorView.isHidden = true
      if let i: UIActivityIndicatorView = self.indicatorView.viewWithTag(777)
        as? UIActivityIndicatorView
      {
        i.stopAnimating()
      }
    }
  }

  func showToast(
    duration: Double = 2.0,
    message: String,
    font: UIFont = .systemFont(ofSize: 13.0),
    title: String? = nil,
    offset: CGFloat = 50
  ) {
    #if DEBUG
      DispatchQueue.main.async {
        if let tView = self.view.viewWithTag(501) {
          tView.removeFromSuperview()
        }
        let _ = UIScreen.ratio
        let x = 0.0
        let _: CGFloat = title == nil ? 40.0 : 0.0
        //let y = UIScreen.height - 80.0 - UIApplication.shared.safeAreaBottom - UIApplication.shared.statusBarHeight + minus
        let y = UIScreen.height - (title == nil ? 47.0 : 77.0) - offset
        let width = UIScreen.width
        let view = UIView(
          frame: CGRect(
            x: x,  //CGFloat(x),
            y: y,
            width: width,
            height: title == nil ? 47.0 : 77.0))
        view.alpha = 1.0
        view.backgroundColor = .black.withAlphaComponent(0.5)
        view.tag = 501
        //view.roundCorners(.allCorners, radius: 20.0 * ratio)
        self.view.addSubview(view)

        let label = UILabel()
        label.textColor = .text
        label.font = font
        label.textAlignment = .center
        label.text = message
        label.numberOfLines = 2
        view.addSubview(label)

        label.snp.makeConstraints { v in
          v.top.equalTo(view.snp.top).offset(4.0)
          v.left.equalTo(view.snp.left).offset(20.0)
          v.right.equalTo(view.snp.right).offset(-20.0)
          v.height.equalTo(40.0)
        }

        UIView.animate(
          withDuration: duration, delay: 0.5, options: .curveEaseOut,
          animations: { view.alpha = 0.0 },
          completion: { (isCompleted) in
            view.removeFromSuperview()
          })
      }
    #endif
  }

  func showReleaseToast(
    duration: Double = 2.0,
    message: String,
    font: UIFont = .systemFont(ofSize: 13.0),
    title: String? = nil,
    location: ToastLocation = .center
  ) {
    DispatchQueue.main.async {
      if let tView = self.view.viewWithTag(501) {
        tView.removeFromSuperview()
      }

      let width = UIScreen.width - 40
      let view = UIView()
      view.alpha = 1.0
      view.backgroundColor = .black.withAlphaComponent(0.5)
      view.tag = 501
      view.roundCorners(.allCorners, radius: 20)
      self.view.addSubview(view)

      switch location {
      case .top:
        view.snp.makeConstraints { make in
          make.top.equalTo(self.view.safeAreaLayoutGuide)
          make.centerX.equalToSuperview()
          make.height.equalTo(title == nil ? 47.0 : 77.0)
          make.width.equalTo(width)
        }
      case .bottom:
        view.snp.makeConstraints { make in
          make.bottom.equalTo(self.view.safeAreaLayoutGuide)
          make.centerX.equalToSuperview()
          make.height.equalTo(title == nil ? 47.0 : 77.0)
          make.width.equalTo(width)
        }
      case .center:
        view.snp.makeConstraints { make in
          make.center.equalTo(self.view.safeAreaLayoutGuide)
          make.centerX.equalToSuperview()
          make.height.equalTo(title == nil ? 47.0 : 77.0)
          make.width.equalTo(width)
        }
      }

      let label = UILabel()
      label.textColor = .text
      label.font = font
      label.textAlignment = .center
      label.text = message
      label.numberOfLines = 2
      view.addSubview(label)

      label.snp.makeConstraints { v in
        v.edges.equalToSuperview()
      }

      UIView.animate(
        withDuration: duration, delay: 0.5, options: .curveEaseOut,
        animations: { view.alpha = 0.0 },
        completion: { (isCompleted) in
          view.removeFromSuperview()
        })

    }
  }
}

extension Reactive where Base: BaseViewController {
  var indicator: Binder<Bool> {
    return Binder(self.base) { vc, isShow in
      switch isShow {
      case true:
        vc.showIndicator()
      case false:
        vc.closeIndicator()
      }
    }
  }
}

extension Reactive where Base: UIViewController {
  public var viewDidLoad: ControlEvent<Void> {
    let source = self.methodInvoked(#selector(Base.viewDidLoad)).map { _ in }
    return ControlEvent(events: source)
  }

  public var viewWillAppear: ControlEvent<Bool> {
    let source = self.methodInvoked(#selector(Base.viewWillAppear)).map {
      $0.first as? Bool ?? false
    }
    return ControlEvent(events: source)
  }
  public var viewDidAppear: ControlEvent<Bool> {
    let source = self.methodInvoked(#selector(Base.viewDidAppear)).map {
      $0.first as? Bool ?? false
    }
    return ControlEvent(events: source)
  }

  public var viewWillDisappear: ControlEvent<Bool> {
    let source = self.methodInvoked(#selector(Base.viewWillDisappear)).map {
      $0.first as? Bool ?? false
    }
    return ControlEvent(events: source)
  }
  public var viewDidDisappear: ControlEvent<Bool> {
    let source = self.methodInvoked(#selector(Base.viewDidDisappear)).map {
      $0.first as? Bool ?? false
    }
    return ControlEvent(events: source)
  }
}
