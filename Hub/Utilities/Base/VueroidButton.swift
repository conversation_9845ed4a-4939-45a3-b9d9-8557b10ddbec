//
//  VueroidButton.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/21/24.
//

import RxCocoa
import RxSwift
import UIKit

class VueroidButton: UIButton {
  var isDismissTapActive = true
}

extension Reactive where Base: VueroidButton {
  var tapWithIsDismissTapActive: Observable<Bool> {
    return self.tap.map { [weak base] _ in
      return base?.isDismissTapActive ?? true
    }
  }
}
