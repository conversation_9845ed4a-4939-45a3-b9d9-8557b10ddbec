// MARK: - Localizable enum

public typealias L = Localizable

public enum Localizable {

  /// VueroidD21
  static let app_name = "app_name"

  /// VUEROID
  static let vueroid_txt = "vueroid_txt"

  /// DashCam List
  static let dashcamlist_txt = "dashcamlist_txt"

  /// DashCam
  static let dashcam_txt = "dashcam_txt"

  /// 대의 DashCame
  static let dashcam_cnt_txt = "dashcam_cnt_txt"

  /// ONLINE
  static let dashcam_online = "dashcam_online"

  /// OFFLINE
  static let dashcam_offline = "dashcam_offline"

  /// DashCame 모델명
  static let dashcam_desc_title1 = "dashcam_desc_title1"

  /// DashCame Private Code
  static let dashcam_desc_title2 = "dashcam_desc_title2"

  /// 가입정보
  static let dashcam_desc_title3 = "dashcam_desc_title3"

  /// Promotion Free
  static let dashcam_desc_title4 = "dashcam_desc_title4"

  /// 유료전환예정일 :
  static let dashcam_desc_title5 = "dashcam_desc_title5"

  /// 가입정보
  static let dashcam_desc_title6 = "dashcam_desc_title6"

  /// Cloud Storage
  static let dashcam_desc_title7 = "dashcam_desc_title7"

  /// Event Auto Upload
  static let dashcam_desc_title8 = "dashcam_desc_title8"

  /// Live View
  static let dashcam_desc_title9 = "dashcam_desc_title9"

  /// VOD 원격 재생
  static let dashcam_desc_title10 = "dashcam_desc_title10"

  /// VOD Upload 지원
  static let dashcam_desc_title11 = "dashcam_desc_title11"

  /// 180일간 무료 이용 가능
  static let dashcam_desc_title12 = "dashcam_desc_title12"

  /// 1일 최대 1분 재생/3회 제공
  static let dashcam_desc_title13 = "dashcam_desc_title13"

  /// 월 최대 100개
  static let dashcam_desc_title14 = "dashcam_desc_title14"

  /// 7익간/최대 150개
  static let dashcam_desc_title15 = "dashcam_desc_title15"

  /// 2023.07.15 (잔여일: 123/1380일)
  static let dashcam_desc_title16 = "dashcam_desc_title16"

  /// 90일/100개
  static let dashcam_desc_title17 = "dashcam_desc_title17"

  /// (단말기 1대만 등록 시)
  static let dashcam_desc_title18 = "dashcam_desc_title18"

  /// 유료 요금제 가입 필요
  static let dashcam_list_checkpayplan_popuptitle = "dashcam_list_checkpayplan_popuptitle"

  /// 무료 요금제에서는\nDashCame 1대만 등록 가능합니다.
  static let dashcam_list_checkpayplan_popupcontent = "dashcam_list_checkpayplan_popupcontent"

  /// 요금제 변경
  static let dashcam_list_checkpayplan_popupok = "dashcam_list_checkpayplan_popupok"

  /// 취소
  static let dashcam_list_checkpayplan_popupcancel = "dashcam_list_checkpayplan_popupcancel"

  /// Open navigation drawer
  static let navigation_drawer_open = "navigation_drawer_open"

  /// Close navigation drawer
  static let navigation_drawer_close = "navigation_drawer_close"

  /// NCN Developer
  static let nav_header_title = "nav_header_title"

  /// <EMAIL>
  static let nav_header_subtitle = "nav_header_subtitle"

  /// Navigation header
  static let nav_header_desc = "nav_header_desc"

  /// Settings
  static let action_settings = "action_settings"

  /// Registration
  static let dashcam_registration = "dashcam_registration"

  /// 등록방법 선택
  static let dashcam_registration_title = "dashcam_registration_title"

  /// 박스 내부 혹은 단말의 Serial Number,\nPrivate Code를 입력하세요.\n카메라가 있으시면 QR코드 촬영으로 자동 입력됩니다.
  static let dashcam_registration_body = "dashcam_registration_body"

  /// 등록절차
  static let dashcam_registration_sub_title = "dashcam_registration_sub_title"

  /// QR code
  static let dashcam_registration_sub_qr = "dashcam_registration_sub_qr"

  /// Form
  static let dashcam_registration_sub_form = "dashcam_registration_sub_form"

  /// 01
  static let dashcam_registration_step1 = "dashcam_registration_step1"

  /// 02
  static let dashcam_registration_step2 = "dashcam_registration_step2"

  /// 02
  static let dashcam_registration_step3 = "dashcam_registration_step3"

  /// 코드 입력
  static let dashcam_registration_step1_text = "dashcam_registration_step1_text"

  /// 위치정보 수집 동의
  static let dashcam_registration_step2_text = "dashcam_registration_step2_text"

  /// 등록 완료
  static let dashcam_registration_step3_text = "dashcam_registration_step3_text"

  /// Home
  static let menu_home = "menu_home"

  /// LiveViewer
  static let menu_live_viewer = "menu_live_viewer"

  /// FileViewer
  static let menu_file_viewer = "menu_file_viewer"

  /// FileVideo
  static let menu_file_video = "menu_file_video"

  /// History
  static let menu_history = "menu_history"

  /// Setting
  static let menu_setting = "menu_setting"

  /// Dash cam List
  static let menu_dashcamlist = "menu_dashcamlist"

  /// Profile
  static let menu_profile = "menu_profile"

  /// Pay Plan
  static let menu_payplan = "menu_payplan"

  /// Model DEBUG
  static let menu_modeldebug = "menu_modeldebug"

  /// Customer Service
  static let menu_customerservice = "menu_customerservice"

  /// SafetyDB
  static let menu_safetydb = "menu_safetydb"

  /// NearDevices
  static let menu_neardevices = "menu_neardevices"

  /// 드라이브 하기 좋은 쾌적한 날씨입니다.
  static let home_weather_type1 = "home_weather_type1"

  /// 비가 예상됩니다. 안전 운행하세요.
  static let home_weather_type2 = "home_weather_type2"

  /// 폭우가 예상됩니다. 시야확보에 유의하세요.
  static let home_weather_type3 = "home_weather_type3"

  /// 눈이 예상됩니다. 감속 운전하세요.
  static let home_weather_type4 = "home_weather_type4"

  /// 구름이 많은 날씨입니다. 안전 운행하세요.
  static let home_weather_type5 = "home_weather_type5"

  /// 드라이브 하기 좋은 대체적으로 맑은 날씨입니다.
  static let home_weather_type6 = "home_weather_type6"

  /// 일별
  static let history_tab_day = "history_tab_day"

  /// 주간
  static let history_tab_week = "history_tab_week"

  /// 월별
  static let history_tab_month = "history_tab_month"

  /// 연간
  static let history_tab_year = "history_tab_year"

  ///  회
  static let history_bottom_cnt = "history_bottom_cnt"

  ///  분
  static let history_bottom_time = "history_bottom_time"

  /// Right Drawer
  static let action_right_drawer = "action_right_drawer"

  /// Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Velit egestas dui id ornare arcu odio. Integer enim neque volutpat ac tincidunt vitae semper quis lectus.
  static let lorem_ipsum = "lorem_ipsum"

  /// Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore.
  static let lorem_ipsum_2 = "lorem_ipsum_2"

  /// VUEROID 12345
  static let model_name = "model_name"

  /// Wi-Fi OFF
  static let wi_fi_off = "wi_fi_off"

  /// Trace off
  static let trace_off = "trace_off"

  /// Bluetooth is not supported on this device.
  static let bt_not_supported = "bt_not_supported"

  /// Bluetooth Advertisements are not supported on this device.
  static let bt_ads_not_supported = "bt_ads_not_supported"

  /// User declined to enable Bluetooth.
  static let bt_not_enabled = "bt_not_enabled"

  /// User declined to permit ACCESS_COARSE_LOCATION.
  static let bt_not_permit_coarse = "bt_not_permit_coarse"

  /// 앱 설정에서 블루투스 필수 권한을 허용해 주세요.
  static let bt_not_permit_ble = "bt_not_permit_ble"

  /// Notification
  static let dialog_info_title = "dialog_info_title"

  /// Please allow the permissions of App.\nApp closing.
  static let alert_msg_permission_denied_text = "alert_msg_permission_denied_text"

  /// 확인
  static let ok = "ok"

  /// 취소
  static let cancel = "cancel"

  /// You can allow App permissions in your mobile Settings > Apps > Vueroid App.
  static let alert_msg_permission_text = "alert_msg_permission_text"

  /// Video
  static let viewer_select_video = "viewer_select_video"

  /// Photo
  static let viewer_select_photo = "viewer_select_photo"

  /// Bookmark
  static let viewer_select_bookmark = "viewer_select_bookmark"

  /// Selected
  static let selected_string = "selected_string"

  /// Connect Wi-Fi
  static let select_menu_description01 = "select_menu_description01"

  /// Connect Cloud
  static let select_menu_description02 = "select_menu_description02"

  /// Local Files
  static let select_menu_description03 = "select_menu_description03"

  /// Drive History
  static let select_menu_description04 = "select_menu_description04"

  /// History
  static let history_files_text = "history_files_text"

  /// Download Files
  static let download_files_text = "download_files_text"

  /// Cloud
  static let cloud_text = "cloud_text"

  /// WIFI
  static let wifi_text = "wifi_text"

  /// Wi-Fi
  static let notification_wifi_text = "notification_wifi_text"

  /// 블루투스 활성화 안내
  static let notification_bluetooth_text = "notification_bluetooth_text"

  /// 안내
  static let notification_text = "notification_text"

  /// 블루투스 기능이 꺼져 있습니다.\n\n블루투스가 꺼진 경우\n수동접속을 해야 합니다.\n\n블루투스를 활성화 하시겠습니까?\n\n확인을 누르시면 블루투스 설정으로 이동합니다.
  static let notification_ble_description = "notification_ble_description"

  /// Wi-Fi 기능이 꺼져 있습니다.\n활성화 하시겠습니까?\n\n확인을 누르시면 Wi-Fi설정으로 이동합니다.
  static let notification_wifi_description = "notification_wifi_description"

  /// 위치 기능이 꺼져 있습니다.\n활성화 하시겠습니까?\n\n확인을 누르시면 위치 설정으로 이동합니다.
  static let notification_location_description = "notification_location_description"

  /// 새로운 업데이트
  static let notification_update_title = "notification_update_title"

  /// 새로운 업데이트가 있습니다.\n업데이트 하시겠습니까?
  static let notification_update_contents = "notification_update_contents"

  /// 앱 업데이트
  static let notification_app_update_title = "notification_app_update_title"

  /// 최신버전으로 업데이트 하시겠습니까?
  static let notification_app_update_contents = "notification_app_update_contents"

  /// 펌웨어다운로드
  static let notification_firmware_download_title = "notification_firmware_download_title"

  /// Vueroid D21 FHD LTE 펌웨어 V0.12/n다운로드 진행을 하시겠습니까?/n/n현재
  static let notification_firmware_download_contents = "notification_firmware_download_contents"

  /// "Please turn on Bluetooth!!"
  static let notification_ble_turn_on = "notification_ble_turn_on"

  /// "Please turn on Wi-Fi!!"
  static let notification_wifi_turn_on = "notification_wifi_turn_on"

  /// LIVE 영상을 불러오는데 실패하였습니다.\nWi-Fi를 재연결 혹은 앱을 다시 실행하여 시도해 주세요.
  static let notification_wifi_failed_to_load_live_video =
    "notification_wifi_failed_to_load_live_video"

  /// ● 주행중
  static let just_drive = "just_drive"

  /// ● 전원꺼짐
  static let just_poweroff = "just_poweroff"

  /// ● 주차중
  static let just_parking = "just_parking"

  /// 방금 전
  static let just_before = "just_before"

  /// 분 전
  static let minutes_ago = "minutes_ago"

  /// 시간 전
  static let hours_ago = "hours_ago"

  /// 일 전
  static let days_ago = "days_ago"

  /// 달 전
  static let months_ago = "months_ago"

  /// 년 전
  static let years_ago = "years_ago"

  /// 검색된 리스트
  static let searched_list = "searched_list"

  /// 연결을 찾고 있습니다.
  static let searched_txt = "searched_txt"

  /// 최근 접속 리스트
  static let recent_connected_list = "recent_connected_list"

  /// 등록된 DashCame
  static let registered_black_box = "registered_black_box"

  /// Characteristic value received: %1$d
  static let characteristic_value_received = "characteristic_value_received"

  /// BLE Connected
  static let ble_connected = "ble_connected"

  /// BLE Disconnected
  static let ble_disconnected = "ble_disconnected"

  /// Unknown error.
  static let error_unknown = "error_unknown"

  /// Nickname 를 입력해 주세요.
  static let direct_connection_edit_nickname = "direct_connection_edit_nickname"

  /// SSID 를 입력해 주세요.
  static let direct_connection_edit_ssid = "direct_connection_edit_ssid"

  /// 패스워드를 입력해주세요.
  static let direct_connection_edit_password = "direct_connection_edit_password"

  /// SSID
  static let ssid_txt = "ssid_txt"

  /// PASSWORD
  static let password_txt = "password_txt"

  /// 수동 접속
  static let direct_connection_txt = "direct_connection_txt"

  /// 접속
  static let connection_txt = "connection_txt"

  /// Dashcam\nSet Button Press
  static let notification_ble_connection_dash_cam_set_txt =
    "notification_ble_connection_dash_cam_set_txt"

  /// DashCame를 인식할 수 있도록\nDashCame의 Set Button을 눌러주세요.\n최초 등록 시 15초 이상 소요될 수 있습니다.
  static let notification_ble_connection_dash_cam_set_btn =
    "notification_ble_connection_dash_cam_set_btn"

  /// Sort by
  static let sort_by = "sort_by"

  /// Name
  static let name = "name"

  /// Date
  static let date = "date"

  /// Size
  static let size = "size"

  /// Slow
  static let slow = "slow"

  /// Medium
  static let medium = "medium"

  /// Fast
  static let fast = "fast"

  /// Start slides
  static let showcase_longclick_dialog_title = "showcase_longclick_dialog_title"

  /// No images found
  static let toast_no_images = "toast_no_images"

  /// No app found to open this file
  static let toast_no_app_match = "toast_no_app_match"

  /// USB OTG
  static let usb_otg_fragment_label = "usb_otg_fragment_label"

  /// USB OTG Explorer
  static let usb_otg_explorer_fragment_label = "usb_otg_explorer_fragment_label"

  /// 접속 이력 있는 기기가 조회되지 않습니다.
  static let list_no_data_txt = "list_no_data_txt"

  /// 권한설정
  static let permission_service_time_txt = "permission_service_time_txt"

  /// 권한 허용 후 서비스를 이용할 수 있습니다.
  static let permission_service_guide_txt = "permission_service_guide_txt"

  /// Vueroid DashCam 펌웨어:%1$s\n 다운로드 진행을 하시겠습니까?\n\n 현재 펌웨어 버전:%2$s
  static let dialog_firmware_msg01 = "dialog_firmware_msg01"

  /// 다운로드
  static let download = "download"

  /// 다운로드중입니다.
  static let dialog_save_ing = "dialog_save_ing"

  /// Downloading onto Phone...
  static let dialog_save_ing_msg = "dialog_save_ing_msg"

  /// 펌웨어다운로드
  static let dialog_firmware_title = "dialog_firmware_title"

  /// Voice Pack Download:%1$s\nLatest Voice Pack Version?\n\nCurrent Voice Pack Version:%2$s
  static let dialog_voice_pack_msg01 = "dialog_voice_pack_msg01"

  /// Voice Pack Download
  static let dialog_voice_pack_title = "dialog_voice_pack_title"

  /// SafetyCamera DB Download:%1$s\n Latest SafetyCamera DB Version?\n\n Current SafetyCamera DB Version:%2$s
  static let dialog_safety_msg01 = "dialog_safety_msg01"

  /// 안전운전 DB 다운로드
  static let dialog_safetycam_title = "dialog_safetycam_title"

  /// 파일이 삭제되었습니다.\n다시 다운로드하시겠습니까?
  static let dialog_firmware_delete_msg = "dialog_firmware_delete_msg"

  /// Wi-Fi 연결이 끊어졌거나, 일시적인 오류가 발생했습니다.\n잠시 후 다시 시도해주세요.
  static let error_toast = "error_toast"

  /// 다운로드가 완료되었습니다.
  static let dialog_save_success = "dialog_save_success"

  /// Phone의 저장 공간이 부족합니다.\n다운로드를 실패하였습니다.
  static let video_size_error = "video_size_error"

  /// 3G,4G로 다운로드시\n데이터요금이 발생할 수 있습니다.
  static let dialog_firmware_msg02 = "dialog_firmware_msg02"

  /// (필수) 카메라
  static let permission_txt_camera = "permission_txt_camera"

  /// VUEROID APP에 DashCam 등록 시 QR코드 스캔과 프로필 촬영 시 사용됩니다.
  static let permission_txt_camera_describe = "permission_txt_camera_describe"

  /// (필수) 위치
  static let permission_txt_location = "permission_txt_location"

  /// 지도에서 현재 위치를 확인하기 위해 사용됩니다.
  static let permission_txt_location_describe = "permission_txt_location_describe"

  /// 저장공간
  static let permission_txt_storage = "permission_txt_storage"

  /// 포토
  static let permission_txt_photo = "permission_txt_photo"
  
  /// 녹화영상을 폰에 저장하고 녹화 화면 캡춰사진 및 파일을 첨부하고 저장하기 위해 사용됩니다.
  static let permission_txt_storage_describe = "permission_txt_storage_describe"

  /// 오디오,음악
  static let permission_txt_media = "permission_txt_media"

  /// 라이브 재생 및 보이스 콜 사용 시 양방향 음성 통신을 위해 사용됩니다.
  static let permission_txt_media_describe = "permission_txt_media_describe"

  /// 블루투스
  static let permission_txt_bluetooth = "permission_txt_bluetooth"

  /// %@ 디바이스의 Bluetooth 권한에 접근하시겠습니까?
  static let bluetooth_device_access = "bluetooth_device_access"

  /// Local Network
  static let permission_txt_localnetwork = "permission_txt_localnetwork"

  /// HUB app이 DashCam 디바이스에 연결하기 위해 사용됩니다
  static let permission_localnetwork_describe = "permission_localnetwork_describe"

  /// VUEROID APP에서 DashCam 제어와 블루투스을 On/Off 하는데 사용됩니다.
  static let permission_txt_bluetooth_describe = "permission_txt_bluetooth_describe"

  /// 알림
  static let permission_txt_notification = "permission_txt_notification"

  /// 이벤트 및 DashCam 으로 부터 정보를 받아보실 수 있습니다.
  static let permission_txt_notification_describe = "permission_txt_notification_describe"

  /// 서비스이용알림
  static let permission_recommend_notification = "permission_recommend_notification"

  /// 필수 권한을 허용해야 서비스 정상 이용이 가능합니다.\n권한 요청 시 반드시 허용해 주세요.
  static let permission_recommend_notification_describe =
    "permission_recommend_notification_describe"

  /// Breathing\nLife Into Dash Cam Video Footage
  static let intro_bottom_txt = "intro_bottom_txt"

  /// WiFi 연결에 실패 했습니다.
  static let notification_wifi_time_out_txt = "notification_wifi_time_out_txt"

  /// BLE 연결에 실패 했습니다.
  static let notification_ble_time_out_txt = "notification_ble_time_out_txt"

  /// DashCam과 연결이 끊어졌습니다.
  static let notification_disconnected_txt = "notification_disconnected_txt"

  /// DashCam이 꺼져 있습니다.
  static let notification_device_state_off_txt = "notification_device_state_off_txt"

  /// 설정으로 이동
  static let go_to_settings_txt = "go_to_settings_txt"

  /// WIFI AP를 입력해주세요.
  static let dialog_wifi_ap_error_msg01 = "dialog_wifi_ap_error_msg01"

  /// 10자까지만 입력가능합니다.
  static let dialog_wifi_ap_error_msg02 = "dialog_wifi_ap_error_msg02"

  /// 입력할 수 없는 문자입니다.
  static let dialog_wifi_ap_error_msg03 = "dialog_wifi_ap_error_msg03"

  /// STA Nickname 또는 SSID를 입력해주세요.
  static let dialog_sta_error_msg01 = "dialog_sta_error_msg01"

  /// 비밀번호를 입력해주세요.
  static let dialog_password_error_msg01 = "dialog_password_error_msg01"

  /// 비밀번호는 8~16 영문 혹은 숫자입니다.
  static let dialog_password_error_msg02 = "dialog_password_error_msg02"

  /// Dashcam 설정
  static let setting_menu_wifi_title = "setting_menu_wifi_title"

  /// Cloud 설정
  static let setting_menu_cloud_title = "setting_menu_cloud_title"

  /// ONLINE
  static let slide_menu_bb_online = "slide_menu_bb_online"

  /// ● 주행중
  static let slide_menu_bb_drive = "slide_menu_bb_drive"

  /// $d V
  static let slide_menu_bb_voltage = "slide_menu_bb_voltage"

  /// S/N
  static let slide_menu_bb_sn = "slide_menu_bb_sn"

  /// %d%%
  static let percent = "percent"

  /// [{ \"relation\": [\"delegate_permission/common.handle_all_urls\"], \"target\": { \"namespace\": \"web\", \"site\": \"https://dev.vueroid-cloud.com:3000\"} }]
  static let asset_statements = "asset_statements"

  /// Broadcast_Internet_Status
  static let keySendInternetStatus = "keySendInternetStatus"

  /// No internet connection
  static let string_no_internet_connection = "string_no_internet_connection"

  /// MM/dd/yyyy
  static let sdf_local_date = "sdf_local_date"

  /// 현재 주행
  static let liveview_menu_title = "liveview_menu_title"

  /// History
  static let dialog_history_title = "dialog_history_title"

  /// Synchronizing drive history.
  static let history_loading_msg = "history_loading_msg"

  /// History
  static let history_title = "history_title"

  /// Day
  static let history_day = "history_day"

  /// Week
  static let history_week = "history_week"

  /// Months
  static let history_months = "history_months"

  /// Year
  static let history_year = "history_year"

  /// Today
  static let history_today = "history_today"

  /// Travel Log
  static let history_travel = "history_travel"

  /// Mileage :
  static let history_content_title01 = "history_content_title01"

  /// %s
  static let history_content_msg01 = "history_content_msg01"

  /// Trip Time :
  static let history_content_title02 = "history_content_title02"

  /// %s
  static let history_content_msg02_01 = "history_content_msg02_01"

  /// %s
  static let history_content_msg02_02 = "history_content_msg02_02"

  /// Event :
  static let history_content_title03 = "history_content_title03"

  /// %s
  static let history_content_msg03_01 = "history_content_msg03_01"

  /// %s
  static let history_content_msg03_02 = "history_content_msg03_02"

  /// %s
  static let history_content_msg03_03 = "history_content_msg03_03"

  /// Speed :
  static let history_content_title04 = "history_content_title04"

  /// %s
  static let history_content_msg04_01 = "history_content_msg04_01"

  /// %s
  static let history_content_msg04_02 = "history_content_msg04_02"

  /// Distance
  static let history_mile_value = "history_mile_value"

  /// Unit(Km)
  static let history_mile_unit01 = "history_mile_unit01"

  /// Unit(Miles)
  static let history_mile_unit02 = "history_mile_unit02"

  /// Driving Recording Time
  static let history_trip_time_value01 = "history_trip_time_value01"

  /// Parking Recording Time
  static let history_trip_time_value02 = "history_trip_time_value02"

  /// Unit(min)
  static let history_trip_time_unit01 = "history_trip_time_unit01"

  /// Unit(hour)
  static let history_trip_time_unit02 = "history_trip_time_unit02"

  /// Driving Impact
  static let history_event_value01 = "history_event_value01"

  /// Parking Motion
  static let history_event_value02 = "history_event_value02"

  /// Parking Impact
  static let history_event_value03 = "history_event_value03"

  /// Max Speed
  static let history_speed_value01 = "history_speed_value01"

  /// Average Speed
  static let history_speed_value02 = "history_speed_value02"

  /// Unit(Km/h)
  static let history_speed_unit01 = "history_speed_unit01"

  /// Unit(Mi/h)
  static let history_speed_unit02 = "history_speed_unit02"

  /// No data exists
  static let history_chart_empty_msg = "history_chart_empty_msg"

  /// No list exists
  static let history_empty_msg = "history_empty_msg"

  /// Mi/h
  static let history_viewer_miles = "history_viewer_miles"

  /// Km/h
  static let history_viewer_kilometers = "history_viewer_kilometers"

  /// Miles
  static let history_viewer_mileage_miles = "history_viewer_mileage_miles"

  /// Km
  static let history_viewer_mileage_kilometers = "history_viewer_mileage_kilometers"

  /// Hour
  static let history_viewer_hour = "history_viewer_hour"

  /// Min
  static let history_viewer_min = "history_viewer_min"

  /// Travel log
  static let travel_title = "travel_title"

  /// Select
  static let travel_select = "travel_select"

  /// Unselect
  static let travel_deselect = "travel_deselect"

  /// All Select
  static let travel_all_select = "travel_all_select"

  /// All Unselect
  static let travel_all_unselect = "travel_all_unselect"

  /// Delete
  static let travel_delete = "travel_delete"

  /// Drive
  static let travel_item_type01 = "travel_item_type01"

  /// Parked
  static let travel_item_type02 = "travel_item_type02"

  /// Travel log-
  static let travel_detail_title = "travel_detail_title"

  /// Total Mileage
  static let travel_detail_distance = "travel_detail_distance"

  ///  mile
  static let travel_detail_distance_type = "travel_detail_distance_type"

  /// Maximum speed
  static let travel_detail_speed_max = "travel_detail_speed_max"

  /// Average Speed
  static let travel_detail_speed_avg = "travel_detail_speed_avg"

  ///  mi/h
  static let travel_detail_speed_type = "travel_detail_speed_type"

  /// Impact event
  static let travel_detail_event = "travel_detail_event"

  /// Motion event
  static let travel_detail_motion = "travel_detail_motion"

  /// Times
  static let travel_detail_event_type = "travel_detail_event_type"

  /// Depart
  static let travel_detail_start = "travel_detail_start"

  /// Arrive
  static let travel_detail_end = "travel_detail_end"

  /// Parking time
  static let travel_detail_park_time = "travel_detail_park_time"

  /// Hour
  static let travel_detail_park_time_type01 = "travel_detail_park_time_type01"

  /// Minute
  static let travel_detail_park_time_type02 = "travel_detail_park_time_type02"

  /// Parking position
  static let travel_detail_park_position = "travel_detail_park_position"

  /// No data exists
  static let travel_detail_error_msg = "travel_detail_error_msg"

  /// To view map, disconnect from Device Wi-Fi and connect to cellular network
  static let travel_detail_map_error_msg = "travel_detail_map_error_msg"

  /// Please select data to delete
  static let travel_delete_error = "travel_delete_error"

  /// Would you like to delete the selected data?
  static let travel_delete_msg = "travel_delete_msg"

  /// All the data will be deleted.\nTouch Okay to delete all.
  static let travel_all_delete_msg = "travel_all_delete_msg"

  /// 인터넷을 연결해 주세요
  static let Please_connect_to_the_internet = "Please_connect_to_the_internet"

  /// Driver
  static let file_viewer_driver = "file_viewer_driver"

  /// Event
  static let file_viewer_event = "file_viewer_event"

  /// Park
  static let file_viewer_park = "file_viewer_park"

  /// Manual
  static let file_viewer_manual = "file_viewer_manual"

  /// VOD
  static let file_viewer_vod = "file_viewer_vod"

  /// Snap Shot
  static let file_viewer_snapshot = "file_viewer_snapshot"

  /// Recommended area
  static let recommended_area_txt = "recommended_area_txt"

  /// Lane Center
  static let adas_vehicle_center = "adas_vehicle_center"

  /// Horizon
  static let adas_horizon = "adas_horizon"

  /// 전방
  static let current_channel_front = "current_channel_front"

  /// 후방
  static let current_channel_rear = "current_channel_rear"

  /// 후방2
  static let current_channel_rear2 = "current_channel_rear2"

  /// First Fragment
  static let first_fragment_label = "first_fragment_label"

  /// Second Fragment
  static let second_fragment_label = "second_fragment_label"

  /// Next
  static let next = "next"

  /// Previous
  static let previous = "previous"

  /// Hello first fragment
  static let hello_first_fragment = "hello_first_fragment"

  /// Hello second fragment. Arg: %1$s
  static let hello_second_fragment = "hello_second_fragment"

  /// Connected
  static let face_chat_matching_success = "face_chat_matching_success"

  /// The connection to the server has been lost.
  static let voice_chat_disconnect_from_server = "voice_chat_disconnect_from_server"

  /// Demo App for WebRTC
  static let title = "title"

  /// Male
  static let gender_male = "gender_male"

  /// Female
  static let gender_female = "gender_female"

  /// Start call
  static let start_call = "start_call"

  /// Earphones are connected
  static let earpiece_connected = "earpiece_connected"

  /// Earphones are disconnected
  static let earpiece_disconnected = "earpiece_disconnected"

  /// Stop Call
  static let back_confirm_title = "back_confirm_title"

  /// Are you sure to stop call? All connection will be stopped!
  static let back_confirm_message = "back_confirm_message"

  /// 확인
  static let confirm = "confirm"

  /// Deny
  static let deny = "deny"

  /// DashCam이 연결되어있지 않습니다.\nHome 화면으로 이동합니다.
  static let blackbox_disconnect = "blackbox_disconnect"

  /// 리스트 내역이 없습니다.
  static let empty_msg = "empty_msg"

  /// \'종료\'버튼을 누르시면 안전하게 종료됩니다.
  static let app_finish = "app_finish"

  /// FW download를 위해 WIFI 연결이 끊어집니다.
  static let wifi_will_be_disconnected = "wifi_will_be_disconnected"

  /// Loading..
  static let progress_title = "progress_title"

  /// 잠시만 기다려주세요.
  static let progress_txt = "progress_txt"

  /// 삭제
  static let dialog_delete_title = "dialog_delete_title"

  /// 다운로드
  static let dialog_save_title = "dialog_save_title"

  /// 공유
  static let dialog_share_title = "dialog_share_title"

  /// 설정
  static let dialog_setting_title = "dialog_setting_title"

  /// 삭제중입니다.
  static let dialog_delete_ing = "dialog_delete_ing"

  /// 삭제하실 파일을 선택해주세요.
  static let dialog_delete_error = "dialog_delete_error"

  /// 삭제하시겠습니까?
  static let dialog_delete = "dialog_delete"

  /// 선택한 파일을 삭제하시겠습니까?
  static let dialog_delete_msg = "dialog_delete_msg"

  /// 선택한 %s 파일을 삭제하시겠습니까?
  static let dialog_delete_msgs = "dialog_delete_msgs"

  /// 삭제가 완료되었습니다.
  static let dialog_delete_success = "dialog_delete_success"

  /// 변경된 설정을 저장하시겠습니까?
  static let dialog_setting_msg = "dialog_setting_msg"

  /// 이전 날짜로 변경시 일부 주행정보가 삭제됩니다.\n변경하시겠습니까?
  static let dialog_time_set_msg = "dialog_time_set_msg"

  /// WIFI AP 이름을 변경하면 WIFI 연결이\n해제되며 DashCam을 재부팅합니다\n변경하시겠습니까?
  static let dialog_ap_name_msg = "dialog_ap_name_msg"

  /// 모델명 : %1$s\n펌웨어 버전 : %2$s\n안전운전 DB Version : %3$s
  static let dialog_info_msg = "dialog_info_msg"

  /// 모델명 : %1$s\n펌웨어 버전 : Ver.%2$s
  static let dialog_info_msg2 = "dialog_info_msg2"

  /// 설정 변경이 완료되었습니다.
  static let dialog_setting_success = "dialog_setting_success"

  /// 변경된 비밀번호는 새로운 WIFI 연결부터 적용됩니다.
  static let dialog_setting_password_success = "dialog_setting_password_success"

  /// 현재시간
  static let dialog_time_current = "dialog_time_current"

  /// 비밀번호 변경
  static let dialog_password_title = "dialog_password_title"

  /// 비밀번호 확인
  static let dialog_password_title2 = "dialog_password_title2"

  /// HotSpot Setting
  static let dialog_hotspot_title = "dialog_hotspot_title"

  /// 변경하실 비밀번호를 입력하세요.
  static let dialog_password_content = "dialog_password_content"

  /// 비밀번호 입력(8~16 영문 혹은 숫자)
  static let dialog_password_content01 = "dialog_password_content01"

  /// 비밀번호 확인(8~16 영문 혹은 숫자)
  static let dialog_password_content02 = "dialog_password_content02"

  /// 비밀번호가 일치하지않습니다.\n다시 시도해주세요.
  static let dialog_password_error_msg03 = "dialog_password_error_msg03"

  /// HotSpot Nickname
  static let dialog_hotspot_content01 = "dialog_hotspot_content01"

  /// SSID
  static let dialog_hotspot_content02 = "dialog_hotspot_content02"

  /// Password
  static let dialog_hotspot_content03 = "dialog_hotspot_content03"

  /// 비밀번호를 변경하면 WIFI 연결이\n해제되며 DashCam을 재부팅합니다\n변경하시겠습니까?
  static let dialog_password_msg = "dialog_password_msg"

  /// WIFI AP 변경
  static let dialog_wifi_ap_title = "dialog_wifi_ap_title"

  /// 변경하실 WIFI AP를 입력하세요.
  static let dialog_wifi_ap_content = "dialog_wifi_ap_content"

  /// WIFI AP 입력(10자리까지)
  static let dialog_wifi_ap_msg = "dialog_wifi_ap_msg"

  /// USIM 설정
  static let dialog_usim_setting_title = "dialog_usim_setting_title"

  /// APN 이름
  static let dialog_usim_apn_name = "dialog_usim_apn_name"

  /// 사용자 이름
  static let dialog_usim_user_name = "dialog_usim_user_name"

  /// 비밀번호
  static let dialog_usim_password = "dialog_usim_password"

  /// PIN Code
  static let dialog_usim_pincode = "dialog_usim_pincode"

  /// PUK Code
  static let dialog_usim_pukcode = "dialog_usim_pukcode"

  /// 다운로드하실 파일을 선택해주세요.
  static let dialog_save_error = "dialog_save_error"

  /// 선택한 파일을 다운로드하시겠습니까?
  static let dialog_save_msg = "dialog_save_msg"

  /// 비디오를 공유하시겠습니까?
  static let dialog_share_msg = "dialog_share_msg"

  /// 편집된 동영상이 저장되었습니다.
  static let dialog_download_msg = "dialog_download_msg"

  /// 확인하실 데이터를 선택해주세요.
  static let dialog_history_error_msg = "dialog_history_error_msg"

  /// Home 화면으로 이동합니다.
  static let dialog_black_box_status_msg = "dialog_black_box_status_msg"

  /// %s upgrade will take few minutes.\nPlease do not power off your dashcam until upgrade completes.
  static let dialog_firmware_update_msg = "dialog_firmware_update_msg"

  /// 업데이트 항목이 없습니다.
  static let dialog_firmware_error_msg02 = "dialog_firmware_error_msg02"

  /// Connect to Device
  static let wifi_info_title = "wifi_info_title"

  /// Wi-Fi설정에서 접속할 DashCam 연결후\n 이용해 주세요.\n확인을 누르시면 Wi-Fi설정으로 이동합니다.
  static let wifi_info_txt = "wifi_info_txt"

  /// Live View
  static let home_live_viewer = "home_live_viewer"

  /// Wi-Fi Connect
  static let home_wifi_connect = "home_wifi_connect"

  /// ON
  static let home_wifi_on = "home_wifi_on"

  /// OFF
  static let home_wifi_off = "home_wifi_off"

  /// Live View
  static let live_video_title = "live_video_title"

  /// LIVE 영상 재생중 오류가 발생하였습니다.
  static let live_video_error = "live_video_error"

  /// Vehicle Center
  static let LiveView_Vehicle_Center_txt = "LiveView_Vehicle_Center_txt"

  /// File Viewer
  static let file_viewer_title = "file_viewer_title"

  /// Drive
  static let file_viewer_drive = "file_viewer_drive"

  /// Parked
  static let file_viewer_parked = "file_viewer_parked"

  /// Phone
  static let file_viewer_phone = "file_viewer_phone"

  /// Select Items
  static let file_viewer_select_item = "file_viewer_select_item"

  /// %s Items Selected
  static let file_viewer_select_item_cnt = "file_viewer_select_item_cnt"

  /// Setting
  static let setting_title = "setting_title"

  /// 녹화
  static let setting_record = "setting_record"

  /// 이벤트
  static let setting_event = "setting_event"

  /// 소리
  static let setting_sound = "setting_sound"

  /// 시간
  static let setting_time = "setting_time"

  /// 메모리
  static let setting_memory = "setting_memory"

  /// 시스템
  static let setting_system = "setting_system"
  
  /// 카메라 설정
  static let camera_system = "camera_system"
  
  ///주차 녹화 설정
  static let parking_recording_system = "parking_recording_system"
  
  //이미지 회전 및 미러
  
  /// 전방 카메라
  static let rotate_mirror_front_camera_text = "rotate_mirror_front_camera_text"
  /// 후방 카메라
  static let rotate_mirror_rear_camera_text = "rotate_mirror_rear_camera_text"
  /// 실내 카메라
  static let rotate_mirror_interior_camera_text = "rotate_mirror_interior_camera_text"
  /// 초기화
  static let rotate_mirror_initialization_text = "rotate_mirror_initialization_text"
  /// Rotate
  static let rotate_text = "rotate_text"
  /// Mirror
  static let mirror_text = "mirror_text"
  
  /// 적용
  static let setting_apply = "setting_apply"

  /// 녹화화질
  static let record_video_bitrate = "record_video_bitrate"

  /// 방전차단전압
  static let record_voltage_limit = "record_voltage_limit"

  /// 방전차단시간
  static let record_park_rec_time = "record_park_rec_time"

  /// 자동주차
  static let record_park_mode = "record_park_mode"

  /// 주차녹화방식
  static let record_time_lapse = "record_time_lapse"

  /// 후방 영상 녹화
  static let record_rear_use = "record_rear_use"

  /// 저전력모드
  static let low_power_mode = "low_power_mode"

  /// 상시충격감도
  static let event_inf_sensor = "event_inf_sensor"

  /// 주차충격감도
  static let event_park_sensor = "event_park_sensor"

  /// 전방모션감도
  static let event_front_motion_sensor = "event_front_motion_sensor"

  /// 후방모션감도
  static let event_rear_motion_sensor = "event_rear_motion_sensor"

  /// 음성녹음
  static let sound_audio_rec = "sound_audio_rec"

  /// 스피커볼륨
  static let sound_speaker_volume = "sound_speaker_volume"

  /// 표준시간대
  static let time_region = "time_region"

  /// 시간설정
  static let time_set_time = "time_set_time"

  /// 메모리할당
  static let memory_assign = "memory_assign"

  /// 상시 25%
  static let memory_assign_prioty_driving = "memory_assign_prioty_driving"

  /// 상시 30% 주차 50%
  static let memory_assign_prioty_parking = "memory_assign_prioty_parking"

  /// 상시 30% 주차 20% 이벤트 30$
  static let memory_assign_prioty_event = "memory_assign_prioty_event"

  /// 상시 80%
  static let memory_assign_only_driving = "memory_assign_only_driving"

  /// SD카드 포맷
  static let memory_sdcard_format = "memory_sdcard_format"

  /// 언어
  static let system_language = "system_language"

  /// 시큐리티 LED
  static let system_secure_led = "system_secure_led"

  /// 전방 Security LED
  static let system_front_secure_led = "system_front_secure_led"

  /// 후방 Security LED
  static let system_rear_secure_led = "system_rear_secure_led"

  /// 속도단위
  static let system_speed_unit = "system_speed_unit"

  /// 속도표시
  static let system_speed_mode = "system_speed_mode"

  /// Wi-Fi 설정
  static let system_wifi_on_auto = "system_wifi_on_auto"

  /// Wi-Fi AP
  static let system_ap_name = "system_ap_name"

  /// 비밀번호 변경
  static let system_password = "system_password"

  /// F/W 업그레이드
  static let system_upgrade = "system_upgrade"

  /// 정보
  static let system_info = "system_info"

  /// 초기화
  static let system_reset = "system_reset"

  /// 공장초기화
  static let s1_setting_factoryreset = "s1_setting_factoryreset"

  /// 공장 초기화를 진행하시겠습니까?
  static let s1_setting_factoryreset_msg = "s1_setting_factoryreset_msg"

  /// 저전력 주차모드에서는 설정기능을 이용할수 없습니다.
  static let s1_disable_setting_in_parking = "s1_disable_setting_in_parking"
  
  /// 좌우반전
  static let video_reversed = "video_reversed"

  /// 원본
  static let video_original = "video_original"

  /// 전방
  static let video_front = "video_front"

  /// 후방
  static let video_rear = "video_rear"

  /// 선택
  static let video_select = "video_select"

  /// 다운로드
  static let video_download = "video_download"

  /// 삭제
  static let video_delete = "video_delete"

  /// Video Trim
  static let video_edit = "video_edit"

  /// Trim
  static let trim = "trim"

  /// 영상저장
  static let video_edit_phone_share_title = "video_edit_phone_share_title"

  /// 편집한 영상을 저장하시겠습니까?
  static let video_edit_phone_share_msg = "video_edit_phone_share_msg"

  /// Done
  static let video_done = "video_done"

  /// Share
  static let video_sns = "video_sns"

  /// 후방2
  static let video_rear2 = "video_rear2"

  /// 후방 영상이 없습니다.
  static let video_channels_error = "video_channels_error"

  /// Mileage
  static let history_search_type01 = "history_search_type01"

  /// Trip Time
  static let history_search_type02 = "history_search_type02"

  /// Event
  static let history_search_type03 = "history_search_type03"

  /// Speed
  static let history_search_type04 = "history_search_type04"

  /// 1월
  static let history_year01 = "history_year01"

  /// 2월
  static let history_year02 = "history_year02"

  /// 3월
  static let history_year03 = "history_year03"

  /// 4월
  static let history_year04 = "history_year04"

  /// 5월
  static let history_year05 = "history_year05"

  /// 6월
  static let history_year06 = "history_year06"

  /// 7월
  static let history_year07 = "history_year07"

  /// 8월
  static let history_year08 = "history_year08"

  /// 9월
  static let history_year09 = "history_year09"

  /// 10월
  static let history_year10 = "history_year10"

  /// 11월
  static let history_year11 = "history_year11"

  /// 12월
  static let history_year12 = "history_year12"

  /// 일
  static let history_week01 = "history_week01"

  /// 월
  static let history_week02 = "history_week02"

  /// 화
  static let history_week03 = "history_week03"

  /// 수
  static let history_week04 = "history_week04"

  /// 목
  static let history_week05 = "history_week05"

  /// 금
  static let history_week06 = "history_week06"

  /// 토
  static let history_week07 = "history_week07"

  /// LOWEST
  static let setting_detail_lowest_value = "setting_detail_lowest_value"

  /// LOW
  static let setting_detail_low_value = "setting_detail_low_value"

  /// MID
  static let setting_detail_mid_value = "setting_detail_mid_value"

  /// HIGH
  static let setting_detail_high_value = "setting_detail_high_value"

  /// HIGHEST
  static let setting_detail_highest_value = "setting_detail_highest_value"

  /// Off
  static let setting_detail_off_value = "setting_detail_off_value"

  /// On
  static let setting_detail_on_value = "setting_detail_on_value"

  /// 11V External Battery
  static let setting_detail_voltage_value01 = "setting_detail_voltage_value01"

  /// 11.7V/23.7V
  static let setting_detail_voltage_value02 = "setting_detail_voltage_value02"

  /// 11.9V/23.9V
  static let setting_detail_voltage_value03 = "setting_detail_voltage_value03"

  /// 12.0V/24.0V
  static let setting_detail_voltage_value04 = "setting_detail_voltage_value04"

  /// 12.1V/24.1V
  static let setting_detail_voltage_value05 = "setting_detail_voltage_value05"

  /// 12.3V/24.3V
  static let setting_detail_voltage_value06 = "setting_detail_voltage_value06"
  
  /// 12.5V/24.3V
  static let setting_detail_voltage_value07 = "setting_detail_voltage_value07"

  /// 12.7V/24.3V
  static let setting_detail_voltage_value08 = "setting_detail_voltage_value08"

  /// 12.9V/24.3V
  static let setting_detail_voltage_value09 = "setting_detail_voltage_value09"

  /// 13.1V/24.3V
  static let setting_detail_voltage_value10 = "setting_detail_voltage_value10"

  /// 13.3V/24.3V
  static let setting_detail_voltage_value11 = "setting_detail_voltage_value11"
  
  /// 13.5V (EV)
  static let setting_detail_voltage_value12 = "setting_detail_voltage_value12"
  
  /// 13.5V (EV)
  static let setting_detail_voltage_value13 = "setting_detail_voltage_value13"
  
  /// 13.5V (EV)
  static let setting_detail_voltage_value14 = "setting_detail_voltage_value14"

  /// 6Hours
  static let setting_park_rec_time_value01 = "setting_park_rec_time_value01"

  /// 12Hours
  static let setting_park_rec_time_value02 = "setting_park_rec_time_value02"

  /// 24Hours
  static let setting_park_rec_time_value03 = "setting_park_rec_time_value03"

  /// 48Hours
  static let setting_park_rec_time_value04 = "setting_park_rec_time_value04"

  /// 3Hours
  static let setting_park_rec_time_value05 = "setting_park_rec_time_value05"

  /// 모션녹화
  static let setting_time_lapse_value01 = "setting_time_lapse_value01"

  /// 타임랩스
  static let setting_time_lapse_value02 = "setting_time_lapse_value02"

  /// Park Recording FPS
  static let setting_parkfps_value00 = "setting_parkfps_value00"

  /// 30fps
  static let setting_parkfps_value01 = "setting_parkfps_value01"

  /// 15fps
  static let setting_parkfps_value02 = "setting_parkfps_value02"

  /// 5fps
  static let setting_parkfps_value03 = "setting_parkfps_value03"

  /// 일반녹화
  static let setting_drive_time_lapse_value01 = "setting_drive_time_lapse_value01"

  /// 타임랩스
  static let setting_drive_time_lapse_value02 = "setting_drive_time_lapse_value02"

  /// Always recording quality
  static let setting_drive_isp_mode_value00 = "setting_drive_isp_mode_value00"

  /// Normal
  static let setting_drive_isp_mode_value01 = "setting_drive_isp_mode_value01"

  /// HDR
  static let setting_drive_isp_mode_value02 = "setting_drive_isp_mode_value02"

  /// Ultra LP Capture
  static let setting_drive_isp_mode_value03 = "setting_drive_isp_mode_value03"

  /// Park recording quality
  static let setting_park_isp_mode_value00 = "setting_park_isp_mode_value00"

  /// Normal
  static let setting_park_isp_mode_value01 = "setting_park_isp_mode_value01"

  /// HDR
  static let setting_park_isp_mode_value02 = "setting_park_isp_mode_value02"

  /// Ultra Night Vision
  static let setting_park_isp_mode_value03 = "setting_park_isp_mode_value03"

  /// Event alert
  static let setting_audiorec_value03 = "setting_audiorec_value03"

  /// Event Only
  static let setting_audiorec_value01 = "setting_audiorec_value01"

  /// Event+Park
  static let setting_audiorec_value02 = "setting_audiorec_value02"

  /// 1
  static let setting_speaker_volume_value01 = "setting_speaker_volume_value01"

  /// 2
  static let setting_speaker_volume_value02 = "setting_speaker_volume_value02"

  /// 3
  static let setting_speaker_volume_value03 = "setting_speaker_volume_value03"

  /// 4
  static let setting_speaker_volume_value04 = "setting_speaker_volume_value04"

  /// 5
  static let setting_speaker_volume_value05 = "setting_speaker_volume_value05"

  /// 상시우선
  static let setting_memory_assign_value01 = "setting_memory_assign_value01"

  /// 주차우선
  static let setting_memory_assign_value02 = "setting_memory_assign_value02"

  /// 이벤트우선
  static let setting_memory_assign_value03 = "setting_memory_assign_value03"

  /// 상시전용
  static let setting_memory_assign_value04 = "setting_memory_assign_value04"

  /// Km/h
  static let setting_speed_uniot_value01 = "setting_speed_uniot_value01"

  /// Mi/h
  static let setting_speed_uniot_value02 = "setting_speed_uniot_value02"

  /// 한국어
  static let setting_language_value01 = "setting_language_value01"

  /// 영어
  static let setting_language_value02 = "setting_language_value02"

  /// 중국어
  static let setting_language_value03 = "setting_language_value03"

  /// 일본어
  static let setting_language_value04 = "setting_language_value04"

  /// 년
  static let year = "year"

  /// 월
  static let month = "month"

  /// 일
  static let day = "day"

  /// 시
  static let hour = "hour"

  /// 분
  static let minute = "minute"

  /// 초
  static let second = "second"

  /// Snapshot
  static let viewer_snapshot = "viewer_snapshot"

  ///  파일이 저장 되었습니다.
  static let viewer_snapshot_save = "viewer_snapshot_save"

  /// 북마크
  static let dialog_bookmark_ing = "dialog_bookmark_ing"

  /// Downloading onto SD card...
  static let dialog_bookmark_ing_msg = "dialog_bookmark_ing_msg"

  /// 북마크하실 파일을 선택해주세요.
  static let dialog_bookmark_error = "dialog_bookmark_error"

  /// 선택한 파일을 북마크하시겠습니까?
  static let dialog_bookmark_msg = "dialog_bookmark_msg"

  /// 선택한 %s 파일을 북마크하시겠습니까?
  static let dialog_bookmark_msgs = "dialog_bookmark_msgs"

  /// 북마크
  static let dialog_bookmark_title = "dialog_bookmark_title"

  /// 위치 정보 동의가 필요합니다.\nGPS 기능 사용을 설정해주세요.
  static let msg_location_check01 = "msg_location_check01"

  /// GPS 정보 수신이 불가능 하여 앱이 종료됩니다.
  static let msg_location_check02 = "msg_location_check02"

  /// 썸머타임
  static let daylight_saving_time = "daylight_saving_time"

  /// 전체삭제
  static let dialog_delete_all_title = "dialog_delete_all_title"

  /// 1. [ Vueroid_xxxx ] Wi-Fi에 연결해 주세요.
  static let info_wifi_msg_1 = "info_wifi_msg_1"

  /// 초기 비밀번호는 \'12345678\' 입니다.
  static let info_wifi_msg_2 = "info_wifi_msg_2"

  /// 2. GPS가 꺼져있거나 권한이 허용되지 않으면 이용에 제한이 생깁니다.
  static let info_wifi_msg_3 = "info_wifi_msg_3"

  /// 설정 ▶ App관리 ▶ Vueroid \n ▶ GPS 권한 허용
  static let info_wifi_msg_4 = "info_wifi_msg_4"

  /// 3. Wi-Fi 고급설정
  static let info_wifi_msg_5 = "info_wifi_msg_5"

  /// Wi-Fi ▶ 고급 ▶ 모바일 데이터 전환 끄기
  static let info_wifi_msg_6 = "info_wifi_msg_6"

  /// Full Screen
  static let live_fullscreen = "live_fullscreen"

  /// 모든 파일을 삭제하시겠습니까?
  static let dialog_delete_all_msg = "dialog_delete_all_msg"

  /// 터치하여 움직여서 ADAS 영상 화면 영역을 선택해 주세요.
  static let msg_adas_1 = "msg_adas_1"

  /// 수직/수평 중심선과 보조선을 Show/Hide 합니다.
  static let msg_adas_2 = "msg_adas_2"

  /// 수직 라인을 차선의 중심, 수평 라인을 먼 도로의 지평선(또는 앞차의 본네트 상단)에 맞춰주세요.
  static let msg_adas_3 = "msg_adas_3"

  /// 차선 인식 민감도를 설정 합니다. 좌/우측 숫자가 높을 수록 더 민감하게 인식합니다.
  static let msg_adas_4 = "msg_adas_4"

  /// 이 라인을 차랑 중앙에 맞춰주세요.
  static let msg_adas_5 = "msg_adas_5"

  /// 이 라인을 도로의 지평선에 맞춰주세요.
  static let msg_adas_6 = "msg_adas_6"

  /// 터치하여 움직여서 이 라인을 차량의 본넷에 맞춰주세요.
  static let msg_adas_7 = "msg_adas_7"

  /// 터치하여 움직여서 이 라인을 차량의 트렁크에 맞춰주세요.
  static let msg_adas_8 = "msg_adas_8"

  /// Confirm exit
  static let exit_app_title = "exit_app_title"

  /// Vueroid 앱을 종료하시겠습니까?\nWi-Fi 연결을 종료 하시거나\n기기와의 연결을 해제하여 주십시오.
  static let exit_app_desc = "exit_app_desc"

  /// 종료
  static let exit_app = "exit_app"

  /// Exit Wi-Fi Connection
  static let exit_app_with_wifi = "exit_app_with_wifi"

  /// Vueroid 앱을 종료하시겠습니까?\n\'종료\'버튼을 누르시면 안전하게 종료됩니다.
  static let exit_app_desc_disc = "exit_app_desc_disc"

  /// 설정 변경 시 DashCam이 재시작 됩니다.
  static let msg_setting_alert1 = "msg_setting_alert1"

  /// 설정 변경 시 DashCam이 포멧 후 재시작 됩니다.
  static let msg_setting_alert2 = "msg_setting_alert2"

  /// 설정 변경
  static let msg_setting_alert_title = "msg_setting_alert_title"

  /// 설정 변경 시 DashCam이 초기화 후 재시작 됩니다.\n영상화질, 메모리 할당 등 일부 설정변경에 따라 SD카드가 포멧될 수 도 있습니다.
  static let msg_setting_alert3 = "msg_setting_alert3"

  /// 시스템 기본값으로 재설정 됩니다. 포맷을 진행 하시겠습니까?
  static let msg_setting_alert5 = "msg_setting_alert5"

  /// %s 업그레이드
  static let dialog_firmware_msg03 = "dialog_firmware_msg03"

  /// DashCam의 %s를 최신 버전으로 업그레이드 하시겠습니까?
  static let dialog_firmware_msg04 = "dialog_firmware_msg04"

  /// 펌웨어
  static let dialog_firmware_txt = "dialog_firmware_txt"

  /// Safety Camera
  static let dialog_safetycamera_txt = "dialog_safetycamera_txt"

  /// 상시녹화방식
  static let drive_time_lapse = "drive_time_lapse"

  /// 이벤트 녹화 Beep
  static let sound_event_alert = "sound_event_alert"

  /// 다음의 경우 DashCam의 LiveViewer가 동작하지 않습니다. \n\n1.DashCam의 `설정` 화면에 진입해 있는 경우.\n2.DashCam과 App사이의 Wi-Fi 연결이 끊긴경우.\n3.기타사유로 DashCam의 정보를 읽어오지 못하는 경우.\n\n대부분의 경우 Wi-Fi를 다시 접속할 경우 해결되나 문제가 지속될 경우 DashCam를 재시작해 주십시오.
  static let liveview_error_text = "liveview_error_text"

  /// 11.6v/23.2v
  static let setting_detail_voltage_d10_value01 = "setting_detail_voltage_d10_value01"

  /// 11.8v/23.6v
  static let setting_detail_voltage_d10_value02 = "setting_detail_voltage_d10_value02"

  /// 12.0v/24.0v
  static let setting_detail_voltage_d10_value03 = "setting_detail_voltage_d10_value03"

  /// 12.2v/24.4v
  static let setting_detail_voltage_d10_value04 = "setting_detail_voltage_d10_value04"

  /// HEV 12.8v
  static let setting_detail_voltage_d10_value05 = "setting_detail_voltage_d10_value05"

  /// HEV 13.0v
  static let setting_detail_voltage_d10_value06 = "setting_detail_voltage_d10_value06"

  /// HEV 13.2v
  static let setting_detail_voltage_d10_value07 = "setting_detail_voltage_d10_value07"

  /// HEV 13.4v
  static let setting_detail_voltage_d10_value08 = "setting_detail_voltage_d10_value08"

  /// 펌웨어 업데이트를 실패 하였습니다
  static let toast_firmware_update_failed = "toast_firmware_update_failed"

  /// Wi-Fi 연결 해제
  static let share_wifi_error_title = "share_wifi_error_title"

  /// Share는 WIFI 연결을 끊어야 합니다. 와이파이를 끄거나 변경 하시겠습니까?
  static let share_wifi_error_msg = "share_wifi_error_msg"

  /// SaftyCamera Country Setting
  static let saftycamera_country_setting_txt = "saftycamera_country_setting_txt"

  /// SafetyCamera DB Upgrade
  static let safety_camera_upgrade_txt = "safety_camera_upgrade_txt"

  /// HDR
  static let hdr_txt = "hdr_txt"

  /// No chart data available
  static let no_chart_data_available = "no_chart_data_available"

  /// Model : %1$s
  static let model_string_format = "model_string_format"

  /// F/W : ver.%1$s
  static let fw_string_format = "fw_string_format"

  /// 모델명
  static let model_name_title = "model_name_title"

  /// Firmware Version
  static let firmware_version_title = "firmware_version_title"

  /// SD 카드 정보
  static let sd_card_information_title = "sd_card_information_title"


  /// SafetyCam : ver.%1$s
  static let safety_cam_string_format = "safety_cam_string_format"

  /// CH : %1$s
  static let ch_string_format = "ch_string_format"

  /// ADAS Setting
  static let adas_setting_txt = "adas_setting_txt"

  /// Can you see the trunk on the screen ? (No/Yes)
  static let trunk_switch_txt = "trunk_switch_txt"

  /// Bonnet
  static let bonnet_txt = "bonnet_txt"

  /// Trunk
  static let trunk_txt = "trunk_txt"

  /// Vehicle Center
  static let vehicle_center_txt = "vehicle_center_txt"

  /// Horizon
  static let horizon_txt = "horizon_txt"

  /// Sensitivity
  static let sensitive_txt = "sensitive_txt"

  /// FCW (OFF/ON)
  static let fcw_off_on_txt = "fcw_off_on_txt"

  /// RCW (OFF/ON)
  static let rcw_off_on_txt = "rcw_off_on_txt"

  /// 채널 선택
  static let channel_select_txt = "channel_select_txt"

  /// Trace Off
  static let trace_off_txt = "trace_off_txt"

  /// Trace On
  static let trace_on_txt = "trace_on_txt"

  /// SafetyCamera Country
  static let safety_camera_country_txt = "safety_camera_country_txt"

  /// Selected
  static let selected_txt = "selected_txt"

  /// Delete All
  static let delete_all_txt = "delete_all_txt"

  /// Wi-Fi connection help
  static let wi_fi_connection_help_txt = "wi_fi_connection_help_txt"

  /// Not Connected
  static let not_connected_txt = "not_connected_txt"

  /// United States
  static let united_states_country_txt = "united_states_country_txt"

  /// Australia
  static let australia_country_txt = "australia_country_txt"

  /// Japan
  static let japan_country_txt = "japan_country_txt"

  /// Singapore
  static let singapore_country_txt = "singapore_country_txt"

  /// Malaysia
  static let malaysia_country_txt = "malaysia_country_txt"

  /// United Kingdom
  static let united_kingdom_country_txt = "united_kingdom_country_txt"

  /// Israel
  static let israel_country_txt = "israel_country_txt"

  /// Safety camera Alert (OFF/ON)
  static let safety_camera_alert_txt = "safety_camera_alert_txt"

  /// Mobile Camera Zone Alert (OFF/ON)
  static let mobile_camera_zone_alert_txt = "mobile_camera_zone_alert_txt"

  /// Vehicle Type
  static let vehicle_type = "vehicle_type"

  /// LDWS (OFF/ON)
  static let ldws_off_on_txt = "ldws_off_on_txt"

  /// LDWS Speed
  static let ldws_txt = "ldws_txt"

  /// LDWS Sensitivity
  static let ldws_sensitivity = "ldws_sensitivity"

  /// FCWS (OFF/ON)
  static let fcws_off_on_txt = "fcws_off_on_txt"

  /// FCWS Sensitivity
  static let fcws_sensitivity = "fcws_sensitivity"

  /// FVSA (OFF/ON)
  static let fvsa_off_on_txt = "fvsa_off_on_txt"

  /// RCWS (OFF/ON)
  static let rcws_off_on_txt = "rcws_off_on_txt"

  /// RCWS Sensitivity
  static let rcws_sensitivity = "rcws_sensitivity"

  /// Front PCWS
  static let front_pcws = "front_pcws"

  /// Rear PCWS
  static let rear_pcws = "rear_pcws"

  /// Event Only
  static let setting_detail_on_value01 = "setting_detail_on_value01"

  /// Event+Park
  static let setting_detail_on_value02 = "setting_detail_on_value02"

  /// Wi-Fi 대역폭
  static let wifi_bandwidth = "wifi_bandwidth"

  /// Sensitivity
  static let adas_sensitive = "adas_sensitive"

  /// Sedan
  static let adas_car_type_sedan = "adas_car_type_sedan"

  /// SUV
  static let adas_car_type_suv = "adas_car_type_suv"

  /// Truck
  static let adas_car_type_truck = "adas_car_type_truck"

  /// Bus
  static let adas_car_type_bus = "adas_car_type_bus"

  /// English
  static let country_voice_pack_name_en = "country_voice_pack_name_en"

  /// Hebrew
  static let country_voice_pack_name_he = "country_voice_pack_name_he"

  /// Voice Pack
  static let country_voice_pack_name = "country_voice_pack_name"

  /// Country
  static let country_name = "country_name"

  /// GMT
  static let country_GMT_name = "country_GMT_name"

  /// Australia
  static let country_name_au = "country_name_au"

  /// Austria
  static let country_name_at = "country_name_at"

  /// Bahrain
  static let country_name_bh = "country_name_bh"

  /// Belarus
  static let country_name_by = "country_name_by"

  /// Belgium
  static let country_name_be = "country_name_be"

  /// Bosnia
  static let country_name_ba = "country_name_ba"

  /// Brazil
  static let country_name_br = "country_name_br"

  /// Bulgaria
  static let country_name_bg = "country_name_bg"

  /// Canada
  static let country_name_ca = "country_name_ca"

  /// China
  static let country_name_cn = "country_name_cn"

  /// Croatia
  static let country_name_hr = "country_name_hr"

  /// Czech Republic
  static let country_name_cz = "country_name_cz"

  /// Denmark
  static let country_name_dk = "country_name_dk"

  /// Estonia
  static let country_name_ee = "country_name_ee"

  /// Finland
  static let country_name_fi = "country_name_fi"

  /// France
  static let country_name_fr = "country_name_fr"

  /// Germany
  static let country_name_de = "country_name_de"

  /// Greece
  static let country_name_gr = "country_name_gr"

  /// Hungary
  static let country_name_hu = "country_name_hu"

  /// Iceland
  static let country_name_is = "country_name_is"

  /// Ireland
  static let country_name_ie = "country_name_ie"

  /// Israel
  static let country_name_il = "country_name_il"

  /// Italy
  static let country_name_it = "country_name_it"

  /// Japan
  static let country_name_jp = "country_name_jp"

  /// Kuwait
  static let country_name_kw = "country_name_kw"

  /// Latvia
  static let country_name_lv = "country_name_lv"

  /// Lithuania
  static let country_name_lt = "country_name_lt"

  /// Luxembourg
  static let country_name_lu = "country_name_lu"

  /// Malta
  static let country_name_mt = "country_name_mt"

  /// Netherlands
  static let country_name_nl = "country_name_nl"

  /// Norway
  static let country_name_no = "country_name_no"

  /// Oman
  static let country_name_om = "country_name_om"

  /// Poland
  static let country_name_pl = "country_name_pl"

  /// Portugal
  static let country_name_pt = "country_name_pt"

  /// Qatar
  static let country_name_qa = "country_name_qa"

  /// Romania
  static let country_name_ro = "country_name_ro"

  /// Russia
  static let country_name_ru = "country_name_ru"

  /// Serbia
  static let country_name_rs = "country_name_rs"

  /// Singapore
  static let country_name_sg = "country_name_sg"

  /// SingaporeMalaysia
  static let country_name_my = "country_name_my"

  /// Slovakia
  static let country_name_sk = "country_name_sk"

  /// Slovenia
  static let country_name_si = "country_name_si"

  /// Spain
  static let country_name_es = "country_name_es"

  /// Sweden
  static let country_name_se = "country_name_se"

  /// Türkiye
  static let country_name_tr = "country_name_tr"

  /// UAE
  static let country_name_ae = "country_name_ae"

  /// UK
  static let country_name_gb = "country_name_gb"

  /// USA
  static let country_name_us = "country_name_us"

  /// Initial Setting
  static let dialog_country_setting_title = "dialog_country_setting_title"

  /// Connect mobile network
  static let help_internet_title = "help_internet_title"

  /// Please use mobile network to download SafetyCamera DB and Voice Pack.\nClick OK to move to Wi-Fi setting
  static let help_internet_txt = "help_internet_txt"

  /// Please connect Dashcam with Vueroid Wi-Fi to complete the settings
  static let dialog_save_success_for_country_setting = "dialog_save_success_for_country_setting"

  /// Selected items will be applied to Dashcam
  static let dialog_notification_guide_for_country_setting =
    "dialog_notification_guide_for_country_setting"

  /// Low
  static let low_txt = "low_txt"

  /// Middle
  static let Middle_txt = "Middle_txt"

  /// High
  static let High_txt = "High_txt"

  /// Off
  static let zero_km_h_txt = "zero_km_h_txt"

  /// 50km/h
  static let fifty_km_h_txt = "fifty_km_h_txt"

  /// 60km/h
  static let sixty_km_h_txt = "sixty_km_h_txt"

  /// 80km/h
  static let eighty_km_h_txt = "eighty_km_h_txt"

  /// 100km/h
  static let hundred_km_h_txt = "hundred_km_h_txt"

  /// Off
  static let zero_mph_txt = "zero_mph_txt"

  /// 30mph
  static let fifty_mph_txt = "fifty_mph_txt"

  /// 40mph
  static let sixty_mph_txt = "sixty_mph_txt"

  /// 50mph
  static let eighty_mph_txt = "eighty_mph_txt"

  /// 60mph
  static let hundred_mph_txt = "hundred_mph_txt"

  /// Rear Camera Flip
  static let rearcam_acp_flip_txt = "rearcam_acp_flip_txt"

  /// Rear Camera Bright
  static let rearcam_acp_bright_txt = "rearcam_acp_bright_txt"

  /// Rear Camera FPS
  static let rearcam_acp_fps_txt = "rearcam_acp_fps_txt"

  /// Normal
  static let rearcam_acp_flip_normal_txt = "rearcam_acp_flip_normal_txt"

  /// Flip
  static let rearcam_acp_flip_flip_txt = "rearcam_acp_flip_flip_txt"

  /// 30fps
  static let rearcam_acp_fps_thirty_txt = "rearcam_acp_fps_thirty_txt"

  /// 29.1fps
  static let rearcam_acp_flip_twenty_nine_one_txt = "rearcam_acp_flip_twenty_nine_one_txt"

  /// 상시 Mode
  static let driving_recording_isp_mode_txt = "driving_recording_isp_mode_txt"

  /// 일반
  static let driving_recording_isp_mode_normal_txt = "driving_recording_isp_mode_normal_txt"

  /// HDR
  static let driving_recording_isp_mode_hdr_txt = "driving_recording_isp_mode_hdr_txt"

  /// 주차 Mode
  static let parking_recording_isp_mode_txt = "parking_recording_isp_mode_txt"

  /// 일반
  static let parking_recording_isp_mode_normal_txt = "parking_recording_isp_mode_normal_txt"

  /// HDR
  static let parking_recording_isp_mode_hdr_txt = "parking_recording_isp_mode_hdr_txt"

  /// Ultra Night Vision
  static let parking_recording_isp_mode_ultra_night_vision_txt =
    "parking_recording_isp_mode_ultra_night_vision_txt"

  /// 선택수는 최대 10입니다.
  static let warning_select_file = "warning_select_file"

  /// 번호판 인식 강화
  static let driving_recording_isp_mode_license_plate_txt =
    "driving_recording_isp_mode_license_plate_txt"

  /// 비식별화
  static let ai_privacy_protection = "ai_privacy_protection"

  /// Others
  static let country_name_others = "country_name_others"

  /// 4.Wi-Fi 주파수 2.4/5GHz 수동 설정
  static let info_wifi_msg_7 = "info_wifi_msg_7"

  /// - 기본 Wi-Fi 주파수는 5GHz입니다.\n- 스마트폰이 5GHz Wi-Fi를 지원하지 않는 경우 DashCame의 Wi-Fi 버튼을 눌러 Wi-Fi를 끈 후 다시 Wi-Fi 버튼을 3초 이상 누르면 2.4GHz로 전환됩니다. \n- 다시 5GHz로 전환하려면 Wi-Fi를 껐다가 다시 Wi-Fi 버튼을 3초 이상 누르세요.
  static let info_wifi_msg_8 = "info_wifi_msg_8"

  /// 표준시간대
  static let setting_timeregion_txt = "setting_timeregion_txt"

  /// Formatting
  static let setting_formatting_txt = "setting_formatting_txt"

  /// Rebooting
  static let setting_rebooting_txt2 = "setting_rebooting_txt2"

  /// %s
  static let home_voltage_txt = "home_voltage_txt"

  /// fcm_default_channel
  static let default_notification_channel_id = "default_notification_channel_id"

  /// FCM Message
  static let fcm_message = "fcm_message"

  /// Vueroid-Cloud
  static let default_notification_channel_name = "default_notification_channel_name"

  /// Version : %s
  static let setting_appInfo_txt = "setting_appInfo_txt"

  /// 계정 설정
  static let setting_cloud_item_account = "setting_cloud_item_account"

  /// 서비스 알림 설정
  static let setting_cloud_item_service = "setting_cloud_item_service"

  /// 충돌 감지 이벤트 알림 설정
  static let setting_cloud_item_collision_detection_event =
    "setting_cloud_item_collision_detection_event"

  /// 언어 설정
  static let setting_cloud_item_language = "setting_cloud_item_language"

  /// 수동녹화 클라우드 저장 설정
  static let setting_cloud_item_Manual_recording_cloud_storage =
    "setting_cloud_item_Manual_recording_cloud_storage"

  /// App 정보
  static let setting_cloud_item_app_info = "setting_cloud_item_app_info"

  /// 녹화
  static let setting_wifi_item_recoding = "setting_wifi_item_recoding"

  /// 이벤트
  static let setting_wifi_item_event = "setting_wifi_item_event"

  /// 소리
  static let setting_wifi_item_sound = "setting_wifi_item_sound"

  /// 시간
  static let setting_wifi_item_time = "setting_wifi_item_time"

  /// 메모리
  static let setting_wifi_item_memory = "setting_wifi_item_memory"

  /// 시스템
  static let setting_wifi_item_system = "setting_wifi_item_system"

  /// 홈화면 보기
  static let home_item_home = "home_item_home"

  /// 접속모드 선택
  static let home_item_changeMode = "home_item_changeMode"

  /// Live View
  static let home_item_liveView = "home_item_liveView"

  /// File View
  static let home_item_fileView = "home_item_fileView"

  /// History
  static let home_item_history = "home_item_history"

  /// Setting
  static let home_item_setting = "home_item_setting"

  /// Customer Service
  static let home_item_customerService = "home_item_customerService"

  /// Debug
  static let home_item_debug = "home_item_debug"

  /// Dash cam List
  static let home_item_dashCamList = "home_item_dashCamList"

  /// 토큰 : %s
  static let msg_token_fmt = "msg_token_fmt"

  /// SSID와 Password가 존재 하지 않습니다.
  static let direct_connection_not_wifi_connection_notification_describe =
    "direct_connection_not_wifi_connection_notification_describe"

  /// Nickname이 존재 하지 않습니다.
  static let sta_notification_describe = "sta_notification_describe"

  /// Wi-Fi가 꺼저 있습니다.\nWi-Fi을 켜는 중입니다.\n잠시만 기다려주세요.
  static let notification_wifi_on_delay = "notification_wifi_on_delay"

  /// STA 모드입니다.\nWi-Fi로 전환 중입니다.\n잠시만 기다려주세요.
  static let notification_wifi_Change_delay = "notification_wifi_Change_delay"

  /// 차후 지원 예정입니다.
  static let notification_supported_later = "notification_supported_later"

  /// 최근 위치
  static let recent_location_txt = "recent_location_txt"

  /// G-Sensor Graph
  static let gsensor_graph_txt = "gsensor_graph_txt"

  /// 위치갱신
  static let location_update_txt = "location_update_txt"

  /// 좌표복사
  static let copy_coordinates_txt = "copy_coordinates_txt"

  /// 주행시간
  static let driving_time_txt = "driving_time_txt"

  /// 주행거리
  static let distance_drive_txt = "distance_drive_txt"

  /// 최고속도
  static let top_speed_txt = "top_speed_txt"

  /// 평균속도
  static let average_speed_txt = "average_speed_txt"

  /// 충격녹화
  static let shock_recording_txt = "shock_recording_txt"

  /// 자주 사용하는 STA
  static let sta_setting_main_title_text = "sta_setting_main_title_text"

  /// STA 추가
  static let sta_setting_sub_title_text = "sta_setting_sub_title_text"

  /// Please Register your Device
  static let dashcam_list_register_main_title_txt = "dashcam_list_register_main_title_txt"

  /// Sign Up
  static let Sign_Up_txt = "Sign_Up_txt"

  /// Sign In
  static let Sign_In_txt = "Sign_In_txt"

  /// Sign Out
  static let Sign_Out_txt = "Sign_Out_txt"

  /// 바코드 인식을 위해 카메라 권한이 필요합니다. 카메라 권한을 부여해주세요.
  static let barcode_camera_permission_txt = "barcode_camera_permission_txt"

  /// QR코드를 스캔해주세요.
  static let barcode_camera_main_title_txt = "barcode_camera_main_title_txt"

  /// 후면 카메라의 뷰파인더에\nQR코드가 나타나도록 해주세요.
  static let barcode_camera_sub_title_txt = "barcode_camera_sub_title_txt"

  /// DASHCAM Registration
  static let cloud_manual_connection_main_title_txt = "cloud_manual_connection_main_title_txt"

  /// 모델명
  static let cloud_manual_connection_sub_title_model_txt =
    "cloud_manual_connection_sub_title_model_txt"

  /// 모델을 선택해 주세요.
  static let cloud_manual_connection_sub_title_model_hint_txt =
    "cloud_manual_connection_sub_title_model_hint_txt"

  /// 시리얼넘버
  static let cloud_manual_connection_sub_title_serial_txt =
    "cloud_manual_connection_sub_title_serial_txt"

  /// 시리얼 번호를 입력해 주세요.
  static let cloud_manual_connection_sub_title_serial_hint_txt =
    "cloud_manual_connection_sub_title_serial_hint_txt"

  /// IMEI
  static let cloud_manual_connection_sub_title_IMEI_txt =
    "cloud_manual_connection_sub_title_IMEI_txt"

  /// IMEI를 입력해 주세요.
  static let cloud_manual_connection_sub_title_IMEI_hint_txt =
    "cloud_manual_connection_sub_title_IMEI_hint_txt"

  /// Private Code
  static let cloud_manual_connection_sub_title_private_code_txt =
    "cloud_manual_connection_sub_title_private_code_txt"

  /// Private Code를 입력해 주세요.
  static let cloud_manual_connection_sub_title_private_code_hint_txt =
    "cloud_manual_connection_sub_title_private_code_hint_txt"

  /// 위치정보 수집에 동의
  static let cloud_manual_connection_location_agree_txt =
    "cloud_manual_connection_location_agree_txt"

  /// 등록실패
  static let notification_cloud_manual_connection_check_insert_info_title =
    "notification_cloud_manual_connection_check_insert_info_title"

  /// 입력하신 정보를 확인해주세요.
  static let notification_cloud_manual_connection_check_insert_info_des =
    "notification_cloud_manual_connection_check_insert_info_des"

  /// 등록성공
  static let notification_cloud_manual_connection_regist_success_title =
    "notification_cloud_manual_connection_regist_success_title"

  /// DashCame이 등록되었습니다.
  static let notification_cloud_manual_connection_regist_success_des =
    "notification_cloud_manual_connection_regist_success_des"

  /// 단말등록대기
  static let notification_cloud_manual_connection_regist_loading_title =
    "notification_cloud_manual_connection_regist_loading_title"

  /// DashCame를 인식할 수 있도록\nDashCame의 Set Button을 눌러주세요.\n최초 등록 시 30초 이상 소요될 수 있습니다.
  static let notification_cloud_manual_connection_regist_loading_des =
    "notification_cloud_manual_connection_regist_loading_des"

  /// 등록실패
  static let notification_cloud_manual_connection_regist_timeout_title =
    "notification_cloud_manual_connection_regist_timeout_title"

  /// 등록 시간을 초과하였습니다.
  static let notification_cloud_manual_connection_regist_timeout_des =
    "notification_cloud_manual_connection_regist_timeout_des"

  /// 채널선택
  static let liveview_channel_select_title_txt = "liveview_channel_select_title_txt"

  /// 알림
  static let notification_txt = "notification_txt"

  /// 공지사항
  static let notice_txt = "notice_txt"

  /// 공지 삭제
  static let notice_delete_txt = "notice_delete_txt"

  /// 알림 삭제
  static let noti_delete_txt = "noti_delete_txt"

  /// 전체 삭제
  static let notification_all_delete_txt = "notification_all_delete_txt"

  /// 새로운 알림이 없습니다.
  static let notification_no_data_txt = "notification_no_data_txt"

  /// 알림을 모두 삭제하시겠습니까?
  static let notification_delete_all_req = "notification_delete_all_req"

  /// 알림을 삭제하시겠습니까?
  static let notification_delete_req = "notification_delete_req"

  /// 알림을 모두 삭제되었습니다.
  static let notification_delete_all_success = "notification_delete_all_success"

  /// 알림이 삭제되었습니다.
  static let notification_delete_success = "notification_delete_success"

  /// %s 메세지
  static let notification_message_cnt = "notification_message_cnt"

  /// %1s 시간 %2s 분
  static let driving_time_value = "driving_time_value"

  /// %s 회
  static let shock_recording_value = "shock_recording_value"

  /// 파일 삭제
  static let popup_delete_title = "popup_delete_title"

  /// 해당 영상을 삭제하시겠습니까?
  static let popup_delete_desc = "popup_delete_desc"

  /// 블랙 박스 전원을 끄시겠습니까?
  static let turn_on_black_box_power = "turn_on_black_box_power"

  /// 이미 Power Off 되었습니다.
  static let turn_off_black_box_power = "turn_off_black_box_power"

  /// 전원 변경
  static let turn_on_off_black_box_power = "turn_on_off_black_box_power"

  /// 로그아웃
  static let account_logout_text = "account_logout_text"

  /// 프로필 변경
  static let system_change_profile = "system_change_profile"

  /// 나의 구독
  static let system_my_subscribe = "system_my_subscribe"

  /// 나의 문의
  static let system_my_qna = "system_my_qna"

  /// 회원 탈퇴
  static let withdraw_account_dialog_title = "withdraw_account_dialog_title"

  /// 회원 탈퇴시 모든 개인 정보 및 클라우드 데이터가 즉시 삭제 됩니다.\n\n회원 탈퇴 하시겠습니까?[번역]\n입력 폼에 \'탈퇴신청\'을 입력하시고 회원탈퇴를 완료해주세요
  static let withdraw_account_dialog_message = "withdraw_account_dialog_message"

  /// 정확한 텍스트를 입력해주세요.
  static let withdraw_account_check_fail_alert = "withdraw_account_check_fail_alert"

  /// (%1$s/3)Bluetooth 연결을 시도 합니다.
  static let bluetooth_try_connect = "bluetooth_try_connect"

  /// Bluetooth 연결 성공
  static let bluetooth_successful_connect = "bluetooth_successful_connect"

  /// Bluetooth 연결이 실패했습니다.
  static let bluetooth_fail_connect = "bluetooth_fail_connect"

  /// WIFI 정보를 받아 오지 못했습니다.
  static let bluetooth_fail_received_wifi_info = "bluetooth_fail_received_wifi_info"

  /// (%1$s/3)WIFI 연결을 시도합니다.
  static let wifi_try_connect = "wifi_try_connect"

  /// WiFi 연결을 위해 블랙박스 화면에서 YES 를 선택해주세요.
  static let wifi_verify_popup_title = "wifi_verify_popup_title"

  /// DashCam 삭제
  static let delete_dashcam_title = "delete_dashcam_title"

  /// DashCam 삭제 성공
  static let delete_dashcam_desc_suc = "delete_dashcam_desc_suc"

  /// DashCam 삭제 실패
  static let delete_dashcam_desc_fail = "delete_dashcam_desc_fail"

  /// Dashcam 별명
  static let cloud_manual_connection_sub_title_nickname_txt =
    "cloud_manual_connection_sub_title_nickname_txt"

  /// Dashcam 별명을 입력해주세요
  static let cloud_manual_connection_sub_title_nickname_hint_txt =
    "cloud_manual_connection_sub_title_nickname_hint_txt"

  /// 등록
  static let register = "register"

  /// 로그아웃 하시겠습니까?
  static let cloud_logout_title = "cloud_logout_title"

  /// 로그아웃
  static let cloud_logout_desc = "cloud_logout_desc"

  /// Drive DB 선택
  static let local_history_sheet_title = "local_history_sheet_title"

  /// Mi/h
  static let unit_mph = "unit_mph"

  /// Mile
  static let unit_mile = "unit_mile"

  /// Km/h
  static let unit_kmh = "unit_kmh"

  /// Km
  static let unit_kilo = "unit_kilo"

  /// 재생시간 종료되었습니다.
  static let cloud_live_time_end = "cloud_live_time_end"

  /// To Cloud
  static let popup_tocloud_title = "popup_tocloud_title"

  /// 클라우드로 업로드를 진행합니다
  static let popup_tocloud_desc = "popup_tocloud_desc"

  /// Disconnect
  static let popup_disconnect_title = "popup_disconnect_title"

  /// 블랙박스 전원이 꺼져있어 이용이 불가능 합니다 전원을 켠 후 이용해 주세요.
  static let popup_disconnect_desc = "popup_disconnect_desc"

  /// 취소가 불가능한 작업입니다.
  static let not_cancelable_task_msg = "not_cancelable_task_msg"

  /// 업로드
  static let upload_title = "upload_title"

  /// Uploading onto Phone
  static let dialog_uploading_msg = "dialog_uploading_msg"

  /// Phone의 저장 공간이 부족합니다.\n업로드를 실패하였습니다.
  static let video_upload_size_error = "video_upload_size_error"

  /// 나가기
  static let simple_text_exit = "simple_text_exit"

  /// 이동
  static let simple_text_move = "simple_text_move"

  /// 이미 스캔중입니다.
  static let already_ble_scanning = "already_ble_scanning"

  /// 장치를 찾을 수 없습니다. 연결할 수 없습니다.
  static let not_found_device = "not_found_device"

  /// BluetoothAdapter가 초기화되지 않았거나 주소가 지정되지 않았습니다.
  static let not_initial_ble_adapter = "not_initial_ble_adapter"

  /// WIFI Connected
  static let wifi_connected = "wifi_connected"

  /// WIFI Disconnected
  static let wifi_disconnected = "wifi_disconnected"

  /// DashCam 오류: DashCam을 다시 시작하십시오.
  static let error_restart_dash_cam = "error_restart_dash_cam"

  /// WiFi 연결 시간 초과!!
  static let error_time_out_wifi_connection = "error_time_out_wifi_connection"

  /// 존재하지 않는 주소
  static let not_exists_map_address = "not_exists_map_address"

  /// Drive Data가 존재하지 않습니다.
  static let not_exists_drive_database = "not_exists_drive_database"

  /// 다른 기기나 브라우저에서 이 계정에서 로그아웃했습니다.
  static let login_other_account_device = "login_other_account_device"

  /// Start Live
  static let start_live_video = "start_live_video"

  /// Stop Live
  static let stop_live_video = "stop_live_video"

  /// 유저 정보가 존재 하지 않습니다.
  static let no_exists_user_data = "no_exists_user_data"

  /// 재생 가능 횟수 초과
  static let limit_video_play_count = "limit_video_play_count"

  /// 업로드 도중 문제가 발생했습니다.
  static let error_upload_fail = "error_upload_fail"

  /// 업로드를 완료했습니다.
  static let upload_success_msg = "upload_success_msg"

  /// 업로드 할 파일을 선택해 주세요.
  static let dialog_upload_msg = "dialog_upload_msg"

  /// 한개의 파일만 공유가 가능 합니다.
  static let shrae_warning_msg_01 = "shrae_warning_msg_01"

  /// 공유 할 파일을 선택해 주세요.
  static let dialog_wraning_msg_02 = "dialog_wraning_msg_02"

  /// 스킵
  static let onboarding_skip = "onboarding_skip"

  /// 선택 하신 내용을 삭제 하시겠습니까?
  static let delete_select_drive_contents = "delete_select_drive_contents"

  /// Delete Dashcam
  static let delete_dashcam = "delete_dashcam"

  /// 등록된 대시캠이 없습니다.
  static let dashcamlist_empty = "dashcamlist_empty"

  /// 대시캠의 Set 버튼을 눌러주세요
  static let dashcamlist_set_button_press = "dashcamlist_set_button_press"

  /// 인식 오류
  static let qr_scan_error = "qr_scan_error"

  /// 인식되었습니다
  static let qr_recognized = "qr_recognized"

  /// 로그인이 만료되었습니다 재로그인 해주세요.
  static let login_expired_desc = "login_expired_desc"

  /// 상시 충격 이벤트
  static let history_permanent_shock_event = "history_permanent_shock_event"

  /// 주차 모션 이벤트
  static let history_park_motion_event = "history_park_motion_event"

  /// 주차 충격 이벤트
  static let history_park_shock_event = "history_park_shock_event"

  /// 상시 녹화 시간
  static let history_always_recording_time = "history_always_recording_time"

  /// 주차 녹화 시간
  static let history_park_recording_time = "history_park_recording_time"

  /// 평균 속도
  static let history_average_speed = "history_average_speed"

  /// 최고 속도
  static let history_top_speed = "history_top_speed"

  /// 성공
  static let dashcam_regist_success_title = "dashcam_regist_success_title"

  /// Dashcam 등록 성공
  static let dashcam_regist_success_desc = "dashcam_regist_success_desc"

  /// 시간 초과
  static let dashcam_regist_timeout_title = "dashcam_regist_timeout_title"

  /// Dashcam 등록 시간 초과
  static let dashcam_regist_timeout_desc = "dashcam_regist_timeout_desc"

  /// 실패
  static let dashcam_regist_fail_title = "dashcam_regist_fail_title"

  /// 실패
  static let fail = "dashcam_regist_fail_title"

  /// Dashcam 등록 실패
  static let dashcam_regist_fail_desc = "dashcam_regist_fail_desc"

  /// 대기
  static let dashcam_regist_waiting_title = "dashcam_regist_waiting_title"

  /// Dashcam 등록 대기
  static let dashcam_regist_waiting_desc = "dashcam_regist_waiting_desc"

  /// 시간 초과
  static let notification_wifi_time_out_txt_title = "notification_wifi_time_out_txt_title"

  /// 연결
  static let connect = "connect"

  /// 현재 주차
  static let liveview_menu_park_title = "liveview_menu_park_title"

  /// 경고
  static let warning = "warning"

  /// LIVE 보기
  static let watch_live_title = "watch_live_title"

  /// 현재 라이브 보기를 이용하실 수 없습니다.
  static let can_not_watch_live_msg = "can_not_watch_live_msg"

  /// GPS가 꺼져 있습니다
  static let map_gps_off_msg = "map_gps_off_msg"

  /// 습도
  static let weather_humidity_title = "weather_humidity_title"

  /// 풍속
  static let weather_windspeed_title = "weather_windspeed_title"

  /// 장치 켜짐/꺼짐 상태를 확인할 수 없습니다.
  static let file_change_device_on_off_msg = "file_change_device_on_off_msg"

  /// 해당 설정을 변경할 경우 Wifi가 껐다 켜집니다. 진행하시겠습니까?
  static let msg_setting_alert4 = "msg_setting_alert4"

  /// 메모리 포맷 시작
  static let memory_format_start_msg = "memory_format_start_msg"

  /// DashCam 리셋 시작
  static let dashcam_reset_start_msg = "dashcam_reset_start_msg"

  /// 작성일
  static let notice_write_date = "notice_write_date"

  /// 설정 변경 실패\n 재 진입 후 시도해 주세요.
  static let fail_setting_change_msg = "fail_setting_change_msg"

  /// 재생 가능 시간
  static let live_playable_time = "live_playable_time"

  /// 모드를 선택하세요!
  static let onboarding_select_mode_msg = "onboarding_select_mode_msg"

  /// WiFi 또는 클라우드
  static let onboarding_wifi_ot_cloud_msg = "onboarding_wifi_ot_cloud_msg"

  /// 블랙박스 모델에 따라 연결 모드를 자유롭게 선택할 수 있습니다.
  static let onboarding_explain_msg = "onboarding_explain_msg"

  /// NICKNAME
  static let simple_nickname_text = "simple_nickname_text"

  /// 파일을 선택해 주세요.
  static let filelist_none_select = "filelist_none_select"

  /// 닫기
  static let simple_close_text = "simple_close_text"

  /// ※ USIM 변경 등 필요 시에만 입력 해 주세요.\n  잘못 입력 시 통신이 안될 수 있습니다.
  static let usim_auth_change_warning = "usim_auth_change_warning"

  /// PIN 입력
  static let enter_pin_hint = "enter_pin_hint"

  /// PUK 입력
  static let enter_puk_hint = "enter_puk_hint"

  /// WiFi 자동 켜짐
  static let wifi_auto_on_text = "wifi_auto_on_text"

  /// AP 이름 (SSID)
  static let ap_name_title = "ap_name_title"

  /// AP 비밀번호 (PASSWORD)
  static let ap_password_title = "ap_password_title"

  /// Audio
  static let simple_audio_text = "simple_audio_text"

  /// 변환 시작
  static let eidt_btn_text = "eidt_btn_text"

  /// 재생 가능 횟수
  static let possible_video_play_count = "possible_video_play_count"

  /// VoicePack
  static let simple_voice_pack_text = "simple_voice_pack_text"

  /// 개인정보 보호정책
  static let footer_private_poltcy_text = "footer_private_poltcy_text"

  /// 서비스 이용 약관
  static let footer_term_of_use_text = "footer_term_of_use_text"

  /// 오픈 소스 라이브러리
  static let footer_open_source_lib_text = "footer_open_source_lib_text"

  /// NC&Co.,Ltd\nⓒNC& All Rights Reserved
  static let footer_company_name = "footer_company_name"

  /// 상시녹화
  static let simple_always_record_text = "simple_always_record_text"

  /// ON(All)
  static let setting_audiorec_value00 = "setting_audiorec_value00"

  /// 1 Hour
  static let setting_park_rec_time_value06 = "setting_park_rec_time_value06"

  /// 2 Hours
  static let setting_park_rec_time_value07 = "setting_park_rec_time_value07"

  /// FREE
  static let simple_free_text = "simple_free_text"

  /// 주차 이미지
  static let parking_image_text = "parking_image_text"

  /// 주차 이미지 없음
  static let no_exists_parking_image = "no_exists_parking_image"

  /// 최종 수정 날짜:
  static let last_modified_date = "last_modified_date"

  static let onboarding_get_started_button = "onboarding_get_started_button"

  static let direct_connection_edit_ssid_password = "direct_connection_edit_ssid_password"

  static let init_setting_language_title = "init_setting_language_title"

  static let init_setting_timezone_title = "init_setting_timezone_title"

  static let init_setting_voicepack_title = "init_setting_voicepack_title"

  static let onboarding_menu_text_msg_01 = "onboarding_menu_text_msg_01"
  static let onboarding_menu_text_msg_02 = "onboarding_menu_text_msg_02"
  static let onboarding_menu_text_msg_03 = "onboarding_menu_text_msg_03"
  static let onboarding_service_text_msg_01 = "onboarding_service_text_msg_01"
  static let onboarding_service_text_msg_02 = "onboarding_service_text_msg_02"

  static let park_rec_event_noti = "park_rec_event_noti"

  static let error_code_405 = "error_code_405"

  //신규 수정
  //lcd -ontime
  static let alwaysOn = "alwaysOn"
  static let night_time = "night_time"
  static let hdr_timer = "hdr_timer"
  static let hdr_timer_description = "hdr_timer_description"
  
  
  static let sec_30 = "30sec"
  static let min_1 = "1min"
  static let clockOnLcd = "clock_on_LCD"
  static let hdr_Normal =  ""


  static let driving_history = "driving_history"
  /// 해상도&프레임레이트
  static let s1_setting_resolution_framerate = "s1_setting_resolution_framerate"
  /// 녹화화질
  static let s1_setting_bitrate = "s1_setting_bitrate"
  /// 주파수
  static let s1_setting_frequency = "s1_setting_frequency"
  /// 상시 영상 화질 모드
  static let s1_setting_drive_isp = "s1_setting_drive_isp"
  /// 상시 타임랩스
  static let s1_setting_timelapse = "s1_setting_timelapse"
  /// 충격 감도
  static let s1_setting_inf_sensor = "s1_setting_inf_sensor"
  /// 이미지 회전 및 미러
  static let s1_setting_rotate_mirror = "s1_setting_rotate_mirror"
  /// 이벤트 영상 보호
  static let s1_setting_event_protection = "s1_setting_event_protection"
  /// 겨울철 모드
  static let s1_setting_wintermode = "s1_setting_wintermode"
  /// 주차모드지연 90초
  static let s1_setting_parkdelay_ninety_s = "s1_setting_parkdelay_ninety_sec"
  ///주차 모드지연 3분
  static let s1_setting_parkdelay_three_m = "s1_setting_parkdelay_three_min"
  
  /// 주차 모드 지연
  static let s1_setting_parking_mode_delay = "s1_setting_parking_mode_delay"
  /// 주차 영상 화질 모드
  static let s1_setting_park_isp_mode = "s1_setting_park_isp_mode"
  /// 녹화 방식
  static let s1_setting_rec_type = "s1_setting_rec_type"
  /// 실내카메라
  static let s1_setting_inner_camera = "s1_setting_inner_camera"
  
  /// 후방카메라
  static let s1_setting_rear_camera_usage = "s1_setting_rear_camera_usage"

  /// 싦내카메라
  static let s1_setting_interior_camera_usage = "s1_setting_interior_camera_usage"

  /// settingTab
  /// 음성안내
  static let voice_alert = "quick_voice_alert"
  /// HDR+Infinite Plate Capture
  static let HDR_infinite_plate_capture = "s1_setting_videoenhancing_value02"
  
  static let driving_impact_sensitivity = "s1_setting_event_sensitivity_driving_impact"
  
  static let parking_impact_sensitivity = "s1_setting_event_sensitivity_parking_impact"
  
  static let motion_impact_sensitivity = "s1_setting_event_sensitivity_parking_motion"
  
  static let rectype_impact_motion = "s1_setting_rectype_motion"
  
  static let rectype_low_powermode = "s1_setting_rectype_lowpower"
  
  static let rectype_timeplase = "s1_setting_rectype_timelapse"
  
  //상태 LED
  static let setting_status_led = "s1_setting_status_led"
  
  //주차모드 LED
  static let setting_parking_led = "s1_setting_parking_led"
  
  /// LCDOFF옵션
  static let lcdoff_alwayson = "s1_setting_lcdoff_value01"
  static let lcdoff_halfmin = "s1_setting_lcdoff_value02"
  static let lcdoff_onemin = "s1_setting_lcdoff_value03"
  static let lcdoff_clock = "s1_setting_lcdoff_value04"
  
  ///setting/SystemSettings

  
  //영상로고
  static let setting_watermark = "s1_setting_watermark"
  //비밀번호 잠금
  static let setting_setpassword = "s1_setting_setpassword"
  //비밀번호 변경
  static let setting_changepassword = "s1_setting_changepassword"
  //언어 변경
  static let setting_language = "s1_setting_language"
  
  
  /// COMMON
  
  static let s1_setting_off = "s1_standard_off"
  static let s1_setting_parkdelay_no_delay = "s1_setting_parkdelay_no_delay"
  /// 주행 정보
  static let s1_drive_info_title = "s1_drive_info_title"
  
  /// New thing
  ///
  static let text_find_devices = "text_find_devices"
  
  static let plate_restoration_privacy_protection = "plate_restoration_privacy_protection"
  
  static let text_favorites = "text_favorites"
  
  // Favorites Edit
  static let quick_menu_title = "quick_menu_title"
  
  static let edit_device_list_information = "edit_device_list_information"
  
  static let ai_plate_popup_title = "ai_plate_popup_title"

  /// 일일 사용 한도에 도달했습니다.
  static let ai_function_limit_msg = "ai_function_limit_msg"
  
  /// Analyzing
  static let ai_analyzing_txt = "ai_analyzing_txt"

  /// 분석중입니다.....
  static let ai_analyzing_desc_txt = "ai_analyzing_desc_txt"
  
  /// Transfer
  static let ai_plate_send = "ai_plate_send"

  /// Specify Restore Area
  static let ai_plate_setting_arrange = "ai_plate_setting_arrange"
  
  static let ai_plate_popup_text = "ai_plate_popup_text"
  
  /// 예상 소요 시간 : %@ \n\n결과를 받고 싶은 방법을 선택해 주세요.
  static let ai_waiting_time_msg = "ai_waiting_time_msg"

  /// 분석이 완료되면 푸시 알림을 보내드립니다.\n미리 알림 설정을 확인해 주세요.
  static let ai_push_alert_msg = "ai_push_alert_msg"

  /// 대기 없음
  static let ai_no_wait_text = "ai_no_wait_text"

  /// Push
  static let ai_push_btn_text = "ai_push_btn_text"

  /// Wait
  static let ai_wait_btn_text = "ai_wait_btn_text"

  static let history_dashboard = "history_dashboard"
  
  static let lcd_alwaysOn = "lcd_alwaysOn"
  
  //번호판 인식강화
  static let plate_restore_info_title = "plate_restore_info_title"
  //번호판 인식강화 Context
  static let plate_restore_info_message = "plate_restore_info_message"
  
  static let driving_record = "driving_record"
  
  //MARK: - Bottom Navigation (아래에 없는 것들은 기존에 있어서 추가 안됨)
  //Home
  static let menu_home_title = "menu_home_title"
  //Live
  static let menu_live_title = "menu_live_title"
  //fileView
  static let fileviewer_title = "fileviewer_title"
  //My library
  static let my_library_title = "my_library_title"
  //More
  static let menu_more_title = "menu_more_title"
  //setting_update
  static let setting_update = "setting_update"
  
  static let hdr_subtitle = "hdr_subtitle"
  
  static let restore_area_popup = "restore_area_popup"
  //다시 표시 안함
  static let close_popup = "close_popup"
  
  //Wi-Fi connection failed.
  static let manual_connect_error = "manual_connect_error"
  
  static let ai_privacy_popup = "ai_privacy_popup"
  
  //This feature is optimized to work ...
  static let ai_resoration_popup = "ai_resoration_popup"
  
  //주차 녹화 없이 LCD가 꺼집니다.
  static let lcd_popup = "lcd_popup"
  
  //Non-North American...(국문 X)
  static let restore_start_popup = "restore_start_popup"

  // 보호된 파일이 선택 되어 있습니다.
  static let select_protect_file_txt = "select_protect_file_txt"

  // 보호된 파일은 삭제할 수 없습니다.
  static let delete_protect_file_txt = "delete_protect_file_txt"

  //  ICE, Hybrid, EV 등 기재된 차종은 참고용 입니다.
  static let setting_vehicle_type = "setting_vehicle_type"
  
  //주행 전용 메모리 할당 설정은 주차 녹화를 지원하지 않습니다.
  static let setting_memory_warning_popup = "setting_memory_warning_popup"
  
  //Always on을 선택하더라도 LCD 보호를 위해 기기가 적정온도를 넘어가면 LCD가 꺼집니다.
  static let lcd_always_on_alert_message = "lcd_always_on_alert_message"
  
  //WiFi 연결을 위해 블랙박스 화면에서 "Yes" 를 선택해주세요.
  static let wifi_verify_popup_message = "wifi_verify_popup_message"

  static let rotate_mirror_rear_camera_text_menu = "rotate_mirror_rear_camera_text_menu"
  //Rear Camera
  static let rotate_mirror_interior_camera_text_menu = "rotate_mirror_interior_camera_text_menu"

  // 많은 분들이 동시에 이용 중이라 서버가 잠시 바쁜 상태입니다. \n잠시 후 다시 시도해 주세요.
  static let ai_server_error_msg = "ai_server_error_msg"

  static let history_max_speed = "history_max_speed"
  
  static let home_firmware_check_subtitle = "home_firmware_check_subtitle"
  
  /// 기존 파일을 삭제하여 공간을 확보한 다음 다시 시도하십시오.
  static let error_diskfull_msg = "error_diskfull_msg"
  
  /// 다시 연결을 시도해 주세요.
  static let connect_again_msg = "connect_agaien_msg"

  static let quick_parking_mode_delay = "quick_parking_mode_delay"
  
  //Edit Connection
  static let dashcam_edit_title = "dashcam_edit_title"
  
  //Disconnect Wi-Fi
  static let home_disconnect_wifi = "home_disconnect_wifi"

  //EDIT
  static let home_edit_text = "home_edit_text"
  
  //채널 시트 값 Front - Interior
  static let channel_front = "channel_front"
  static let channel_rear = "channel_rear"
  static let channel_interior = "channel_interior"
  
  /// 다운로드 하시겠습니까?
  static let ask_download_file_msg = "ask_download_file_msg"
}
