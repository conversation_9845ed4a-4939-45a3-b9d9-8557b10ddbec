//
//  PhotoUtils.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 7/22/24.
//

import Foundation
import Photos
import UIKit

func MainAsync(after: TimeInterval = 0, handler: @escaping (() -> Void)) {
  if after > 0 {
    DispatchQueue.main.asyncAfter(deadline: .now() + after) {
      handler()
    }
  } else {
    if Thread.isMainThread {
      handler()
    } else {
      DispatchQueue.main.async {
        handler()
      }
    }
  }
}

public class PhotoUtils {

  static var status: PHAuthorizationStatus {
    switch PHPhotoLibrary.authorizationStatus() {
    case .authorized: return .authorized
    case .denied: return .denied
    case .notDetermined: return .notDetermined
    case .restricted: return .denied
    case .limited: return .authorized
    @unknown default: return .denied
    }
  }

  public class func request(completion: @escaping (PHAuthorizationStatus) -> Void) {
    PHPhotoLibrary.requestAuthorization({ finished in
      MainAsync { completion(finished) }
    })
  }

  public class func savePhoto(
    image: UIImage?,
    toAlbum titled: String,
    completion: @escaping (Error?) -> Void
  ) {
    fLogger.info("savePhoto album : \(titled)")
    guard let image = image else {
      fLogger.warning("savePhoto a image nil")
      return
    }
    getAlbum(title: titled) { (album) in
      guard let album = album else { return }
      PHPhotoLibrary.shared().performChanges(
        {
          let albumChangeRequest = PHAssetCollectionChangeRequest(for: album)
          let createAssetRequest = PHAssetChangeRequest.creationRequestForAsset(from: image)
          let photoPlaceholder = createAssetRequest.placeholderForCreatedAsset!
          albumChangeRequest?.addAssets([photoPlaceholder] as NSArray)
        },
        completionHandler: { success, error in
          if success {
            completion(nil)
          } else if let error = error {
            completion(error)
          } else {
            completion(CustomError.UnKnown)
          }
        })
    }
  }

  public class func saveVideo(
    url: URL?, toAlbum titled: String, completion: @escaping (Bool, Error?) -> Void
  ) {
    guard let url = url else {
      completion(false, NCError.init(title: "URL ERROR", description: "URL IS NIL", code: -1))
      return
    }
    fLogger.info("saveVideo url : \(url)")
    getAlbum(title: titled) { (album) in
      DispatchQueue.global(qos: .background).async {
        PHPhotoLibrary.shared().performChanges(
          {
            let newAssetRequest = PHAssetChangeRequest.creationRequestForAssetFromVideo(
              atFileURL: url)
            let assets =
              newAssetRequest?.placeholderForCreatedAsset
              .map { [$0] as NSArray } ?? NSArray()
            let albumChangeRequest = album.flatMap { PHAssetCollectionChangeRequest(for: $0) }
            albumChangeRequest?.addAssets(assets)
          },
          completionHandler: { (success, error) in
            MainAsync {
              completion(success, error)
            }
          })
      }
    }
  }

  public class func saveVideoToDefault(url: URL, completion: ((Bool, PHAsset?) -> Void)?) {
    let status = PHPhotoLibrary.authorizationStatus()

    if status == .denied || status == .restricted {
      // When permission is denied, we should guide the user to settings
      // This will be handled by the view controller using PermissionManager
      completion?(false, nil)
      return
    }

    var placeholderAsset: PHObjectPlaceholder?
    PHPhotoLibrary.shared().performChanges({
      let newAssetRequest = PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: url)
      placeholderAsset = newAssetRequest?.placeholderForCreatedAsset
    }) { success, _ in
      MainAsync {
        if success {
          let asset = self.getAsset(from: placeholderAsset?.localIdentifier)
          completion?(success, asset)
        } else {
          completion?(false, nil)
        }
      }
    }
  }

  private class func getAsset(from localIdentifier: String?) -> PHAsset? {
    guard let id = localIdentifier else {
      return nil
    }

    let result = PHAsset.fetchAssets(withLocalIdentifiers: [id], options: nil)
    return result.firstObject
  }

  public class func fetchAVAsset(
    forVideo asset: PHAsset,
    completion: @escaping (AVAsset?, [AnyHashable: Any]?) -> Void
  ) -> PHImageRequestID {
    let options = PHVideoRequestOptions()
    options.deliveryMode = .automatic
    options.isNetworkAccessAllowed = false

    return PHImageManager.default().requestAVAsset(forVideo: asset, options: options) {
      avAsset, _, info in
      MainAsync {
        completion(avAsset, info)
      }
    }
  }

  /// Create album with given title
  /// - Parameters:
  ///   - title: the title
  ///   - completionHandler: the completion handler
  private class func createAlbum(
    withTitle title: String, completionHandler: @escaping (PHAssetCollection?) -> Void
  ) {
    DispatchQueue.global(qos: .background).async {
      var placeholder: PHObjectPlaceholder?

      PHPhotoLibrary.shared().performChanges(
        {
          let createAlbumRequest = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(
            withTitle: title)
          placeholder = createAlbumRequest.placeholderForCreatedAssetCollection
        },
        completionHandler: { (created, error) in
          var album: PHAssetCollection?
          if created {
            let collectionFetchResult = placeholder.map {
              PHAssetCollection.fetchAssetCollections(
                withLocalIdentifiers: [$0.localIdentifier], options: nil)
            }
            album = collectionFetchResult?.firstObject
          }
          MainAsync {
            completionHandler(album)
          }
        })
    }
  }

  /// Get album with given title
  /// - Parameters:
  ///   - title: the title
  ///   - completionHandler: the completion handler
  public class func getAlbum(
    title: String, completionHandler: @escaping (PHAssetCollection?) -> Void
  ) {
    DispatchQueue.global(qos: .background).async {
      let fetchOptions = PHFetchOptions()
      fetchOptions.predicate = NSPredicate(format: "title = %@", title)
      let collections = PHAssetCollection.fetchAssetCollections(
        with: .album, subtype: .any, options: fetchOptions)

      MainAsync {
        if let album = collections.firstObject {
          fLogger.info("Found album with title: \(title)")
          completionHandler(album)
        } else {
          fLogger.info("Album with title: \(title) not found. Creating new album.")
          createAlbum(
            withTitle: title,
            completionHandler: { (album) in
              completionHandler(album)
            })
        }
      }
    }
  }
}
