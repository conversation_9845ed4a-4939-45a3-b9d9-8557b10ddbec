//
//  HubFileNameGeneratorExamples.swift
//  Hub
//
//  Created by ncn on 2025/07/01.
//
//

import Foundation

#if DEBUG

/// Examples demonstrating how to use HubFileNameGenerator
public class HubFileNameGeneratorExamples {
  
  // MARK: - Basic Usage Examples
  
  /// Example: Generate a standard recording file name
  public static func basicRecordingExample() {
    tLogger.log("=== Basic Recording File Name Generation ===")
    
    // Generate a front camera information recording
    let frontVideoName = HubFileNameGenerator.generateInformationFileName(
      cameraChannel: .front,
      isVideo: true
    )
    tLogger.log("Front camera video: \(frontVideoName)")
    // Output: 20250701_120710_INF_FN.mp4

    // Generate a rear camera event recording
    let rearVideoName = HubFileNameGenerator.generateEventFileName(
      cameraChannel: .rear,
      isVideo: true
    )
    tLogger.log("Rear camera event: \(rearVideoName)")
    // Output: 20250701_120710_EVT_RN.mp4

    // Generate an image capture
    let baseName = "20250701_120710_FVT_FPB.mp4"
    let imageName = try! HubFileNameGenerator.generateImageCaptureFileName(
      baseFileName: baseName
    )
    tLogger.log("Image capture: \(imageName)")
    // Output: 20250701_120710_FVT_F.jpg
  }
  
  /// Example: Generate trimmed video file names
  public static func trimmedVideoExample() {
    tLogger.log("\n=== Trimmed Video File Name Generation ===")
    
    let originalFileName = "20250701_120710_INF_F_N.mp4"

    do {
      // Generate first trimmed version
      let trimmedName1 = try HubFileNameGenerator.generateTrimmedFileName(
        baseFileName: originalFileName
      )
      tLogger.log("First trim: \(trimmedName1)")
      // Output: 20250701_120710_INF_FT.mp4

      // Generate second trimmed version (with duplicate handling)
      let trimmedName2 = try HubFileNameGenerator.generateTrimmedFileName(
        baseFileName: originalFileName
      )
      tLogger.log("Second trim: \(trimmedName2)")
      // Output: 20250701_120710_INF_FT(2).mp4
      
    } catch {
      tLogger.log("Error generating trimmed file name: \(error)")
    }
  }
  
  /// Example: Generate AI service file names
  public static func aiServiceExample() {
    tLogger.log("\n=== AI Service File Name Generation ===")
    
    let testFileNames = [
      "20250703_120710_INF_F_N.mp4",
      "20250703_120710_INF_F_L.mp4",
      "20250703_120710_INF_FLO.jpg",
      "011_20250703_120710_INF_F_L.mp4"
    ]

    for originalFileName in testFileNames {
      do {
        // License plate restoration workflow
        tLogger.log("License Plate Restoration:")
        let licensePlateOriginal = try HubFileNameGenerator.generateLicensePlateFileName(
          baseFileName: originalFileName,
          stage: .original
        )
        tLogger.log("  Original capture: \(licensePlateOriginal)")
        // Output: 20250701_120710_INF_FLO.jpg
        
        let licensePlateCropped = try HubFileNameGenerator.generateLicensePlateFileName(
          baseFileName: originalFileName,
          stage: .beforeRestoration
        )
        tLogger.log("  Cropped image: \(licensePlateCropped)")
        // Output: 20250701_120710_INF_FLB.jpg
        
        let licensePlateRestored = try HubFileNameGenerator.generateLicensePlateFileName(
          baseFileName: originalFileName,
          stage: .afterRestoration
        )
        tLogger.log("  Restored image: \(licensePlateRestored)")
        // Output: 20250701_120710_INF_FLA.jpg
        
        // Privacy blur workflow
        tLogger.log("\nPrivacy Blur Service:")
        let privacyBlurInput = try HubFileNameGenerator.generatePrivacyBlurFileName(
          baseFileName: originalFileName,
          stage: .beforeProcessing
        )
        tLogger.log("  Input video: \(privacyBlurInput)")
        // Output: 20250701_120710_INF_FPB.mp4
        
        let privacyBlurOutput = try HubFileNameGenerator.generatePrivacyBlurFileName(
          baseFileName: originalFileName,
          stage: .afterProcessing
        )
        tLogger.log("  Output video: \(privacyBlurOutput)")
        // Output: 20250701_120710_INF_FPA.mp4
        
      } catch {
        tLogger.error("Error generating AI service file names: \(error)")
      }
    }
  }
  
  /// Example: Parse existing file names
  public static func fileParsingExample() {
    tLogger.log("\n=== File Name Parsing ===")
    
    let fileNames = [
      "20250701_120710_INF_F_N.mp4",
      "20250701_120710_EVT_RT(3).mp4",
      "20250701_120710_INF_ILO.jpg",
      "20250701_120710_INF_F_L.mp4",
      "001_20250702_134921_INF_F_N.jpg"
    ]
    
    for fileName in fileNames {
      do {
        let components = try HubFileNameGenerator.parseFileName(fileName)
        tLogger.log("File: \(fileName)")
        tLogger.log("  Recording Type: \(components.recordingType.description)")
        tLogger.log("  Camera Channel: \(components.cameraChannel.description)")
        tLogger.log("  File Suffix: \(components.fileSuffix.description)")
        tLogger.log("  Extension: \(components.fileExtension.rawValue)")
        if let duplicateNumber = components.duplicateNumber {
          tLogger.log("  Duplicate Number: \(duplicateNumber)")
        }
      } catch {
        tLogger.log("Failed to parse \(fileName): \(error)")
      }
    }
  }
  
  /// Example: Handle duplicate files in a directory
  public static func duplicateHandlingExample() {
    tLogger.log("\n=== Duplicate File Handling ===")
    
    // Simulate existing files in a directory
    let existingFiles = [
      "20250701_120710_INF_F_N.mp4",
      "20250701_120710_INF_F_N(2).mp4",
      "20250701_120710_INF_F_N(3).mp4"
    ]

    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: Date(),
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4
    )

    let uniqueFileName = HubFileNameGenerator.generateUniqueFileName(
      from: components
    )

    tLogger.log("Existing files:")
    existingFiles.forEach { tLogger.log("  \($0)") }
    tLogger.log("New unique file name: \(uniqueFileName)")
    // Output: 20250701_120710_INF_FN(4).mp4
  }
  
  /// Example: Get related files for a base recording
  public static func relatedFilesExample() {
    tLogger.log("\n=== Related Files Generation ===")
    
    let baseFileName = "20250701_120710_INF_F_N.mp4"

    do {
      let relatedFiles = try HubFileNameGenerator.getRelatedFileNames(
        for: baseFileName,
        availableSuffixes: [.normal, .trimmed, .privacyBlurBefore, .privacyBlurAfter]
      )

      tLogger.log("Related files for \(baseFileName):")
      for (suffix, fileName) in relatedFiles {
        tLogger.log("  \(suffix.description): \(fileName)")
      }
      
    } catch {
      tLogger.log("Error generating related files: \(error)")
    }
  }
  
  /// Example: Validate file names
  public static func validationExample() {
    tLogger.log("\n=== File Name Validation ===")
    
    let testFileNames = [
      "20250701_120710_INF_F_N.mp4",      // Valid
      "20250701_120710_EVT_RT(2).mp4",   // Valid with duplicate number
      "001_20250702_134921_INF_F_N.jpg",  //valid bookmark file
      "20250702_134921_PRK_F_L.jpg",     // valid park lock file
      "invalid_format.mp4",               // Invalid format
      "20250701_120710_INVALID_FN.mp4",  // Invalid recording type
      "20250701_120710_INF_XN.mp4"      // Invalid camera channel
    ]
    
    for fileName in testFileNames {
      let isValid = HubFileNameGenerator.isValidHubFileName(fileName)
      tLogger.log("\(fileName): \(isValid ? "✅ Valid" : "❌ Invalid")")
    }
  }
  
  /// Run all examples
  public static func runAllExamples() {
//    basicRecordingExample()
    trimmedVideoExample()
    aiServiceExample()
//    fileParsingExample()
//    duplicateHandlingExample()
//    relatedFilesExample()
//    validationExample()
  }
}

#endif
