//
//  FileManager.swift
//  Hub
//
//  Created by ncn on 2023/04/27.
//

import AVFoundation
import Gzip
import UIKit

//import ffmpegkit

extension FileManager {

  static func remove(file path: URL) {
    try? FileManager.default.removeItem(at: path)
  }

  static func remove(folder path: URL) {
    let fileList = loadFileAtFolder(path: path)
    fileList.forEach { filePath in
      try? FileManager.default.removeItem(at: filePath)
    }
  }

  static func rename(file path: URL, newName: String) {
    let newPath = path.deletingLastPathComponent().appendingPathComponent(newName)
    try? FileManager.default.moveItem(at: path, to: newPath)
  }

  static func write(folder atPath: URL) {
    if !FileManager.default.fileExists(atPath: atPath.path) {
      try? FileManager.default.createDirectory(
        atPath: atPath.path,
        withIntermediateDirectories: false,
        attributes: nil)
    }
  }

  static func writeToFile(data: Data?, atPath: URL, name: String, isSaveToPhoto: Bool = true) {
    guard let data = data else { return }
    if FileManager.default.fileExists(atPath: atPath.path) != true {
      try! FileManager.default.createDirectory(
        atPath: atPath.path,
        withIntermediateDirectories: false,
        attributes: nil)
    }

    let fileurl = atPath.appendingPathComponent(name)
    if FileManager.default.fileExists(atPath: fileurl.path) {
      if let fileHandle = FileHandle(forWritingAtPath: fileurl.path) {
        //                fileHandle.seekToEndOfFile()
        fileHandle.seek(toFileOffset: 0)
        fileHandle.write(data)
        fileHandle.closeFile()
      } else {
        fLogger.error("Can't open file to write.")
      }
    } else {
      do {
        try data.write(to: fileurl, options: .atomic)
      } catch {
        fLogger.error("Unable to write in new file.")
      }
    }

    fLogger.info("writeToFile path: \(fileurl)")

    if isSaveToPhoto == false {
      fLogger.info("isSaveToPhoto is false, so return.")
      return
    }
    
    let modelName = AppManager.shared.deviceInfo?.model ?? Current.modelName
    fLogger.info("saved photo app modelName: \(modelName)")
    if fileurl.pathExtension == "jpg" {
      let image = UIImage(data: data)
      PhotoUtils.savePhoto(image: image, toAlbum: modelName, completion: { error in
        guard let error = error else {
          aLogger.info("image photoapp saved: \(fileurl.absoluteString)")
          return
        }
        aLogger.error("error: \(error.localizedDescription)")
      })
    } else if fileurl.pathExtension == "mp4" {
      PhotoUtils.saveVideo(url: fileurl, toAlbum: modelName, completion:{ isSuccess, error in
        guard let error = error else {
          aLogger.info("video photoapp saved: \(fileurl.absoluteString)")
          return
        }
        aLogger.error("error: \(error.localizedDescription)")
      })
    }
  }

  static func ungz(data: Data, toName: String) {
    if data.isGzipped == true {
      do {
        let decompress: Data = try data.gunzipped()
        let path = UrlList.tempPath()
        let name = toName  //String(format: "%@.db", toName)
        FileManager.writeToFile(data: decompress, atPath: path!, name: name)
      } catch let err {
        Log.message(to: "\(err)")
      }
    }
  }
}

extension FileManager {
  static func loadFileAtFolder(path: URL?) -> [URL] {
    guard let path = path else { return [] }
    var fileURLs: [URL] = []
    let localFileManager = FileManager()
    let resourceKeys = Set<URLResourceKey>([.nameKey, .isDirectoryKey])
    let directoryEnumerator = localFileManager.enumerator(
      at: path,
      includingPropertiesForKeys: Array(resourceKeys),
      options: .skipsHiddenFiles)!
    for case let fileURL as URL in directoryEnumerator {
      guard let resourceValues = try? fileURL.resourceValues(forKeys: resourceKeys),
        let isDirectory = resourceValues.isDirectory,
        let name = resourceValues.name
      else {
        continue
      }

      if isDirectory {
        if name == "_extras" {
          directoryEnumerator.skipDescendants()
        }
      } else {
        if let decoded = fileURL.description.removingPercentEncoding {
          Log.message(to: "At file app: " + decoded)
        } else {
          Log.message(to: "At file app: " + fileURL.description)
        }
        fileURLs.append(fileURL)
      }
    }

    return fileURLs
  }
}

extension FileManager {
  @discardableResult
  static func copyItemWithUrl(url: URL, folder: String = "SD") -> URL? {
    let fromPath = url
    if let docDir = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
      let targetDir = docDir.appendingPathComponent(folder)
      let targetUrl = targetDir.appendingPathComponent(fromPath.lastPathComponent)
      let targetPath = targetUrl.path

      /* target folder */
      if FileManager.default.fileExists(atPath: targetDir.path) != true {
        do {
          try FileManager.default.createDirectory(
            atPath: targetDir.path,
            withIntermediateDirectories: true,
            attributes: nil)
        } catch {
          Log.error(to: error.localizedDescription)
        }
      }

      /* target file */
      do {
        if FileManager.default.fileExists(atPath: targetPath) {
          Log.message(to: "\(fromPath.lastPathComponent) exist file. So return.")
          return targetUrl
        }

        if FileManager.default.isWritableFile(atPath: fromPath.path) {
          try FileManager.default.copyItem(atPath: fromPath.path, toPath: targetPath)
          Log.message(
            to: "write to file at \(docDir.lastPathComponent), \(fromPath.lastPathComponent)")
          return targetUrl
        } else {
          Log.message(to: "Disable write file \(fromPath.lastPathComponent)")
        }
      } catch {
        Log.message(to: "error: \(error.localizedDescription) ")
      }
    }

    return nil
  }
}

extension FileManager {

  func writeSnapshot(image: UIImage, fileName: String? = nil, channel: Int? = nil) {
    guard let data = image.jpegData(compressionQuality: 1) ?? image.pngData() else {
      aLogger.error("Fail to convert data.")
      return
    }

    guard let path = UrlList.screenshotPath() else {
      aLogger.error("Fail to get path.")
      return
    }
    
    aLogger.info("orgin fileName: \(fileName ?? "")")
    var uniqueName: String = ""
    if let fileName = fileName {
      do {
        uniqueName = try HubFileNameGenerator.generateImageCaptureFileName(baseFileName: fileName)
      } catch {
        vLogger.error("writeSnapshnot exception")
      }
    } else {
      let date = Date().toString(
        format: "yyyyMMdd_HHmmss", timezone: TimeZone.current.identifier,
        locale: Locale.current.identifier)
      let tempName = "\(date).jpg"
      uniqueName = getUniqueName(fileName: tempName, path: path)
    }

    LKPopupView.popup.debugToast(hit: "\(uniqueName) saved")
    FileManager.writeToFile(data: data, atPath: path, name: uniqueName)
  }

  func getUniqueName(fileName: String, path: URL) -> String {
    let extensionName = fileName.components(separatedBy: ".").last ?? "jpg"

    let noExtensionName =
      fileName
      .replacingOccurrences(of: ".avi", with: "")
      .replacingOccurrences(of: ".mp4", with: "")
      .replacingOccurrences(of: ".png", with: "")
      .replacingOccurrences(of: ".jpg", with: "")
      .replacingOccurrences(of: ".jpeg", with: "")
      .replacingOccurrences(of: ".", with: "")

    var newFileName = noExtensionName + ".\(extensionName)"

    var uniqueFileURL = path.appendingPathComponent(newFileName)
    // 중복 파일 처리
    var index = 1
    while FileManager.default.fileExists(atPath: uniqueFileURL.path) {
      // 중복 파일이 이미 존재하는 경우, 새로운 파일 이름을 생성.
      if noExtensionName.contains("(") {
        let noBracketName = noExtensionName.components(separatedBy: "(").first ?? ""
        newFileName = noExtensionName + "(\(index)).\(extensionName)"
      } else {
        newFileName = noExtensionName + "(\(index)).\(extensionName)"
      }
      uniqueFileURL = path.appendingPathComponent(newFileName)
      index += 1
    }

    return newFileName
  }

  static func writeTempVodFile(data: Data?) -> URL? {
    guard let data = data else { return nil }
    guard let path = UrlList.vodTemp() else {
      Log.message(to: "Fail to get path.")
      return nil
    }
    if FileManager.loadFileAtFolder(path: path) != [] {
      FileManager.remove(folder: path)
    }
    let name = "tempVOD.mp4"
    FileManager.writeToFile(data: data, atPath: path, name: name, isSaveToPhoto: false)
    guard let aviFullPath = FileManager.loadFileAtFolder(path: path).first else {
      return nil
    }
    return aviFullPath
  }

  static func encodeVideo(channelCount: Int, aviFullPath: URL?) -> Bool {
    guard let aviFullPath = aviFullPath else { return false }

    var isAllComplete: [String: Bool] = [:]
    for channel in 0..<channelCount {
      let state = FFMpegUtils.encodeToTempMP4(aviFullPath: aviFullPath, channel: channel)
      switch state {
      case .completed:
        Log.message(to: "completed")
        isAllComplete.updateValue(true, forKey: "ch_\(channel)_end")
      default:
        Log.message(to: "fail")
        isAllComplete.updateValue(false, forKey: "ch_\(channel)_end")
      }
    }

    Log.message(
      to: "isAllComplete.values.allSatisfy \(isAllComplete.values.allSatisfy({ $0 == true }))"
    )

    if isAllComplete.values.allSatisfy({ $0 == true }) {
      FileManager.remove(file: aviFullPath)
      return true
    } else {
      return false
    }

  }

  static func saveImageCache(cacheName: String, image: UIImage?) {
    guard let cachePath = UrlList.imageCachePath()?.appendingPathComponent(cacheName).path else {
      Log.warning(to: "Cache Save Fail")
      return
    }

    if !FileManager.default.fileExists(atPath: cachePath) {
      FileManager.default.createFile(
        atPath: cachePath,
        contents: image?.jpegData(compressionQuality: 0.1),
        attributes: nil
      )
    }
  }

  static func checkImageCache(cacheName: String?) -> UIImage? {
    guard let cacheName = cacheName else {
      Log.warning(to: "Cache Name nil")
      return nil
    }
    guard let cacheUrl = UrlList.imageCachePath()?.appendingPathComponent(cacheName) else {
      Log.warning(to: "Cache Call Fail")
      return nil
    }
    if FileManager.default.fileExists(atPath: cacheUrl.path) {
      guard let imageData = try? Data(contentsOf: cacheUrl) else {
        Log.warning(to: "Cache Url to Data Convert Fail")
        return nil
      }
      guard let image = UIImage(data: imageData) else {
        Log.warning(to: "Data to UIImage Convert Fail")
        return nil
      }
      return image
    }
    return nil
  }
}
