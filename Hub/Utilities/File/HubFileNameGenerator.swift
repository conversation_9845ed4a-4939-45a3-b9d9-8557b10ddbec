//
//  HubFileNameGenerator.swift
//  Hub
//
//  Created by ncn on 2025/07/01.
//

import Foundation

/// Standardized file name generator for dashcam/hub application
/// Generates file names following the format: YYYYMMDD_HHMMSS_[TYPE]_[CHANNEL]_[SUFFIX].[EXTENSION]
public class HubFileNameGenerator {
  
  // MARK: - Thread Safety
  private static let generationQueue = DispatchQueue(label: "com.hub.filename.generation", qos: .userInitiated)
  private static let duplicateCheckLock = NSLock()
  
  // MARK: - Type Definitions
  
  /// Recording type prefixes
  public enum RecordingType: String, CaseIterable {
    case information = "INF"  // Information/Normal recording
    case event = "EVT"        // Event recording
    case park = "PRK"        // Parking recording
    case parkEvent = "PVT"        // Parking recording
    case manual = "USR"          // User recording
    
    var description: String {
      switch self {
      case .information: return "Information/Normal recording"
      case .event: return "Event recording"
      case .park: return "Parking recording"
      case .parkEvent: return "Park Event recording"
      case .manual: return "User recording"
      }
    }
  }
  
  /// Camera channel indicators
  public enum CameraChannel: String, CaseIterable {
    case front = "F"     // Front camera
    case rear = "R"      // Rear camera
    case `internal` = "I"  // Internal camera
    
    var description: String {
      switch self {
      case .front: return "Front camera"
      case .rear: return "Rear camera"
      case .internal: return "Internal camera"
      }
    }
  }
  
  /// File suffix codes
  public enum FileSuffix: String, CaseIterable {
    case none = ""                     // No suffix (original file)
    case normal = "_N"                    // Normal/Original file
    case lock = "_L"                    // Lock  file
    case trimmed = "T"                   // Trimmed video file
    case licensePlateOriginal = "LO"     // License plate capture - Original
    case licensePlateBefore = "LB"       // License plate capture - Before restoration/cropped
    case licensePlateAfter = "LA"        // License plate capture - After restoration/result
    case privacyBlurBefore = "PB"        // Privacy blur service - Before processing/trimmed
    case privacyBlurAfter = "PA"         // Privacy blur service - After processing/result
    
    var description: String {
      switch self {
      case .none: return "No suffix"
      case .normal: return "Normal/Original file"
      case .lock: return "Lock file"
      case .trimmed: return "Trimmed video file"
      case .licensePlateOriginal: return "License plate capture - Original"
      case .licensePlateBefore: return "License plate capture - Before restoration/cropped"
      case .licensePlateAfter: return "License plate capture - After restoration/result"
      case .privacyBlurBefore: return "Privacy blur service - Before processing/trimmed"
      case .privacyBlurAfter: return "Privacy blur service - After processing/result"
      
      }
    }
  }
  
  /// File extension types
  public enum FileExtension: String, CaseIterable {
    case mp4 = "mp4"
    case jpg = "jpg"
    case png = "png"
    
    var isVideo: Bool {
      return self == .mp4
    }
    
    var isImage: Bool {
      return [.jpg, .png].contains(self)
    }
  }
  
  // MARK: - Error Types
  
  public enum FileNameError: Error, LocalizedError {
    case invalidDateFormat
    case invalidRecordingType
    case invalidCameraChannel
    case invalidFileSuffix
    case invalidFileExtension
    case emptyFileName
    case invalidFileNameFormat
    
    public var errorDescription: String? {
      switch self {
      case .invalidDateFormat: return "Invalid date format. Expected YYYYMMDD_HHMMSS"
      case .invalidRecordingType: return "Invalid recording type. Must be INF or EVT"
      case .invalidCameraChannel: return "Invalid camera channel. Must be F, R, or I"
      case .invalidFileSuffix: return "Invalid file suffix"
      case .invalidFileExtension: return "Invalid file extension"
      case .emptyFileName: return "File name cannot be empty"
      case .invalidFileNameFormat: return "File name does not match expected format"
      }
    }
  }
  
  // MARK: - File Name Components
  
  /// Represents the components of a hub file name
  public struct FileNameComponents {
    public let bookmarkIndex: Int?
    public let dateTime: Date
    public let recordingType: RecordingType
    public let cameraChannel: CameraChannel
    public let fileSuffix: FileSuffix
    public let fileExtension: FileExtension
    public let duplicateNumber: Int?
    
    public init(
      bookmarkIndex: Int? = nil,
      dateTime: Date = Date(),
      recordingType: RecordingType,
      cameraChannel: CameraChannel,
      fileSuffix: FileSuffix,
      fileExtension: FileExtension,
      duplicateNumber: Int? = nil
    ) {
      self.bookmarkIndex = bookmarkIndex
      self.dateTime = dateTime
      self.recordingType = recordingType
      self.cameraChannel = cameraChannel
      self.fileSuffix = fileSuffix
      self.fileExtension = fileExtension
      self.duplicateNumber = duplicateNumber
    }
  }
  
  // MARK: - Date Formatter
  
  private static let dateFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyyMMdd_HHmmss"
    formatter.timeZone = TimeZone.current
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
  }()
  
  private static let dateParsingFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyyMMdd_HHmmss"
    formatter.timeZone = TimeZone.current
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
  }()
  
  // MARK: - Public Methods
  
  /// Generates a base file name with the specified components
  /// - Parameters:
  ///   - dateTime: The date and time for the file (defaults to current time)
  ///   - recordingType: The type of recording (INF or EVT)
  ///   - cameraChannel: The camera channel (F, R, or I)
  ///   - fileSuffix: The file suffix code
  ///   - fileExtension: The file extension
  /// - Returns: Generated file name string
  /// - Example: `generateFileName(recordingType: .information, cameraChannel: .front, fileSuffix: .normal, fileExtension: .mp4)`
  ///   Returns: `"20250701_120710_INF_F_N.mp4"`, `"20250701_120710_INF_FT.mp4"`
  public static func generateFileName(
    bookmarkIndex: Int? = nil,
    dateTime: Date = Date(),
    recordingType: RecordingType,
    cameraChannel: CameraChannel,
    fileSuffix: FileSuffix,
    fileExtension: FileExtension
  ) -> String {
    let dateTimeString = dateFormatter.string(from: dateTime)
    
    if let index = bookmarkIndex {
      let indexString = String(format: "%03d", index)
      return "\(indexString)_\(dateTimeString)_\(recordingType.rawValue)_\(cameraChannel.rawValue)\(fileSuffix.rawValue).\(fileExtension.rawValue)"
    }
    return "\(dateTimeString)_\(recordingType.rawValue)_\(cameraChannel.rawValue)\(fileSuffix.rawValue).\(fileExtension.rawValue)"
  }
  
  /// Generates a file name from components
  /// - Parameter components: The file name components
  /// - Returns: Generated file name string
  public static func generateFileName(from components: FileNameComponents) -> String {
    let baseFileName = generateFileName(
      bookmarkIndex: components.bookmarkIndex,
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: components.fileSuffix,
      fileExtension: components.fileExtension
    )
    
    if let duplicateNumber = components.duplicateNumber {
      let nameWithoutExtension = baseFileName.replacingOccurrences(of: ".\(components.fileExtension.rawValue)", with: "")
      return "\(nameWithoutExtension)(\(duplicateNumber)).\(components.fileExtension.rawValue)"
    }
    
    return baseFileName
  }

  /// Generates a trimmed video file name with automatic numbering
  /// - Parameters:
  ///   - baseFileName: The original file name or base components
  /// - Returns: Trimmed file name with appropriate numbering
  /// - Example: `generateTrimmedFileName(baseFileName: "20250701_120710_INF_F_N.mp4")`
  ///   Returns: `"20250701_120710_INF_FT.mp4"` or `"20250701_120710_INF_F_T(2).mp4"` if duplicate exists
  public static func generateTrimmedFileName(
    baseFileName: String
  ) throws -> String {
    let components = try parseFileName(baseFileName)
    let trimmedComponents = FileNameComponents(
      bookmarkIndex: components.bookmarkIndex,
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: .trimmed,
      fileExtension: components.fileExtension
    )

    return generateUniqueFileName(from: trimmedComponents)
  }

  public static func generateLiveFileName(
    channel: Int
  ) -> String {
    
    let type: RecordingType = AppManager.shared.initInfo?.driveMode == 0 ? .information : .park
    let channel: HubFileNameGenerator.CameraChannel = channel == 1 ? .front : (channel == 2 ? .rear : .internal)
    let components = FileNameComponents(
      recordingType: type,
      cameraChannel: channel,
      fileSuffix: .none,
      fileExtension: .jpg
    )
    return generateUniqueFileName(from: components)
  }

  public static func generatePushResultFileName(
    baseFileName: String
  ) throws -> String {
    let components = try parseFileName(baseFileName)
    var suffix: FileSuffix
    if components.fileExtension.isImage {
      suffix = .licensePlateAfter
    } else {
      suffix = .privacyBlurAfter
    }
      
    let resultComponents = FileNameComponents(
      bookmarkIndex: components.bookmarkIndex,
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: suffix,
      fileExtension: components.fileExtension
    )

    return generateUniqueFileName(from: resultComponents)
  }
  /// Generates an image capture file name with automatic numbering
  /// - Parameters:
  ///   - dateTime: The date and time for the capture
  ///   - recordingType: The type of recording
  ///   - cameraChannel: The camera channel
  ///   - existingFiles: Array of existing file names to check for duplicates
  /// - Returns: Image file name with appropriate numbering
  /// - Example: `generateImageCaptureFileName(recordingType: .information, cameraChannel: .front)`
  ///   Returns: `"20250701_120710_INF_F_N.jpg"` or `"20250701_120710_INF_F_N(2).jpg"` if duplicate exists
  public static func generateImageCaptureFileName(
    baseFileName: String,
  ) throws -> String {
    let components = try parseFileName(baseFileName)
    
    let captureComponents = FileNameComponents(
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: .none,
      fileExtension: .jpg
    )

    return generateUniqueFileName(from: captureComponents)
  }

  /// Generates license plate restoration file names
  /// - Parameters:
  ///   - baseFileName: The original file name
  ///   - stage: The processing stage (original, before, after)
  ///   - existingFiles: Array of existing file names to check for duplicates
  /// - Returns: License plate file name
  public static func generateLicensePlateFileName(
    baseFileName: String,
    stage: LicensePlateStage,
    existingFiles: [String] = []
  ) throws -> String {
    let components = try parseFileName(baseFileName)
    let suffix: FileSuffix

    switch stage {
    case .original:
      suffix = .licensePlateOriginal
    case .beforeRestoration:
      suffix = .licensePlateBefore
    case .afterRestoration:
      suffix = .licensePlateAfter
    }

    let licensePlateComponents = FileNameComponents(
      bookmarkIndex: components.bookmarkIndex,
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: suffix,
      fileExtension: .jpg
    )

    return generateUniqueFileName(from: licensePlateComponents)
  }

  /// Generates privacy blur service file names
  /// - Parameters:
  ///   - baseFileName: The original file name
  ///   - stage: The processing stage (before or after)
  ///   - existingFiles: Array of existing file names to check for duplicates
  /// - Returns: Privacy blur file name
  public static func generatePrivacyBlurFileName(
    baseFileName: String,
    stage: PrivacyBlurStage,
    existingFiles: [String] = []
  ) throws -> String {
    let components = try parseFileName(baseFileName)
    let suffix: FileSuffix = stage == .beforeProcessing ? .privacyBlurBefore : .privacyBlurAfter

    let privacyBlurComponents = FileNameComponents(
      bookmarkIndex: components.bookmarkIndex,
      dateTime: components.dateTime,
      recordingType: components.recordingType,
      cameraChannel: components.cameraChannel,
      fileSuffix: suffix,
      fileExtension: components.fileExtension
    )

    return generateUniqueFileName(from: privacyBlurComponents)
  }

  // MARK: - Processing Stages

  public enum LicensePlateStage {
    case original           // LO - Original capture
    case beforeRestoration  // LB - Before restoration/cropped
    case afterRestoration   // LA - After restoration/result
  }

  public enum PrivacyBlurStage {
    case beforeProcessing   // PB - Before processing/trimmed
    case afterProcessing    // PA - After processing/result
  }

  // MARK: - Duplicate Handling and Validation

  /// Generates a unique file name by checking for duplicates and adding numbering
  /// - Parameters:
  ///   - components: The file name components
  ///   - existingFiles: Array of existing file names to check against
  /// - Returns: Unique file name with numbering if necessary
  public static func generateUniqueFileName(
    from components: FileNameComponents
  ) -> String {
    return generationQueue.sync {
      duplicateCheckLock.lock()
      defer { duplicateCheckLock.unlock() }

      let baseFileName = generateFileName(from: components)

      // If no duplicates, return the base name
      guard let saveFolderPath = components.fileExtension.isVideo ? UrlList.downloadedPath() : UrlList.screenshotPath() else {
        fLogger.error("Failed to get save folder path")
        return baseFileName
      }
      
      let baseFileURL = saveFolderPath.appendingPathComponent(baseFileName)
      if !FileManager.default.fileExists(atPath: baseFileURL.path) {
        return baseFileName
      }

      // Find the next available number
      var duplicateNumber = 1
      var candidateFileName: String
      var candidateFilePath: String = ""
      
      repeat {
        let componentsWithNumber = FileNameComponents(
          bookmarkIndex: components.bookmarkIndex,
          dateTime: components.dateTime,
          recordingType: components.recordingType,
          cameraChannel: components.cameraChannel,
          fileSuffix: components.fileSuffix,
          fileExtension: components.fileExtension,
          duplicateNumber: duplicateNumber
        )
        candidateFileName = generateFileName(from: componentsWithNumber)
        candidateFilePath = saveFolderPath.appendingPathComponent(candidateFileName).path
        duplicateNumber += 1
      } while FileManager.default.fileExists(atPath: candidateFilePath)

      return candidateFileName
    }
  }

  /// Generates a unique file name by checking against files in a directory
  /// - Parameters:
  ///   - components: The file name components
  ///   - directoryPath: The directory path to check for existing files
  /// - Returns: Unique file name with numbering if necessary
  public static func generateUniqueFileName(
    from components: FileNameComponents,
    inDirectory directoryPath: URL
  ) -> String {
//    let existingFiles = getExistingFileNames(in: directoryPath)
    return generateUniqueFileName(from: components)
  }

  /// Gets existing file names in a directory
  /// - Parameter directoryPath: The directory path to scan
  /// - Returns: Array of file names in the directory
  private static func getExistingFileNames(in directoryPath: URL) -> [String] {
    do {
      let fileURLs = try FileManager.default.contentsOfDirectory(
        at: directoryPath,
        includingPropertiesForKeys: [.nameKey],
        options: .skipsHiddenFiles
      )
      return fileURLs.map { $0.lastPathComponent }
    } catch {
      fLogger.warning("Failed to read directory contents: \(error.localizedDescription)")
      return []
    }
  }

  /// Validates file name components
  /// - Parameter components: The components to validate
  /// - Throws: FileNameError if any component is invalid
  public static func validateComponents(_ components: FileNameComponents) throws {
    // Date validation is implicit since Date type is used

    // Validate that the combination makes sense
    if components.fileSuffix.description.contains("License plate") && !components.fileExtension.isImage {
      throw FileNameError.invalidFileExtension
    }

    if components.fileSuffix == .trimmed && !components.fileExtension.isVideo {
      throw FileNameError.invalidFileExtension
    }
  }

  // MARK: - File Name Parsing

  /// Parses an existing file name to extract its components
  /// - Parameter fileName: The file name to parse
  /// - Returns: FileNameComponents extracted from the file name
  /// - Throws: FileNameError if the file name format is invalid
  /// - Example: `parseFileName("20250701_120710_INF_F_N.mp4")`
  ///   Returns: FileNameComponents with appropriate values
  public static func parseFileName(_ fileName: String) throws -> FileNameComponents {
    guard !fileName.isEmpty else {
      throw FileNameError.emptyFileName
    }

    // Remove file extension
    let nameWithoutExtension: String
    let fileExtension: FileExtension

    if let lastDotIndex = fileName.lastIndex(of: ".") {
      let extensionString = String(fileName[fileName.index(after: lastDotIndex)...])
      guard let ext = FileExtension(rawValue: extensionString.lowercased()) else {
        throw FileNameError.invalidFileExtension
      }
      fileExtension = ext
      nameWithoutExtension = String(fileName[..<lastDotIndex])
    } else {
      throw FileNameError.invalidFileNameFormat
    }

    // Check for duplicate number pattern like (2), (3), etc.
    let duplicateNumber: Int?
    let baseNameWithoutNumber: String

    let duplicatePattern = #"\((\d+)\)$"#
    if let regex = try? NSRegularExpression(pattern: duplicatePattern),
       let match = regex.firstMatch(in: nameWithoutExtension, range: NSRange(nameWithoutExtension.startIndex..., in: nameWithoutExtension)) {
      let numberRange = Range(match.range(at: 1), in: nameWithoutExtension)!
      duplicateNumber = Int(String(nameWithoutExtension[numberRange]))
      baseNameWithoutNumber = String(nameWithoutExtension[..<nameWithoutExtension.index(nameWithoutExtension.endIndex, offsetBy: -match.range.length)])
    } else {
      duplicateNumber = nil
      baseNameWithoutNumber = nameWithoutExtension
    }

    // Split the base name into components
    let components = baseNameWithoutNumber.split(separator: "_")
    guard components.count > 2 && components.count < 7 else {
      throw FileNameError.invalidFileNameFormat
    }

    // parse bookmark index if present
    var bookmarkIndex: Int? = nil
    var dateTimeOffset: Int = 0
    if components.count > 5 && components[0].count == 3 {
      bookmarkIndex = Int(components[0])
      dateTimeOffset = 1
    }
    // Parse date and time
    let dateTimeString = "\(components[dateTimeOffset])_\(components[dateTimeOffset + 1])"
    guard let dateTime = dateParsingFormatter.date(from: dateTimeString) else {
      throw FileNameError.invalidDateFormat
    }

    // Parse recording type
    guard let recordingType = RecordingType(rawValue: String(components[dateTimeOffset + 2])) else {
      throw FileNameError.invalidRecordingType
    }
    

    // Parse camera channel and file suffix (combined in components[3])
    let channelSuffixString = String(components[dateTimeOffset + 3])

    // Extract camera channel (first character)
    let channelString = String(channelSuffixString.prefix(1))
    guard let cameraChannel = CameraChannel(rawValue: channelString) else {
      throw FileNameError.invalidCameraChannel
    }

    // Extract file suffix (remaining characters)
    let suffixString = String(channelSuffixString.dropFirst())
    guard let fileSuffix = FileSuffix(rawValue: suffixString) else {
      throw FileNameError.invalidFileSuffix
    }

    let result = FileNameComponents(
      bookmarkIndex: bookmarkIndex,
      dateTime: dateTime,
      recordingType: recordingType,
      cameraChannel: cameraChannel,
      fileSuffix: fileSuffix,
      fileExtension: fileExtension,
      duplicateNumber: duplicateNumber
    )

    try validateComponents(result)
    return result
  }

  /// Checks if a file name follows the hub naming convention
  /// - Parameter fileName: The file name to check
  /// - Returns: True if the file name is valid, false otherwise
  public static func isValidHubFileName(_ fileName: String) -> Bool {
    do {
      _ = try parseFileName(fileName)
      return true
    } catch {
      return false
    }
  }

  /// Extracts the base file name without duplicate numbering
  /// - Parameter fileName: The file name to process
  /// - Returns: Base file name without (2), (3), etc. numbering
  /// - Example: `getBaseFileName("20250701_120710_INF_F_N(2).mp4")` returns `"20250701_120710_INF_F_N.mp4"`
  public static func getBaseFileName(_ fileName: String) -> String {
    do {
      let components = try parseFileName(fileName)
      return generateFileName(
        dateTime: components.dateTime,
        recordingType: components.recordingType,
        cameraChannel: components.cameraChannel,
        fileSuffix: components.fileSuffix,
        fileExtension: components.fileExtension
      )
    } catch {
      return fileName
    }
  }

  // MARK: - Convenience Methods

  /// Generates a standard recording file name for current time
  /// - Parameters:
  ///   - recordingType: The type of recording
  ///   - cameraChannel: The camera channel
  ///   - isVideo: Whether this is a video file (true) or image file (false)
  /// - Returns: Standard file name for recording
  public static func generateRecordingFileName(
    recordingType: RecordingType,
    cameraChannel: CameraChannel,
    isVideo: Bool = true
  ) -> String {
    return generateFileName(
      recordingType: recordingType,
      cameraChannel: cameraChannel,
      fileSuffix: .normal,
      fileExtension: isVideo ? .mp4 : .jpg
    )
  }

  /// Generates an event recording file name for current time
  /// - Parameters:
  ///   - cameraChannel: The camera channel
  ///   - isVideo: Whether this is a video file (true) or image file (false)
  /// - Returns: Event recording file name
  public static func generateEventFileName(
    cameraChannel: CameraChannel,
    isVideo: Bool = true
  ) -> String {
    return generateRecordingFileName(
      recordingType: .event,
      cameraChannel: cameraChannel,
      isVideo: isVideo
    )
  }

  /// Generates an information recording file name for current time
  /// - Parameters:
  ///   - cameraChannel: The camera channel
  ///   - isVideo: Whether this is a video file (true) or image file (false)
  /// - Returns: Information recording file name
  public static func generateInformationFileName(
    cameraChannel: CameraChannel,
    isVideo: Bool = true
  ) -> String {
    return generateRecordingFileName(
      recordingType: .information,
      cameraChannel: cameraChannel,
      isVideo: isVideo
    )
  }

  /// Gets the duplicate number from a file name
  /// - Parameter fileName: The file name to check
  /// - Returns: The duplicate number if present, nil otherwise
  /// - Example: `getDuplicateNumber("file(3).mp4")` returns `3`
  public static func getDuplicateNumber(from fileName: String) -> Int? {
    do {
      let components = try parseFileName(fileName)
      return components.duplicateNumber
    } catch {
      return nil
    }
  }

  /// Checks if two file names represent the same base file (ignoring duplicate numbers)
  /// - Parameters:
  ///   - fileName1: First file name
  ///   - fileName2: Second file name
  /// - Returns: True if they represent the same base file
  public static func isSameBaseFile(_ fileName1: String, _ fileName2: String) -> Bool {
    return getBaseFileName(fileName1) == getBaseFileName(fileName2)
  }

  /// Gets all related file names for a base file (different suffixes, same base info)
  /// - Parameters:
  ///   - baseFileName: The base file name
  ///   - availableSuffixes: The suffixes to generate names for
  /// - Returns: Dictionary mapping suffix to file name
  public static func getRelatedFileNames(
    for baseFileName: String,
    availableSuffixes: [FileSuffix] = FileSuffix.allCases
  ) throws -> [FileSuffix: String] {
    let components = try parseFileName(baseFileName)
    var relatedFiles: [FileSuffix: String] = [:]

    for suffix in availableSuffixes {
      let relatedComponents = FileNameComponents(
        bookmarkIndex: components.bookmarkIndex,
        dateTime: components.dateTime,
        recordingType: components.recordingType,
        cameraChannel: components.cameraChannel,
        fileSuffix: suffix,
        fileExtension: components.fileExtension
      )
      relatedFiles[suffix] = generateFileName(from: relatedComponents)
    }

    return relatedFiles
  }
}
