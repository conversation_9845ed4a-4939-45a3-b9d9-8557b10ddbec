//
//  HubFileNameGeneratorTest.swift
//  Hub
//
//  Created by ncn on 2025/07/01.
//
//  Quick test to verify the updated file name format
//

import Foundation

#if DEBUG

public class HubFileNameGeneratorTest {
  
  public static func runQuickTest() {
    tLogger.log("🧪 Quick Test - Updated File Name Format")
    
    // Test basic generation
    let frontVideo = HubFileNameGenerator.generateInformationFileName(
      cameraChannel: .front,
      isVideo: true
    )
    tLogger.log("✅ Front video: \(frontVideo)")
    // Expected: YYYYMMDD_HHMMSS_INF_FN.mp4
    
    let rearImage = HubFileNameGenerator.generateEventFileName(
      cameraChannel: .rear,
      isVideo: false
    )
    tLogger.log("✅ Rear image: \(rearImage)")
    // Expected: YYYYMMDD_HHMMSS_EVT_RN.jpg
    
    // Test trimmed files
    do {
      let originalFile = "20250701_120710_INF_F_N.mp4"
      let trimmedFile = try HubFileNameGenerator.generateTrimmedFileName(
        baseFileName: originalFile
      )
      tLogger.log("✅ Trimmed file: \(trimmedFile)")
      // Expected: 20250701_120710_INF_FT.mp4
      
      // Test AI service files
      let licensePlate = try HubFileNameGenerator.generateLicensePlateFileName(
        baseFileName: originalFile,
        stage: .original
      )
      tLogger.log("✅ License plate: \(licensePlate)")
      // Expected: 20250701_120710_INF_FLO.jpg
      
      let privacyBlur = try HubFileNameGenerator.generatePrivacyBlurFileName(
        baseFileName: originalFile,
        stage: .beforeProcessing
      )
      tLogger.log("✅ Privacy blur: \(privacyBlur)")
      // Expected: 20250701_120710_INF_FPB.mp4
      
      // Test parsing
      let testFileName = "20250701_120710_EVT_RT(3).mp4"
      let components = try HubFileNameGenerator.parseFileName(testFileName)
      tLogger.log("✅ Parsed file: \(testFileName)")
      tLogger.log("   Type: \(components.recordingType.rawValue)")
      tLogger.log("   Channel: \(components.cameraChannel.rawValue)")
      tLogger.log("   Suffix: \(components.fileSuffix.rawValue)")
      tLogger.log("   Duplicate: \(components.duplicateNumber ?? 0)")
      
      // Test validation
      let validFiles = [
        "20250701_120710_INF_FN.mp4",
        "20250701_120710_EVT_RT(2).mp4",
        "20250701_120710_INF_ILO.jpg"
      ]
      
      tLogger.log("\n📋 Validation Results:")
      for fileName in validFiles {
        let isValid = HubFileNameGenerator.isValidHubFileName(fileName)
        tLogger.log("   \(fileName): \(isValid ? "✅ Valid" : "❌ Invalid")")
      }
      
      tLogger.log("\n🎉 All tests passed! New format working correctly.")
      
    } catch {
      tLogger.log("❌ Test failed: \(error)")
    }
  }
}

// Extension for string repetition
private extension String {
  static func * (string: String, count: Int) -> String {
    return String(repeating: string, count: count)
  }
}

#endif
