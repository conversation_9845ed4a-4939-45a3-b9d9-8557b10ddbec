//
//  BleMessage.swift
//  Hub
//
//  Created by ncn on 2023/02/16.
//

import Foundation

let _ble_header = 4
let _ble_command = 1
let _ble_lenght = 2
let _ble_crc = 1

class BleMessage: NSObject {
  private var header: [CUnsignedChar]
  private var command: BLE.Command
  private var payload: String?
  var data: Data?

  init(command: BLE.Command, payload: String?) {
    header = AppManager.shared.NCBT
    self.command = command
    self.payload = payload
  }

  private func makeBody() -> ([CUnsignedChar], Int) {
    /* command: 1, lenght: 2, value: n, crs: 1 */
    var length = 3
    var len = UnicodeScalar(0)  // value length
    if let d = payload?.toData() {
      length = length + d.count + 1  // with crc
      len = UnicodeScalar(d.count)
    }

    var body = [CUnsignedChar](repeating: 0, count: length)

    // command id, length
    var com: CUnsignedChar = getCommand()
    body.withUnsafeMutableBytes { ptr in
      guard var dest = ptr.baseAddress else { return }
      memcpy(dest, &com, 1)
      dest = dest + 2
      memcpy(dest, &len, 1)
    }

    // data
    if let value = payload?.toData() as? NSData {
      body.withUnsafeMutableBytes { ptr in
        guard var dest = ptr.baseAddress else { return }
        dest = dest + 3
        memcpy(dest, value.bytes, value.count)
      }
    }

    // crs
    let data = Data(bytes: body, count: length)
    var xor: UInt8 = 0
    for i in 0..<data.count {
      xor = xor ^ body[i]
    }
    var xorScalar = UnicodeScalar(xor)

    body.withUnsafeMutableBytes { prt in
      let offset = prt.baseAddress! + (length - 1)
      memcpy(offset, &xorScalar, 1)
    }

    return (body, length)
  }

  private func getCommand() -> CUnsignedChar {
    switch command {
    case .uuid:
      return 0xA0
    case .check:
      return 0xA1
    case .button:
      return 0xA2
    case .ap:
      return 0xA3
    case .mode:
      return 0xA4
    case .sta:
      return 0xA5
    case .unkonwn:
      return 0x00
    }
  }
}

extension BleMessage {
  func make() {
    let ret = makeBody()
    let length = ret.1 + 4

    var buffer = [CUnsignedChar](repeating: 0, count: length)
    buffer.withUnsafeMutableBytes { dest in
      var offset = dest.baseAddress
      memcpy(offset, header, 4)
      offset = dest.baseAddress! + 4
      memcpy(offset, ret.0, ret.1)
    }
    self.data = NSData(bytes: buffer, length: length) as Data
  }
}
