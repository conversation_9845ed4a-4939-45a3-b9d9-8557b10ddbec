//
//  BLECentral.swift
//  Hub
//
//  Created by ncn on 2022/11/30.
//

import CoreBluetooth
import RxSwift
import UIKit

/*
 2001: peripheral discover error
 */

let _searchRssi: Int = 100

class BLECentral: NSObject {
  var centralManager: CBCentralManager?
  var connectedPeripheral: CBPeripheral?
  var writeCharacteristic: CBCharacteristic? /* write */

  var isScanning = false
  var scaneOption: [String: Any] = [
    CBCentralManagerScanOptionAllowDuplicatesKey: NSNumber(value: false)
  ]

  var scanedPeripherals: [DiscoverdModel] = []
  var scaneServices: [CBUUID]?
  var discoverServices: [CBUUID]?

  var scanHandler: (([DiscoverdModel]?, Error?) -> Void)?
  var connectHandler:
    ((Bool, Bool, Bool, Error?) -> Void)? /* isConnect, isWritable, isSetNoti, error */

  var didUpdateValueHandler: ((CBCharacteristic, Error?) -> Void)?

  var rxDiscovered = PublishSubject<[DiscoverdModel]>()
  var rxIsConnect = PublishSubject<Bool>()
  var rxIsWritable = PublishSubject<Bool>()
  var rxIsSetNoti = PublishSubject<Bool>()
  var rxState = PublishSubject<CBManagerState>()
  //var rxReciveData = PublishSubject<BLE.Response>()
  var rxError = PublishSubject<Error>() /* Delegate error */
  // error 메세지?

  var isConnect: Bool = false {
    didSet {
      rxIsConnect.onNext(isConnect)
    }
  }

  var isWritable: Bool = false {
    didSet {
      rxIsWritable.onNext(isWritable)
    }
  }

  var isSetNoti: Bool = false {
    didSet {
      rxIsSetNoti.onNext(isWritable)
    }
  }

  var token: NSKeyValueObservation?

  private var lastServices: [CBUUID]?
  private var targetPeripheral: CBPeripheral? = nil

  override init() {
    super.init()
    initCentralManager()
  }

  private func initCentralManager() {
    if centralManager == nil {
      self.centralManager = CBCentralManager(delegate: self, queue: nil)
    }
  }

  // scane
  @discardableResult
  private func start(services: [CBUUID]? = nil) -> Bool {
    initCentralManager()
    scanedPeripherals.removeAll()
    // targetPeripheral 체크 필요 여부
    if let central = centralManager,
      central.isScanning != true
    {
      if central.state == .poweredOn {
        isScanning = true
        self.lastServices = services
        Log.info(category: .Ble, to: "Ble scans with services: \(String(describing: services))")
        central.scanForPeripherals(withServices: services, options: scaneOption)
        return true
      } else {
        if central.isScanning == true {
          central.stopScan()
        }
      }
    }
    return false
  }

  private func restart() {
    stop()
    cleanPheralInfo()
    start(services: lastServices)
  }

  private func stop() {
    isScanning = false
    if let manager = centralManager {
      manager.stopScan()
    }
    //self.centralManager = nil
  }

  private func cleanPheralInfo() {
    if let peripheral = connectedPeripheral, let central = centralManager {
      central.cancelPeripheralConnection(peripheral)
    }
    self.writeCharacteristic = nil
    self.connectedPeripheral = nil
  }

  private func connectPeripheral(_ peripheral: CBPeripheral, options: [String: Any]? = nil) {
    if let central = centralManager {
      self.targetPeripheral = peripheral
      if central.isScanning == true {
        central.stopScan()
      }
      central.connect(peripheral, options: nil)
    }
  }
}

extension BLECentral {
  @discardableResult
  func startScane(services: [CBUUID]? = nil) -> Bool {
    return start(services: services)
  }

  func stopScane() {
    stop()
  }

  func resetBLE() {
    //reset()
    restart()
  }

  func reset() {
    scanedPeripherals.removeAll()
    stop()
    self.centralManager = nil
    cleanPheralInfo()
  }

  func close() {
    cleanPheralInfo()
  }

  func connect(peripheral: CBPeripheral, options: [String: Any]? = nil) {
    connectPeripheral(peripheral, options: options)
  }

  func writeData(data: Data) {
    if let peripheral = connectedPeripheral,
      let characteristic = writeCharacteristic
    {
      Log.info(category: .Ble, to: "send data on ble \(data.count)")
      peripheral.writeValue(data, for: characteristic, type: .withResponse)
    }
  }

  class func stateToString(_ state: CBManagerState) -> String {
    switch state {
    case .unknown:
      return "unknown"
    case .resetting:
      return "resetting"
    case .unsupported:
      return "unsupported"
    case .unauthorized:
      return "unauthorized"
    case .poweredOff:
      return "poweredOff"
    case .poweredOn:
      return "poweredOn"
    @unknown default:
      return "unknown unknowns"
    }
  }
}

// MARK: - CBCentralManager Delegate
extension BLECentral: CBCentralManagerDelegate {
  func centralManagerDidUpdateState(_ central: CBCentralManager) {
    Log.info(
      category: .Ble, to: "state: \(central.state) - \(BLECentral.stateToString(central.state)) ")
    isScanning = (central.state == .poweredOn)
    rxState.onNext(central.state)
  }

  func centralManager(_ central: CBCentralManager, willRestoreState dict: [String: Any]) {
    Log.message(to: "")
  }

  func containMacaddress(name: String) -> Bool {
    // check S1&98:03:CF:7F:CA:43
    return name.contains("&") && name.contains(":")
  }

  // MARK: - Scane perpheral

  /* Received Signal Strength Indicator */
  func centralManager(
    _ central: CBCentralManager,
    didDiscover peripheral: CBPeripheral,
    advertisementData: [String: Any], rssi RSSI: NSNumber
  ) {
    if abs(RSSI.intValue) <= _searchRssi
      && ((peripheral.name?.contains("S1")) == true)
    {
      if let dataLocalName = advertisementData["kCBAdvDataLocalName"] as? String,
        let peripheralName = peripheral.name
      {

        let name =
          containMacaddress(name: dataLocalName)
          ? dataLocalName : containMacaddress(name: peripheralName) ? peripheralName : dataLocalName

        hLogger.info(
          "ble name: \(name), dataLocalName: \(dataLocalName), peripheralName: \(peripheralName)")
        let temp = String(format: "%@", name)
        let trimString = temp.trimmingCharacters(in: ["\n"])
        let array = trimString.components(separatedBy: "_")
        if array.last != nil {
          let model = DiscoverdModel(
            uuid: peripheral.identifier.uuidString,
            peripheral: peripheral,
            advertisementData: advertisementData,
            rssi: RSSI,
            localName: trimString)
          if scanedPeripherals.appendIfUnique(model, check: { $0.uuid }) {  // and key로도 처리할것
            Log.info(
              category: .Ble,
              to: "Discover peripheral: \(peripheral.identifier), \n"
                + "name: \(String(describing: peripheral.name))"
                + "\n addvertisemnt data: \(advertisementData), \n rssi:\(RSSI)")
            Log.info(category: .Ble, to: "Scand peripheral count: \(scanedPeripherals.count)")

            if let handler = self.scanHandler {
              handler(self.scanedPeripherals, nil)
            }
            self.rxDiscovered.onNext(self.scanedPeripherals)
          }
        }
      }
    }
  }

  // MARK: peripheral connect
  func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
    Log.info(
      category: .Ble, to: "\(peripheral.identifier) - \(String(describing: peripheral.name))")

    if targetPeripheral == peripheral {
      self.connectedPeripheral = peripheral
      self.isConnect = true

      connectHandler?(true, isWritable, isSetNoti, nil)

      if let services = discoverServices {
        peripheral.delegate = self
        peripheral.discoverServices(services)
      } else {
        Log.warning(category: .Ble, to: "Not exist discover services.")
      }
    } else {
      Log.info(category: .Ble, to: "Connected to a different peripheral.")
    }
  }

  func centralManager(
    _ central: CBCentralManager,
    didFailToConnect peripheral: CBPeripheral, error: Error?
  ) {
    Log.error(category: .Ble, to: "didFailToConnect() error: \(String(describing: error))")
    if let e = error {
      rxError.onNext(e)
      if let handler = connectHandler {
        handler(false, false, false, e)
      }
    }

    if peripheral != targetPeripheral {
      Log.error(category: .Ble, to: "Error connecting to a different peripheral!")
      return
    }
    self.targetPeripheral = nil
    //reevaluateScanning(central)
    //start() // 정책
  }

  func centralManager(
    _ central: CBCentralManager,
    didDisconnectPeripheral peripheral: CBPeripheral,
    error: Error?
  ) {
    Log.error(category: .Ble, to: "Did disconnect error: \(String(describing: error))")
    if let e = error {
      rxError.onNext(e)
      if let handler = connectHandler {
        handler(false, false, false, e)
      }
    }
    // 실패 되면 다시 스캔?
    //start()
  }

  func centralManager(
    _ central: CBCentralManager,
    connectionEventDidOccur event: CBConnectionEvent,
    for peripheral: CBPeripheral
  ) {
    Log.message(to: "")
  }

  func centralManager(
    _ central: CBCentralManager, didUpdateANCSAuthorizationFor peripheral: CBPeripheral
  ) {
    Log.message(to: "")
  }
}

// MARK: - CBPeripheral Delegate
extension BLECentral: CBPeripheralDelegate {
  func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
    if let e = error {
      Log.error(category: .Ble, to: "Error discovering services: \(String(describing: error))")
      rxError.onNext(e)
    } else {
      Log.error(category: .Ble, to: "Discovered \(peripheral.services!.count) service(s)")
      for service in peripheral.services! {
        Log.info(category: .Ble, to: "* Service: \(service.uuid)")
        peripheral.discoverCharacteristics(nil, for: service)
      }

      if let count = peripheral.services?.count, count < 1 {
        let ncError = NCError(title: "Ble error", description: "Not founc services.", code: 2001)
        rxError.onNext(ncError)
      }
    }
  }

  func peripheral(
    _ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?
  ) {
    if let e = error {
      Log.error(
        category: .Ble, to: "Error discovering characteristics: \(String(describing: error))")
      rxError.onNext(e)
    } else {
      Log.info(category: .Ble, to: "Discovered \(service.characteristics!.count) characteristic(s)")
      for characteristic in service.characteristics! {
        Log.info(category: .Ble, to: "  * Characteristic: \(characteristic.uuid)")
        /* Notify */
        if characteristic.properties.contains(.notify) {
          //&& (characteristic.uuid.uuidString == _ble_notify_uuid) {
          Log.info(category: .Ble, to: ".notify prpoerty: \(characteristic.properties)")
          peripheral.setNotifyValue(true, for: characteristic)
          self.isSetNoti = true
          connectHandler?(isConnect, isWritable, true, nil)
        }

        /* Write */
        if characteristic.properties.contains(.write) {
          Log.info(category: .Ble, to: ".write prpoerty: \(characteristic.properties)")
          if characteristic.uuid.uuidString == _ble_write_uuid {
            self.writeCharacteristic = characteristic /* write */
            self.isWritable = true
            connectHandler?(isConnect, true, isSetNoti, nil)
          }
        }

        /* Read */
        if characteristic.properties.contains(.read) {
          Log.info(category: .Ble, to: ".read property: \(characteristic.properties)")
          peripheral.readValue(for: characteristic)
        }
      }
    }
  }

  func peripheral(
    _ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?
  ) {
    Log.info(
      category: .Ble,
      to: "Characteristic: \(characteristic), "
        + "Characteristic uuid: \(characteristic.uuid), "
        + "Value: \(String(describing: characteristic.value?.count)), "
        + "error: \(String(describing: error))")
    didUpdateValueHandler?(characteristic, error)
  }

  func peripheral(_ peripheral: CBPeripheral, didModifyServices invalidatedServices: [CBService]) {
    Log.info(
      category: .Ble,
      to: "peripheral: \(peripheral.identifier) \n invalidated : \(invalidatedServices)")
  }

  func peripheral(
    _ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?
  ) {
    Log.info(category: .Ble, to: "\(characteristic)")
  }
}
