//
//  AppManager.swift
//  Hub
//
//  Created by ncn on 2022/11/18.
//

import CoreLocation
import GoogleMaps
import RxSwift
import Security
import SystemConfiguration.CaptiveNetwork
import UIKit

/*
 * Define error code
 * 1000: WebSocket
 * 2000: Ble
 * 3000: Data base
 * 4000: QR Scane
 * 5000: Restful api, vueroid cloud
 * 6000: Signaling
 */

enum AppModeType: Int, CustomDebugStringConvertible {
  case wifi = 0
  case file
  
  var debugDescription: String {
    switch self {
    case .wifi:
      return "WiFi Mode"
    case .file:
      return "File Mode"
    }
  }
}


let _auto_login_value = "AutoLoginValue"
let _aws_region_value = "AwsRegionValue"

let _account_speed_unit = "AccountSpeedUnit"
let _account_temp_unit = "AccountTempUnit"

let _onboarding_show = "isOnboardingShow"

let _ble_service_uuid = "9d706e09-3774-4f65-ab0b-009ef39e3797"
let _ble_advertise_uuid = "094e95a4-355f-4963-a2af-27edc2ed2032"
let _ble_read_uuid = "d17f47ee-361a-4070-a308-d7e97828ff4c"
let _ble_write_uuid = "E02EF3B7-ABBE-45E2-AA64-D76E868634C9"

let _api_access_token: String = "apiAccessToken"
let _api_refresh_token: String = "apiRefreshToken"

let _adas_all_setting_value: String = "AdasAllSettingValue"

let _weather_api_key = "9939f4f88fb844655cd233d37e6bdf69"

let _timezone: [String] = [
  "Buenos Aires(GMT -3)",
  "São Paulo(GMT -3)",
  "Columbus, OH(GMT -5)",
  "Jacksonville, FL(GMT -5)",
  "New York(GMT -5)",
  "Philadelphia, PA(GMT -5)",
  "Chicago(GMT -6)",
  "Houston, TX(GMT -6)",
  "Mexico City(GMT -6)",
  "Phoenix, AZ(GMT -7)",
  "Los Angeles(GMT -8)",
  "San Diego, CA(GMT -8)",
  "San Francisco, CA(GMT -8)",
  "Seattle, WA(GMT -8)",
  "London(GMT 0)",
  "Berlin(GMT +1)",
  "Paris(GMT +1)",
  "Rome(GMT +1)",
  "Johannesburg(GMT +2)",
  "Moscow(GMT +3)",
  "Riyadh(GMT +3)", //20
  "Dubai, Abu Dhabi(GMT +4)",
  "New Delhi(GMT +5:30)",
  "Bangkok(GMT +7)",
  "Ho Chi Minh City(GMT +7)",
  "Jakarta(GMT +7)",
  "Beijing(GMT +8)",
  "Hong Kong(GMT +8)",
  "Kuala Lumpur(GMT +8)",
  "Perth(GMT +8)",
  "Singapore(GMT +8)", //30
  "Seoul(GMT +9)",
  "Tokyo(GMT +9)",
  "Adelaide(GMT +9:30)",
  "Sydney, Canberra(GMT +10)",
]

//let _timezone: [String] = [
//  "+12:00 Auckland, Wellington",
//  "+11:00 Magadan",
//  "+10:00 Melbourne, Sydney",
//  "+9:00 Seoul, Tokyo",
//  "+8:00 Hong Kong, Singapore",
//  "+7:00 Bangkok, Hanoi",
//  "+6:00 Astana",
//  "+5:30 Mumbai, New Delhi",
//  "+5:00 Ashgabat, Tashkent",
//  "+4:00 Abu Dhabi, Muscat",
//  "+3:00 Istanbul, Riyadh",
//  "+2:00 Cairo, Jerusalem",
//  "+1:00 Amsterdam, Berlin",
//  "+0:00 Dublin, London",
//  "-1:00 Cabo Verde",
//  "-2:00 South Georgia",
//  "-3:00 Buenos Aires",
//  "-4:00 Dominica",
//  "-5:00 New York",
//  "-6:00 Chicago",
//  "-7:00 Denver",
//  "-8:00 Los Angeles",
//  "-9:00 Alaska",
//  "-10:00 Hawaii",
//  "-11:00 Midway Is., Samoa",
//  "-12:00 International",
//]

class AppManager: NSObject {
  let NCBT: [CUnsignedChar] = [0x4e, 0x43, 0x42, 0x54]
  let plist_Configuration: String = "Configuration"  // debug, release 체크

  let userDefault_UserId: String = "userId"  // 유저 아이디
  let userDefault_connected_device = "connectedDevice"
  let userdefault_uuid = "appUUID"
  let NetworkConnectionMode: String = "networkConnection"  // wifi, cellura

  var isRelease: Bool
  var mode: AppModeType = .wifi

  var fcmToken: String?

  var rxUserInfo = PublishSubject<UainfoModel>()
  var user: UainfoModel? {
    didSet {
      if let info = self.user {
        self.rxUserInfo.onNext(info)
      }
    }
  }

  var rxDashcame = PublishSubject<HomeDashcamModel>()
  var dashcam: HomeDashcamModel? {
    didSet(oldVal) {
      if let target = self.dashcam,
        let obj = oldVal
      {
        self.dashcam = obj.merge(with: target)
        self.rxDashcame.onNext(self.dashcam!)
      }
    }
  }
  var dashcamWifiConfig: ConfigurationModel?

  var accessToken: String? {
    get {
      //      return getAccessToken()
      guard let token = loadUserDefault(key: _api_access_token) as? String else { return nil }
      return token
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: _api_access_token, value: v)
        //        setAccessToken(token: v)
      }
    }
  }

  var refreshToken: String? {
    get {
      guard let token = loadUserDefault(key: _api_refresh_token) as? String else { return nil }
      return token
      //      return getAccessToken(account: _api_refresh_token)
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: _api_refresh_token, value: v)
        //        setAccessToken(token: v, account: _api_refresh_token)
      }
    }
  }

  var autoLogin: Bool {
    get {
      guard let token = loadUserDefault(key: _auto_login_value) as? Bool else { return false }
      return token
    }
    set(newVal) {
      updateUserDefault(key: _auto_login_value, value: newVal)
    }
  }

  var awsRegion: String? {
    get {
      guard let region = loadUserDefault(key: _aws_region_value) as? String else { return nil }
      return region
    }
    set(newVal) {
      if let v = newVal {
        updateUserDefault(key: _aws_region_value, value: v)
      }
    }
  }

  var deviceInfo: InformationModel?

  var initInfo: GetInitInfoModel?

  var statusInfo: GetStatusModel?

  var commandSocket: WebSocketClient = {
    var socketUrl = ""
    //    #if DEVELOP || QA
    //      socketUrl = DevelopURL().commandSocketUrl
    //    #elseif RELEASE
    socketUrl = ReleaseURL().commandSocketUrl
    //    #endif
    let socket = WebSocketClient(url: socketUrl)
    return socket
  }()

  var hubSocket: HubWebSocketClient = {
    var socketUrl = ""
    //    #if DEVELOP || QA
    //      socketUrl = DevelopURL().commandSocketUrl
    //    #elseif RELEASE
    socketUrl = ReleaseURL().commandSocketUrl
    //    #endif
    let socket = HubWebSocketClient(
      url: URL(string: socketUrl)!,
      configuration: .init(pingInterval: 30, pingTryReconnectCountLimit: 5)
    )
    return socket
  }()

//  var cloudRegisterSocketUrl: WebSocketClient = {
//    var socketUrl = ""
//    //    #if DEVELOP || QA
//    //      socketUrl = DevelopURL().cloudRegisterSocketUrl
//    //    #elseif RELEASE
//    socketUrl = ReleaseURL().cloudRegisterSocketUrl
//    //    #endif
//    let socket = WebSocketClient(url: socketUrl)
//    return socket
//  }()
//
//  var cloudSocket: WebSocketClient = {
//    var socketUrl = ""
//    //    #if DEVELOP || QA
//    //      socketUrl = DevelopURL().cloudSocketUrl
//    //    #elseif RELEASE
//    socketUrl = ReleaseURL().cloudSocketUrl
//    //    #endif
//    let socket = WebSocketClient(url: socketUrl)
//    return socket
//  }()

  var rxlocationValue = PublishSubject<CLLocationCoordinate2D>()
  var locationLimit = 3
  var locationValue: CLLocationCoordinate2D?
  lazy var location: CLLocationManager = {
    let manager = CLLocationManager()
    manager.desiredAccuracy = kCLLocationAccuracyBest
    return manager
  }()

  static var appVersion: String {
    guard let dictionary = Bundle.main.infoDictionary,
      let version = dictionary["CFBundleShortVersionString"] as? String
    else { return "0.0.0" }
    return version
  }
  static var currentBuildNumber: String {
    if let info: [String: Any] = Bundle.main.infoDictionary,
      let buildNumber: String = info["CFBundleVersion"] as? String
    {
      return buildNumber
    }
    return "00"
  }

//  var accountSpeedUnit: String? {  // Y: Kilo / N:Mile
//    get {
//      return loadUserDefault(key: _account_speed_unit) as? String
//    }
//    set {
//      updateUserDefault(key: _account_speed_unit, value: newValue ?? "")
//    }
//  }
  var accountTempUnit: String? {  // Y: °C / N: °F
    get {
      return loadUserDefault(key: _account_temp_unit) as? String
    }
    set {
      updateUserDefault(key: _account_temp_unit, value: newValue ?? "")
    }
  }

  static let shared: AppManager = {
    let instance = AppManager()
    return instance
  }()

  override init() {
    //    #if DEVELOP || QA
    //      self.isRelease = false
    //    #elseif RELEASE
    self.isRelease = true
    //    #endif

    super.init()
  }
}

// MARK: - Application Status
extension AppManager {
  func getConnectedDevice() -> [BleItemModel] {
    if let obj = loadUserDefault(key: userDefault_connected_device) as? Data {
      let decoder = JSONDecoder()
      if var array = try? decoder.decode(Array<BleItemModel>.self, from: obj) {
        array.sort(by: { $0.date!.timeIntervalSinceNow > $1.date!.timeIntervalSinceNow })
        array.indices.forEach {
          array[$0].isDiscovered = false
        }
        return array
      } else {
        return [BleItemModel]()
      }
    } else {
      return [BleItemModel]()
    }
  }

  func appendConnectedDevice(obj: BleItemModel) {
    var array = getConnectedDevice()
    array.replaceIfExist(obj, check: { $0.mac })
    if let encoded = try? JSONEncoder().encode(array) {
      aLogger.info("Save connected device - \(array.count)")
      updateUserDefault(key: userDefault_connected_device, value: encoded)
    }
  }

  func deleteConnectedDevice(mac: String) {
    var array = getConnectedDevice()
    aLogger.info("delete mac: \(mac)")
    _ = array.map { aLogger.info("saved mac: \($0.mac ?? "")") }
    array.removeAll { $0.mac == mac }
    if let encoded = try? JSONEncoder().encode(array) {
      aLogger.info("Save connected device - \(array.count)")
      updateUserDefault(key: userDefault_connected_device, value: encoded)
    }
  }

  func getUUID() -> String? {
    if let obj = loadUserDefault(key: userdefault_uuid) as? Data {
      let decoder = JSONDecoder()
      if let ret = try? decoder.decode(UUID.self, from: obj) {
        return ret.uuidString
      }
    } else {
      let ret = UUID()
      DispatchQueue.main.async {
        if let encoded = try? JSONEncoder().encode(ret) {
          self.updateUserDefault(key: self.userdefault_uuid, value: encoded)
        }
      }
      return ret.uuidString
    }
    return nil
  }

  func getSSID() -> String? {
    var ssid: String?
    if let interfaces = CNCopySupportedInterfaces() as NSArray? {
      for interface in interfaces {
        if let interfaceInfo = CNCopyCurrentNetworkInfo(interface as! CFString) as NSDictionary? {
          ssid = interfaceInfo[kCNNetworkInfoKeySSID as String] as? String
          break
        }
      }
    }
    return ssid
  }

  func removeUserInfo() {
    self.removeUserDefaultKey(key: _api_access_token)
    self.removeUserDefaultKey(key: userDefault_UserId)
  }

  // MARK: - Token
  func setAccessToken(token: String, account: String = _api_access_token) {
    let data = token.toData()

    let query: [CFString: Any] = [
      kSecClass: kSecClassGenericPassword,
      kSecAttrAccount: account,
      kSecValueData: data!,
    ]
    let status = SecItemAdd(query as CFDictionary, nil)

    switch status {
    case errSecSuccess:
      break
    case errSecDuplicateItem:
      updateItemnKeyChain(value: data!, key: account)
      break
    default:
      break
    }
  }

  func getAccessToken(account: String = _api_access_token) -> String? {
    let query: [CFString: Any] = [
      kSecClass: kSecClassGenericPassword,
      kSecAttrAccount: account,
      kSecReturnAttributes: true,
      kSecReturnData: true,
    ]
    var item: CFTypeRef?
    if SecItemCopyMatching(query as CFDictionary, &item) != errSecSuccess {
      Log.message(to: "Failt to SecItemCopyMatching()")
      return nil
    }

    //        Log.message(to: "Copy item is \(String(describing: item))")
    if let dic = item,
      let obj = dic["v_Data"] as? Data,
      let value = obj.toString()
    {
      return value
    }

    return nil
  }

  func updateItemnKeyChain(value: Any, key: Any) {
    let previousQuery: [CFString: Any] = [
      kSecClass: kSecClassGenericPassword,
      kSecAttrAccount: key,
    ]
    let updateQuery: [CFString: Any] = [kSecValueData: value]
    let status = SecItemUpdate(previousQuery as CFDictionary, updateQuery as CFDictionary)

    if status == errSecSuccess {
      Log.message(to: "update complete")
    } else {
      Log.message(to: "not finished update")
    }
  }
}

extension AppManager {
  // 특정 값을 삭제
  func removeUserDefaultKey(key: String) {
    UserDefaults.standard.removeObject(forKey: key)
  }

  // 모든 값을 삭제
  func removeAllUserDefault() {
    if let appDomain = Bundle.main.bundleIdentifier {
      UserDefaults.standard.removePersistentDomain(forName: appDomain)
    }
  }

  // 특정 값을 추가
  @discardableResult
  func updateUserDefault(key: String, value: Any) -> Bool {
    let defaults = UserDefaults.standard
    defaults.setValue(value, forKey: key)
    return defaults.synchronize()
  }

  func loadUserDefault(key: String) -> Any? {
    return UserDefaults.standard.value(forKey: key)
  }
}

extension AppManager: CLLocationManagerDelegate {
  func setGoogleMapKey() {
    #if !targetEnvironment(simulator)
      let GMS_API_KEY = "AIzaSyCTEhnaV9uSgeVRsvV0bDQO1ae0B7AnlaM"
      GMSServices.provideAPIKey(GMS_API_KEY)
      //      GMSPlacesClient.provideAPIKey(GMS_API_KEY)
      onLoaction()
    #endif
  }

  func onLoaction() {
    locationLimit = 3
    location.delegate = self
    location.startUpdatingLocation()
  }

  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    guard let updateValue = manager.location?.coordinate else { return }

    if let value = locationValue {
      if value.latitude != updateValue.latitude
        && value.longitude != updateValue.longitude
      {
        self.locationValue = updateValue
        //print("locations = \(updateValue.latitude) \(updateValue.longitude)")
        locationLimit -= 1
        if locationLimit < 0 {
          location.stopUpdatingLocation()
        }
      }
    } else {
      self.locationValue = updateValue
      //print("locations = \(updateValue.latitude) \(updateValue.longitude)")
    }
  }

  func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    Log.error(to: error.localizedDescription)
  }
}

@propertyWrapper
struct UserDefaultWrapper<T: Codable> {
  private let key: String
  private let defaultValue: T?

  init(key: String, defaultValue: T?) {
    self.key = key
    self.defaultValue = defaultValue
  }

  var wrappedValue: T? {
    get {
      if let savedData = UserDefaults.standard.object(forKey: key) as? Data {
        let decoder = JSONDecoder()
        if let lodedObejct = try? decoder.decode(T.self, from: savedData) {
          return lodedObejct
        }
      }
      return defaultValue
    }
    set {
      let encoder = JSONEncoder()
      if let encoded = try? encoder.encode(newValue) {
        UserDefaults.standard.setValue(encoded, forKey: key)
      }
    }
  }

}

// MARK: 중복사용 방지를 위한 웹소켓
extension AppManager {

}
