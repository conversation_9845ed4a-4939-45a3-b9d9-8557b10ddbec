//
//  UrlList.swift
//  Hub
//
//  Created by ncn on 2022/11/18.
//

import Foundation

let webUrlLocaleList = ["ko", "en", "he", "fr", "ru"]
//let webSocketProtocol = Current.isSecure ? "wss" : "ws"
//let restProtocol = Current.isSecure ? "https" : "http"
let webSocketProtocol = "wss"
let restProtocol = "https"
let rtspProtocol = "rtsp"

enum AiVersion: String {
  case v1 = "v1"
  case v2 = "v2"
  case v3 = "v3"
}

// Host URL Setting
struct DevelopURL {
  let webDomain: String
  let firmwareUrl: String = "https://firmware.nconnect.co.kr/api/firmware?model="
  let vueroidCloud: String
  let blackBoxUrl: String
  let commandSocketUrl: String
  let cloudRegisterSocketUrl: String
  let cloudSseSocketUrl: String
  let cloudSocketUrl: String
  let signaling: String
  let defaultIceServers: [String]
  // HUB
  let aiServerBaseUrl: String = "https://ai.vueroid-cloud.com/api/"
  /* Mobile web */
  let homePageUrl: String
  let loginUrl: String
  let settingUrl: String
  let csUrl: String
  //  let docServiceUrl: String
  let termsOfUseUrl: String
  let privatePolicyUrl: String

  let libraryUrl: String
  let announcementUrl: String

  /* Mobile web Setting */
  let settingPasswordUrl: String
  let settingProfileUrl: String
  let settingPlanUrl: String
  let settingQnaUrl: String

  let settingServiceAlertUrl: String
  let settingEventAlertUrl: String
  let settingManualRecCloudUrl: String
  let settingDataUnitUrl: String

  let streamUrl: String
  let streamRegionCode: String

  init() {
    var devDomain = ""
//    #if DEVELOP
//      devDomain = "www"
//    #elseif QA
//      devDomain = "stage"
//    #else
      devDomain = "dev"
//    #endif
    self.blackBoxUrl = "\(restProtocol)://192.168.111.1"
    self.commandSocketUrl = "\(webSocketProtocol)://192.168.111.1:5000"

    // MARK: - API
    self.vueroidCloud = "https://\(devDomain).vueroid-cloud.com"
    // MARK: - DEVICE-REG
    self.cloudRegisterSocketUrl = "wss://\(devDomain).vueroid-cloud.com/deviceRegist/ncnRegist"
    // MARK: - SSE
    self.cloudSseSocketUrl = "wss://\(devDomain).vueroid-cloud.com/bypass/sseSocket"
    // MARK: - BYPASS
    self.cloudSocketUrl = "wss://\(devDomain).vueroid-cloud.com/bypass/webSocketTest"
    // MARK: - SIGNALING
    self.signaling = "wss://\(devDomain).vueroid-cloud.com/signaling/signaling"
    self.defaultIceServers = [
      "stun:13.250.97.196:3478",
      "turn:13.250.97.196:443?transport=udp",
      "turn:13.250.97.196:443?transport=tcp",
    ]

    // MARK: - 웹
    self.webDomain = "https://\(devDomain).vueroid-cloud.com"
    self.homePageUrl = "http://www.nc-and.com/kr/index.php"
    self.loginUrl = "/m/unauth/signin"
    self.settingUrl = "/m/unauth/suc"
    self.csUrl = "/m/cs/faq"
    self.termsOfUseUrl = "/m/doc/terms/privacy"
    self.privatePolicyUrl = "/m/doc/policies/service"
    self.libraryUrl = "/m/doc/libraries"
    self.announcementUrl = "/m/cs/anc"

    /* Mobile web Setting */
    self.settingPasswordUrl = "/m/user/password"  // rtoken
    self.settingProfileUrl = "/m/user/profile"  // rtoken
    self.settingPlanUrl = "/m/user/plan"  // rtoken
    self.settingQnaUrl = "/m/user/inq"  // rtoken

    self.settingServiceAlertUrl = "/m/settings/device"  // rtoken & sn
    self.settingEventAlertUrl = "/m/settings/device"  // rtoken & sn
    self.settingManualRecCloudUrl = "/m/settings/cloud"  // rtoken & sn
    self.settingDataUnitUrl = "/m/settings/user"  // rtoken & sn

    // MARK: - VOD

//    #if QA || DEVELOP
//      self.streamRegionCode = "sg"
//      self.streamUrl = "\(devDomain).vueroid-cloud.com"
//    #else
      self.streamRegionCode = "sg"
      self.streamUrl = "prod.vueroid-cloud.com"
//    #endif
  }
}

struct ReleaseURL {
  let webDomain: String
  let firmwareUrl: String = "https://firmware.nconnect.co.kr/api/firmware?model="
  let vueroidCloud: String
  let blackBoxUrl: String
  let commandSocketUrl: String
  let cloudRegisterSocketUrl: String
  let cloudSseSocketUrl: String
  let cloudSocketUrl: String
  let signaling: String
  let defaultIceServers: [String]

  // HUB
  let aiServerBaseUrl: String = "https://ai.vueroid-cloud.com/api/"

  /* Mobile web */
  let homePageUrl: String
  let loginUrl: String
  let settingUrl: String
  let csUrl: String
  //  let docServiceUrl: String
  let termsOfUseUrl: String
  let privatePolicyUrl: String

  let libraryUrl: String
  let announcementUrl: String

  /* Mobile web Setting */
  let settingPasswordUrl: String
  let settingProfileUrl: String
  let settingPlanUrl: String
  let settingQnaUrl: String

  let settingServiceAlertUrl: String
  let settingEventAlertUrl: String
  let settingManualRecCloudUrl: String
  let settingDataUnitUrl: String

  let streamUrl: String
  let streamRegionCode: String

  init() {
    self.blackBoxUrl = "\(restProtocol)://192.168.111.1"
    self.commandSocketUrl = "\(webSocketProtocol)://192.168.111.1:5000"

    self.vueroidCloud = "https://prod.vueroid-cloud.com"
    self.cloudRegisterSocketUrl = "wss://prod.vueroid-cloud.com/deviceRegist/ncnRegist"
    self.cloudSseSocketUrl = "wss://prod.vueroid-cloud.com/bypass/sseSocket"
    self.cloudSocketUrl = "wss://prod.vueroid-cloud.com/bypass/webSocketTest"

    /* WebRTC */
    self.signaling = "wss://prod.vueroid-cloud.com/signaling/signaling"

    self.defaultIceServers = [
      "stun:13.250.97.196:3478",
      "turn:13.250.97.196:443?transport=udp",
      "turn:13.250.97.196:443?transport=tcp",
    ]

    /* Mobile web */
    self.webDomain = "https://www.vueroid-cloud.com"
    self.homePageUrl = "http://www.nc-and.com/kr/index.php"
    self.loginUrl = "/m/unauth/signin"
    self.settingUrl = "/m/unauth/suc"
    self.csUrl = "/m/cs/faq"
    self.termsOfUseUrl = "/m/doc/terms/privacy"
    self.privatePolicyUrl = "/m/doc/policies/service"
    self.libraryUrl = "/m/doc/libraries"
    self.announcementUrl = "/m/cs/anc"

    /* Mobile web Setting */
    self.settingPasswordUrl = "/m/user/password"
    self.settingProfileUrl = "/m/user/profile"
    self.settingPlanUrl = "/m/user/plan"
    self.settingQnaUrl = "/m/user/inq"

    self.settingServiceAlertUrl = "/m/settings/device"
    self.settingEventAlertUrl = "/m/settings/device"
    self.settingManualRecCloudUrl = "/m/settings/cloud"
    self.settingDataUnitUrl = "/m/settings/user"

    /* Stream */
    //    self.streamUrl = "https://ko.prod.vueroid-cloud.com/"
    self.streamRegionCode = "sg"
    self.streamUrl = "prod.vueroid-cloud.com"
  }
}

//Service URL List
enum ServiceUrl {
  struct Get {
    // capacity
    static let capacityLive: String = "/api/capacity/live"

    // template
    static let api: String = "/api/%@"  //{String}

    // Streaming
    static let streamVod: String = "/STREAM/VOD/m/%@/%@/"  //{serial}/{fileNm}/{channel}

  }

  struct Post {
    /* dashcam Only Download file*/
    static let filedownload = "/filedownload"
    static let newfirmware = "/newfirmware"

    // template
    static let api: String = "/api/%@"  //{String}

    /* login, user */
    static let authenticate = "/api/authenticate"
    static let getToken = "/api/getToken"
    static let uainfo: String = "/api/web/uaUser/uaInfo"

    /* Park info image */
    static let picList: String = "/api/web/pic/list"

    /* dashcam list*/
    static let dvcList: String = "/api/web/mdDevice/list"
    static let mdDeviceListdtl: String = "/api/web/mdDevice/listDtl"
    static let mdDeviceDvcDelete: String = "/api/web/mdDevice/dvcDelete"
    static let mdModelList: String = "/api/web/mdModel/list"

    static let mdDeviceDvcCommand: String = "/api/web/mdDevice/dvcCommand"

    /* vod list */
    static let dvList: String = "/api/web/dvFile/list"
    static let fileDownUrl: String = "/api/web/dvFile/file"
    static let removeCloudFile: String = "/api/web/dvFile/file/cancel"
    static let toCloudFile: String = "/api/web/dvFile/filetype"
    static let cloudUsage: String = "/api/web/dvFile/file/usage"
    static let cloudSearchFile: String = "/api/web/dvFile/search"

    /* ws connect */
    static let wsConnectOne: String = "/api/device/wsConnect/one"
    static let wsConnectZero: String = "/api/device/wsConnect/zero"

    /* stream */
    static let getVod: String = "/STREAM/VOD/getVod"
    static let videoRemove: String = "/STREAM/VOD/videoRemove"

    /* history */
    static let calendar: String = "/api/web/udDeviceHistory/calendar"
    static let eventDayList: String = "/api/web/udDeviceHistory/eventDayList"
    static let eventWeekList: String = "/api/web/udDeviceHistory/eventWeekList"
    static let eventMonthList: String = "/api/web/udDeviceHistory/eventMonthList"
    static let eventYearList: String = "/api/web/udDeviceHistory/eventYearList"

    static let eventDay: String = "/api/web/udDeviceHistory/eventDay"
    static let eventWeek: String = "/api/web/udDeviceHistory/eventWeek"
    static let eventMonth: String = "/api/web/udDeviceHistory/eventMonth"
    static let eventYear: String = "/api/web/udDeviceHistory/eventYear"

    static let udDeviceHistory: String = "/api/web/udDeviceHistory/udDeviceHistory"
    static let lastDrive: String = "/api/web/udDeviceHistory/lastDrive?serial=%@"
    static let travelLogDeleteAll: String = "/api/web/udDeviceHistory/deleteAll"

    static let validation: String = "/api/web/mdDevice/validation"
    static let validationRenewal: String = "/api/web/mdDevice/validationRenewal"

    static let updateDB: String = "/api/mdDevice/deviceCommand"
    /* push noti */
    static let appToken: String = "/api/app/ucNoti/appToken"
    static let notiList: String = "/api/web/ucNoti/list"
    static let notiDetail: String = "/api/web/ucNoti/listDtl"
    static let notiDeleteAll: String = "/api/web/ucNoti/deleteAll"
    static let unreadCount: String = "/api/v2/web/ucNoti/unread/count"
  }

  struct Put {
    /* dashcam regist*/
    static let useUpdate: String = "/api/web/mdDevice/useUpdate"

    /* push noti */
    static let notiStatusUpdate: String = "/api/web/ucNoti/statusUpdate"

    /* uaUser UaDelete */
    static let uaUserUaDelete: String = "​/api/web/uaUser/uaDelete"
  }

  struct Delete {
    static let logoutToken = "/api/logoutToken"
  }

  struct Signal {
    static let nameSpace = "/signal"
  }
}
// MARK: - Network
class UrlList {
  class func firmwareUrl() -> String {
    let urlWithModel: String =
      AppManager.shared.isRelease ? ReleaseURL().firmwareUrl : DevelopURL().firmwareUrl
    return urlWithModel
  }

  class func blackBoxUrl(path: String) -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().blackBoxUrl : DevelopURL().blackBoxUrl
    return "\(domain)\(path)"
  }

  class func AiUrl(path: String, version: AiVersion) -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().aiServerBaseUrl : DevelopURL().aiServerBaseUrl
    return "\(domain)\(version.rawValue)/\(path)"
  }

  class func blackBoxUrl(path: String, port: String) -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().blackBoxUrl : DevelopURL().blackBoxUrl
    return "\(domain):\(port)/\(path)"
  }

  class func commandSocketUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().commandSocketUrl : DevelopURL().commandSocketUrl
    //    let domain: String = DevelopURL().commandSocketUrl
    return domain
  }

  #if false
  class func cloudStream(path: String) -> String {
    let regionCode: String =
      AppManager.shared.isRelease ? ReleaseURL().streamRegionCode : DevelopURL().streamRegionCode
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().streamUrl : DevelopURL().streamUrl
    return "https://\(regionCode).\(domain)\(path)"
  }

  class func veuroidCloud(path: String) -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().vueroidCloud : DevelopURL().vueroidCloud
    return "\(domain)\(path)"
  }

  class func cloudRegistSocketUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease
      ? ReleaseURL().cloudRegisterSocketUrl : DevelopURL().cloudRegisterSocketUrl
    return domain
  }

  class func cloudSseSocketUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease
      ? ReleaseURL().cloudSseSocketUrl : ReleaseURL().cloudSseSocketUrl
    return domain
  }

  class func cloudSocketUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().cloudSocketUrl : DevelopURL().cloudSocketUrl
    //    let domain: String = DevelopURL().cloudSocketUrl
    return domain
  }

  class func signalingUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().signaling : DevelopURL().signaling
    return domain
  }

  class func iceServers() -> [String] {
    var iceServers =
      AppManager.shared.isRelease ? ReleaseURL().defaultIceServers : DevelopURL().defaultIceServers
    let kvsIceServers = [ "stun:stun.kinesisvideo.\(Current.awsRegion).amazonaws.com:443"]
    iceServers = Current.useKvsWebRTC ? kvsIceServers: iceServers

//    _ = connectAsRole()
//    let urlStrings = KVSClient.iceServers?.map { $0.urlStrings }
    return iceServers
  }
  #endif
}
// MARK: - webview
extension UrlList {
  class func homePageUrl() -> String {
    let domain: String =
      AppManager.shared.isRelease ? ReleaseURL().homePageUrl : DevelopURL().homePageUrl
    return domain
  }

  class func settingUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().settingUrl : DevelopURL().settingUrl
    return host + "/" + localeCode + path
  }

  class func loginUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().loginUrl : DevelopURL().loginUrl
    return host + "/" + localeCode + path
  }

  class func csUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().csUrl : DevelopURL().csUrl
    return host + "/" + localeCode + path
  }

  class func termsOfUseUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().termsOfUseUrl : DevelopURL().termsOfUseUrl
    return host + "/" + localeCode + path
  }

  class func privatePolicyUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease ? ReleaseURL().privatePolicyUrl : DevelopURL().privatePolicyUrl
    return host + "/" + localeCode + path
  }

  class func libraryUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().libraryUrl : DevelopURL().libraryUrl
    return host + "/" + localeCode + path
  }

  class func announcementUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease ? ReleaseURL().announcementUrl : DevelopURL().announcementUrl
    return host + "/" + localeCode + path
  }

  /* Mobile web Setting */
  class func settingPasswordUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease
      ? ReleaseURL().settingPasswordUrl : DevelopURL().settingPasswordUrl
    return host + "/" + localeCode + path
  }

  class func settingProfileUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease ? ReleaseURL().settingProfileUrl : DevelopURL().settingProfileUrl
    return host + "/" + localeCode + path
  }

  class func settingPlanUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease ? ReleaseURL().settingPlanUrl : DevelopURL().settingPlanUrl
    return host + "/" + localeCode + path
  }

  class func settingQnaUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path = AppManager.shared.isRelease ? ReleaseURL().settingQnaUrl : DevelopURL().settingQnaUrl
    return host + "/" + localeCode + path
  }

  class func settingServiceAlertUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease
      ? ReleaseURL().settingServiceAlertUrl : DevelopURL().settingServiceAlertUrl
    return host + "/" + localeCode + path
  }

  class func settingEventAlertUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease
      ? ReleaseURL().settingEventAlertUrl : DevelopURL().settingEventAlertUrl
    return host + "/" + localeCode + path
  }

  class func settingManualRecCloudUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease
      ? ReleaseURL().settingManualRecCloudUrl : DevelopURL().settingManualRecCloudUrl
    return host + "/" + localeCode + path
  }

  class func settingDataUnitUrl() -> String {
    var localeCode = Locale.current.languageCode ?? "en"
    if !webUrlLocaleList.contains(localeCode) {
      localeCode = "en"
    }
    let host = AppManager.shared.isRelease ? ReleaseURL().webDomain : DevelopURL().webDomain
    let path =
      AppManager.shared.isRelease
      ? ReleaseURL().settingDataUnitUrl : DevelopURL().settingDataUnitUrl
    return host + "/" + localeCode + path
  }
}

// MARK: - File or DB
extension UrlList {
  class func documentPath() -> URL? {
    return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first
  }

  class func vodTemp() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("VOD_TEMP")
  }

  class func screenshotPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("SCREEN")
  }

  class func dbPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("DB")
  }

  class func tempPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("TEMP")
  }

  class func downloadedPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("DOWNLOADED")
  }

  class func downloadedThumbPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("DOWNLOADEDTHUMB")
  }

  class func SDPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("SD")
  }

  class func safetyDBPath() -> URL? {
    guard let document = documentPath()
    else { return nil }
    return document.appendingPathComponent("safetyDB")
  }

  class func imageCachePath() -> URL? {
    guard
      let cachePath = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true)
        .first
    else { return nil }
    return URL(fileURLWithPath: cachePath)
  }
}
