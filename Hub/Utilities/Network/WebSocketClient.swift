//
//  WebSocketClient.swift
//  Hub
//
//  Created by ncn on 2023/02/22.
//

import BackgroundTasks
import Foundation
import UIKit

/*
 1001: connect
 1101: send
 */

/* SocketEventId
 event : Id
 connect : 999
 registEvent : 10000
 fileDownload : 10005 +
 gsensor : 10006
 */

public typealias WebSocketCallback = @Sendable ([String: Any]?, Error?) -> Void

struct SoketBinaryModel {
  let command: String
  let id: Int
  let jsonString: String
}

public struct SocketEventHandler: Equatable, Sendable {
  // MARK: Properties
  public let event: String
  public let id: Int
  public let callback: WebSocketCallback
  public func executeCallback(with items: Any?, error: Error?) {
    let ret = items as? [String: Any]
    callback(ret, error)
  }
  public static func == (lhs: SocketEventHandler, rhs: SocketEventHandler) -> Bool {
    return (lhs.id == rhs.id) && (lhs.event == rhs.event)
  }
}

class WebSocketClient: NSObject {

  var session: URLSession?
  var webSocket: URLSessionWebSocketTask?
  var url: URL?
  //  public private(set) var handlers = [SocketEventHandler]()
  var handlers: [SocketEventHandler]? = [SocketEventHandler]()
  var isOpen = false
  var config: [String: String]?
  var binary: SoketBinaryModel?

  var isUserDisconnect = false
  
  var pingpongWorkItem: DispatchWorkItem?

  init(url: String, config: [String: String]? = ["": ""]) {
    super.init()
    Log.info(category: .WebSocket, to: "init")
    self.url = URL(string: url)
    self.config = config
  }

  deinit {
    Log.info(category: .WebSocket, to: "deinit")
  }

  private func openSocket(timeout: Int = 30) {
    isUserDisconnect = false
    if isOpen == true { return }
    if let url = self.url {
      Log.info(category: .WebSocket, to: "websocket url: \(url)")
      let request = URLRequest(url: url, timeoutInterval: TimeInterval(timeout))
      let session = URLSession(
        configuration: .default, delegate: self, delegateQueue: OperationQueue())
      self.webSocket = session.webSocketTask(with: request)
      self.session = session
      webSocket?.maximumMessageSize = 1024 * 1024 * 100
      webSocket?.resume()
      isOpen = true
      Log.debug(category: .WebSocket, to: "Open web socekt.")
    } else {
      self.webSocket = nil
    }
  }

  private func filter(_ event: String, id: Int) -> [SocketEventHandler]? {
    //    Log.info(category: .WebSocket, to: "\(handlers?.debugDescription ?? "") && \(event), \(id)")
    if id == -1 {
      let ret = handlers?.filter({ $0.event == event })
      return ret
    } else if id == 999 {
      let ret = handlers?.filter({ $0.event == event && $0.id == id })
      handlers?.removeAll(where: { $0.event == event && $0.id == id })
      return ret
    } else if id == 999999 {
      let ret = handlers?.filter({ $0.event == event && $0.id == id })
      handlers?.removeAll(where: { $0.event == event && $0.id == id })
      return ret
    } else if id == 10000 {
      let ret = handlers?.filter({ $0.event == event && $0.id == id })
      handlers?.removeAll(where: { $0.event == event && $0.id == id })
      return ret
    } else {
      let ret = handlers?.filter({ $0.event == event && $0.id == id })
      return ret
    }
  }

  func subscribe(with completion: @escaping (String?) -> Void) {
    if isOpen != true && !isUserDisconnect {
      Log.warning(category: .WebSocket, to: "is not open.")
      openSocket()
    }
    guard let socket = self.webSocket else {
      completion("Not exist socket.")
      handlers?.removeAll()
      return
    }

    socket.receive(completionHandler: { [weak self] result in /* Result<Success, Failure> */
      guard let self = self else {
        completion(nil)
        return
      }
      switch result {
      case .success(let message): /* URLSessionWebSocketTask.Message */
        switch message {
        case .string(let string):
          self.parsing(jsonString: string)
          self.subscribe(with: completion)
          break
        case .data(let data):
          self.parsing(data: data)
          self.subscribe(with: completion)
          break
        @unknown default:
          fatalError("Failed. Received unknown data format.")
        }
      case .failure(let error):
        self.webSocket = nil
        self.isOpen = false
        Log.error(category: .WebSocket, to: "Webscoket error: \(error)")
        let dic = ["error": "socket receive to error."]
        self.handlers?.last?.executeCallback(with: dic, error: error)
        let message = "ERROR: \(error.localizedDescription)"
        self.handlers?.removeAll()
        NotificationCenter.default.post(name: .disconnectedDevice, object: nil)
        completion(message)
      }
    })
  }

  private func parsing(data: Data) {
    if let obj = binary,
      let _ = AppManager.shared.dashcam?.macaddress
    {
      switch obj.command {
      case "getdriverecord":
        self.binary = nil
        FileManager.ungz(data: data, toName: _DB_HISTORY)
        break
      case "getgpsrecord":
        self.binary = nil
        FileManager.ungz(data: data, toName: _DB_GPS)
        break
      default:
        break
      }

      let msg = FileManager.loadFileAtFolder(path: UrlList.tempPath())
      Log.debug(category: .WebSocket, to: "\(msg)")
      let handlers = filter(obj.command, id: obj.id)
      for handler in handlers ?? [] {
        if let temp = obj.jsonString.dictionaryFromJson(),
          let header = temp["header"]
        {
          let dic = [obj.command: "file", "header": header]
          handler.executeCallback(with: dic, error: nil)
        }
      }
    }

    if let url = self.url,
      let downloadUrl = URL(string: UrlList.commandSocketUrl()),
      url == downloadUrl
    {
      let handler = handlers?.last(where: { $0.event == "filedownload" })
      let encodedData = data.base64EncodedString()
      handler?.executeCallback(with: ["file": encodedData], error: nil)
    }

  }

  private func parsing(jsonString: String) {
    //        wsLog.info( "\(jsonString)")
    guard let dic = jsonString.dictionaryFromJson(),
      let url = self.url
    else { return }

    if let header = dic["header"] as? [String: Any],
      let cmd = header["cmd"] as? String,
      let seq = header["seq"] as? Int
    {
      guard cmd != "keepalive" else { return }
      let lastHandler = filter(cmd, id: seq)?.last
      lastHandler?.executeCallback(with: dic, error: nil)
      return
    }

    if dic["gsensor"] != nil {
      if let liveViewGsensorHandler = filter("startliveview", id: -1)?.last {
        liveViewGsensorHandler.executeCallback(with: dic, error: nil)
      } else if let fileViewGsensorHandler = filter("startrecordvod", id: -1)?.last {
        fileViewGsensorHandler.executeCallback(with: dic, error: nil)
      }
      return
    }

    if dic["status"] != nil {
      guard let lastHandler = handlers?.last else { return }
      lastHandler.executeCallback(with: dic, error: nil)
      return
    }

    if let _ = dic["header"] as? [String: Any],
      let data = dic["data"] as? [String: Any]
    {
      guard let lastHandler = handlers?.last else { return }
      lastHandler.executeCallback(with: data, error: nil)
      return
    }

  }
}

extension WebSocketClient {
  func connect(completion: @escaping WebSocketCallback) {
    if isOpen {
      if let socket = webSocket, socket.state == .completed || socket.state == .canceling {
        wsLogger.warning("‼️ isOpen true but WebSocket is closed.")
        isOpen = false
        openSocket()
      } else {
        completion(["connect": "true", "alreadyConnect": "true"], nil)
        return
      }
    }else {
      openSocket()
    }

    let handler = SocketEventHandler(event: "connect", id: 999, callback: completion)
    self.handlers?.append(handler)

    subscribe { [weak self] response in
      Log.info(category: .WebSocket, to: "Connect error: \(String(describing: response))")

      if let string = response,
        string.contains("ERROR:") && !(self?.isUserDisconnect ?? false)
      {
//        let ncError = NCError(title: "Custom error", description: string, code: 1001)
//        completion(nil, ncError)
      }
    }
  }

  func disconnect() {
    guard let socket = webSocket else { return }
    isOpen = false
    handlers?.removeAll()
    // https://stackoverflow.com/questions/74196497/urlsessionwebsocket-not-calling-didclose-delegate-method
    socket.receive { _ in
      wsLogger.info("WebSocket receive when disconnect.")
    }
    
    socket.cancel(with: .goingAway, reason: "Websocket Ended".data(using: .utf8))
    isUserDisconnect = true
    webSocket = nil
    pingpongWorkItem = nil
  }

  func ping() {
//    Log.info(category: .WebSocket, to: "Send ping.")
    if let socket = webSocket {
      var interval: Double = 20  //default
      if let dic = config,
        let sec = dic["PING_INTERVAL"],
        let value = Double(sec)
      {
        interval = value
      }
      switch AppManager.shared.mode {
      case .wifi:
//        Log.info(category: .WebSocket, to: "Send ping with Socket.")
        socket.sendPing { [weak self] error in
          guard let self = self else { return }
          if let e = error {
            Log.error(category: .WebSocket, to: "Error when sending ping - \(e)")
            self.isOpen = false
            NotificationCenter.default.post(name: .disconnectedDevice, object: nil)
          } else {
            DispatchQueue.global().asyncAfter(deadline: .now() + interval, execute: self.pingpongWorkItem ?? DispatchWorkItem(block: {}) )
          }
        }
        break
        
      default:
        break
      }
    }
  }

  func send(type: String, id: Int, jsonString: String, completion: @escaping WebSocketCallback) {
    guard let socket = webSocket else {
      let message = "The Internet connection appears to be offline.(-1)"
      let error = NCError(title: "Custom error", description: message, code: 1101)
      openSocket()
      completion(nil, error)
      return
    }
    do {
      Log.message(category: .WebSocket, to: "Send message [\(type)-\(id)]: \(jsonString)")
      if type == "getdriverecord" || type == "getgpsrecord" {
        self.binary = SoketBinaryModel(command: type, id: id, jsonString: jsonString)
      }
      let value = URLSessionWebSocketTask.Message.string(jsonString)

      socket.send(value) { [weak self] error in
        guard let self = self else { return }
        if let e = error {
          Log.error(category: .WebSocket, to: "Fail to send message -\(e)")
          completion(nil, e)
        } else {
          let handler = SocketEventHandler(event: type, id: id, callback: completion)
          if handlers == nil { return }
          self.handlers?.append(handler)
        }
      }
    }
  }

  func send(type: String, id: Int, data: Data, completion: @escaping WebSocketCallback) {
    guard let socket = webSocket else {
      let message = "The Internet connection appears to be offline.(-1)"
      let error = NCError(title: "Custom error", description: message, code: 1101)
      openSocket()
      completion(nil, error)
      return
    }
    do {
      Log.info(category: .WebSocket, to: "Send data [\(type)-\(id)]: \(data)")
      let value = URLSessionWebSocketTask.Message.data(data)
      socket.send(value) { [weak self] error in
        guard let self = self else { return }
        if let e = error {
          Log.error(category: .WebSocket, to: "Fail to send message -\(e)")
          completion(nil, e)
        } else {
          let handler = SocketEventHandler(event: type, id: id, callback: completion)
          self.handlers?.append(handler)
        }
      }
    }
  }

  func close() {
    if let socket = webSocket {
      let aString = "{\"unsubscribeFrom\":\"\(111)\"}"
      let msg = aString.data(using: .utf8)
      socket.removeObserver(self, forKeyPath: #keyPath(Progress.completedUnitCount))
      socket.cancel(with: .goingAway, reason: msg)
    }
  }
  func startPingPong() {
    self.pingpongWorkItem = DispatchWorkItem(block: { [weak self] in
      self?.ping()
    })
    self.ping()
  }

}

// MARK: - URLSessionWebSocketDelegate
extension WebSocketClient: URLSessionWebSocketDelegate {
  func urlSession(
    _ session: URLSession,
    webSocketTask: URLSessionWebSocketTask,
    didOpenWithProtocol protocol: String?
  ) {
    let msg = """
      WebSocket Open session: \(session.description),\n
      websocketTask: \(webSocketTask.description),\n protocol: \(String(describing: `protocol`))
      """
    Log.info(category: .WebSocket, to: "delegate \(msg)")
    self.isOpen = true
    self.pingpongWorkItem = DispatchWorkItem(block: { [weak self] in
      self?.ping()
    })

    self.ping()

    // callback 처리
    let dic = [
      "connect": "true",
      "alreadyConnect": "false",
    ]
    let handlers = filter("connect", id: 999)
    for handler in handlers ?? [] {
      handler.executeCallback(with: dic, error: nil)
    }
  }

  func urlSession(
    _ session: URLSession,
    webSocketTask: URLSessionWebSocketTask,
    didCloseWith closeCode: URLSessionWebSocketTask.CloseCode,
    reason: Data?
  ) {
    Log.info(category: .WebSocket, to: "Web socket close.(\(closeCode)")
    self.webSocket = nil
    self.isOpen = false

    // callback 처리

    if self.isOpen != true {
      self.handlers?.removeAll()
    } else {
      let dic = [
        "connect": "false",
        "alreadyConnect": "false",
      ]
      let handlers = filter("connect", id: 999)
      for handler in handlers ?? [] {
        handler.executeCallback(with: dic, error: nil)
      }
    }
  }
}

extension WebSocketClient: URLSessionDelegate {
  // URLSessionDelegate: 인증서 검증
  func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
    
    if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
      if let serverTrust = challenge.protectionSpace.serverTrust {
        // 서버 인증서 체인 가져오기
        if let serverCerts = SecTrustCopyCertificateChain(serverTrust) as? [SecCertificate],
           let serverCert = serverCerts.first {
//           SecCertificateCopyData(serverCert) == SecCertificateCopyData(localCert) {
          #if false
          if let dic = SecTrustCopyResult(serverTrust) {
            hLogger.info("certificate info:  \(dic)")
          }
          let secKey = SecTrustCopyKey(serverTrust)
          hLogger.info("public key:  \(secKey.debugDescription)")
          #endif
          
          hLogger.info("wss certificate")
          completionHandler(.useCredential, URLCredential(trust: serverTrust))
          return
        }
      }
    }
    
    LKPopupView.popup.toast(hit: "wss 인증서 없음")
    // 인증서 없을 시 연결 취소
    hLogger.critical("wss cancelAuthenticationChallenge")
    completionHandler(.cancelAuthenticationChallenge, nil)
  }
}
