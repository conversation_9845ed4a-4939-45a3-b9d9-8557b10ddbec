//
//  WebSocketPingController.swift
//  Hub
//
//  Created by ncn on 1/7/25.
//

import Foundation

protocol WebSocketPingControllerDelegate: AnyObject {
  func sendPing()
  func disconnectOnNoPongReceived()
}

/// 핑과 퐁 타이머를 관리. 웹 소켓 연결을 유지하기 위해 컨트롤러는 주기적으로 ping 을 보낸다 .
/// 핑이 전송된 후 pong 대기 타이머가 시작되며, pong이 오지 않으면 delegate의 disconnectOnNoPongReceived 호출.
class WebSocketPingController {

  static let pingTimeInterval: TimeInterval = 25
  static let pongTimeoutTimeInterval: TimeInterval = 3
  private let timerType: WSTimer.Type
  private let timerQueue: DispatchQueue
  private var pingTimerControl: RepeatingTimerControl?
  private var pongTimeoutTimer: TimerControl?

  weak var delegate: WebSocketPingControllerDelegate?

  deinit {
    cancelPongTimeoutTimer()
  }

  /// Creates a ping controller.
  /// - Parameters:
  ///   - timerType: a timer type.
  ///   - timerQueue: a timer dispatch queue.
  init(timerType: WSTimer.Type, timerQueue: DispatchQueue) {
    self.timerType = timerType
    self.timerQueue = timerQueue
  }

  /// `WebSocketClient` should call this when the connection state did change.
  func connectionStateDidChange(_ connectionState: WebSocketConnectionState) {
    guard delegate != nil else { return }

    cancelPongTimeoutTimer()
    schedulePingTimerIfNeeded()

    if connectionState.isConnected {
      wsLogger.info("Resume WebSocket Ping timer")
      pingTimerControl?.resume()
    } else {
      pingTimerControl?.suspend()
    }
  }

  // MARK: - Ping

  private func sendPing() {
    schedulePongTimeoutTimer()

    wsLogger.info("WebSocket Ping")
    delegate?.sendPing()
  }

  func pongReceived() {
    wsLogger.info("WebSocket Pong")
    cancelPongTimeoutTimer()
  }

  // MARK: Timers

  private func schedulePingTimerIfNeeded() {
    guard pingTimerControl == nil else { return }
    pingTimerControl = timerType
      .scheduleRepeating(
        timeInterval: Self.pingTimeInterval,
        queue: timerQueue
      ) { [weak self] in
      self?.sendPing()
    }
  }

  private func schedulePongTimeoutTimer() {
    cancelPongTimeoutTimer()
    // Start pong timeout timer.
    pongTimeoutTimer = timerType
      .schedule(
        timeInterval: Self.pongTimeoutTimeInterval,
        queue: timerQueue
      ) { [weak self] in
      wsLogger.info("WebSocket Pong timeout. Reconnect")
      self?.delegate?.disconnectOnNoPongReceived()
    }
  }

  private func cancelPongTimeoutTimer() {
    pongTimeoutTimer?.cancel()
    pongTimeoutTimer = nil
  }
}
