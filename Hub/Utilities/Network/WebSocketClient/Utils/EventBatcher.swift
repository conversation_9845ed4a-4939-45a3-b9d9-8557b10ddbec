//
//  EventBatcher.swift
//  Hub
//
//  Created by ncn on 1/8/25.
//

import Foundation

protocol EventBatcher {
  typealias Batch = [WSEvent]
  typealias BatchHandler = (_ batch: Batch, _ completion: @escaping () -> Void) -> Void

  /// The current batch of events.
  var currentBatch: Batch { get }

  /// Creates new batch processor.
  init(period: TimeInterval, timerType: WSTimer.Type, handler: @escaping BatchHandler)

  func append(_ event: WSEvent)
  func processImmediately(completion: @escaping () -> Void)
}

extension Batcher: EventBatcher where Item == WSEvent {}

final class Batcher<Item> {
  private let period: TimeInterval
  /// The time used to create timers.
  private let timerType: WSTimer.Type
  /// The timer that  calls `processor` when fired.
  private var batchProcessingTimer: TimerControl?
  private let handler: (_ batch: [Item], _ completion: @escaping () -> Void) -> Void

  /// The serial queue where item appends and batch processing is happening on.
  private let queue = DispatchQueue(label: "com.vueroidHub.Batch.\(Item.self)")
  /// The current batch of items.
  private(set) var currentBatch: [Item] = []

  init(
    period: TimeInterval,
    timerType: WSTimer.Type = DefaultTimer.self,
    handler: @escaping (_ batch: [Item], _ completion: @escaping () -> Void) -> Void
  ) {
    self.period = max(period, 0)
    self.timerType = timerType
    self.handler = handler
  }

  func append(_ item: Item) {
    timerType.schedule(timeInterval: 0, queue: queue) { [weak self] in
      self?.currentBatch.append(item)

      guard let self = self, self.batchProcessingTimer == nil else { return }

      self.batchProcessingTimer = self.timerType.schedule(
        timeInterval: self.period,
        queue: self.queue,
        onFire: { self.process() }
      )
    }
  }

  func processImmediately(completion: @escaping () -> Void) {
    timerType.schedule(timeInterval: 0, queue: queue) { [weak self] in
      self?.process(completion: completion)
    }
  }

  private func process(completion: (() -> Void)? = nil) {
    handler(currentBatch) { completion?() }
    currentBatch.removeAll()
    batchProcessingTimer?.cancel()
    batchProcessingTimer = nil
  }
}
