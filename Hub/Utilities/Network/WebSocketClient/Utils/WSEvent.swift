//
//  WSEvent.swift
//  Hub
//
//  Created by ncn on 1/8/25.
//

import Foundation

/// An `Event` object representing an event in the hub
public protocol WSEvent {}

extension WSEvent {
  var name: String {
    String(describing: Self.self).replacingOccurrences(of: "DTO", with: "")
  }
}

public protocol ConnectionEvent: WSEvent {
  var connectionId: String { get }
}

public class HealthCheckEvent: ConnectionEvent {
  public let connectionId: String
  init(connectionId: String) {
    self.connectionId = connectionId
  }
}


public protocol EventDecodable{
  func decode(from data: Data) throws -> WSEvent
}

public protocol EventHandler {
  func process(_ event: WSEvent, completion: @escaping () -> Void)
}
