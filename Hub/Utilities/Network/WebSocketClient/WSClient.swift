//
//  WSClient.swift
//  Hub
//
//  Created by ncn on 1/7/25.
//

import Foundation

class WSClient {
  /// The batch of events received via the web-socket that wait to be processed.
  private(set) lazy var eventsBatcher = environment.eventBatcherBuilder { [weak self] events, completion in
//    self?.eventNotificationCenter.process(events, completion: completion)
  }

  /// The current state the web socket connection.
  @Atomic
  private(set) var connectionState: WebSocketConnectionState = .initialized {
    didSet {
      engineQueue.async { [connectionState, pingController] in
        pingController.connectionStateDidChange(connectionState)
      }

      guard connectionState != oldValue else { return }

//      wsLogger.info("Web socket connection state changed: \(connectionState)")
      wsLogger.info("Web socket connection state changed")

      connectionStateDelegate?.webSocketClient(self, didUpdateConnectionState: connectionState)

      let previousStatus = ConnectionStatus(webSocketConnectionState: oldValue)
      let event = ConnectionStatusUpdated(webSocketConnectionState: connectionState)

      if event.connectionStatus != previousStatus {
        // Publish Connection event with the new state
        eventsBatcher.append(event)
      }
    }
  }

  weak var connectionStateDelegate: ConnectionStateDelegate?

  /// The endpoint used for creating a web socket connection.
  ///
  /// Changing this value doesn't automatically update the existing connection. You need to manually call `disconnect`
  /// and `connect` to make a new connection to the updated endpoint.
//  var connectEndpoint: Endpoint<EmptyResponse>?

  private let eventDecoder: EventDecodable

  /// The web socket engine used to make the actual WS connection
  private(set) var engine: WebSocketEngine?

  /// The queue on which web socket engine methods are called
  private let engineQueue: DispatchQueue = .init(label: "com.vueroidHub.web_socket_engine_queue", qos: .userInitiated)

//  private let requestEncoder: RequestEncoder

  /// The session config used for the web socket engine
  private let sessionConfiguration: URLSessionConfiguration

  /// An object containing external dependencies of `WebSocketClient`
  private let environment: Environment

  private(set) lazy var pingController: WebSocketPingController = {
    let pingController = environment.createPingController(environment.timerType, engineQueue)
    pingController.delegate = self
    return pingController
  }()

  private func createEngineIfNeeded() throws -> WebSocketEngine {
    let url = URL(string: ReleaseURL().commandSocketUrl )!
    let request: URLRequest = URLRequest(url: url, timeoutInterval: TimeInterval(30))
//    do {
//      request = try requestEncoder.encodeRequest(for: connectEndpoint)
//    } catch {
//      wsLogger.error("error: \(error.localizedDescription)")
//      throw error
//    }

    if let existedEngine = engine, existedEngine.request == request {
      return existedEngine
    }

    let engine = environment.createEngine(request, sessionConfiguration, engineQueue)
    engine.delegate = self
    return engine
  }

  init(
    sessionConfiguration: URLSessionConfiguration,
//    requestEncoder: RequestEncoder,
    eventDecoder: EventDecodable,
//    eventNotificationCenter: EventNotificationCenter,
    environment: Environment = .init()
  ) {
    self.environment = environment
//    self.requestEncoder = requestEncoder
    self.sessionConfiguration = sessionConfiguration
    self.eventDecoder = eventDecoder

//    self.eventNotificationCenter = eventNotificationCenter
  }

  /// Connects the web connect.
  ///
  /// Calling this method has no effect is the web socket is already connected, or is in the connecting phase.
  func connect() {
//    guard let endpoint = connectEndpoint else {
//      log.assertionFailure("Attempt to connect `web-socket` while endpoint is missing", subsystems: .webSocket)
//      return
//    }

    switch connectionState {
      // Calling connect in the following states has no effect
    case .connecting, .waitingForConnectionId, .connected:
      return
    default: break
    }

    do {
      engine = try createEngineIfNeeded()
    } catch {
      return
    }

    connectionState = .connecting

    engineQueue.async { [weak engine] in
      engine?.connect()
    }
  }

  /// Disconnects the web socket.
  ///
  /// Calling this function has no effect, if the connection is in an inactive state.
  /// - Parameter source: Additional information about the source of the disconnection. Default value is `.userInitiated`.
  func disconnect(
    source: WebSocketConnectionState.DisconnectionSource = .userInitiated,
    completion: @escaping () -> Void
  ) {
    connectionState = .disconnecting(source: source)
    engineQueue.async { [engine, eventsBatcher] in
      engine?.disconnect()

      eventsBatcher.processImmediately(completion: completion)
    }
  }

  func timeout() {
    let previousState = connectionState
    connectionState = .disconnected(source: .timeout(from: previousState))
    engineQueue.async { [engine, eventsBatcher] in
      engine?.disconnect()

      eventsBatcher.processImmediately {}
    }
//    log.error("Connection timed out. `\(connectionState)", subsystems: .webSocket)
  }
}

protocol ConnectionStateDelegate: AnyObject {
  func webSocketClient(_ client: WSClient, didUpdateConnectionState state: WebSocketConnectionState)
}

extension WSClient {
  /// An object encapsulating all dependencies of `WebSocketClient`.
  struct Environment {
    typealias CreatePingController = (_ timerType: WSTimer.Type, _ timerQueue: DispatchQueue) -> WebSocketPingController

    typealias CreateEngine = (
      _ request: URLRequest,
      _ sessionConfiguration: URLSessionConfiguration,
      _ callbackQueue: DispatchQueue
    ) -> WebSocketEngine

    var timerType: WSTimer.Type = DefaultTimer.self

    var createPingController: CreatePingController = WebSocketPingController.init

    var createEngine: CreateEngine = {
      NativeWebSocketEngine(request: $0, sessionConfiguration: $1, callbackQueue: $2)
    }

    var eventBatcherBuilder: (
      _ handler: @escaping ([WSEvent], @escaping () -> Void) -> Void
    ) -> EventBatcher = {
      Batcher<WSEvent>(period: 0.5, handler: $0)
    }
  }
}

// MARK: - Web Socket Delegate

extension WSClient: WebSocketEngineDelegate {
  func webSocketDidConnect() {
    connectionState = .waitingForConnectionId
  }

  func webSocketDidReceiveMessage(_ message: String) {
    do {
      let messageData = Data(message.utf8)
      wsLogger.debug("Event received:\n\(messageData.debugPrettyPrintedJSON)")

      let event = try eventDecoder.decode(from: messageData)
      if let healthCheckEvent = event as? HealthCheckEvent {
        engineQueue.async { [weak self] in
          self?.pingController.pongReceived()
          self?.connectionState = .connected(connectionId: healthCheckEvent.connectionId)
        }
      } else {
        eventsBatcher.append(event)
      }
    } catch let error {
      wsLogger.error("Error decoding event: \(error.localizedDescription)")
      connectionState = .disconnected(source: .noPongReceived)
    }
  }

  func webSocketDidDisconnect(error engineError: WebSocketEngineError?) {
    switch connectionState {
    case .connecting, .waitingForConnectionId, .connected:
      let serverError = engineError.map { ClientError.WebSocket(with: $0) }

      connectionState = .disconnected(source: .serverInitiated(error: serverError))

    case let .disconnecting(source):
      connectionState = .disconnected(source: source)

    case .initialized, .disconnected:
      wsLogger.error("Web socket can not be disconnected when in disconnected state.")
    }
  }
}

// MARK: - Ping Controller Delegate

extension WSClient: WebSocketPingControllerDelegate {
  func sendPing() {
    engineQueue.async { [weak engine] in
      engine?.sendPing()
    }
  }

  func disconnectOnNoPongReceived() {
    disconnect(source: .noPongReceived) {
      wsLogger.debug("Websocket is disconnected because of no pong received")
    }
  }
}


// MARK: - Test helpers

#if TESTS
extension WSClient {
  /// Simulates connection status change
  func simulateConnectionStatus(_ status: WebSocketConnectionState) {
    connectionState = status
  }
}
#endif

// MARK: - Notifications

extension Notification.Name {
  /// The name of the notification posted when a new event is published/
  static let newEventReceived = Notification.Name("com.vueroidHub.new_event_received")
}

extension Notification {
  private static let eventKey = "com.vueroidHub.event_key"

  init(newEventReceived event: WSEvent, sender: Any) {
    self.init(name: .newEventReceived, object: sender, userInfo: [Self.eventKey: event])
  }

  var event: WSEvent? {
    userInfo?[Self.eventKey] as? WSEvent
  }
}

extension ClientError {
  public final class WebSocket: ClientError {}
}

/// WebSocket Error
struct WebSocketErrorContainer: Decodable {
  /// A server error was received.
  let error: ErrorPayload
}

extension Data {
  /// Converts the data into a pretty-printed JSON string. Use only for debug purposes since this operation can be expensive.
  var debugPrettyPrintedJSON: String {
    do {
      let jsonObject = try JSONSerialization.jsonObject(with: self, options: [.allowFragments])
      let prettyPrintedData = try JSONSerialization.data(withJSONObject: jsonObject, options: [.prettyPrinted])
      return String(data: prettyPrintedData, encoding: .utf8) ?? "Error: Data to String decoding failed."
    } catch {
      return "JSON decoding failed with error: \(error)"
    }
  }
}
