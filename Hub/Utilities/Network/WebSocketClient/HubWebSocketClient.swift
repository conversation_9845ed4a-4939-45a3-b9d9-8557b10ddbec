//
//  HubWebSocketClient.swift
//  Hub
//
//  Created by ncn on 3/21/25.
//

import Foundation
import Network

struct WebSocketConfiguration {
  var additionalHeaders: [String: String]
  var pingInterval: TimeInterval
  var pingTryReconnectCountLimit: Int
  
  init(
    additionalHeaders: [String: String] = [:],
    pingInterval: TimeInterval = 20,
    pingTryReconnectCountLimit: Int = 3
  ) {
    self.additionalHeaders = additionalHeaders
    self.pingInterval = pingInterval
    self.pingTryReconnectCountLimit = pingTryReconnectCountLimit
  }
}

enum ConnectionState {
  case disconnected
  case connecting
  case connected
}

@globalActor actor WebSocketActor {
  static let shared = WebSocketActor()
}

typealias WebSocketClientMessage = URLSessionWebSocketTask.Message

@WebSocketActor
class HubWebSocketClient: NSObject, Sendable {
  
  private var session: URLSession?
  private let url: URL
  private let configuration: WebSocketConfiguration
  
  private var monitor: NWPathMonitor?
  private var wsTask: URLSessionWebSocketTask?
  private var pingTask: Task<Void, Never>?
  private var pingTryCount = 0
  
  var onReceive: ((Result<WebSocketClientMessage, Error>) -> Void)?
  var onConnectionStateChange: ((ConnectionState) -> Void)?
  
  private(set) var connectionState = ConnectionState.disconnected {
    didSet {
      onConnectionStateChange?(connectionState)
    }
  }
  
  private var pendingRequests: [Int: CheckedContinuation<WebSocketClientMessage, Error>] = [:]
  
  nonisolated init(
    url: URL,
    configuration: WebSocketConfiguration
  ) {
    self.url = url
    self.configuration = configuration
  }
  
  func connect() {
    guard wsTask == nil else {
      wsLogger.info("WebSocket Task is already exists")
      return
    }

    let session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
    self.session = session

    var request = URLRequest(url: url, timeoutInterval: TimeInterval(30))
    configuration.additionalHeaders.forEach {
      request.addValue($0.value, forHTTPHeaderField: $0.key)
    }
    
    wsTask = session.webSocketTask(with: request)
    wsTask?.delegate = self
    wsTask?.maximumMessageSize = 1024 * 1024 * 100
    wsTask?.resume()
    
    connectionState = .connecting
    receiveMessage()
    startMonitorNetworkConnectivity()
    schedulePing()
    
  }
  
  func disconnect() {
    disconnect(shouldRemoveNetworkMonitor: true)
  }
  
  private func disconnect(shouldRemoveNetworkMonitor: Bool) {
    self.wsTask?.cancel()
    self.wsTask = nil
    self.pingTask?.cancel()
    self.pingTask = nil
    self.connectionState = .disconnected
    if shouldRemoveNetworkMonitor {
      self.monitor?.cancel()
      self.monitor = nil
    }
  }
  
  func reconnect() {
    self.disconnect(shouldRemoveNetworkMonitor: false)
    self.connect()
  }
  
  func send(_ message: WebSocketClientMessage) async throws -> Result<WebSocketClientMessage, Error> {
    guard let task = wsTask, connectionState == .connected else {
      return .failure(NSError(domain: "WebSocketClient", code: 1, userInfo: [NSLocalizedDescriptionKey: "WebSocket is not connected"]))
    }
    
    do {
      try await task.send(message)
      
      // Extract sequence number from message
      if case .string(let text) = message,
         let dict = text.dictionaryFromJson(),
         let header = dict["header"] as? [String: Any],
         let seq = header["seq"] as? Int {
        
        // Wait for response using continuation
        return try await withCheckedThrowingContinuation { continuation in
          pendingRequests[seq] = continuation as! CheckedContinuation<WebSocketClientMessage, Error>
          
          // Set timeout
          Task {
            try await Task.sleep(nanoseconds: 10_000_000_000) // 10 seconds timeout
            if let cont = pendingRequests.removeValue(forKey: seq) {
              cont.resume(throwing: NSError(domain: "WebSocketClient", code: 2, userInfo: [NSLocalizedDescriptionKey: "Response timeout"]))
            }
          }
        }
      }
      
      return .success(message)
    } catch {
      return .failure(error)
    }
  }
  
  private func receiveMessage() {
    self.wsTask?.receive { [weak self] result in
      Task { @WebSocketActor in
        guard let self = self else { return }
        
        // Handle pending requests first
        if case .success(let message) = result,
           case .string(let text) = message,
           let dict = text.dictionaryFromJson(),
           let header = dict["header"] as? [String: Any],
           let responseSeq = header["seq"] as? Int {
          
          if let continuation = self.pendingRequests.removeValue(forKey: responseSeq) {
            continuation.resume(returning: message)
            return
          }
        }
        
        // Handle regular message
        self.onReceive?(result)
        
        if self.connectionState == .connected {
          self.receiveMessage()
        }
      }
    }
  }
  
  private func startMonitorNetworkConnectivity() {
    guard monitor == nil else { return }
    monitor = .init()
    monitor?.pathUpdateHandler = { path in
      Task { @WebSocketActor [weak self] in
        guard let self else { return }
        if path.status == .satisfied, self.wsTask == nil {
          self.connect()
          return
        }
        
        if path.status != .satisfied {
          self.disconnect(shouldRemoveNetworkMonitor: false)
        }
      }
    }
    monitor?.start(queue: .main)
  }
  
  private func schedulePing() {
    pingTask?.cancel()
    pingTryCount = 0
    pingTask = Task { [weak self] in
      while true {
        if #available(iOS 16.0, *) {
          try? await Task.sleep(for: .seconds(self?.configuration.pingInterval ?? 5))
        } else {
          // Fallback on earlier versions
        }
        guard !Task.isCancelled, let self, let task = self.wsTask else { break }
        
        if task.state == .running, self.pingTryCount < self.configuration.pingTryReconnectCountLimit {
          self.pingTryCount += 1
          wsLogger.info("Ping: Send")
          task.sendPing { error in
            if let error {
              wsLogger.error("Ping Failed: \(error.localizedDescription)")
            } else {
              wsLogger.debug("Ping: Pong Received")
              Task { @WebSocketActor [weak self] in
                self?.pingTryCount = 0
              }
            }
          }
           
          
        } else {
          self.reconnect()
          break
        }
      }
    }
  }
  
}

extension HubWebSocketClient: URLSessionWebSocketDelegate {
  nonisolated func urlSession(
    _ session: URLSession,
    webSocketTask: URLSessionWebSocketTask,
    didOpenWithProtocol protocol: String?
  ) {
    Task { @WebSocketActor [weak self] in
      self?.connectionState = .connected
    }
  }
  
  nonisolated func urlSession(
    _ session: URLSession,
    webSocketTask: URLSessionWebSocketTask,
    didCloseWith closeCode: URLSessionWebSocketTask.CloseCode,
    reason: Data?
  ) {
    Task { @WebSocketActor [weak self] in
      self?.connectionState = .disconnected
    }
  }
}

extension HubWebSocketClient: URLSessionDelegate {
  // URLSessionDelegate: 인증서 검증
  nonisolated func urlSession(
    _ session: URLSession,
    didReceive challenge: URLAuthenticationChallenge,
    completionHandler: @escaping (
      URLSession.AuthChallengeDisposition,
      URLCredential?
    ) -> Void
  ) {
    
    if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
      if let serverTrust = challenge.protectionSpace.serverTrust {
        // 서버 인증서 체인 가져오기
        if let serverCerts = SecTrustCopyCertificateChain(serverTrust) as? [SecCertificate],
           let _ = serverCerts.first {
//           SecCertificateCopyData(serverCert) == SecCertificateCopyData(localCert) {
          #if false
          if let dic = SecTrustCopyResult(serverTrust) {
            hLogger.info("certificate info:  \(dic)")
          }
          let secKey = SecTrustCopyKey(serverTrust)
          hLogger.info("public key:  \(secKey.debugDescription)")
          #endif
          
          hLogger.info("wss certificate")
          completionHandler(.useCredential, URLCredential(trust: serverTrust))
          return
        }
      }
    }
    
    hLogger.error("wss 인증서 없음")
    // 인증서 없을 시 연결 취소
    hLogger.critical("wss cancelAuthenticationChallenge")
    completionHandler(.cancelAuthenticationChallenge, nil)
  }
}
