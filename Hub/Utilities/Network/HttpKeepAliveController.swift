//
//  HttpKeepAliveController.swift
//  Hub
//
//  Created by ncn on 1/16/25.
//


import Foundation

protocol HttpKeepAliveControllerDelegate: AnyObject {
  func sendKeepAlive()
  func disconnectOnNoPongReceived()
}
/*
 curl 'http://*************/keepalive' \
 -X POST \
 -H 'Host: *************' \
 -H 'Content-Type: application/x-www-form-urlencoded; charset=utf-8' \
 --data-raw 'timeout=600000' \
 --proxy http://localhost:9090
 */
class HttpKeepAliveController {

  // timeout 값 6초 주기는 3초.
  static let KeepAliveTimeInterval: TimeInterval = 3
  static let pongTimeoutTimeInterval: TimeInterval = 3
  private let timerType: WSTimer.Type
  private let timerQueue: DispatchQueue
  private var keepAliveTimerControl: RepeatingTimerControl?
  private var pongTimeoutTimer: TimerControl?

  weak var delegate: HttpKeepAliveControllerDelegate?

  deinit {
    cancelPongTimeoutTimer()
  }

  /// Creates a KeepAlive controller.
  /// - Parameters:
  ///   - timerType: a timer type.
  ///   - timerQueue: a timer dispatch queue.
  init(timerType: WSTimer.Type, timerQueue: DispatchQueue) {
    self.timerType = timerType
    self.timerQueue = timerQueue
  }

  /// `WebSocketClient` should call this when the connection state did change.
  func connectionStateDidChange(_ connectionState: WebSocketConnectionState) {
    guard delegate != nil else { return }

    cancelPongTimeoutTimer()
    scheduleKeepAliveTimerIfNeeded()

    if connectionState.isConnected {
      nLogger.info("Resume Http KeepAlive timer")
      keepAliveTimerControl?.resume()
    } else {
      keepAliveTimerControl?.suspend()
    }
  }

  // MARK: - KeepAlive

  private func sendKeepAlive() {
    schedulePongTimeoutTimer()

    nLogger.trace("send KeepAlive")
    delegate?.sendKeepAlive()
  }

  func pongReceived() {
    nLogger.info("WebSocket Pong")
    cancelPongTimeoutTimer()
  }

  // MARK: Timers

  private func scheduleKeepAliveTimerIfNeeded() {
    guard keepAliveTimerControl == nil else { return }
    keepAliveTimerControl = timerType
      .scheduleRepeating(
        timeInterval: Self.KeepAliveTimeInterval,
        queue: timerQueue
      ) { [weak self] in
        self?.sendKeepAlive()
      }
  }

  private func schedulePongTimeoutTimer() {
    cancelPongTimeoutTimer()
    // Start pong timeout timer.
    pongTimeoutTimer = timerType
      .schedule(
        timeInterval: Self.pongTimeoutTimeInterval,
        queue: timerQueue
      ) { [weak self] in
        wsLogger.info("WebSocket Pong timeout. Reconnect")
        self?.delegate?.disconnectOnNoPongReceived()
      }
  }

  private func cancelPongTimeoutTimer() {
    pongTimeoutTimer?.cancel()
    pongTimeoutTimer = nil
  }
}
