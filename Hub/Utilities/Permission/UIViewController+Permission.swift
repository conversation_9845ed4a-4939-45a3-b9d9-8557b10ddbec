//
//  UIViewController+Permission.swift
//  Hub
//
//  Created on 2024/07/25.
//

import UIKit

extension UIViewController {
    
    /// Request Bluetooth permission and show settings alert if needed
    /// - Parameter completion: Called with true if permission is granted, false otherwise
    func requestBluetoothPermission(completion: @escaping (Bool) -> Void) {
        PermissionManager.checkAndRequestPermission(type: .bluetooth, completion: completion)
    }
    
    /// Request Photos permission and show settings alert if needed
    /// - Parameter completion: Called with true if permission is granted, false otherwise
    func requestPhotosPermission(completion: @escaping (Bool) -> Void) {
        PermissionManager.checkAndRequestPermission(type: .photos, completion: completion)
    }
    
    /// Request Location permission and show settings alert if needed
    /// - Parameter completion: Called with true if permission is granted, false otherwise
    func requestLocationPermission(completion: @escaping (Bool) -> Void) {
        PermissionManager.checkAndRequestPermission(type: .location, completion: completion)
    }
}
