//
//  PermissionManager.swift
//  Hub
//
//  Created on 2024/07/25.
//

import Foundation
import Photos
import CoreBluetooth
import CoreLocation
import UIKit
import Network

/// A utility class to handle permission requests and guide users to settings when needed
class PermissionManager {

  enum PermissionType {
    case bluetooth
    case photos
    case location
    case localNetwork

    var title: String {
      switch self {
      case .bluetooth:
        return L.permission_txt_bluetooth.localized
      case .photos:
        return L.permission_txt_photo.localized
      case .location:
        return L.permission_txt_location.localized
      case .localNetwork:
        return L.permission_txt_localnetwork.localized
      }
    }

    var message: String {
      switch self {
      case .bluetooth:
        return L.permission_txt_bluetooth_describe.localized
      case .photos:
        return L.permission_txt_storage_describe.localized
      case .location:
        return L.permission_txt_location_describe.localized
      case .localNetwork:
        return L.permission_localnetwork_describe.localized
      }
    }
  }

  // MARK: - Bluetooth Permission

  static func checkBluetoothPermission(completion: @escaping (Bool) -> Void) {
    // state 체크를 위한 임시
    let manager = CBCentralManager(delegate: nil, queue: nil, options: [CBCentralManagerOptionShowPowerAlertKey: false])

    switch manager.state {
    case .poweredOn:
      completion(true)
    case .unauthorized:
      completion(false)
    case .poweredOff, .resetting, .unsupported, .unknown:
      completion(false)
    @unknown default:
      completion(false)
    }
  }

  // MARK: - Photos Permission

  static func checkPhotosPermission(completion: @escaping (Bool) -> Void) {
    let status = PHPhotoLibrary.authorizationStatus()
    switch status {
    case .authorized, .limited:
      completion(true)
    case .denied, .restricted:
      completion(false)
    case .notDetermined:
      requestPhotosPermission { granted in
        completion(granted)
      }
    @unknown default:
      completion(false)
    }
  }

  static func requestPhotosPermission(completion: @escaping (Bool) -> Void) {
    PHPhotoLibrary.requestAuthorization { status in
      DispatchQueue.main.async {
        completion(status == .authorized || status == .limited)
      }
    }
  }

  /// Check photo permission for tab selection
  /// - Parameters:
  ///   - completion: Called with true if permission is granted, false otherwise
  static func checkPhotosPermissionForTab(completion: @escaping (Bool) -> Void) {
    let status = PHPhotoLibrary.authorizationStatus()

    switch status {
    case .authorized, .limited:
      completion(true)

    case .denied, .restricted:
      // Show settings alert if permission was denied
      showSettingsAlert(for: .photos)
      completion(false)

    case .notDetermined:
      // Request permission
      PHPhotoLibrary.requestAuthorization { status in
        DispatchQueue.main.async {
          let granted = (status == .authorized || status == .limited)
          if !granted {
            // Show alert if permission was denied
            showSettingsAlert(for: .photos)
          }
          completion(granted)
        }
      }

    @unknown default:
      completion(false)
    }
  }

  // MARK: - Location Permission

  static var locationManager: CLLocationManager?
  static var locationCompletionHandler: ((Bool) -> Void)?

  static func checkLocationPermission(completion: @escaping (Bool) -> Void) {
    let status = CLLocationManager().authorizationStatus
    switch status {
    case .authorizedAlways, .authorizedWhenInUse:
      completion(true)
    case .denied, .restricted:
      completion(false)
    case .notDetermined:
      // We'll need to request permission through a CLLocationManager instance
      completion(false)
    @unknown default:
      completion(false)
    }
  }

  static func requestLocationPermission(completion: @escaping (Bool) -> Void) {
    locationCompletionHandler = completion
    locationManager = CLLocationManager()
    locationManager?.delegate = LocationPermissionDelegate.shared
    locationManager?.requestWhenInUseAuthorization()
  }

  /// Check location permission for tab selection
  /// - Parameters:
  ///   - completion: Called with true if permission is granted, false otherwise
  static func checkLocationPermissionForTab(completion: @escaping (Bool) -> Void) {
    let status = CLLocationManager().authorizationStatus

    switch status {
    case .authorizedAlways, .authorizedWhenInUse:
      completion(true)

    case .denied, .restricted:
      // Show settings alert if permission was denied
      showSettingsAlert(for: .location)
      completion(false)

    case .notDetermined:
      // Request permission
      locationCompletionHandler = { granted in
        DispatchQueue.main.async {
          if !granted {
            // Show alert if permission was denied
            showSettingsAlert(for: .location)
          }
          completion(granted)
        }
      }
      locationManager = CLLocationManager()
      locationManager?.delegate = LocationPermissionDelegate.shared
      locationManager?.requestWhenInUseAuthorization()

    @unknown default:
      completion(false)
    }
  }

  // MARK: - Local Network Permission

  private static var browser: NWBrowser?

  /// Check if the app has Local Network permission
  /// - Parameters:
  ///   - completion: Called with true if permission is granted or if iOS version doesn't require it
  static func checkLocalNetworkPermission(completion: @escaping (Bool) -> Void) {
    // Local Network permission is only required on iOS 14 and later
    guard #available(iOS 14.0, *) else {
      // For iOS 13 and earlier, no explicit permission is needed
      completion(true)
      return
    }

    // Create a browser that searches for Bonjour services
    let parameters = NWParameters()
    parameters.includePeerToPeer = true

    // Use the _http._tcp service type which is commonly used
    browser = NWBrowser(for: .bonjour(type: "_http._tcp", domain: nil), using: parameters)

    // Set up a state update handler
    browser?.stateUpdateHandler = { state in
      switch state {
      case .ready, .setup:
        // Browser is ready, which means permission was granted
        DispatchQueue.main.async {
          browser?.cancel()
          browser = nil
          completion(true)
        }

      case .failed(let error):
        // Check if the error is related to permission denial
        if error.debugDescription.contains("denied") ||
           error.debugDescription.contains("permission") {
          DispatchQueue.main.async {
            browser?.cancel()
            browser = nil
            showSettingsAlert(for: .localNetwork)
            completion(false)
          }
        } else {
          // Other errors might not be permission-related
          DispatchQueue.main.async {
            browser?.cancel()
            browser = nil
            completion(true)
          }
        }

      default:
        // For other states, we'll wait for the next update
        break
      }
    }

    // Start the browser to trigger the permission dialog
    browser?.start(queue: .main)

    // Set a timeout to ensure we don't wait forever
    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
      if browser != nil {
        browser?.cancel()
        browser = nil
        completion(true) // Assume granted if we didn't get a definitive answer
      }
    }
  }

  // MARK: - Settings Navigation

  /// Shows an alert directing the user to the app settings
  /// - Parameters:
  ///   - type: The type of permission
  ///   - viewController: The view controller to present the alert on
  static func showSettingsAlert(for type: PermissionType) {
    LKPopupView.popup.alert {[
      .title(type.title),
      .subTitle(type.message),
      .showCancel(true),
      .confirmAction([
        .text("Settings"),
        .bgColor(.vueroidBlue),
        .tapActionCallback({
          if let url = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(url)
          }
        })
      ])
    ]}
  }

  /// Checks if the permission is granted and shows settings alert if needed
  /// - Parameters:
  ///   - type: The type of permission to check
  ///   - completion: Called with true if permission is granted, false otherwise
  static func checkAndRequestPermission(type: PermissionType, completion: @escaping (Bool) -> Void) {
    switch type {
    case .bluetooth:
      checkBluetoothPermission { granted in
        if !granted {
          showSettingsAlert(for: type)
        }
        completion(granted)
      }

    case .photos:
      checkPhotosPermission { granted in
        if !granted {
          showSettingsAlert(for: type)
        }
        completion(granted)
      }

    case .location:
      checkLocationPermissionForTab(completion: completion)

    case .localNetwork:
      checkLocalNetworkPermission(completion: completion)
    }
  }
}

// MARK: - Location Permission Delegate
class LocationPermissionDelegate: NSObject, CLLocationManagerDelegate {
  static let shared = LocationPermissionDelegate()

  func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
    let status = manager.authorizationStatus
    let granted = (status == .authorizedWhenInUse || status == .authorizedAlways)
    PermissionManager.locationCompletionHandler?(granted)
    PermissionManager.locationManager = nil
  }

  func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
    // This method is called on iOS 13 and earlier
    let granted = (status == .authorizedWhenInUse || status == .authorizedAlways)
    PermissionManager.locationCompletionHandler?(granted)
    PermissionManager.locationManager = nil
  }
}
