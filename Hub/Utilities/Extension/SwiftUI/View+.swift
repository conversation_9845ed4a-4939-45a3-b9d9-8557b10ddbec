//
//  View+.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 1/5/24.
//

import SwiftUI

extension View {
  public func loading(_ isLoading: Bool) -> Self {
    if isLoading {
      LoadingWindow.shared.show()
    } else {
      LoadingWindow.shared.hide()
    }
    return self
  }
  public func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
    clipShape(RoundedCorner(radius: radius, corners: corners))
  }
}
