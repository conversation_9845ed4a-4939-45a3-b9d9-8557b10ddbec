//
//  TextField+.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/9/24.
//

import SwiftUI

extension TextField {
  
  /// TextField MaxLength Modifier
  /// - Parameters:
  ///   - text: binding text
  ///   - maxLength: maxLength, 0이하로 설정한 경우 무제한으로 설정
  /// - Returns: TextField
  func maxLength(text: Binding<String>, _ maxLength: Int) -> some View {
    return ModifiedContent(
      content: self,
      modifier: MaxLengthModifier(
        text: text,
        maxLength: maxLength
      )
    )
  }
}

struct MaxLengthModifier: ViewModifier {
  @Binding var text: String
  let maxLength: Int
  
  func body(content: Content) -> some View {
    content
      .onChange(
        of: text,
        perform: { newValue in
          if maxLength <= 0 {
            return
          }
          if newValue.count > maxLength {
            text = String(newValue.prefix(maxLength))
          }
        }
      )
  }
}

