//
//  SecureField+.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/9/24.
//

import SwiftUI

extension SecureField {
  /// TextField 에서 최대 글자수를 정하기 위한 Modifier
  func maxLength(text: Binding<String>, _ maxLength: Int) -> some View {
    return ModifiedContent(
      content: self,
      modifier: MaxLengthModifier(
        text: text,
        maxLength: maxLength
      )
    )
  }
}
