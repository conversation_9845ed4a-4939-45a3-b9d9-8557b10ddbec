//
//  UIImage+.swift
//  Hub
//
//  Created by ncn on 2023/02/15.
//

import UIKit

extension UIImage {
  public func withRoundedCorners(radius: CGFloat? = nil) -> UIImage? {
    let maxRadius = min(size.width, size.height) / 2
    let cornerRadius: CGFloat
    if let radius = radius, radius > 0 && radius <= maxRadius {
      cornerRadius = radius
    } else {
      cornerRadius = maxRadius
    }
    UIGraphicsBeginImageContextWithOptions(size, false, scale)
    let rect = CGRect(origin: .zero, size: size)
    UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius).addClip()
    draw(in: rect)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return image
  }

  func imageWithColor(color: UIColor) -> UIImage? {
    let rect = CGRect(x: 0.0, y: 0.0, width: 1.0, height: 1.0)
    UIGraphicsBeginImageContext(rect.size)
    let context = UIGraphicsGetCurrentContext()

    context?.setFillColor(color.cgColor)
    context?.fill(rect)

    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    return image
  }
}

// 아이콘 변수화
extension UIImage {
  static let pageUp = UIImage(named: "icon_pageup")!
  static let pageDown = UIImage(named: "icon_pagedown")!
  static let arrowRight14 = UIImage(named: "icon_line-arrow_right_14")!
  static let whiteDeleteIcon = UIImage(named: "icon_del_w")!
  static let whiteExitIcon = UIImage(named: "icon_exit")!
  static let blueCheckIcon = UIImage(named: "icon_check_blue")!
  static let traceIcon = UIImage(named: "icon_trace")!

  public enum LKPopupAssetIcon: String {
    case toastFail = "toast_fail"
    case toastSuccess = "toast_success"
    case loading = "lk_loading"
    var image: UIImage? {
      return UIImage(named: self.rawValue)
    }
  }
}
