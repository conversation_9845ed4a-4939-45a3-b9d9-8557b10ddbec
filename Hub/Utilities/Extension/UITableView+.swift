//
//  UITableView+.swift
//  Hub
//
//  Created by ncn on 2022/12/01.
//

import UIKit

extension UITableView {

  func register<T: UITableViewCell>(cellType: T.Type) where T: Reusable {
    register(cellType.self, forCellReuseIdentifier: cellType.identifier)
  }

  func dequeueReusableCell<T: UITableViewCell>(
    for indexPath: IndexPath,
    cellType: T.Type = T.self
  ) -> T where T: Reusable {
    guard let cell = dequeueReusableCell(withIdentifier: cellType.identifier, for: indexPath) as? T
    else {
      fatalError("Failed to dequeue a cell \(cellType.identifier) matching type \(cellType.self).")
    }
    cell.row = 1
    return cell
  }
}
