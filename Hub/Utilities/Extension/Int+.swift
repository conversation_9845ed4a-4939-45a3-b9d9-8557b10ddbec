//
//  Int+.swift
//  Hub
//
//  Created by ncn on 2023/03/07.
//

import UIKit

extension Int {
  public func getMinuteFromEpoch() -> Int {
    return Int((Double(self) / 60.0).rounded())
  }

  public func toTimeString(
    format: String, timezone: TimeZone? = TimeZone.current, locale: Locale = Locale.current
  ) -> String {
    let date = Date(timeIntervalSince1970: TimeInterval(self))
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    dateFormatter.timeZone = timezone
    dateFormatter.locale = locale
    return dateFormatter.string(from: date)
  }

  public func getTimeString() -> String {
    let seconds = abs(self) / 1000
    var ms = NSMutableString.init(capacity: 8)
    let m = seconds / 60 % 60
    let s = seconds % 60

    if seconds < 0 {
      ms = "∞"
    } else {
      if m < 10 { ms = "0" }
      ms.appendFormat("%d:", m)
      if s < 10 { ms.append("0") }
      ms.appendFormat("%d", s)
    }
    return ms as String
  }

  public func getDurationString() -> String {
    let seconds = abs(self)
    var ms = NSMutableString.init(capacity: 8)
    let h = seconds / 3600
    let m = seconds / 60 % 60
    let s = seconds % 60

    let hourString = String(format: "%02d", h)
    let minuteString = String(format: "%02d", m)
    let secondString = String(format: "%02d", s)

    return String(
      format: L.driving_time_value.localized, arguments: [hourString, minuteString, secondString])
  }

  public func secondToTime(seperate: String) -> String {
    let hours = self / 3600
    let minutes = (self % 3600) / 60
    let seconds = self % 60
    
    return String(format: "%02d\(seperate)%02d\(seperate)%02d", hours, minutes, seconds)
  }
    
  var secondsToFormattedTime: String {
    guard self > 0 else { return L.ai_no_wait_text.localized }
    let hours = self / 3600
    let minutes = (self % 3600) / 60
    let seconds = self % 60

    return String(format: "%02dh %02dm %02ds", hours, minutes, seconds)
  }

  /// UnixTimeStamp를 같은 날짜의 24:00:00 (다음 날 00:00:00)으로 변환
  /// - Returns: 같은 날짜의 자정(24:00:00)에 해당하는 UnixTimeStamp
  func toEndOfSameDay() -> Int {
    let date = Date(timeIntervalSince1970: TimeInterval(self))
    var calendar = Calendar(identifier: .gregorian)
    calendar.timeZone = Current.timezone

    let startOfDay = calendar.startOfDay(for: date)
    let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? date

    return Int(endOfDay.timeIntervalSince1970)
  }

  /// UnixTimeStamp를 같은 날짜의 00:00:00으로 변환
  /// - Returns: 같은 날짜의 시작(00:00:00)에 해당하는 UnixTimeStamp
  func toStartOfSameDay() -> Int {
    let date = Date(timeIntervalSince1970: TimeInterval(self))
    var calendar = Calendar(identifier: .gregorian)
    calendar.timeZone = Current.timezone

    let startOfDay = calendar.startOfDay(for: date)

    return Int(startOfDay.timeIntervalSince1970)
  }

  /// UnixTimeStamp를 Date로 변환하여 포맷된 문자열 반환
  /// - Parameter format: 날짜 포맷 (기본값: "yyyy/MM/dd HH:mm:ss")
  /// - Returns: 포맷된 날짜 문자열
  func toDateString(format: String = "yyyy/MM/dd HH:mm:ss") -> String {
    let date = Date(timeIntervalSince1970: TimeInterval(self))
    return date.toString(format: format)
  }
}

extension Int {
  /// 비트 패턴: 001(1), 011(3), 111(7), 101(5) 등
  var channelBitMaskingCount: Int {
    let channel1 = self & 0b001
    let channel2 = self & 0b010
    let channel3 = self & 0b100
    return [channel1, channel2, channel3].filter { $0 != 0 }.count
  }


  var attachChannel: S1Channel {
    let value = S1Channel(rawValue: self) ?? .F
    aLogger.debug("attchChannel: \(value.rawValue)")
    return value
  }

  var asInt32: Int32 {
    Int32(self)
  }
}

extension Int32 {
  var asInt: Int {
    Int(self)
  }
}
