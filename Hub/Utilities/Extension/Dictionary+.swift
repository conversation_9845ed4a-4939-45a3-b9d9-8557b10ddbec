//
//  Dictionary+.swift
//  Hub
//
//  Created by ncn on 2022/11/10.
//

import Foundation

extension Dictionary {
  var queryString: String? {
    return self.reduce("") { "\($0!)\($1.0)=\($1.1)&" }
  }

  func contains(key: Key) -> Bool {
    self.index(forKey: key) != nil
  }

  var queryItems: [URLQueryItem]? {
    var components = URLComponents()
    components.queryItems = self.map {
      URLQueryItem(name: $0 as! String, value: "\($1)")
    }
    return components.queryItems
  }

  func dictionaryToObject<T: Decodable>(objectType: T.Type) -> (obj: T?, error: Error?) {
    do {
      let jsonData = try JSONSerialization.data(withJSONObject: self, options: .prettyPrinted)
      let decoder = JSONDecoder()
      decoder.keyDecodingStrategy = .useDefaultKeys
      let obj = try decoder.decode(objectType, from: jsonData)
      return (obj, nil)
    } catch let DecodingError.dataCorrupted(context) {
      Log.error(to: "\(#function) error: \(context)")
      return (nil, DecodingError.dataCorrupted(context))
    } catch let DecodingError.keyNotFound(key, context) {
      Log.error(to: "\(#function) error: Key '\(key)' not found: \(context.debugDescription)")
      Log.error(to: "\(#function) error: codingPath: \(context.codingPath)")
      return (nil, DecodingError.keyNotFound(key, context))
    } catch let DecodingError.valueNotFound(value, context) {
      Log.error(to: "\(#function) error: value: \(value) codingPath: \(context.codingPath)")
      return (nil, DecodingError.valueNotFound(value, context))
    } catch let DecodingError.typeMismatch(type, context) {
      Log.error(to: "\(#function) error: Type '\(type)' mismatch: \(context.debugDescription)")
      Log.error(to: "\(#function) error: codingPath: \(context.codingPath)")
      return (nil, DecodingError.typeMismatch(type, context))
    } catch {
      Log.error(to: "\(#function) error: Error from JSON because: \(error.localizedDescription)")
      return (nil, error)
    }
  }

  func getJsonString() -> String? {
    if let jsonData = try? JSONSerialization.data(withJSONObject: self, options: []),
      let jsonString = String(data: jsonData, encoding: .utf8)
    {
      return jsonString
    }

    return nil
  }
  
  var prettyPrintedJson: String {
    do {
      let data = try JSONSerialization.data(withJSONObject: self, options: [.withoutEscapingSlashes, .prettyPrinted])
      return String(data: data, encoding: .utf8) ?? ""
    } catch {
      return ""
    }
  }
}
