//
//  Data+.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import Foundation

enum Endianness {
  case BigEndian
  case LittleEndian
}

extension Data {
  func dictionaryFromJson() -> [String: Any]? {
    do {
      let dic =
        try JSONSerialization.jsonObject(
          with: self,
          options: [.mutableContainers]) as? [String: Any]
      return dic
    } catch let error {
      Log.message(to: "error: \(error)")
      return nil
    }
  }

  func arrayFromJson() -> [[String: Any]]? {
    do {
      let array =
        try JSONSerialization.jsonObject(
          with: self,
          options: [.mutableContainers]) as? [[String: Any]]
      return array
    } catch let error {
      Log.message(to: "error: \(error)")
      return nil
    }
  }

  func toString(encoding: String.Encoding = .utf8) -> String? {
    return String(data: self, encoding: encoding)
  }
}

extension Data {
  func subdata(in range: ClosedRange<Index>) -> Data {
    return subdata(in: range.lowerBound..<range.upperBound + 1)
  }

  func int8<T>(at index: Index = 0) -> T {
    subdata(in: index..<self.index(index, offsetBy: MemoryLayout<T>.size))
      .withUnsafeBytes { $0.load(as: T.self) }
  }

  func scanValue<T: FixedWidthInteger>(at index: Data.Index, endianess: Endianness) -> T {
    let number: T =
      self.subdata(in: index..<index + MemoryLayout<T>.size).withUnsafeBytes({ $0.baseAddress })
      as! T
    switch endianess {
    case .BigEndian:
      return number.bigEndian
    case .LittleEndian:
      return number.littleEndian
    }
  }
}

extension Data {
  func parsing<T: Codable>(
    type: T.Type,
    strategy: JSONDecoder.KeyDecodingStrategy = .useDefaultKeys,
    completion: @escaping (_ data: T?, _ error: Error?) -> Void
  ) {
    do {
      let decoder = JSONDecoder()
      decoder.keyDecodingStrategy = strategy
      let data = try decoder.decode(type.self, from: self)
      return completion(data, nil)
    } catch let DecodingError.dataCorrupted(context) {
      Log.message(to: "\(#function) error: \(context)")
    } catch let DecodingError.keyNotFound(key, context) {
      Log.message(to: "\(#function) error: Key '\(key)' not found: \(context.debugDescription)")
      Log.message(to: "\(#function) error: codingPath: \(context.codingPath)")
    } catch let DecodingError.valueNotFound(value, context) {
      Log.message(to: "\(#function) error: value: \(value) codingPath: \(context.codingPath)")
    } catch let DecodingError.typeMismatch(type, context) {
      Log.message(to: "\(#function) error: Type '\(type)' mismatch: \(context.debugDescription)")
      Log.message(to: "\(#function) error: codingPath: \(context.codingPath)")
    } catch {
      Log.message(to: "\(#function) error: Error from JSON because: \(error.localizedDescription)")
      return completion(nil, error)
    }
  }
}
