//
//  UIScrollView+.swift
//  Hub
//
//  Created by ncn on 2023/02/13.
//

import UIKit

let _tag_scrollView_size = 20001

extension UIScrollView {
  func updateContentSize() {
    let unionCalculatedTotalRect = recursiveUnionInDepthFor(view: self)
    self.contentSize = CGSize(width: self.frame.width, height: unionCalculatedTotalRect.height)
  }

  private func recursiveUnionInDepthFor(view: UIView) -> CGRect {
    var totalRect: CGRect = .zero
    for subView in view.subviews {
      if subView.tag == _tag_scrollView_size {
        totalRect = totalRect.union(recursiveUnionInDepthFor(view: subView))
      }
    }
    return totalRect.union(view.frame)
  }
}
