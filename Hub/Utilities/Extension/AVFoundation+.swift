//
//  AVFoundation+.swift
//  Hub
//
//  Created by le<PERSON><PERSON><PERSON><PERSON> on 1/16/24.
//

import AVFoundation
import UIKit
import VLCKitSPM

extension AVMutableComposition {
  static func copyWithoutAudio(from asset: AVAsset) -> AVMutableComposition? {
    let composition = AVMutableComposition()
    guard
      let videoTrack = composition.addMutableTrack(
        withMediaType: .video, preferredTrackID: kCMPersistentTrackID_Invalid),
      let assetVideoTrack = asset.tracks(withMediaType: .video).first
    else { return nil }

    do {
      try videoTrack.insertTimeRange(
        CMTimeRangeMake(start: CMTime.zero, duration: asset.duration),
        of: assetVideoTrack,
        at: CMTime.zero)
    } catch {
      return nil
    }

    return composition
  }

}

// MARK: thumbnail
class AVFoundationUtils {

  // FIXME: video의 thumbnail 생성하지 않고 KingsFisher 로 변환
  #if false
  static func makeListThumbnail(cropVodUrl: URL?) {
    guard let cropVodUrl = cropVodUrl else { return }
    let asset = AVAsset(url: cropVodUrl)
    let generate = AVAssetImageGenerator(asset: asset)
    generate.appliesPreferredTrackTransform = true
    generate.requestedTimeToleranceAfter = CMTime.zero
    generate.requestedTimeToleranceBefore = CMTime.zero
    generate.appliesPreferredTrackTransform = true

    let fullTime: CMTime = asset.duration
    let thumbtimeSeconds = Int(CMTimeGetSeconds(fullTime))
    let maxLength = "\(thumbtimeSeconds)" as NSString
    let thumbTime = 1
    var image: UIImage?
    do {
      let time = CMTimeMakeWithSeconds(
        Float64(thumbTime), preferredTimescale: Int32(maxLength.length))
      Log.message(to: time)
      let cgImage = try generate.copyCGImage(at: time, actualTime: nil)
      image = UIImage(cgImage: cgImage)
    } catch let e as NSError {
      Log.message(to: e.localizedDescription)
    }

    guard let data = image?.jpegData(compressionQuality: 0.01),
      let fileName = cropVodUrl.lastPathComponent.components(separatedBy: ".").first,
      let filePath = UrlList.downloadedThumbPath()
    else { return }

    let name = String(format: "%@.jpg", fileName)

    FileManager.writeToFile(data: data, atPath: filePath, name: name, isSaveToPhoto: false)
    Log.message(to: filePath.appendingPathComponent(name))
  }
  #endif
  
  static func generateThumbnail(path: URL) -> UIImage? {
    do {
      let asset = AVURLAsset(url: path, options: nil)
      let imgGenerator = AVAssetImageGenerator(asset: asset)
      imgGenerator.appliesPreferredTrackTransform = true
      let time = CMTimeMake(value: 0, timescale: 1)
      let cgImage = try imgGenerator.copyCGImage( at: time, actualTime: nil)
      let thumbnail = UIImage(cgImage: cgImage)
      return thumbnail
    } catch let error {
      fLogger.error("*** Error generating thumbnail: \(error.localizedDescription)")
      return nil
    }
  }

  // New method that accepts VLCTime
  static func generateThumbnail(path: URL, vlcTime: VLCTime) -> UIImage? {
      do {
          let asset = AVURLAsset(url: path, options: nil)
          let imgGenerator = AVAssetImageGenerator(asset: asset)
          imgGenerator.appliesPreferredTrackTransform = true
          
          // Convert VLCTime to CMTime
          let timeInSeconds = Double(vlcTime.intValue) / 1000.0 // VLCTime is in milliseconds
          let time = CMTime(seconds: timeInSeconds, preferredTimescale: 1000)
          
          let cgImage = try imgGenerator.copyCGImage(at: time, actualTime: nil)
          let thumbnail = UIImage(cgImage: cgImage)
          return thumbnail
      } catch let error {
          fLogger.error("*** Error generating thumbnail: \(error.localizedDescription)")
          return nil
      }
  }
  
  // Convenience method with time validation
  static func generateThumbnailSafely(path: URL, vlcTime: VLCTime, maxDuration: VLCTime? = nil) -> UIImage? {
      // Validate time bounds
      if let maxDuration = maxDuration, vlcTime.intValue > maxDuration.intValue {
          fLogger.warning("Capture time (\(vlcTime.intValue)ms) exceeds video duration (\(maxDuration.intValue)ms)")
          return generateThumbnail(path: path, vlcTime: maxDuration)
      }
      
      if vlcTime.intValue < 0 {
          fLogger.warning("Negative capture time, using 0ms")
          return generateThumbnail(path: path, vlcTime: VLCTime(int: 0))
      }
      
      return generateThumbnail(path: path, vlcTime: vlcTime)
  }
}

@available(iOS 15.0, *)
func generateThumbnailFromRemoteVideo(url: URL) async throws -> UIImage? {
  let asset = AVURLAsset(url: url)
  let loader = AVAssetResourceLoader()

  // AVAssetResourceLoaderDelegate를 async/await로 변경하기 위한 브릿지
  let loadedAsset = try await withCheckedThrowingContinuation { continuation in
    asset.resourceLoader.setDelegate(loader, queue: DispatchQueue.main)
    loader.loadResource(for: asset) { (loadedAsset) in
      if let loadedAsset = loadedAsset {
        continuation.resume(returning: loadedAsset)
      } else {
        continuation.resume(throwing: NSError(domain: "AVAssetResourceLoaderError", code: 0))
      }
    }
  }

  let generator = AVAssetImageGenerator(asset: loadedAsset)
  generator.appliesPreferredTrackTransform = true

  let time = CMTime(seconds: 1, preferredTimescale: 600)
  do {
    let cgImage = try generator.copyCGImage(at: time, actualTime: nil)
    return UIImage(cgImage: cgImage)
  } catch {
    print("Error generating thumbnail: \(error)")
    throw error
  }
}

class AVAssetResourceLoader: NSObject, AVAssetResourceLoaderDelegate {
  private var pendingRequests = [AVAssetResourceLoadingRequest]()

  func resourceLoader(
    _ resourceLoader: AVAssetResourceLoader,
    shouldWaitForLoadingOfRequestedResource loadingRequest: AVAssetResourceLoadingRequest
  ) -> Bool {
    pendingRequests.append(loadingRequest)
    startDataRequest(loadingRequest)
    return true
  }

  func resourceLoader(
    _ resourceLoader: AVAssetResourceLoader,
    didCancel loadingRequest: AVAssetResourceLoadingRequest
  ) {
    pendingRequests.removeAll(where: { $0 === loadingRequest })
  }

  private func startDataRequest(_ loadingRequest: AVAssetResourceLoadingRequest) {
    guard let url = loadingRequest.request.url,
      let dataRequest = loadingRequest.dataRequest
    else {
      loadingRequest.finishLoading(with: NSError(domain: "Invalid Request", code: 0, userInfo: nil))
      return
    }

    URLSession.shared.dataTask(with: url) { (data, response, error) in
      defer {
        loadingRequest.finishLoading()
      }

      if let error = error {
        loadingRequest.finishLoading(with: error)
        return
      }

      guard let data = data, let response = response as? HTTPURLResponse else {
        loadingRequest.finishLoading(with: NSError(domain: "Invalid Data", code: 0, userInfo: nil))
        return
      }

      loadingRequest.contentInformationRequest?.contentLength = response.expectedContentLength
      loadingRequest.contentInformationRequest?.contentType = response.mimeType
      loadingRequest.contentInformationRequest?.isByteRangeAccessSupported = true

      dataRequest.respond(with: data)
    }.resume()
  }

  func loadResource(for asset: AVURLAsset, completion: @escaping (AVURLAsset?) -> Void) {
    asset.loadTracks(withMediaType: .video) { [weak self] tracks, _ in
      DispatchQueue.main.async {
        completion(asset)
      }
    }
  }
}
