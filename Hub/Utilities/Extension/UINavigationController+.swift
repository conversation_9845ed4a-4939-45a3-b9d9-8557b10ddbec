//
//  UINavigationController+.swift
//  Hub
//
//  Created by ncn on 2023/04/03.
//

import UIKit

func setNavigationBarAppearance() {
  let appearance = UINavigationBarAppearance()
  appearance.configureWithOpaqueBackground()
  appearance.shadowColor = .clear
  appearance.backgroundColor = .background
  appearance.titleTextAttributes = UINavigationController.textAttributes(
    for: .pretendard(ofSize: 16, weight: .bold) ?? .boldSystemFont(ofSize: 16),
    color: .mainBlack
  )

  let backImage = #imageLiteral(resourceName: "back.pdf").withRenderingMode(.alwaysOriginal)
  appearance.setBackIndicatorImage(backImage, transitionMaskImage: backImage)

  UINavigationBar.appearance().standardAppearance = appearance
  UINavigationBar.appearance().scrollEdgeAppearance = appearance
  UINavigationBar.appearance().compactAppearance = appearance
}

extension UINavigationController {
  @discardableResult
  func popViewController(animated: Bool, completion: @escaping (() -> Void)) -> UIViewController? {
    CATransaction.setCompletionBlock(completion)
    CATransaction.begin()
    let poppedViewController = self.popViewController(animated: animated)
    CATransaction.commit()
    return poppedViewController
  }

  fileprivate class func textAttributes(
    for font: UIFont,
    color: UIColor
  ) -> [NSAttributedString.Key: Any] {
    [
      .font: font,
      .foregroundColor: color,
      .shadow: NSShadow.clear(),
    ]
  }
  
  func share(_ activityItems: [Any], completion: (() -> Void)? = nil) {
      let activityViewController = UIActivityViewController(
          activityItems: activityItems, applicationActivities: nil
      )
      activityViewController.completionWithItemsHandler = { _, _, _, _ in
          completion?()
      }

      let view = navigationController?.view ?? view
      let viewBound = view.map { CGRect(x: $0.bounds.midX, y: $0.bounds.midY, width: 0, height: 0) }
      activityViewController.modalPresentationStyle = .formSheet
      activityViewController.popoverPresentationController?.permittedArrowDirections = []
      activityViewController.popoverPresentationController?.sourceView = view
      activityViewController.popoverPresentationController?.sourceRect = viewBound ?? .zero
      present(activityViewController, animated: true)
  }
}

extension NSShadow {
  fileprivate class func clear() -> NSShadow {
    let shadow = NSShadow()
    shadow.shadowColor = UIColor.clear
    shadow.shadowOffset = .zero
    return shadow
  }
}

extension UITabBarController {
  func toggleTabbar(isShow: Bool) {
    aLogger.debug("toggleTabbar: \(isShow)")
    
    DispatchQueue.main.async {
      if #available(iOS 18.0, *) {
        self.isTabBarHidden = !isShow
      } else {
        self.tabBar.isHidden = !isShow
      }
    }
  }
  
  func isShowTabbar() -> Bool {
    var tabbarHidden = false
    if #available(iOS 18.0, *) {
      tabbarHidden = self.tabBarController?.isTabBarHidden ?? false
    } else {
      tabbarHidden = self.tabBarController?.tabBar.isHidden ?? false
    }
    return !tabbarHidden
  }
}
