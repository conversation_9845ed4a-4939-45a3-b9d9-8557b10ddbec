//
//  Keychain+.swift
//  Hub
//
//  Created by ncn on 2023/04/05.
//

import Foundation
import Security

public class Keychain {
  //    public class func save(str: String?, forKey: String) -> Bool {
  //        if str == nil {
  //            return false
  //        }
  //
  //        let dataFromString: NSData = str!.data(using: NSUTF8StringEncoding, allowLossyConversion: false)! as NSData
  //        let query = [
  //            kSecClass as String: kSecClassGenericPassword as String,
  //            kSecAttrAccount as String: forKey,
  //            kSecValueData as String: dataFromString
  //        ] as [String : Any]
  //
  //        SecItemDelete(query as CFDictionary)
  //
  //        let status: OSStatus = SecItemAdd(query as CFDictionary, nil)
  //
  //        return status == noErr
  //    }

  //    public class func load(key: String) -> String? {
  //        let query = [
  //            kSecClass as String: kSecClassGenericPassword,
  //            kSecAttrAccount as String: key,
  //            kSecReturnData as String: kCFBooleanTrue,
  //            kSecMatchLimit as String: kSecMatchLimitOne
  //        ] as [String : Any]
  //
  //        var dataTypeRef: Unmanaged<AnyObject>?
  //
  //        let status: OSStatus = SecItemCopyMatching(query, &dataTypeRef)
  //        let opaque = dataTypeRef?.toOpaque()
  //
  //        if let op = opaque? {
  //            let retrievedData = Unmanaged<NSData>.fromOpaque(op).takeUnretainedValue()
  //            return NSString(data: retrievedData, encoding: NSUTF8StringEncoding)
  //        }
  //        return nil
  //    }
  //
  //    public class func delete(key: String) -> Bool {
  //        let query = [
  //            kSecClass as String: kSecClassGenericPassword,
  //            kSecAttrAccount as String: key
  //        ]
  //
  //        let status: OSStatus = SecItemDelete(query as CFDictionaryRef)
  //
  //        return status == noErr
  //    }
  //
  //    public class func clear() -> Bool {
  //        let query = [
  //            kSecClass as String: kSecClassGenericPassword
  //        ]
  //
  //        let status: OSStatus = SecItemDelete(query as CFDictionaryRef)
  //
  //        return status == noErr
  //    }
}
