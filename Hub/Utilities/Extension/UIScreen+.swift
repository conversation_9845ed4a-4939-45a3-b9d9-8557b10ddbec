//
//  UIScreen+.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import UIKit

extension UIScreen {
  class var width: CGFloat {
    return UIScreen.main.bounds.width
  }

  class var height: CGFloat {
    return UIScreen.main.bounds.height
  }

  class var isPortrait: Bool {
    return UIScreen.main.bounds.height > UIScreen.main.bounds.width
  }

  class var isLandscape: Bool { return !isPortrait }

  class var ratio: CGFloat {
    return max(UIScreen.height, UIScreen.width) / 780.0
  }

  class var wratio: CGFloat {
    return UIScreen.width / 360.0
  }

}
