//
//  Array+.swift
//  Hub
//
//  Created by ncn on 2022/11/30.
//

import Foundation

extension Array {
  subscript(safe index: Int) -> Element? {
    return indices ~= index ? self[index] : nil
  }

  subscript(safe range: Range<Int>) -> [Element] {
    let count = self.count
    if count < range.lowerBound {
      return []
    }
    return count < range.upperBound ? Array(self[range.lowerBound..<count]) : Array(self[range])
  }
}
extension Array {
  @discardableResult
  public mutating func appendIfUnique<T: Hashable>(
    _ newElement: Element,
    check property: ((Element) -> (T))
  ) -> Bool {
    for element in self {
      if property(element) == property(newElement) { return false }
    }
    append(newElement)
    return true
  }
}

extension Array where Element: Hashable {
  var isUnique: Bool {
    var seen = Set<Element>()
    return allSatisfy { seen.insert($0).inserted }
  }

  func difference(from other: [Element]) -> [Element] {
    let thisSet = Set(self)
    let otherSet = Set(other)
    return Array(thisSet.symmetricDifference(otherSet))
  }
}

let _limit = 3
extension Array {
  public init(capacity: Int) {
    self.init()
    self.reserveCapacity(capacity)
  }

  mutating func dequeue() -> Element? {
    return self.removeFirst()
  }

  mutating func enqueue(obj: Element) {
    if self.count < _limit {
      self.append(obj)
    } else {
      self.removeLast()
      self.append(obj)
    }
  }

  mutating func remove(_ range: Range<Int>) -> Array {
    let values = Array(self[range])
    self.removeSubrange(range)
    return values
  }

  @discardableResult
  public mutating func replaceIfExist<T: Hashable>(
    _ newElement: Element,
    check property: ((Element) -> (T))
  ) -> Bool {
    var at = _limit - 1
    var isUnigue = true
    for (idx, element) in self.enumerated() {
      if property(element) == property(newElement) {
        isUnigue = false
        at = idx
      }
    }
    isUnigue ? enqueue(obj: newElement) : (self[at] = newElement)

    return isUnigue
  }
}

extension RangeReplaceableCollection where Element: Equatable {
  @discardableResult
  mutating func remove(_ element: Element) -> Element? {
    if let index = firstIndex(of: element) {
      return remove(at: index)
    }
    return nil
  }
}

extension Array {
  func sliced(by dateComponents: Set<Calendar.Component>, for key: KeyPath<Element, Date>) -> [Date:
    [Element]]
  {
    let initial: [Date: [Element]] = [:]
    let groupedByDateComponents = reduce(into: initial) { acc, cur in
      var calendar = Calendar.current
      calendar.timeZone = Current.timezone
      let components = calendar.dateComponents(dateComponents, from: cur[keyPath: key])
      let date = calendar.date(from: components)!
      let existing = acc[date] ?? []
      acc[date] = existing + [cur]
    }
    return groupedByDateComponents
  }

  func chunked(chunkSize: Int) -> [[Element]] {
    var result = [[Element]]()
    for i in stride(from: 0, to: count, by: chunkSize) {
      if i >= chunkSize {
        result.append(Array(self[i - chunkSize..<i]))
      }
    }
    let last = count % chunkSize
    result.append(Array(self[count - last..<count]))
    return result
  }

  //FIXME: 위 chunked func의 마지막 3element가 지워지는 오류 아래 함수로 대체
  func chunked(size: Int) -> [[Element]] {
    var result = [[Element]]()
    for i in stride(from: 0, to: count, by: size) {
      let end = Swift.min(i + size, count)
      result.append(Array(self[i..<end]))
    }
    return result
  }
}

struct FixedFIFOArray<T> {
  var _maxSize: Int
  var _array: [T] = []

  init(maxSize: Int) {
    self._maxSize = maxSize
  }

  mutating func append(_ element: T) {
    _array.append(element)
    _array = _array.suffix(_maxSize)
  }

  mutating func removeAll() {
    _array.removeAll()
  }
}
