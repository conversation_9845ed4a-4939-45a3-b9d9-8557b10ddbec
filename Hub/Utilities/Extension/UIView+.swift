//
//  UIView+.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import Foundation
import RxSwift
import UIKit

extension UIView {
  // MARK: - Frame
  var parentViewController: UIViewController? {
    var parentResponder: UIResponder? = self
    while parentResponder != nil {
      parentResponder = parentResponder!.next
      if let viewController = parentResponder as? UIViewController {
        return viewController
      }
    }
    return nil
  }

  var left: CGFloat {
    get { return frame.minX }
    set { frame.origin.x = newValue }
  }

  var right: CGFloat {
    get { return frame.maxX }
    set { frame.origin.x = newValue - width }
  }

  var top: CGFloat {
    get { return frame.minY }
    set { frame.origin.y = newValue }
  }

  var bottom: CGFloat {
    get { return frame.maxY }
    set { frame.origin.y = newValue - height }
  }

  var width: CGFloat {
    get { return frame.width }
    set { frame.size.width = newValue }
  }

  var height: CGFloat {
    get { return frame.height }
    set { frame.size.height = newValue }
  }

  var size: CGSize {
    get { return self.frame.size }
    set {
      var frame:CGRect = self.frame
      frame.size = newValue
      self.frame = frame
    }
  }

  func roundCorners(_ corners: UIRectCorner, radius: CGFloat) {
    clipsToBounds = true
    layer.cornerRadius = radius
    layer.maskedCorners = CACornerMask(rawValue: corners.rawValue)
  }

  func circleCorners() {
    self.clipsToBounds = true
    self.layer.cornerRadius = (self.height / 2)
  }
}

extension UIView {
  // MARK: - Animation
  @objc func fadeIn(duration: TimeInterval = 0.4) {
    DispatchQueue.main.async { [weak self] in
      self?.isHidden = false
      UIView.animate(
        withDuration: duration,
        animations: {
          self?.alpha = 1.0
        })
    }
  }

  func fadeOut(duration: TimeInterval = 0.4) {
    DispatchQueue.main.async { [weak self] in
      UIView.animate(
        withDuration: duration,
        animations: {
          self?.alpha = 0.0
        }
      ) { (complete) in
        self?.isHidden = true
      }
    }
  }

  func removeFadeOut(duration: TimeInterval = 0.4) {
    UIView.animate(
      withDuration: duration,
      animations: {
        self.alpha = 0.0
      }
    ) { (complete) in
      self.removeFromSuperview()
    }
  }

  func animationToMoveUpdown(offset: CGFloat) {
    UIView.animate(withDuration: 0.4) {
      self.transform = CGAffineTransform(translationX: 0, y: offset)
    }
  }

  func animationToMoveLeftRight(offset: CGFloat) {
    UIView.animate(withDuration: 1.0) {
      self.transform = CGAffineTransform(translationX: offset, y: 0)
    }
  }
}

extension UIView {
  func rotation(sx: CGFloat) {
    self.transform = CGAffineTransformMakeScale(sx, 1)
  }

  func rotation(sy: CGFloat) {
    self.transform = CGAffineTransformMakeScale(1, sy)
  }
  
  func rotation(sx: CGFloat, sy: CGFloat) {
    self.transform = CGAffineTransformMakeScale(sx, sy)
  }
}

extension UIView {
  func takeScreenshot() -> UIImage? {
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, false, UIScreen.main.scale)
    drawHierarchy(in: self.bounds, afterScreenUpdates: true)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return image
  }

  func rxTakeScreenShot() -> Observable<UIImage?> {
    UIGraphicsBeginImageContextWithOptions(self.bounds.size, false, UIScreen.main.scale)
    drawHierarchy(in: self.bounds, afterScreenUpdates: true)
    let image = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()
    return Observable.just(image)
  }
}

extension UIView {

  func x() -> CGFloat {
    return self.frame.origin.x
  }

  func y() -> CGFloat {
    return self.frame.origin.y
  }

  func endX() -> CGFloat {
    return self.frame.origin.x + self.frame.size.width
  }

  func endY() -> CGFloat {
    return self.frame.origin.y + self.frame.size.height
  }
}

// MARK: - safearea
extension UIView {
  var safeAreaTop: CGFloat {
    return UIApplication.key?.safeAreaInsets.top ?? 0
  }
}
