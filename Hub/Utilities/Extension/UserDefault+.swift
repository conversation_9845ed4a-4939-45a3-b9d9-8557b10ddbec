//
//  UserDefault+.swift
//  Hub
//
//  Created by ncn on 2/14/25.
//

import Foundation

extension UserDefaults {
  static var shared: UserDefaults {
    let bundleID = Bundle.main.bundleIdentifier ?? "com.ncand.VueroidHub"
    let suiteName = "group.\(bundleID)"
    return UserDefaults(suiteName: suiteName) ?? UserDefaults.standard
  }


  func deleteConnectedDevice(macAddress: String?) {
    func formatMacAddress(_ mac: String) -> String {
      let prefix = "CC:64:1A"
      let formattedMac = mac.uppercased().chunked(into: 2).joined(separator: ":")
      return "\(prefix):\(formattedMac)"
    }

    if let macAddress = macAddress {
      UserDefaults.shared.dashcamCardCellModels.removeAll { $0.macAddress.uppercased() == macAddress.uppercased() }
      let formattedMac = formatMacAddress(macAddress)
      AppManager.shared.deleteConnectedDevice(mac: formattedMac)
    } else {
      _ = UserDefaults.shared.dashcamCardCellModels.map { model in
        if model.isConnect {
          let formattedMac = formatMacAddress(model.macAddress)
          AppManager.shared.deleteConnectedDevice(mac: formattedMac)
        }
      }
      UserDefaults.shared.dashcamCardCellModels.removeAll { $0.isConnect }
    }
  }


  var dashcamCardCellModels: [DashcamCardCellModel] {
    get {
      var cellModel: [DashcamCardCellModel]?
      if let rawValues = UserDefaults.shared.value(forKey: Current.dashcamCardCellModelsKey) as? Data {
        cellModel = try? PropertyListDecoder().decode([DashcamCardCellModel].self, from: rawValues)
      }

      aLogger.debug("saved cellModel count: \(cellModel?.count ?? 0)")
      // log cellModel macAddress and name
      for model in cellModel ?? [] {
#if DEBUG
        aLogger.debug("GET UserDefaults cellModel macAddress: \(model.macAddress), ssid: \(model.ssid), password: \(model.password) isConnect: \(model.isConnect)")
#endif
      }

      return cellModel ?? []
    }
    set {
#if DEBUG
      _ = newValue.map {
        aLogger.debug("SET UserDefaults cellModel ssid: \($0.ssid), isConnect: \($0.isConnect)")
      }
#endif

      let value = try? PropertyListEncoder().encode(newValue)
      UserDefaults.shared.set(value, forKey: Current.dashcamCardCellModelsKey)
    }
  }

  var notifications: [NotificationModel] {
    get {
      var models: [NotificationModel]?
      if let rawValues = UserDefaults.shared.value(forKey: Current.notificationModelsKey) as? Data {
        models = try? PropertyListDecoder().decode([NotificationModel].self, from: rawValues)
      }

      #if DEBUG
      aLogger.info("saved notification count: \(models?.count ?? 0)")
      for model in models ?? [] {
        aLogger.debug("saved notifications : \(model.fileUrl)")
      }
      #endif

      return models ?? []
    }
    set {
      let value = try? PropertyListEncoder().encode(newValue)
      UserDefaults.shared.set(value, forKey: Current.notificationModelsKey)
    }
  }

  var favoriteFastSettingItems: [FavoriteFastSettingItem] {
    get {
      var items: [FavoriteFastSettingItem]?
      if let rawValues = UserDefaults.shared.value(forKey: Current.homeShortcutKey) as? Data {
        do {
          items = try PropertyListDecoder().decode([FavoriteFastSettingItem].self, from: rawValues)
        } catch {
          aLogger.error("Failed to decode FavoriteFastSettingItem: \(error)")
        }
      }

      #if DEBUG
      aLogger.info("Get favoriteFastSettingItems count: \(items?.count ?? 0)")
      for model in items ?? [] {
        aLogger.debug("saved items title: \(model.title), isSelected: \(model.isSelected)")
      }
      #endif

      return items ?? []
    }
    set {
      do {
        let value = try PropertyListEncoder().encode(newValue)
        UserDefaults.shared.set(value, forKey: Current.homeShortcutKey)
        UserDefaults.shared.synchronize()

        #if DEBUG
        aLogger.info("Set favoriteFastSettingItems \(newValue.count) items to UserDefaults")
        #endif
      } catch {
        aLogger.error("Failed to encode FavoriteFastSettingItem: \(error)")
      }
    }
  }
}



