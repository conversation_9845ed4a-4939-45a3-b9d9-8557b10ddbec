//
//  CGFloat+.swift
//  Hub
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/23/24.
//

import UIKit

extension CGFloat {
  var toInt: Int {
    return Int(self)
  }

  //ratioValue
  var rv: CGFloat {
    return self * UIScreen.ratio
  }

  var wrv: CGFloat {
    return self * UIScreen.wratio
  }

}

extension Double {
  var rv: CGFloat {
    return CGFloat(self) * UIScreen.ratio
  }

  var wrv: CGFloat {
    return CGFloat(self) * UIScreen.wratio
  }

}
