//
//  UIStackView+.swift
//  Hub
//
//  Created by ncn on 2/18/25.
//

import UIKit

// MARK: - Layout

extension UIStackView {
  @discardableResult
  public func withMargins(_ margins: UIEdgeInsets) -> UIStackView {
    layoutMargins = margins
    isLayoutMarginsRelativeArrangement = true
    return self
  }

  @discardableResult
  public func padLeft(_ left: CGFloat) -> UIStackView {
    isLayoutMarginsRelativeArrangement = true
    layoutMargins.left = left
    return self
  }

  @discardableResult
  public func padTop(_ top: CGFloat) -> UIStackView {
    isLayoutMarginsRelativeArrangement = true
    layoutMargins.top = top
    return self
  }

  @discardableResult
  public func padBottom(_ bottom: CGFloat) -> UIStackView {
    isLayoutMarginsRelativeArrangement = true
    layoutMargins.bottom = bottom
    return self
  }

  @discardableResult
  public func padRight(_ right: CGFloat) -> UIStackView {
    isLayoutMarginsRelativeArrangement = true
    layoutMargins.right = right
    return self
  }
}

// MARK: - Builder

extension UIStackView {
  public func distribution(justify: UIStackView.Distribution) -> UIStackView {
    distribution = justify
    return self
  }
}

// MARK: - utility

extension UIStackView {
  public func removeAllArrangedSubviews() {
    let removedSubviews = arrangedSubviews.reduce([]) { (allSubviews, subview) -> [UIView] in
      self.removeArrangedSubview(subview)
      return allSubviews + [subview]
    }

    // Deactivate all constraints
    NSLayoutConstraint.deactivate(removedSubviews.flatMap { $0.constraints })

    // Remove the views from self
    removedSubviews.forEach { $0.removeFromSuperview() }
  }
}
