//
//  PaddingLabel.swift
//  Hub
//
//  Created by leejungchul on 2023/08/24.
//

import UIKit

class PaddingLabel: UILabel {
  private var padding = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)

  convenience init(padding: UIEdgeInsets) {
    self.init()
    self.padding = padding
  }

  override func drawText(in rect: CGRect) {
    super.drawText(in: rect.inset(by: padding))
  }

  override var intrinsicContentSize: CGSize {
    var contentSize = super.intrinsicContentSize
    contentSize.height += padding.top + padding.bottom
    contentSize.width += padding.left + padding.right

    return contentSize
  }
}
