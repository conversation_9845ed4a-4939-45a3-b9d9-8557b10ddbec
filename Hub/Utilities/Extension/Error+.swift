//
//  NCError.swift
//  Hub
//
//  Created by ncn on 2022/11/21.
//

import UIKit

protocol NCErrorProtocol: LocalizedError {
  var title: String { get }
  var code: Int { get }
}

struct NCError: NCErrorProtocol {
  var title: String
  var code: Int
  var errorDescription: String? { return _description }
  var failureReason: String {
    switch code {
    case 200..<300:
      return ""
    case 405:
      return L.error_code_405.localized
    default:
      return L.error_unknown.localized
    }
  }
  
  private var _description: String
  
  init() {
    self.title = "Vueroid Error"
    self._description = "Unknown Error"
    self.code = -400
  }
  
  init(title: String?, description: String, code: Int) {
    self.title = title ?? "Error"
    self._description = description
    self.code = code
  }
}

enum PreDefineErrorCode {
  
}

enum CustomError: Error {
  case ExpiredToken
  case NoDashCam
  case UnKnown
  case SelfIsNil
}

extension NSError {
  convenience init(message: String) {
    let userInfo = [NSLocalizedDescriptionKey: message]
    self.init(domain: "kr.ncn.hub.error", code: -1, userInfo: userInfo)
  }
}

extension NSError {
  static let noWriteAuthError = NSError(message: "No permission to write to the album")
  static let videoMergeError = NSError(message: "video merge failed")
  static let videoExportTypeError = NSError(message: "The mediaType of asset must be video")
  static let videoExportError = NSError(message: "Video export failed")
  static let assetSaveError = NSError(message: "Asset save failed")
  static let timeoutError = NSError(message: "timeout")
}
