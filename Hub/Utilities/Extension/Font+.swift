//
//  Font+.swift
//  Hub
//
//  Created by ncn on 2023/04/07.
//

import SwiftUI
import UIKit
import Pretendard

extension UIFont {
  class func MontserratBold(ofSize size: CGFloat) -> UIFont {
    return UIFont(name: "Montserrat-Bold", size: size)!
  }

  class func MontserratSemiBold(ofSize size: CGFloat) -> UIFont {
    return UIFont(name: "Montserrat-SemiBold", size: size)!
  }

  class func MontserratMedium(ofSize size: CGFloat) -> UIFont {
    return UIFont(name: "Montserrat-Medium", size: size)!
  }

  class func MontserratRegular(ofSize size: CGFloat) -> UIFont {
    return UIFont(name: "Montserrat-Regular", size: size)!
  }
}

// MARK: - Hub font
extension UIFont {
  static var h1: UIFont {
    let size = 20.0
    return .pretendard(ofSize: size, weight: .semibold) ?? .boldSystemFont(ofSize: size)
  }

  static var h2: UIFont {
    let size = 16.0
    return .pretendard(ofSize: size, weight: .bold) ?? .boldSystemFont(ofSize: size)
  }

  static var h2Regular: UIFont {
    let size = 16.0
    return .pretendard(ofSize: size, weight: .regular) ?? .boldSystemFont(ofSize: size)
  }

  static var sub1: UIFont {
    let size = 15.0
    return .pretendard(ofSize: size, weight: .bold) ?? .boldSystemFont(ofSize: size)
  }

  static var sub2: UIFont {
    let size = 15.0
    return .pretendard(ofSize: size, weight: .medium) ?? .boldSystemFont(ofSize: size)
  }

  static var sub3: UIFont {
    let size = 15.0
    return .pretendard(ofSize: size, weight: .regular) ?? .boldSystemFont(ofSize: size)
  }

  static var subText: UIFont {
    let size = 14.0
    return .pretendard(ofSize: size, weight: .regular) ?? .systemFont(ofSize: size)
  }

  static var body1: UIFont {
    let size = 13.0
    return .pretendard(ofSize: size, weight: .bold) ?? .boldSystemFont(ofSize: size)
  }

  static var body2: UIFont {
    let size = 13.0
    return .pretendard(ofSize: size, weight: .medium) ?? .boldSystemFont(ofSize: size)
  }

  static var body3: UIFont {
    let size = 13.0
    return .pretendard(ofSize: size, weight: .regular) ?? .boldSystemFont(ofSize: size)
  }

  static var body4: UIFont {
    let size = 10.0
    return .pretendard(ofSize: size, weight: .medium) ?? .boldSystemFont(ofSize: size)
  }

  static var body5: UIFont {
    let size = 12.0
    return .pretendard(ofSize: size, weight: .medium) ?? .boldSystemFont(ofSize: size)
  }

  static var body5Bold: UIFont {
    let size = 12.0
    return .pretendard(ofSize: size, weight: .medium) ?? .boldSystemFont(ofSize: size)
  }

  static var label: UIFont {
    let size = 10.0
    return .pretendard(ofSize: size, weight: .bold) ?? .boldSystemFont(ofSize: size)
  }

}


extension Font {
  static func MontserratBold(ofSize size: CGFloat) -> Font {
    return .custom("Montserrat-Bold", size: size)
  }

  static func MontserratSemiBold(ofSize size: CGFloat) -> Font {
    return .custom("Montserrat-SemiBold", size: size)
  }

  static func MontserratMedium(ofSize size: CGFloat) -> Font {
    return .custom("Montserrat-Medium", size: size)
  }

  static func MontserratRegular(ofSize size: CGFloat) -> Font {
    return .custom("Montserrat-Medium", size: size)
  }
}

