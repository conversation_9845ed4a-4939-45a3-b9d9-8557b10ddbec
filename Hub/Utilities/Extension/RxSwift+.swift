//
//  RxSwift+.swift
//  Hub
//
//  Created by ncn on 1/1/25.
//

import RxSwift
import OSLog
let rxLogger = Logger(subsystem: "hub", category: "rx")

extension ObservableType {
  public func rxDebug(_ identifier: String) -> Observable<Element> {
    return Observable.create { observer in
      rxLogger.debug("##Rx subscribed \(identifier)")
      let subscription = self.subscribe { e in
        rxLogger.debug("##Rx event \(identifier)  \(e.debugDescription)")
        switch e {
        case .next(let value):
          observer.on(.next(value))

        case .error(let error):
          observer.on(.error(error))

        case .completed:
          observer.on(.completed)
        }
      }

      return Disposables.create {
        rxLogger.debug("##Rx disposing \(identifier)")
        subscription.dispose()
      }
    }
  }

  func printValue(_ prefix: String = "") -> Observable<Element> {
    return self.do(onNext: { debugPrint("##Rx \(prefix)\($0)") })
  }
}
