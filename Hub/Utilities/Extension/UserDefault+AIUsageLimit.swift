//
//  UserDefault+AIUsageLimit.swift
//  Hub
//
//  Created by ncn on 6/24/25.
//

import Foundation

// MARK: - Daily AI Usage Limit Extension
extension UserDefaults {
  /// 하루 최대 사용 횟수
  private var maxDailyAiPrivacyFeatureUsageCount: Int { 50 }
  private var maxDailyAiLicenceFeatureUsageCount: Int { 50 }

  /// 오늘 날짜 문자열 (yyyy-MM-dd 형식)
  private var todayDateString: String {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyy-MM-dd"
    return formatter.string(from: Date())
  }

  /// Privacy 기능의 오늘 사용 횟수
  var dailyAiPrivacyFeatureUsageCount: Int {
    get {
      let lastUsageDate = UserDefaults.shared.string(forKey: Current.lastAiFeatureUsageDateKey) ?? ""
      let todayDate = todayDateString

      // 날짜가 바뀌었으면 카운트 리셋
      if lastUsageDate != todayDate {
        return 0
      }

      return UserDefaults.shared.integer(forKey: Current.dailyAiPrivacyFeatureUsageCountKey)
    }
    set {
      UserDefaults.shared.set(newValue, forKey: Current.dailyAiPrivacyFeatureUsageCountKey)
      UserDefaults.shared.set(todayDateString, forKey: Current.lastAiFeatureUsageDateKey)
      UserDefaults.shared.synchronize()
    }
  }

  /// License 기능의 오늘 사용 횟수
  var dailyAiLicenceFeatureUsageCount: Int {
    get {
      let lastUsageDate = UserDefaults.shared.string(forKey: Current.lastAiFeatureUsageDateKey) ?? ""
      let todayDate = todayDateString

      // 날짜가 바뀌었으면 카운트 리셋
      if lastUsageDate != todayDate {
        return 0
      }

      return UserDefaults.shared.integer(forKey: Current.dailyAiLicenseFeatureUsageCountKey)
    }
    set {
      UserDefaults.shared.set(newValue, forKey: Current.dailyAiLicenseFeatureUsageCountKey)
      UserDefaults.shared.set(todayDateString, forKey: Current.lastAiFeatureUsageDateKey)
      UserDefaults.shared.synchronize()
    }
  }

  /// 오늘 더 사용할 수 있는지 확인
  var canUseAiPrivacyFeatureToday: Bool {
    return dailyAiPrivacyFeatureUsageCount < maxDailyAiPrivacyFeatureUsageCount
  }

  var canUseAiLicenceFeatureToday: Bool {
    return dailyAiLicenceFeatureUsageCount < maxDailyAiLicenceFeatureUsageCount
  }

  /// 남은 사용 횟수
  var remainingAiPrivacyUsageCount: Int {
    return max(0, maxDailyAiPrivacyFeatureUsageCount - dailyAiPrivacyFeatureUsageCount)
  }

  /// 기능 사용 시 호출 (사용 가능하면 true 반환하고 카운트 증가)
  @discardableResult
  func incrementAiPrivacyFeatureUsage() -> Bool {
    guard canUseAiPrivacyFeatureToday else {
      return false
    }

    dailyAiPrivacyFeatureUsageCount += 1
    aLogger.info("pricacy usage count : \(self.dailyAiPrivacyFeatureUsageCount)")
    return true
  }

  @discardableResult
  func incrementAiLicenseFeatureUsage() -> Bool {
    guard canUseAiLicenceFeatureToday else {
      return false
    }

    dailyAiLicenceFeatureUsageCount += 1
    aLogger.info("license usage count : \(self.dailyAiLicenceFeatureUsageCount)")
    return true
  }
  /// 사용 횟수를 수동으로 리셋 (테스트용)
  func resetDailyFeatureUsage() {
    UserDefaults.shared.removeObject(forKey: Current.dailyAiLicenseFeatureUsageCountKey)
    UserDefaults.shared.removeObject(forKey: Current.dailyAiPrivacyFeatureUsageCountKey)
    UserDefaults.shared.removeObject(forKey: Current.lastAiFeatureUsageDateKey)
    UserDefaults.shared.synchronize()
  }
}
