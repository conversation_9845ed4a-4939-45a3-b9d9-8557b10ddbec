//
//  UITextField+.swift
//  Hub
//
//  Created by ncn on 2022/12/12.
//

import UIKit

extension UITextField {
  func applyCustomMaskingButton() {
    clearButtonMode = .never
    rightViewMode = .whileEditing
    let clearButton = UIButton(frame: CGRectMake(0, 0, 16, 16))
    clearButton.setImage(UIImage(systemName: "eye"), for: .normal)
    clearButton.setImage(UIImage(systemName: "eye.slash"), for: .selected)
    clearButton.addTarget(self, action: #selector(buttonTouch(_:)), for: .touchUpInside)
    rightView = clearButton
  }

  @objc func buttonTouch(_ sender: UIButton) {
    //sender.isSelected != sender.isSelected
    var isShow = false
    if isSecureTextEntry == false {
      isShow = true
    }
    isSecureTextEntry = isShow
    sender.isSelected = !isShow
  }
}
