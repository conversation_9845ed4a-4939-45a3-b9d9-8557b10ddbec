//
//  String+.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import Alamofire
import CommonCrypto
import Foundation
import UIKit

extension String {
  fileprivate func index(at offset: Int) -> String.Index {
    index(startIndex, offsetBy: offset)
  }

  func convertToDictionary() throws -> [String: Any] {
    let data = Data(utf8)

    if let anyResult = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
      return anyResult
    } else {
      return [:]
    }
  }

  func trim() -> String {
    return trimmingCharacters(in: NSCharacterSet.whitespaces)
  }
  
  func chunked(into size: Int) -> [String] {
    var startIndex = self.startIndex
    return stride(from: 0, to: self.count, by: size).map { _ in
      let endIndex = self.index(startIndex, offsetBy: size, limitedBy: self.endIndex) ?? self.endIndex
      defer { startIndex = endIndex }
      return String(self[startIndex..<endIndex])
    }
  }
}

extension String {
  subscript(value: Int) -> Character {
    self[index(at: value)]
  }
  subscript(value: NSRange) -> Substring {
    self[value.lowerBound..<value.upperBound]
  }
  subscript(value: CountableClosedRange<Int>) -> Substring {
    self[index(at: value.lowerBound)...index(at: value.upperBound)]
  }

  subscript(value: CountableRange<Int>) -> Substring {
    let length = self.count
    if length < value.lowerBound {
      return ""
    }
    if length < value.upperBound {
      return self[index(at: value.lowerBound)..<index(at: length)]
    }
    return self[index(at: value.lowerBound)..<index(at: value.upperBound)]
  }

  subscript(value: PartialRangeUpTo<Int>) -> Substring {
    self[..<index(at: value.upperBound)]
  }

  subscript(value: PartialRangeThrough<Int>) -> Substring {
    self[...index(at: value.upperBound)]
  }

  subscript(value: PartialRangeFrom<Int>) -> Substring {
    self[index(at: value.lowerBound)...]
  }

  //Json to Dictionary
  func dictionaryFromJson() -> [String: Any]? {
    var ret: [String: Any]? = nil
    guard let data = self.data(using: .utf8) else {
      Log.message(to: "Fail to encoded(\(self))")
      return ret
    }

    do {
      ret =
        try JSONSerialization.jsonObject(with: data, options: .mutableContainers) as? [String: Any]
    } catch let error {
      Log.message(to: "\(#function) error: \(error)")
    }

    return ret
  }

  //Json to Array
  func arrayFromJson() -> [[String: String]?]? {
    var ret: [[String: String]?]? = nil
    guard let data = self.data(using: .utf8) else {
      Log.message(to: "Fail to encoded(\(self))")
      return ret
    }

    do {
      ret =
        try JSONSerialization.jsonObject(with: data, options: .mutableContainers)
        as? [[String: String]?]
    } catch let error {
      Log.message(to: "\(#function) error: \(error)")
    }

    return ret
  }

  var localized: String {
    return NSLocalizedString(self, tableName: nil, bundle: Bundle.main, value: "", comment: "")
  }

}

extension String {
  func toData(encoding: String.Encoding = .utf8) -> Data? {
    return self.data(using: encoding)
  }
  func toDouble() -> Double {
    return Double(self) ?? 0.0
  }
}

extension String {
  func toDate(
    format: String, timezone: String = "GMT", locale: String = "kr_KR"
  ) -> Date? {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = format
    dateFormatter.timeZone = Current.timezone
//    dateFormatter.locale = Current.locale
    return dateFormatter.date(from: self)
  }

  func convertDayString(
    from: String, to target: String, timezone: String = "GMT", local: String = "kr_KR"
  ) -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = from
    dateFormatter.timeZone = Current.timezone
    dateFormatter.locale = Locale(identifier: local)
    let date = dateFormatter.date(from: self) ?? Date()

    dateFormatter.dateFormat = target
    return dateFormatter.string(from: date)
  }
}

extension String {
  public func toSecond() -> TimeInterval {
    guard let minuteString = self.components(separatedBy: ":").first,
      let minute = Int(minuteString),
      let secondString = self.components(separatedBy: ":").last,
      let second = Int(secondString)
    else { return 0.0 }
    return TimeInterval(minute * 60 + second)
  }

  public func timeToSecond() -> TimeInterval {
    guard let hourString = self.components(separatedBy: ":")[safe: 0],
      let minuteString = self.components(separatedBy: ":")[safe: 1],
      let secondString = self.components(separatedBy: ":")[safe: 2],
      let hour = Int(hourString),
      let minute = Int(minuteString),
      let second = Int(secondString)
    else { return 0.0 }
    return TimeInterval(hour * 3600 + minute * 60 + second)
  }
  /**
   HH:mm:ss 포맷의 String 시간을 분으로 나타내며 초는 반올림한다.

   example : 01:11:53 -> 72
   */
  public func toMinute() -> TimeInterval {
    guard let hourString = self.components(separatedBy: ":")[safe: 0],
      let minuteString = self.components(separatedBy: ":")[safe: 1],
      let secondString = self.components(separatedBy: ":")[safe: 2],
      let hour = Int(hourString),
      let minute = Int(minuteString),
      let second = Int(secondString)
    else { return 0.0 }
    return TimeInterval(hour * 60 + minute + (second >= 30 ? 1 : 0))
  }
}

extension String {
  func size(OfFont font: UIFont) -> CGSize {
    return (self as NSString).size(withAttributes: [NSAttributedString.Key.font: font])
  }
}

extension String {
  var fileName: String {
    URL(fileURLWithPath: self).deletingPathExtension().lastPathComponent
  }

  var fileExtension: String {
    URL(fileURLWithPath: self).pathExtension
  }

  var shortFileName: String {
    (split(separator: "/").last?.description ?? self)
      .replacingOccurrences(of: ".swift", with: "")
  }
}

extension String: ParameterEncoding {

  public func encode(_ urlRequest: URLRequestConvertible, with parameters: Parameters?) throws
    -> URLRequest
  {
    var request = try urlRequest.asURLRequest()
    request.httpBody = data(using: .utf8, allowLossyConversion: false)
    return request
  }

}

// Crypto


extension Data {
  func toHexString() -> String {
    let hexString = map { String(format: "%02x", $0) }.joined()
    return hexString
  }

  func bytes() -> [UInt8] {
    let array = [UInt8](self)
    return array
  }
}

extension String {
  func sha256() -> String {
    if let stringData = self.data(using: String.Encoding.utf8) {
      return hexStringFromData(input: digest(input: stringData as NSData))
    }
    return ""
  }

  private func digest(input: NSData) -> NSData {
    let digestLength = Int(CC_SHA256_DIGEST_LENGTH)
    var hash = [UInt8](repeating: 0, count: digestLength)
    CC_SHA256(input.bytes, UInt32(input.length), &hash)
    return NSData(bytes: hash, length: digestLength)
  }

  private func hexStringFromData(input: NSData) -> String {
    var bytes = [UInt8](repeating: 0, count: input.length)
    input.getBytes(&bytes, length: input.length)

    var hexString = ""
    for byte in bytes {
      hexString += String(format: "%02x", UInt8(byte))
    }

    return hexString
  }

  func hmac(keyString: String) -> Data {
    var digest = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
    CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA256), keyString, keyString.count, self, count, &digest)
    return Data(digest)
  }

  func hmac(keyData: Data) -> Data {
    let keyBytes = keyData.bytes()
    let data = cString(using: String.Encoding.utf8)
    let dataLen = Int(lengthOfBytes(using: String.Encoding.utf8))
    var result = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
    CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA256), keyBytes, keyData.count, data, dataLen, &result)

    return Data(result)
  }
}

extension String {
  public func base64Encoded() -> String? {
    return data(using: .utf8)?.base64EncodedString()
  }

  public func base64Decoded() -> String? {
    print("decode base64")

    var localData: Data?
    localData = Data(base64Encoded: self)
    var temp: String = self
    if localData == nil {
      temp = self + "=="
    }
    guard let data = Data(base64Encoded: temp, options: Data.Base64DecodingOptions(rawValue: 0)) else {
      return nil
    }
    return String(data: data, encoding: .utf8)
  }

  public func convertToDictionaryValueAsString() throws -> [String: Any] {
    let data = Data(utf8)

    if let anyResult = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
      return anyResult
    } else {
      return [:]
    }
  }

  // "2024/12/11" -> "2024.12.11 Wed"
  func toFormattedDateString() -> String {
    let dateFormatter = DateFormatter()
    dateFormatter.dateFormat = "yyyy/MM/dd"

    guard let date = dateFormatter.date(from: self) else { return self }

    let outputFormatter = DateFormatter()
    outputFormatter.dateFormat = "yyyy.MM.dd E"
    outputFormatter.locale = Locale(identifier: "en_US")

    return outputFormatter.string(from: date)
  }
  
  // channel 2 면 20250317_071221_INF_F_N.mp4 -> 20250317_071221_INF_R_N.mp4
  // channel 3 이면 20250317_071221_INF_I_N.mp4
  func toChannelFileName(channel: Int) -> String {
    let components = self.split(separator: "_")
    guard components.count >= 4 else { return self }
    
    var newComponents = components

    newComponents[3] = channel == 2 ? "R" : channel == 1 ? "F" : "I"
    
    return newComponents.joined(separator: "_")
  }
}

func rotN(_ unicodeScalar: UnicodeScalar, intervals:[ClosedRange<UnicodeScalar>]) -> Character {
    var result = unicodeScalar.value

    for interval in intervals {
        let half = (interval.upperBound.value - interval.lowerBound.value + 1) / 2
        let halfway = UnicodeScalar(interval.lowerBound.value + half)!

        switch unicodeScalar {
        case interval.lowerBound..<halfway:
            result += half
        case halfway...interval.upperBound:
            result -= half
        default:
            break
        }
    }

    return Character(UnicodeScalar(result)!)
}

func rotN(_ input: String, intervals:[ClosedRange<UnicodeScalar>]) -> String {
    return String(input.unicodeScalars.map {rotN($0, intervals: intervals)})
}

func rot13(_ input: String) -> String {
    return rotN(input, intervals:["A"..."Z", "a"..."z"])
}

func rot47(_ input: String) -> String {
    return rotN(input, intervals:["!"..."~"])
}

func rot5(_ input: String) -> String {
    return rotN(input, intervals:["0"..."9"])
}

func rot13and5(_ input: String) -> String {
    return rotN(input, intervals:["A"..."Z", "a"..."z", "0"..."9"])
}
