//
//  UICollectionViewCell+.swift
//  Hub
//
//  Created by ncn on 2023/02/27.
//

import UIKit

extension UICollectionView {

  func register<T: UICollectionViewCell>(cellType: T.Type) where T: Reusable {
    register(cellType.self, forCellWithReuseIdentifier: cellType.identifier)
  }

  // ofKind: UICollectionView.elementKindSectionFooter
  func register<T: UICollectionReusableView>(viewType: T.Type, ofKind: String) where T: Reusable {
    register(
      viewType.self, forSupplementaryViewOfKind: ofKind, withReuseIdentifier: viewType.identifier)
  }

  func dequeueReusableCell<T: UICollectionViewCell>(
    for indexPath: IndexPath,
    cellType: T.Type = T.self
  ) -> T where T: Reusable {
    guard
      let cell = dequeueReusableCell(withReuseIdentifier: cellType.identifier, for: indexPath) as? T
    else {
      fatalError("Failed to dequeue a cell \(cellType.identifier) matching type \(cellType.self).")
    }

    return cell
  }

  func dequeueReusableView<T: UICollectionReusableView>(
    for indexPath: IndexPath, kind: String, viewType: T.Type = T.self
  ) -> T where T: Reusable {
    guard
      let view = dequeueSupplementary(viewType.identifier, indexPath: indexPath, kind: kind) as? T
    else {
      fatalError("Failed to dequeue a view \(viewType.identifier) matching type \(viewType.self).")
    }

    return view
  }
}

extension CGSize {
  func scale(_ factor: CGFloat) -> CGSize {
    let transform = CGAffineTransform(scaleX: factor, y: factor)
    return self.applying(transform)
  }
}

extension CGRect {
  func scale(_ factor: CGFloat) -> CGRect {
    let transform = CGAffineTransform(scaleX: factor, y: factor)
    return self.applying(transform)
  }
}

extension UICollectionView {
  func dequeueSupplementary<T: UICollectionReusableView>(
    _ identifier: String? = nil,
    indexPath: IndexPath,
    kind: String
  ) -> T {
    let identifier = identifier ?? String(describing: T.self)
    let view =
      dequeueReusableSupplementaryView(
        ofKind: kind, withReuseIdentifier: identifier, for: indexPath) as? T
    switch view {
    case let .some(unwrapped): return unwrapped
    default: fatalError("Unable to dequeue" + T.description())
    }
  }

  func getLingeringCells() -> [UICollectionViewCell] {
    let visibleRect = CGRect(origin: contentOffset, size: bounds.size)
    let visibleCells: [UIView] = self.visibleCells

    return subviews.filter { view in
      view is UICollectionViewCell && visibleRect.intersects(view.frame)
        && !visibleCells.contains(view)
    } as! [UICollectionViewCell]
  }

  func hideLingeringCells() {
    getLingeringCells().forEach { $0.isHidden = true }
  }
}
