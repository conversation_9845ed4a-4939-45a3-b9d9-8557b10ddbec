//
//  UIButton+.swift
//  Hub
//
//  Created by ncn on 2022/11/02.
//

import RxSwift
import UIKit

/* for test */
class NCButton: UIButton {
  override open var isHighlighted: Bool {
    didSet {
      backgroundColor = isHighlighted ? .darkGray : .colorRGB(83, 182, 237)
    }
  }

  init() {
    super.init(frame: .zero)
    //self.backgroundColor = .colorRGB(83, 182, 237)
    self.backgroundColor = .lightGray
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
}

extension UIButton {
  func setBackgroundColor(_ color: UIColor, for forState: UIControl.State) {
    UIGraphicsBeginImageContext(CGSize(width: 20, height: 20))
    UIGraphicsGetCurrentContext()!.setFillColor(color.cgColor)
    UIGraphicsGetCurrentContext()!.fill(CGRect(x: 0, y: 0, width: 20, height: 20))
    let colorImage = UIGraphicsGetImageFromCurrentImageContext()
    UIGraphicsEndImageContext()

    self.setBackgroundImage(colorImage, for: forState)
  }

  func setCircularBackground(size: CGFloat, color: UIColor = UIColor.black.withAlphaComponent(0.4)) {
    backgroundColor = color
    layer.cornerRadius = size / 2
    clipsToBounds = true
  }
  
  func alignTextBelow(spacing: CGFloat = 4.0) {
    guard let image = self.imageView?.image else {
      return
    }

    guard let titleLabel = self.titleLabel else {
      return
    }

    guard let titleText = titleLabel.text else {
      return
    }

    let titleSize = titleText.size(withAttributes: [
      NSAttributedString.Key.font: titleLabel.font as Any
    ])

    titleEdgeInsets = UIEdgeInsets(
      top: spacing, left: -image.size.width, bottom: -image.size.height, right: 0)
    imageEdgeInsets = UIEdgeInsets(
      top: -(titleSize.height + spacing), left: 0, bottom: 0, right: -titleSize.width)
  }
}

extension Reactive where Base: UIButton {
  var tapWithIsSelected: Observable<Bool> {
    return self.tap.map { [weak base] _ in
      return base?.isSelected ?? false
    }
  }

  var tapWithIsSelectedAndTitle: Observable<(Bool, String)> {
    return self.tap.map { [weak base] _ in
      let isSelected = base?.isSelected ?? false
      let normalTitle = base?.title(for: .normal) ?? ""
      let selectedTitle = base?.title(for: .selected) ?? ""
      return (isSelected, isSelected ? selectedTitle : normalTitle)
    }
  }
}


// Hub Buttons
extension UIButton {
  // Add this new method for creating custom styled buttons
  static func customStyleButton(
    title: String,
    backgroundColor: UIColor? = .vueroidBlue ,
    textColor: UIColor? = .white,
    fontSize: CGFloat? = 16,
    weight: UIFont.Weight? = .medium,
    cornerRadius: CGFloat? = 10
  ) -> UIButton {
    let button = UIButton(type: .custom)
    button.setTitle(title, for: .normal)
    button.setTitleColor(textColor, for: .normal)
    button.backgroundColor = backgroundColor
    button.layer.cornerRadius = cornerRadius ?? 10
    button.titleLabel?.font = .pretendard(ofSize: 16, weight: .bold)
    return button
  }

  // Add this method for creating a button with an icon and text
  static func iconTextButton(title: String, leftImage: UIImage, backgroundColor: UIColor, textColor: UIColor) -> UIButton {
    let button = customStyleButton(title: title, backgroundColor: backgroundColor, textColor: textColor)

    // left button
    var config = UIButton.Configuration.plain()
    config.image = leftImage
    config.imagePlacement = .leading
    config.imagePadding = 15

    config.titleAlignment = .leading

    button.titleLabel?.textAlignment =  .left
    button.configuration = config
    return button
  }
}
