//
//  UISlider+.swift
//  Hub
//
//  Created by ncn on 2023/02/21.
//

import UIKit

class NCSlider: UISlider {
  var duration: Int = 0

  override func trackRect(forBounds bounds: CGRect) -> CGRect {
    var newBounds = super.trackRect(forBounds: bounds)
    newBounds.size.height = 2

    return newBounds
  }

  override func maximumValueImageRect(forBounds bounds: CGRect) -> CGRect {
    var newBounds = super.maximumValueImageRect(forBounds: bounds)
    newBounds.size.height = 2

    return newBounds
  }

  override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
    var bounds: CGRect = self.bounds
    bounds = bounds.insetBy(dx: 0, dy: -15)

    return bounds.contains(point)
  }

  override func thumbRect(forBounds bounds: CGRect, trackRect rect: CGRect, value: Float) -> CGRect
  {
    let unadjustedRect = super.thumbRect(forBounds: bounds, trackRect: rect, value: value)
    let offset: CGFloat = unadjustedRect.size.width / 2.0 / 2.0
    let minOffset = -offset
    let maxOffset = offset
    let ret =
      minOffset + (maxOffset - minOffset) * CGFloat(value / (self.maximumValue - self.minimumValue))
    var origin = unadjustedRect.origin
    origin.x += ret
    return CGRect(origin: origin, size: unadjustedRect.size)
  }
}
