//
//  UIApplication+.swift
//  Hub
//
//  Created by ncn on 2022/10/31.
//

import UIKit

extension UIApplication {
  var statusBarHeight: CGFloat {
    let keyWindow = UIApplication.shared.connectedScenes
      .filter({ $0.activationState == .foregroundActive })
      .map({ $0 as? UIWindowScene })
      .compactMap({ $0 })
      .first?.windows
      .filter({ $0.isKeyWindow }).first

    return keyWindow?.safeAreaInsets.top ?? 20
  }

  var safeAreaBottom: CGFloat {
    let keyWindow = UIApplication.shared.connectedScenes
      .filter({ $0.activationState == .foregroundActive })
      .map({ $0 as? UIWindowScene })
      .compactMap({ $0 })
      .first?.windows
      .filter({ $0.isKeyWindow }).first

    return keyWindow?.safeAreaInsets.bottom ?? 0
  }

  /* find the top viewcontroller on the application */
  class func topViewController(controller: UIViewController? = UIApplication.shared.keyWindow?.rootViewController)
    -> UIViewController?
  {
    if let navigationController = controller as? UINavigationController {
      return topViewController(controller: navigationController.visibleViewController)
    }
    if let tabController = controller as? UITabBarController {
      if let selected = tabController.selectedViewController {
        return topViewController(controller: selected)
      }
    }
    if let presented = controller?.presentedViewController {
      return topViewController(controller: presented)
    }
    return controller
  }

  class func closePopUp(completion: @escaping (Bool) -> Void) {
    guard let _ = self.topViewController() else {
      completion(true)
      return
    }
  }
}

extension UIApplication {
  static var key: UIWindow? {
    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
      return windowScene.windows.first { $0.isKeyWindow }
    }
    return nil
  }
  
  var currentWindowScene: UIWindowScene? {
    return connectedScenes
      .filter { $0.activationState == .foregroundActive }
      .compactMap { $0 as? UIWindowScene }
      .first
  }
  
  var keyWindow: UIWindow? {
    return currentWindowScene?.windows.first { $0.isKeyWindow }
  }
  
  var firstWindow: UIWindow? {
    return currentWindowScene?.windows.first
  }
}
