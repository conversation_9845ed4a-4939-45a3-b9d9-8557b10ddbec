//
//  JSONEncoder+.swift
//  Hub
//
//  Created by ncn on 2022/11/24.
//

import Foundation

extension Encodable {
  var dictionary: [String: Any]? {
    guard let data = try? JSONEncoder().encode(self) else { return nil }
    return
      (try? JSONSerialization.jsonObject(
        with: data,
        options: .allowFragments)).flatMap { $0 as? [String: Any] }
  }

  func toJsonString() -> String? {
    do {
      let encoder = JSONEncoder()
      let jsonData = try? encoder.encode(self)
      if let jsonData = jsonData, let jsonString = String(data: jsonData, encoding: .utf8) {
        return jsonString
      }
      return nil
    }
  }
}
