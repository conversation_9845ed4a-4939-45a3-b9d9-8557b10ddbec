//
//  HISTORYUnixTimeStampTests.swift
//  HubDevTests
//
//  Created by ncn on 2025/07/07.
//

import XCTest
@testable import Hub

final class HISTORYUnixTimeStampTests: XCTestCase {
  
  func testParkingModeOFFTYPE7EndTimeAdjustment() {
    // Given: TYPE = 2, OFFTYPE = 7인 주차 모드 레코드
    // 2025년 1월 15일 14:30:00 시작
    let startTimestamp = 1737024600 // 2025-01-15 14:30:00 UTC
    let originalEndTimestamp = 1737028200 // 2025-01-15 15:30:00 UTC (원본 종료 시간)
    
    let history = HISTORY(
      type: 2,        // 주차 모드
      start: startTimestamp,
      end: originalEndTimestamp,
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 5,
      motnum: 3,
      offtype: 7,     // OFFTYPE = 7
      reserved: 0
    )
    
    // When: toDomain 메서드 호출
    let result = history.toDomain(day: Date(), type: .day, speedType: .kph)
    
    // Then: END가 START와 같은 날짜의 24:00:00으로 조정되어야 함
    // 2025-01-15 24:00:00 = 2025-01-16 00:00:00 = 1737072000
    let expectedEndOfDay = 1737072000
    let expectedDuration = expectedEndOfDay - startTimestamp // 약 13시간 30분
    
    // 결과 검증
    XCTAssertNotNil(result)
    // 실제 계산된 시간이 예상 범위 내에 있는지 확인
    let calculatedDurationSeconds = Int(result.sumtime2.timeToSecond())
    XCTAssertGreaterThan(calculatedDurationSeconds, 13 * 3600) // 13시간 이상
    XCTAssertLessThan(calculatedDurationSeconds, 14 * 3600) // 14시간 미만
  }
  
  func testParkingModeOFFTYPE7DifferentDay() {
    // Given: 다른 날짜의 TYPE = 2, OFFTYPE = 7 레코드
    // 2025년 1월 20일 09:15:00 시작
    let startTimestamp = 1737360900 // 2025-01-20 09:15:00 UTC
    let originalEndTimestamp = 1737364500 // 2025-01-20 10:15:00 UTC
    
    let history = HISTORY(
      type: 2,
      start: startTimestamp,
      end: originalEndTimestamp,
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 2,
      motnum: 1,
      offtype: 7,
      reserved: 0
    )
    
    // When: toDomain 메서드 호출
    let result = history.toDomain(day: Date(), type: .day, speedType: .kph)
    
    // Then: END가 2025-01-20 24:00:00으로 조정되어야 함
    // 2025-01-20 24:00:00 = 2025-01-21 00:00:00 = 1737417600
    let expectedDuration = 1737417600 - startTimestamp // 약 14시간 45분
    
    let calculatedDurationSeconds = Int(result.sumtime2.timeToSecond())
    XCTAssertGreaterThan(calculatedDurationSeconds, 14 * 3600) // 14시간 이상
    XCTAssertLessThan(calculatedDurationSeconds, 15 * 3600) // 15시간 미만
  }
  
  func testParkingModeOFFTYPENotSeven() {
    // Given: TYPE = 2이지만 OFFTYPE ≠ 7인 경우
    let startTimestamp = 1737024600
    let endTimestamp = 1737028200
    
    let history = HISTORY(
      type: 2,
      start: startTimestamp,
      end: endTimestamp,
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 5,
      motnum: 3,
      offtype: 1, // OFFTYPE ≠ 7
      reserved: 0
    )
    
    // When: toDomain 메서드 호출
    let result = history.toDomain(day: Date(), type: .day, speedType: .kph)
    
    // Then: 원본 END 시간이 그대로 사용되어야 함
    let expectedDuration = endTimestamp - startTimestamp // 1시간
    let calculatedDurationSeconds = Int(result.sumtime2.timeToSecond())
    
    XCTAssertEqual(calculatedDurationSeconds, expectedDuration)
    XCTAssertEqual(result.sumtime2, "01:00:00")
  }
  
  func testDriveModeUnaffected() {
    // Given: TYPE = 1 (주행 모드)인 경우
    let startTimestamp = 1737024600
    let endTimestamp = 1737028200
    
    let history = HISTORY(
      type: 1, // 주행 모드
      start: startTimestamp,
      end: endTimestamp,
      distance: 1000,
      speedmax: 60,
      speedavg: 45,
      evtnum: 5,
      motnum: 3,
      offtype: 7, // OFFTYPE = 7이어도 TYPE = 1이면 영향 없음
      reserved: 0
    )
    
    // When: toDomain 메서드 호출
    let result = history.toDomain(day: Date(), type: .day, speedType: .kph)
    
    // Then: 주행 모드는 영향받지 않아야 함
    let expectedDuration = endTimestamp - startTimestamp
    let calculatedDurationSeconds = Int(result.sumtime1.timeToSecond())
    
    XCTAssertEqual(calculatedDurationSeconds, expectedDuration)
    XCTAssertEqual(result.sumtime1, "01:00:00")
    XCTAssertEqual(result.sumtime2, "00:00:00") // 주차 시간은 0
  }
  
  func testIntExtensionToEndOfSameDay() {
    // Given: 특정 UnixTimeStamp
    let timestamp = 1737024600 // 2025-01-15 14:30:00 UTC
    
    // When: toEndOfSameDay 메서드 호출
    let endOfDay = timestamp.toEndOfSameDay()
    
    // Then: 같은 날짜의 24:00:00 (다음 날 00:00:00)이 반환되어야 함
    // 2025-01-15 24:00:00 = 2025-01-16 00:00:00 = 1737072000
    let expectedEndOfDay = 1737072000
    
    // 시간대 차이를 고려하여 범위 검증
    let timeDifference = abs(endOfDay - expectedEndOfDay)
    XCTAssertLessThan(timeDifference, 24 * 3600) // 24시간 이내 차이
  }
  
  func testIntExtensionToDateString() {
    // Given: UnixTimeStamp
    let timestamp = 1737024600 // 2025-01-15 14:30:00 UTC

    // When: toDateString 메서드 호출
    let dateString = timestamp.toDateString()

    // Then: 올바른 형식의 날짜 문자열이 반환되어야 함
    XCTAssertTrue(dateString.contains("2025"))
    XCTAssertTrue(dateString.contains("01"))
    XCTAssertTrue(dateString.contains("15"))
  }

  func testContinuousParkingSessionStartAdjustment() {
    // Given: 이전 레코드가 TYPE=2, OFFTYPE=7이고 현재 레코드가 TYPE=2, OFFTYPE=2
    let previousRecord = HISTORY(
      type: 2,        // 주차 모드
      start: 1737024600, // 2025-01-15 14:30:00
      end: 1737028200,   // 2025-01-15 15:30:00
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 5,
      motnum: 3,
      offtype: 7,     // OFFTYPE = 7
      reserved: 0
    )

    let currentRecord = HISTORY(
      type: 2,        // 주차 모드
      start: 1737110400, // 2025-01-16 14:00:00 (다음 날)
      end: 1737114000,   // 2025-01-16 15:00:00
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 3,
      motnum: 2,
      offtype: 2,     // OFFTYPE = 2
      reserved: 0
    )

    // When: 이전 레코드 정보와 함께 toDomain 호출
    let result = currentRecord.toDomain(day: Date(), type: .day, speedType: .kph, previousRecord: previousRecord)

    // Then: START가 00:00:00으로 조정되어 더 긴 주차 시간이 계산되어야 함
    // 2025-01-16 00:00:00부터 15:00:00까지 = 15시간
    let calculatedDurationSeconds = Int(result.sumtime2.timeToSecond())
    XCTAssertGreaterThan(calculatedDurationSeconds, 14 * 3600) // 14시간 이상
    XCTAssertLessThan(calculatedDurationSeconds, 16 * 3600) // 16시간 미만
  }

  func testNonContinuousParkingSessionUnaffected() {
    // Given: 이전 레코드가 조건에 맞지 않는 경우
    let previousRecord = HISTORY(
      type: 2,
      start: 1737024600,
      end: 1737028200,
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 5,
      motnum: 3,
      offtype: 1,     // OFFTYPE ≠ 7
      reserved: 0
    )

    let currentRecord = HISTORY(
      type: 2,
      start: 1737110400,
      end: 1737114000,
      distance: 0,
      speedmax: 0,
      speedavg: 0,
      evtnum: 3,
      motnum: 2,
      offtype: 2,
      reserved: 0
    )

    // When: toDomain 호출
    let result = currentRecord.toDomain(day: Date(), type: .day, speedType: .kph, previousRecord: previousRecord)

    // Then: 원본 START 시간이 그대로 사용되어야 함
    let expectedDuration = 1737114000 - 1737110400 // 1시간
    let calculatedDurationSeconds = Int(result.sumtime2.timeToSecond())

    XCTAssertEqual(calculatedDurationSeconds, expectedDuration)
    XCTAssertEqual(result.sumtime2, "01:00:00")
  }

  func testIntExtensionToStartOfSameDay() {
    // Given: 특정 UnixTimeStamp
    let timestamp = 1737024600 // 2025-01-15 14:30:00 UTC

    // When: toStartOfSameDay 메서드 호출
    let startOfDay = timestamp.toStartOfSameDay()

    // Then: 같은 날짜의 00:00:00이 반환되어야 함
    // 2025-01-15 00:00:00 = 1736985600 (대략적인 값, 시간대에 따라 다를 수 있음)
    let expectedStartOfDay = 1736985600

    // 시간대 차이를 고려하여 범위 검증
    let timeDifference = abs(startOfDay - expectedStartOfDay)
    XCTAssertLessThan(timeDifference, 24 * 3600) // 24시간 이내 차이

    // 시간 부분이 00:00:00인지 확인
    let startDate = Date(timeIntervalSince1970: TimeInterval(startOfDay))
    let calendar = Calendar.current
    let components = calendar.dateComponents([.hour, .minute, .second], from: startDate)

    XCTAssertEqual(components.hour, 0)
    XCTAssertEqual(components.minute, 0)
    XCTAssertEqual(components.second, 0)
  }
}
