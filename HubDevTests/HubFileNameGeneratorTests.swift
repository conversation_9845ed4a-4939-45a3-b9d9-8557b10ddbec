//
//  HubFileNameGeneratorTests.swift
//  HubDevTests
//
//  Created by ncn on 2025/07/01.
//

import XCTest
@testable import Hub_Dev

final class HubFileNameGeneratorTests: XCTestCase {
  
  // MARK: - Test Properties
  
  private let testDate = Date(timeIntervalSince1970: 1719835630) // 2024-07-01 12:07:10
  private let dateFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateFormat = "yyyyMMdd_HHmmss"
    formatter.timeZone = TimeZone.current
    formatter.locale = Locale(identifier: "en_US_POSIX")
    return formatter
  }()
  
  // MARK: - Basic File Name Generation Tests
  
  func testGenerateBasicFileName() {
    let fileName = HubFileNameGenerator.generateFileName(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4
    )

    let expectedDate = dateFormatter.string(from: testDate)
    let expected = "\(expectedDate)_INF_FN.mp4"
    XCTAssertEqual(fileName, expected)
  }
  
  func testGenerateFileNameFromComponents() {
    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: testDate,
      recordingType: .event,
      cameraChannel: .rear,
      fileSuffix: .trimmed,
      fileExtension: .mp4
    )
    
    let fileName = HubFileNameGenerator.generateFileName(from: components)
    let expectedDate = dateFormatter.string(from: testDate)
    let expected = "\(expectedDate)_EVT_RT.mp4"
    XCTAssertEqual(fileName, expected)
  }
  
  func testGenerateFileNameWithDuplicateNumber() {
    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4,
      duplicateNumber: 3
    )
    
    let fileName = HubFileNameGenerator.generateFileName(from: components)
    let expectedDate = dateFormatter.string(from: testDate)
    let expected = "\(expectedDate)_INF_FN(3).mp4"
    XCTAssertEqual(fileName, expected)
  }
  
  // MARK: - Trimmed File Name Tests
  
  func testGenerateTrimmedFileName() throws {
    let baseFileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    let trimmedFileName = try HubFileNameGenerator.generateTrimmedFileName(
      baseFileName: baseFileName,
      existingFiles: []
    )

    let expected = "\(dateFormatter.string(from: testDate))_INF_FT.mp4"
    XCTAssertEqual(trimmedFileName, expected)
  }
  
  func testGenerateTrimmedFileNameWithDuplicates() throws {
    let baseFileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    let existingFiles = [
      "\(dateFormatter.string(from: testDate))_INF_FT.mp4",
      "\(dateFormatter.string(from: testDate))_INF_FT(2).mp4"
    ]

    let trimmedFileName = try HubFileNameGenerator.generateTrimmedFileName(
      baseFileName: baseFileName,
      existingFiles: existingFiles
    )

    let expected = "\(dateFormatter.string(from: testDate))_INF_FT(3).mp4"
    XCTAssertEqual(trimmedFileName, expected)
  }
  
  // MARK: - Image Capture Tests
  
  func testGenerateImageCaptureFileName() {
    let fileName = HubFileNameGenerator.generateImageCaptureFileName(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      existingFiles: []
    )
    
    let expected = "\(dateFormatter.string(from: testDate))_INF_FN.jpg"
    XCTAssertEqual(fileName, expected)
  }

  func testGenerateImageCaptureFileNameWithDuplicates() {
    let existingFiles = [
      "\(dateFormatter.string(from: testDate))_INF_FN.jpg"
    ]

    let fileName = HubFileNameGenerator.generateImageCaptureFileName(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      existingFiles: existingFiles
    )

    let expected = "\(dateFormatter.string(from: testDate))_INF_FN(2).jpg"
    XCTAssertEqual(fileName, expected)
  }
  
  // MARK: - License Plate File Name Tests
  
  func testGenerateLicensePlateFileNames() throws {
    let baseFileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"

    let originalFileName = try HubFileNameGenerator.generateLicensePlateFileName(
      baseFileName: baseFileName,
      stage: .original,
      existingFiles: []
    )
    let expected1 = "\(dateFormatter.string(from: testDate))_INF_FLO.jpg"
    XCTAssertEqual(originalFileName, expected1)

    let beforeFileName = try HubFileNameGenerator.generateLicensePlateFileName(
      baseFileName: baseFileName,
      stage: .beforeRestoration,
      existingFiles: []
    )
    let expected2 = "\(dateFormatter.string(from: testDate))_INF_FLB.jpg"
    XCTAssertEqual(beforeFileName, expected2)

    let afterFileName = try HubFileNameGenerator.generateLicensePlateFileName(
      baseFileName: baseFileName,
      stage: .afterRestoration,
      existingFiles: []
    )
    let expected3 = "\(dateFormatter.string(from: testDate))_INF_FLA.jpg"
    XCTAssertEqual(afterFileName, expected3)
  }
  
  // MARK: - Privacy Blur File Name Tests
  
  func testGeneratePrivacyBlurFileNames() throws {
    let baseFileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"

    let beforeFileName = try HubFileNameGenerator.generatePrivacyBlurFileName(
      baseFileName: baseFileName,
      stage: .beforeProcessing,
      existingFiles: []
    )
    let expected1 = "\(dateFormatter.string(from: testDate))_INF_FPB.mp4"
    XCTAssertEqual(beforeFileName, expected1)

    let afterFileName = try HubFileNameGenerator.generatePrivacyBlurFileName(
      baseFileName: baseFileName,
      stage: .afterProcessing,
      existingFiles: []
    )
    let expected2 = "\(dateFormatter.string(from: testDate))_INF_FPA.mp4"
    XCTAssertEqual(afterFileName, expected2)
  }

  // MARK: - File Name Parsing Tests

  func testParseValidFileName() throws {
    let fileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    let components = try HubFileNameGenerator.parseFileName(fileName)

    XCTAssertEqual(components.recordingType, .information)
    XCTAssertEqual(components.cameraChannel, .front)
    XCTAssertEqual(components.fileSuffix, .normal)
    XCTAssertEqual(components.fileExtension, .mp4)
    XCTAssertNil(components.duplicateNumber)
  }

  func testParseFileNameWithDuplicateNumber() throws {
    let fileName = "\(dateFormatter.string(from: testDate))_EVT_RT(5).mp4"
    let components = try HubFileNameGenerator.parseFileName(fileName)

    XCTAssertEqual(components.recordingType, .event)
    XCTAssertEqual(components.cameraChannel, .rear)
    XCTAssertEqual(components.fileSuffix, .trimmed)
    XCTAssertEqual(components.fileExtension, .mp4)
    XCTAssertEqual(components.duplicateNumber, 5)
  }

  func testParseInvalidFileName() {
    let invalidFileNames = [
      "invalid_format.mp4",
      "20250701_120710_INVALID_FN.mp4",
      "20250701_120710_INF_XINVALID.mp4",
      "20250701_120710_INF_FINVALID.mp4",
      "20250701_120710_INF_FN",
      "",
      "20250701_INF_FN.mp4"
    ]

    for fileName in invalidFileNames {
      XCTAssertThrowsError(try HubFileNameGenerator.parseFileName(fileName)) {
        XCTAssertTrue($0 is HubFileNameGenerator.FileNameError)
      }
    }
  }

  // MARK: - Validation Tests

  func testIsValidHubFileName() {
    let validFileNames = [
      "\(dateFormatter.string(from: testDate))_INF_FN.mp4",
      "\(dateFormatter.string(from: testDate))_EVT_RT(2).mp4",
      "\(dateFormatter.string(from: testDate))_INF_ILO.jpg"
    ]

    for fileName in validFileNames {
      XCTAssertTrue(HubFileNameGenerator.isValidHubFileName(fileName))
    }

    let invalidFileNames = [
      "invalid_format.mp4",
      "20250701_120710_INVALID_FN.mp4",
      ""
    ]

    for fileName in invalidFileNames {
      XCTAssertFalse(HubFileNameGenerator.isValidHubFileName(fileName))
    }
  }

  func testGetBaseFileName() {
    let fileName = "\(dateFormatter.string(from: testDate))_INF_FN(3).mp4"
    let baseFileName = HubFileNameGenerator.getBaseFileName(fileName)
    let expected = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    XCTAssertEqual(baseFileName, expected)
  }

  // MARK: - Duplicate Handling Tests

  func testGenerateUniqueFileName() {
    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4
    )

    let existingFiles = [
      "\(dateFormatter.string(from: testDate))_INF_FN.mp4",
      "\(dateFormatter.string(from: testDate))_INF_FN(2).mp4"
    ]

    let uniqueFileName = HubFileNameGenerator.generateUniqueFileName(
      from: components,
      existingFiles: existingFiles
    )

    let expected = "\(dateFormatter.string(from: testDate))_INF_FN(3).mp4"
    XCTAssertEqual(uniqueFileName, expected)
  }

  func testGenerateUniqueFileNameNoConflict() {
    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4
    )

    let uniqueFileName = HubFileNameGenerator.generateUniqueFileName(
      from: components,
      existingFiles: []
    )

    let expected = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    XCTAssertEqual(uniqueFileName, expected)
  }

  // MARK: - Convenience Method Tests

  func testGenerateRecordingFileName() {
    let videoFileName = HubFileNameGenerator.generateRecordingFileName(
      recordingType: .information,
      cameraChannel: .front,
      isVideo: true
    )
    XCTAssertTrue(videoFileName.hasSuffix("_INF_FN.mp4"))

    let imageFileName = HubFileNameGenerator.generateRecordingFileName(
      recordingType: .event,
      cameraChannel: .rear,
      isVideo: false
    )
    XCTAssertTrue(imageFileName.hasSuffix("_EVT_RN.jpg"))
  }

  func testGenerateEventFileName() {
    let fileName = HubFileNameGenerator.generateEventFileName(cameraChannel: .front)
    XCTAssertTrue(fileName.contains("_EVT_FN.mp4"))
  }

  func testGenerateInformationFileName() {
    let fileName = HubFileNameGenerator.generateInformationFileName(cameraChannel: .internal)
    XCTAssertTrue(fileName.contains("_INF_IN.mp4"))
  }

  func testGetDuplicateNumber() {
    let fileName1 = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    XCTAssertNil(HubFileNameGenerator.getDuplicateNumber(from: fileName1))

    let fileName2 = "\(dateFormatter.string(from: testDate))_INF_FN(5).mp4"
    XCTAssertEqual(HubFileNameGenerator.getDuplicateNumber(from: fileName2), 5)
  }

  func testIsSameBaseFile() {
    let fileName1 = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    let fileName2 = "\(dateFormatter.string(from: testDate))_INF_FN(3).mp4"
    let fileName3 = "\(dateFormatter.string(from: testDate))_INF_FT.mp4"

    XCTAssertTrue(HubFileNameGenerator.isSameBaseFile(fileName1, fileName2))
    XCTAssertFalse(HubFileNameGenerator.isSameBaseFile(fileName1, fileName3))
  }

  func testGetRelatedFileNames() throws {
    let baseFileName = "\(dateFormatter.string(from: testDate))_INF_FN.mp4"
    let relatedFiles = try HubFileNameGenerator.getRelatedFileNames(
      for: baseFileName,
      availableSuffixes: [.normal, .trimmed, .privacyBlurBefore]
    )

    XCTAssertEqual(relatedFiles.count, 3)
    XCTAssertEqual(relatedFiles[.normal], "\(dateFormatter.string(from: testDate))_INF_FN.mp4")
    XCTAssertEqual(relatedFiles[.trimmed], "\(dateFormatter.string(from: testDate))_INF_FT.mp4")
    XCTAssertEqual(relatedFiles[.privacyBlurBefore], "\(dateFormatter.string(from: testDate))_INF_FPB.mp4")
  }

  // MARK: - Thread Safety Tests

  func testConcurrentFileNameGeneration() {
    let expectation = XCTestExpectation(description: "Concurrent file name generation")
    expectation.expectedFulfillmentCount = 10

    let components = HubFileNameGenerator.FileNameComponents(
      dateTime: testDate,
      recordingType: .information,
      cameraChannel: .front,
      fileSuffix: .normal,
      fileExtension: .mp4
    )

    var generatedFileNames: [String] = []
    let lock = NSLock()

    for i in 0..<10 {
      DispatchQueue.global().async {
        let existingFiles = Array(0..<i).map { "\(self.dateFormatter.string(from: self.testDate))_INF_FN(\($0 + 2)).mp4" }
        let fileName = HubFileNameGenerator.generateUniqueFileName(
          from: components,
          existingFiles: existingFiles
        )

        lock.lock()
        generatedFileNames.append(fileName)
        lock.unlock()

        expectation.fulfill()
      }
    }

    wait(for: [expectation], timeout: 5.0)

    // All generated file names should be unique
    let uniqueFileNames = Set(generatedFileNames)
    XCTAssertEqual(generatedFileNames.count, uniqueFileNames.count)
  }

  // MARK: - Edge Case Tests

  func testAllRecordingTypes() {
    for recordingType in HubFileNameGenerator.RecordingType.allCases {
      let fileName = HubFileNameGenerator.generateFileName(
        dateTime: testDate,
        recordingType: recordingType,
        cameraChannel: .front,
        fileSuffix: .normal,
        fileExtension: .mp4
      )
      XCTAssertTrue(fileName.contains(recordingType.rawValue))
    }
  }

  func testAllCameraChannels() {
    for channel in HubFileNameGenerator.CameraChannel.allCases {
      let fileName = HubFileNameGenerator.generateFileName(
        dateTime: testDate,
        recordingType: .information,
        cameraChannel: channel,
        fileSuffix: .normal,
        fileExtension: .mp4
      )
      XCTAssertTrue(fileName.contains(channel.rawValue))
    }
  }

  func testAllFileSuffixes() {
    for suffix in HubFileNameGenerator.FileSuffix.allCases {
      let fileName = HubFileNameGenerator.generateFileName(
        dateTime: testDate,
        recordingType: .information,
        cameraChannel: .front,
        fileSuffix: suffix,
        fileExtension: .mp4
      )
      XCTAssertTrue(fileName.contains(suffix.rawValue))
    }
  }
}
