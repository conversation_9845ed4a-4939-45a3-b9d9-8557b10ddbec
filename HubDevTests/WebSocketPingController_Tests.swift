//
//  WebSocketPingController_Tests.swift
//  Hub
//
//  Created by ncn on 1/14/25.
//

@testable import Hub_Dev
import XCTest

public extension String {
  /// Returns a new unique string
  static var unique: String { UUID().uuidString }
}

public extension Int {
  static var unique: Int { .random(in: 1..<1000) }
}

// A concrete `WebSocketPingControllerDelegate` implementation allowing capturing the delegate calls
final class WebSocketPingController_Delegate: WebSocketPingControllerDelegate {
  var sendPing_calledCount = 0
  var disconnectOnNoPongReceived_calledCount = 0

  func sendPing() {
    sendPing_calledCount += 1
  }

  func disconnectOnNoPongReceived() {
    disconnectOnNoPongReceived_calledCount += 1
  }
}



final class WebSocketPingController_Tests: XCTestCase {
  var time: VirtualTime!
  var pingController: WebSocketPingController!
  private var delegate: WebSocketPingController_Delegate!

  override func setUp() {
    super.setUp()
    time = VirtualTime()
    VirtualTimeTimer.time = time
    pingController = .init(timerType: VirtualTimeTimer.self, timerQueue: .main)

    delegate = WebSocketPingController_Delegate()
    pingController.delegate = delegate
  }

  override func tearDown() {
    VirtualTimeTimer.invalidate()
    time = nil
    pingController = nil
    delegate = nil
    super.tearDown()
  }

  func test_sendPing_called_whenTheConnectionIsConnected() throws {
    assert(delegate.sendPing_calledCount == 0)

    // Check `sendPing` is not called when the connection is not connected
    time.run(numberOfSeconds: WebSocketPingController.pingTimeInterval + 1)
    XCTAssertEqual(delegate.sendPing_calledCount, 0)

    // Set the connection state as connected
    pingController.connectionStateDidChange(.connected(connectionId: .unique))

    // Simulate time passed 3x pingTimeInterval (+1 for margin errors)
    time.run(numberOfSeconds: 3 * (WebSocketPingController.pingTimeInterval + 1))
    XCTAssertEqual(delegate.sendPing_calledCount, 3)

    let oldPingCount = delegate.sendPing_calledCount

    // Set the connection state to not connected and check `sendPing` is no longer called
    pingController.connectionStateDidChange(.waitingForConnectionId)
    time.run(numberOfSeconds: 3 * (WebSocketPingController.pingTimeInterval + 1))
    XCTAssertEqual(delegate.sendPing_calledCount, oldPingCount)
  }

  func test_disconnectOnNoPongReceived_called_whenNoPongReceived() throws {
    // Set the connection state as connected
    pingController.connectionStateDidChange(.connected(connectionId: .unique))

    assert(delegate.sendPing_calledCount == 0)

    // Simulate time passing and wait for `sendPing` call
    while delegate.sendPing_calledCount != 1 {
      time.run(numberOfSeconds: 1)
    }

    // Simulate pong received
    pingController.pongReceived()

    // Simulate time passed pongTimeoutTimeInterval + 1 and check disconnectOnNoPongReceived wasn't called
    assert(delegate.disconnectOnNoPongReceived_calledCount == 0)
    time.run(numberOfSeconds: WebSocketPingController.pongTimeoutTimeInterval + 1)
    XCTAssertEqual(delegate.disconnectOnNoPongReceived_calledCount, 0)

    assert(delegate.sendPing_calledCount == 1)

    // Simulate time passing and wait for another `sendPing` call
    while delegate.sendPing_calledCount != 2 {
      time.run(numberOfSeconds: 1)
    }

    // Simulate time passed pongTimeoutTimeInterval + 1 without receiving a pong
    assert(delegate.disconnectOnNoPongReceived_calledCount == 0)
    time.run(numberOfSeconds: WebSocketPingController.pongTimeoutTimeInterval + 1)

    // `disconnectOnNoPongReceived` should be called
    XCTAssertEqual(delegate.disconnectOnNoPongReceived_calledCount, 1)
  }
}
