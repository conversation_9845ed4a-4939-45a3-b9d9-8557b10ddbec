//
//  WebSocketConnectionState_Tests.swift
//  Hub
//
//  Created by ncn on 1/14/25.
//

@testable import Hub_Dev
import XCTest

public struct TestError: Error, Equatable {
  let id = UUID()
}


final class WebSocketConnectionState_Tests: XCTestCase {
  // MARK: - Server error

  func test_disconnectionSource_serverError() {
    // Create test error
    let testError = ClientError(with: TestError())

    let testCases: [(WebSocketConnectionState.DisconnectionSource, ClientError?)] = [
      (.userInitiated, nil),
      (.systemInitiated, nil),
      (.noPongReceived, nil),
      (.serverInitiated(error: nil), nil),
      (.serverInitiated(error: testError), testError)
    ]

    testCases.forEach { source, serverError in
      XCTAssertEqual(source.serverError, serverError)
    }
  }

  // MARK: - Automatic reconnection

  func test_isAutomaticReconnectionEnabled_whenNotDisconnected_returnsFalse() {
    // Create array of connection states excluding disconnected state
    let connectionStates: [WebSocketConnectionState] = [
      .initialized,
      .connecting,
      .waitingForConnectionId,
      .connected(connectionId: .unique),
      .disconnecting(source: .userInitiated),
      .disconnecting(source: .systemInitiated),
      .disconnecting(source: .noPongReceived),
      .disconnecting(source: .serverInitiated(error: nil))
    ]

    for state in connectionStates {
      XCTAssertFalse(state.isAutomaticReconnectionEnabled)
    }
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedBySystem_returnsTrue() {
    let state: WebSocketConnectionState = .disconnected(source: .systemInitiated)
    XCTAssertTrue(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedWithNoPongReceived_returnsTrue() {
    let state: WebSocketConnectionState = .disconnected(source: .noPongReceived)
    XCTAssertTrue(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithoutError_returnsTrue() {
    let state: WebSocketConnectionState = .disconnected(source: .serverInitiated(error: nil))
    XCTAssertTrue(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithRandomError_returnsTrue() {
    //  by the server with random error
    let state: WebSocketConnectionState = .disconnected(source: .serverInitiated(error: ClientError(.unique)))
    XCTAssertTrue(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByUser_returnsFalse() {
    // by the user
    let state: WebSocketConnectionState = .disconnected(source: .userInitiated)
    XCTAssertFalse(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithInvalidTokenError_returnsFalse() {
    // Create invalid token error
    let invalidTokenError = ErrorPayload(
      code: ClosedRange.tokenInvalidErrorCodes.lowerBound,
      message: .unique,
      statusCode: .unique
    )

    // Create disconnected state intiated by the server with invalid token error
    let state: WebSocketConnectionState = .disconnected(
      source: .serverInitiated(error: ClientError(with: invalidTokenError))
    )

    // Assert `isAutomaticReconnectionEnabled` returns false
    XCTAssertFalse(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithExpiredToken_returnsTrue() {
    // Create expired token error
    let expiredTokenError = ErrorPayload(
      code: StreamErrorCode.expiredToken,
      message: .unique,
      statusCode: .unique
    )

    // Create disconnected state intiated by the server with invalid token error
    let state: WebSocketConnectionState = .disconnected(
      source: .serverInitiated(error: ClientError(with: expiredTokenError))
    )

    // Assert `isAutomaticReconnectionEnabled` returns true
    XCTAssertTrue(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithClientError_returnsFalse() {
    // Create client error
    let clientError = ErrorPayload(
      code: .unique,
      message: .unique,
      statusCode: ClosedRange.clientErrorCodes.lowerBound
    )

    // Create disconnected state intiated by the server with client error
    let state: WebSocketConnectionState = .disconnected(
      source: .serverInitiated(error: ClientError(with: clientError))
    )

    // Assert `isAutomaticReconnectionEnabled` returns false
    XCTAssertFalse(state.isAutomaticReconnectionEnabled)
  }

  func test_isAutomaticReconnectionEnabled_whenDisconnectedByServerWithStopError_returnsFalse() {
    // Create stop error
    let stopError = WebSocketEngineError(
      reason: .unique,
      code: WebSocketEngineError.stopErrorCode,
      engineError: nil
    )

    // Create disconnected state intiated by the server with stop error
    let state: WebSocketConnectionState = .disconnected(
      source: .serverInitiated(error: ClientError(with: stopError))
    )

    // Assert `isAutomaticReconnectionEnabled` returns false
    XCTAssertFalse(state.isAutomaticReconnectionEnabled)
  }
}
