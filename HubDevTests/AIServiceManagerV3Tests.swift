//
//  AIServiceManagerV3Tests.swift
//  HubDevTests
//
//  Created by ncn on 2025/07/07.
//

import XCTest
@testable import Hub

final class AIServiceManagerV3Tests: XCTestCase {
  
  var aiServiceManager: AIServiceManager!
  
  override func setUpWithError() throws {
    try super.setUpWithError()
    aiServiceManager = AIServiceManager.shared
  }
  
  override func tearDownWithError() throws {
    aiServiceManager = nil
    try super.tearDownWithError()
  }
  
  func testV3APIConfiguration() {
    // Given
    let initialValue = aiServiceManager.useV3API
    
    // When
    aiServiceManager.useV3API = true
    
    // Then
    XCTAssertTrue(aiServiceManager.useV3API)
    
    // Cleanup
    aiServiceManager.useV3API = initialValue
  }
  
  func testDeIdentifyVideoAutoUsesV2ByDefault() {
    // Given
    aiServiceManager.useV3API = false
    let expectation = XCTestExpectation(description: "Video de-identification should use v2 by default")
    
    // Mock video data
    let videoData = Data("mock video data".utf8)
    let fileName = "test_video.mp4"
    
    // When
    aiServiceManager.deIdentifyVideoAuto(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { progress in
        // Progress callback
      },
      progressCallback: { status in
        // Status callback
      },
      completion: { result in
        // This will likely fail due to no access key, but that's expected in tests
        expectation.fulfill()
      }
    )
    
    // Then
    wait(for: [expectation], timeout: 5.0)
  }
  
  func testDeIdentifyVideoAutoUsesV3WhenEnabled() {
    // Given
    aiServiceManager.useV3API = true
    let expectation = XCTestExpectation(description: "Video de-identification should use v3 when enabled")
    
    // Mock video data
    let videoData = Data("mock video data".utf8)
    let fileName = "test_video.mp4"
    
    // When
    aiServiceManager.deIdentifyVideoAuto(
      videoData: videoData,
      fileName: fileName,
      uploadProgress: { progress in
        // Progress callback
      },
      progressCallback: { status in
        // Status callback
      },
      completion: { result in
        // This will likely fail due to no access key, but that's expected in tests
        expectation.fulfill()
      }
    )
    
    // Then
    wait(for: [expectation], timeout: 5.0)
    
    // Cleanup
    aiServiceManager.useV3API = false
  }
  
  func testUploadUrlResponseModel() {
    // Given
    let jsonData = """
    {
      "url": "https://example.com/upload",
      "uuid": "12345-67890-abcdef"
    }
    """.data(using: .utf8)!
    
    // When
    do {
      let response = try JSONDecoder().decode(UploadUrlResponse.self, from: jsonData)
      
      // Then
      XCTAssertEqual(response.url, "https://example.com/upload")
      XCTAssertEqual(response.uuid, "12345-67890-abcdef")
    } catch {
      XCTFail("Failed to decode UploadUrlResponse: \(error)")
    }
  }
  
  func testCompleteUploadRequestModel() {
    // Given
    let request = CompleteUploadRequest(
      uuid: "test-uuid",
      resultRecv: .push,
      email: nil,
      os: .ios,
      pushToken: "test-token"
    )
    
    // When
    do {
      let jsonData = try JSONEncoder().encode(request)
      let decodedRequest = try JSONDecoder().decode(CompleteUploadRequest.self, from: jsonData)
      
      // Then
      XCTAssertEqual(decodedRequest.uuid, "test-uuid")
      XCTAssertEqual(decodedRequest.resultRecv, "PUSH")
      XCTAssertEqual(decodedRequest.os, "IOS")
      XCTAssertEqual(decodedRequest.pushToken, "test-token")
      XCTAssertNil(decodedRequest.email)
    } catch {
      XCTFail("Failed to encode/decode CompleteUploadRequest: \(error)")
    }
  }
}
